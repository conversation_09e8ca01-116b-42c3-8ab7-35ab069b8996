#!/usr/bin/env python3
"""Check current image URLs in database."""

import os
import sys

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Try to import from existing virtual environments
for venv in ['venv_dedup', 'venv', '.venv']:
    venv_path = os.path.join(os.path.dirname(__file__), venv)
    if os.path.exists(venv_path):
        site_packages = os.path.join(venv_path, 'lib', 'python3.10', 'site-packages')
        if os.path.exists(site_packages):
            sys.path.insert(0, site_packages)
            break

try:
    import psycopg2
    from urllib.parse import urlparse
except ImportError:
    print("Installing required packages...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "psycopg2-binary"])
    import psycopg2
    from urllib.parse import urlparse

# Load environment variables
def load_env():
    env_file = '.env'
    if os.path.exists(env_file):
        with open(env_file, 'r') as f:
            for line in f:
                if '=' in line and not line.strip().startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value.strip('"').strip("'")

load_env()

def main():
    database_url = os.environ.get('DATABASE_URL')
    if not database_url:
        print("ERROR: DATABASE_URL not found")
        return
    
    try:
        conn = psycopg2.connect(database_url)
        cur = conn.cursor()
        
        # Get all image URLs
        cur.execute("SELECT id, name, image_url FROM uses_products ORDER BY id")
        products = cur.fetchall()
        
        # Categorize URLs
        categories = {
            'Local Generated': 0,
            'Local Static': 0,
            'Supabase': 0,
            'Replicate': 0,
            'External': 0,
            'No Image': 0,
            'Other': 0
        }
        
        samples = {k: [] for k in categories.keys()}
        
        for product_id, name, image_url in products:
            if not image_url:
                category = 'No Image'
            elif '/generated_images/' in image_url:
                category = 'Local Generated'
            elif '/images/' in image_url:
                category = 'Local Static'
            elif 'supabase.co' in image_url:
                category = 'Supabase'
            elif 'replicate.delivery' in image_url:
                category = 'Replicate'
            elif image_url.startswith(('http://', 'https://')):
                category = 'External'
            else:
                category = 'Other'
            
            categories[category] += 1
            
            if len(samples[category]) < 3:
                samples[category].append({
                    'id': product_id,
                    'name': name[:50],
                    'url': image_url[:80] if image_url else 'None'
                })
        
        # Print results
        print("\nCURRENT IMAGE URL DISTRIBUTION")
        print("=" * 80)
        
        total = sum(categories.values())
        for category, count in sorted(categories.items(), key=lambda x: x[1], reverse=True):
            if count > 0:
                percentage = (count / total) * 100
                print(f"\n{category}: {count} products ({percentage:.1f}%)")
                
                if samples[category]:
                    print("  Examples:")
                    for sample in samples[category]:
                        print(f"    ID {sample['id']}: {sample['name']}")
                        print(f"       URL: {sample['url']}")
        
        print(f"\n{'='*80}")
        print(f"TOTAL PRODUCTS: {total}")
        
        # Check file system
        print("\n\nFILE SYSTEM CHECK:")
        print("-" * 80)
        
        if os.path.exists('generated_images'):
            files = [f for f in os.listdir('generated_images') if f.endswith('.png')]
            print(f"✓ generated_images/: {len(files)} PNG files")
        else:
            print("✗ generated_images/ directory not found!")
        
        if os.path.exists('HempResourceHub/client/public/images'):
            files = [f for f in os.listdir('HempResourceHub/client/public/images') 
                    if f.endswith(('.png', '.jpg', '.jpeg', '.webp'))]
            print(f"✓ client/public/images/: {len(files)} image files")
        else:
            print("✗ client/public/images/ directory not found!")
        
        # Check Express static serving
        print("\n\nEXPRESS STATIC CONFIGURATION:")
        print("-" * 80)
        server_file = 'HempResourceHub/server/index.ts'
        if os.path.exists(server_file):
            with open(server_file, 'r') as f:
                content = f.read()
                if 'express.static' in content and 'generated_images' in content:
                    print("✓ Express is configured to serve generated_images")
                else:
                    print("✗ Express may not be serving generated_images")
        
        cur.close()
        conn.close()
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()