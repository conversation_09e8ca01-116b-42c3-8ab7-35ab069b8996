# Action Plan: Reaching 10,000 Hemp Products

## Current Status
- **Products**: 1,498 (15% of goal)
- **Needed**: 8,502 more products
- **Timeline**: 45-60 days with optimization

## Immediate Actions (After Claude Code Restart)

### 1. Test Supabase MCP Write Access
```sql
-- Test write capabilities
UPDATE uses_products 
SET description = 'MCP Write Access Confirmed!' 
WHERE id = 1 
RETURNING name, description;
```

### 2. Run High-Volume Agents (Day 1-7)
```bash
# Activate the patent agent with 100+ patents
cd src/agents/specialized
python patent_mining_agent_simple.py

# Run academic research agent
python academic_research_agent.py

# Run regional/cultural agent
python regional_cultural_agent.py
```

### 3. Fix Template Descriptions (Day 1-3)
```sql
-- Via Supabase MCP, update remaining 635 templates
-- Update JSON templates (71 remaining)
-- Update cultural templates (492 remaining)
```

### 4. Fill Industry Gaps (Day 3-5)
Target 31 industries with 0 products:
- Aerospace Industry
- Medical Supplies
- Consumer Electronics
- Packaging Industry
- Bioplastics Manufacturing
- Animal Feed Industry
- And 25 more...

### 5. Scale Quality Agents (Day 5-10)
Focus on agents with 0.85+ confidence:
- AI Discovery Agent
- Innovation Agent
- Sustainability Agent
- Cannabinoids Agent

## Weekly Targets

### Week 1 (Days 1-7): Foundation
- **Goal**: Add 1,500 products → Total: 3,000
- **Actions**: 
  - Run all Phase 1 agents
  - Fix all template descriptions
  - Fill critical industry gaps

### Week 2 (Days 8-14): Acceleration
- **Goal**: Add 2,000 products → Total: 5,000
- **Actions**:
  - Deploy enhanced agents
  - Associate products with companies
  - Add industry-specific variations

### Week 3-4 (Days 15-28): Scale
- **Goal**: Add 3,000 products → Total: 8,000
- **Actions**:
  - Implement cross-reference agent
  - Mine academic papers
  - Expand patent variations

### Week 5-6 (Days 29-45): Completion
- **Goal**: Add 2,000 products → Total: 10,000
- **Actions**:
  - Niche discovery agent
  - Quality improvements
  - Final gap filling

## Automation Commands

### Set Up Continuous Running
```bash
# Create automation script
cat > run_all_agents.sh << 'EOF'
#!/bin/bash
source .env
export DATABASE_URL
source venv_dedup/bin/activate

# Run agents in sequence
echo "Starting agent automation..."

# Patent agent (est. 200-300 products)
python src/agents/specialized/patent_mining_agent_simple.py

# Academic agent (est. 100-200 products)
python src/agents/specialized/academic_research_agent.py

# Regional agent (est. 100-150 products)
python src/agents/specialized/regional_cultural_agent.py

# Industry gap filler
python target_underrepresented_industries.py

echo "Agent run complete!"
EOF

chmod +x run_all_agents.sh
```

### Schedule Hourly Runs
```bash
# Add to crontab
0 * * * * cd /home/<USER>/projects/HQz-Ai-DB-MCP-3 && ./run_all_agents.sh >> logs/agent_runs.log 2>&1
```

## Quality Assurance

### Monitor Progress
```sql
-- Via Supabase MCP - Daily progress check
SELECT 
  DATE(created_at) as date,
  COUNT(*) as products_added,
  AVG(confidence_score) as avg_confidence,
  COUNT(DISTINCT source_agent) as active_agents
FROM uses_products
WHERE created_at > NOW() - INTERVAL '7 days'
GROUP BY DATE(created_at)
ORDER BY date DESC;
```

### Quality Metrics
```sql
-- Check quality distribution
SELECT 
  CASE 
    WHEN confidence_score >= 0.9 THEN 'Excellent (0.9+)'
    WHEN confidence_score >= 0.8 THEN 'Good (0.8-0.9)'
    WHEN confidence_score >= 0.7 THEN 'Fair (0.7-0.8)'
    ELSE 'Needs Improvement (<0.7)'
  END as quality,
  COUNT(*) as count
FROM uses_products
GROUP BY quality
ORDER BY MIN(confidence_score) DESC;
```

## Success Metrics

### Daily Targets
- **Minimum**: 150 products/day
- **Target**: 200-250 products/day
- **Stretch**: 300+ products/day

### Quality Standards
- **Confidence Score**: Maintain >0.75 average
- **Unique Names**: 100% (no duplicates)
- **Description Quality**: No templates
- **Company Association**: >50%

## Next Steps After Restart

1. **Verify MCP Write Access**
2. **Run Patent Agent** (100+ patents ready)
3. **Fix Template Descriptions** via MCP
4. **Start Automation** with run_all_agents.sh
5. **Monitor Progress** hourly

## Contact for Issues
- MCP Issues: Check Claude Code logs
- Database Issues: Check Supabase dashboard
- Agent Issues: Check logs/agent_*.log files

---

Ready to scale to 10,000 products! 🚀