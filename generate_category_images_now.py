#!/usr/bin/env python3
"""
Generate 19 Category Images for Hemp Products
Using Replicate's Stable Diffusion API - Cost: ~$0.04
"""

import os
import json
import time
import requests
from datetime import datetime

# Check for Replicate token
REPLICATE_API_TOKEN = os.getenv('REPLICATE_API_TOKEN')

def check_replicate_setup():
    """Check if Replicate is configured"""
    if not REPLICATE_API_TOKEN:
        print("❌ REPLICATE_API_TOKEN not found!")
        print("\nTo set up Replicate:")
        print("1. Go to https://replicate.com/account/api-tokens")
        print("2. Create an account (free)")
        print("3. Copy your API token")
        print("4. Run: export REPLICATE_API_TOKEN='your-token-here'")
        return False
    
    print("✅ Replicate API token found")
    return True

def load_generation_queue():
    """Load the pre-defined category images to generate"""
    try:
        with open('phase1_implementation.json', 'r') as f:
            data = json.load(f)
            return data['generation_queue']
    except:
        # Fallback queue if file not found
        return [
            {
                "category": "food_beverages",
                "subcategory": "hemp_seeds",
                "prompt": "Professional product photo of organic hemp seeds in a white ceramic bowl, soft natural lighting, minimalist style, high quality commercial photography",
                "filename": "hemp_food_beverages_hemp_seeds.jpg"
            },
            {
                "category": "food_beverages", 
                "subcategory": "hemp_oil_culinary",
                "prompt": "Elegant glass bottle of golden hemp cooking oil, kitchen counter setting, warm lighting, gourmet food photography style",
                "filename": "hemp_food_beverages_hemp_oil_culinary.jpg"
            },
            {
                "category": "health_wellness",
                "subcategory": "cbd_oils", 
                "prompt": "Premium amber CBD oil bottle with dropper, zen wellness setting, soft focus background, product photography",
                "filename": "hemp_health_wellness_cbd_oils.jpg"
            },
            {
                "category": "textiles_fashion",
                "subcategory": "fabric",
                "prompt": "Roll of natural hemp fabric textile, sustainable fashion theme, texture visible, eco-friendly material showcase",
                "filename": "hemp_textiles_fashion_fabric.jpg"
            },
            {
                "category": "building_construction",
                "subcategory": "hempcrete",
                "prompt": "Stack of hempcrete building blocks, construction site background, sustainable building materials, industrial photography",
                "filename": "hemp_building_construction_hempcrete.jpg"
            }
        ]

def generate_with_replicate(prompt, filename):
    """Generate image using Replicate API"""
    
    # Stable Diffusion XL endpoint
    url = "https://api.replicate.com/v1/predictions"
    
    headers = {
        "Authorization": f"Token {REPLICATE_API_TOKEN}",
        "Content-Type": "application/json"
    }
    
    # SDXL model for high quality at low cost
    data = {
        "version": "39ed52f2a78e934b3ba6e2a89f5b1c712de7dfea535525255b1aa35c5565e08b",
        "input": {
            "prompt": prompt,
            "negative_prompt": "low quality, blurry, distorted, watermark, text, logo",
            "width": 1024,
            "height": 1024,
            "num_outputs": 1,
            "scheduler": "K_EULER",
            "num_inference_steps": 25,
            "guidance_scale": 7.5
        }
    }
    
    try:
        # Create prediction
        response = requests.post(url, headers=headers, json=data)
        if response.status_code != 201:
            print(f"❌ Error creating prediction: {response.text}")
            return None
            
        prediction = response.json()
        prediction_id = prediction['id']
        
        # Poll for completion
        print(f"⏳ Generating {filename}...")
        while True:
            time.sleep(2)
            status_response = requests.get(
                f"https://api.replicate.com/v1/predictions/{prediction_id}",
                headers=headers
            )
            
            if status_response.status_code != 200:
                print(f"❌ Error checking status: {status_response.text}")
                return None
                
            result = status_response.json()
            
            if result['status'] == 'succeeded':
                return result['output'][0]  # URL of generated image
            elif result['status'] == 'failed':
                print(f"❌ Generation failed: {result.get('error', 'Unknown error')}")
                return None
                
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def download_image(url, filename):
    """Download image from URL"""
    try:
        response = requests.get(url)
        if response.status_code == 200:
            os.makedirs('generated_category_images', exist_ok=True)
            filepath = f'generated_category_images/{filename}'
            with open(filepath, 'wb') as f:
                f.write(response.content)
            return filepath
        return None
    except Exception as e:
        print(f"❌ Download error: {e}")
        return None

def generate_free_alternative():
    """Alternative: Generate placeholder with free tools"""
    print("\n🎨 FREE ALTERNATIVE OPTIONS:")
    print("-" * 60)
    print("1. Use Bing Image Creator (free, requires Microsoft account)")
    print("   - Go to: https://www.bing.com/create")
    print("   - Use the prompts from phase1_implementation.json")
    print()
    print("2. Use Stable Diffusion locally (free, requires GPU)")
    print("   - Install: https://github.com/AUTOMATIC1111/stable-diffusion-webui")
    print("   - Run on your machine")
    print()
    print("3. Use existing free stock photos")
    print("   - Unsplash: https://unsplash.com/s/photos/hemp")
    print("   - Pexels: https://www.pexels.com/search/hemp/")
    print("   - Pixabay: https://pixabay.com/images/search/hemp/")

def main():
    print("🌿 HEMP CATEGORY IMAGE GENERATOR")
    print("=" * 60)
    
    # Check setup
    if not check_replicate_setup():
        generate_free_alternative()
        return
    
    # Load queue
    queue = load_generation_queue()[:5]  # Start with just 5 for testing
    
    print(f"\n📋 Images to generate: {len(queue)}")
    print(f"💰 Estimated cost: ${len(queue) * 0.0023:.4f}")
    
    # Confirm
    print("\n⚠️  This will use your Replicate credits")
    response = input("Continue? (y/n): ")
    if response.lower() != 'y':
        print("Cancelled.")
        generate_free_alternative()
        return
    
    # Generate images
    results = []
    total_cost = 0
    
    for i, item in enumerate(queue, 1):
        print(f"\n[{i}/{len(queue)}] Generating: {item['subcategory']}")
        
        # Generate
        image_url = generate_with_replicate(item['prompt'], item['filename'])
        
        if image_url:
            # Download
            filepath = download_image(image_url, item['filename'])
            if filepath:
                print(f"✅ Saved: {filepath}")
                results.append({
                    'category': item['category'],
                    'subcategory': item['subcategory'],
                    'filename': item['filename'],
                    'filepath': filepath,
                    'url': image_url
                })
                total_cost += 0.0023
            else:
                print(f"❌ Failed to download")
        else:
            print(f"❌ Failed to generate")
        
        # Rate limit
        if i < len(queue):
            time.sleep(2)
    
    # Summary
    print("\n" + "=" * 60)
    print(f"✅ GENERATION COMPLETE")
    print(f"Images generated: {len(results)}/{len(queue)}")
    print(f"Total cost: ${total_cost:.4f}")
    
    # Save results
    with open('generated_category_images/results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n📁 Images saved in: generated_category_images/")
    print(f"📋 Results saved in: generated_category_images/results.json")
    
    # Next steps
    print("\n🚀 NEXT STEPS:")
    print("1. Upload images to Supabase storage")
    print("2. Run category_image_mapping.sql to assign to products")
    print("3. Verify coverage with quality dashboard")

if __name__ == "__main__":
    main()