#!/usr/bin/env python3
"""
Comprehensive prompt templates for hemp product image generation
Organized by industry and product type for consistent, high-quality results
"""

class HempImagePromptTemplates:
    """
    Industry-specific prompt templates for generating appropriate hemp product images
    """
    
    # Base style instructions for all images
    BASE_STYLE = "professional product photography, high quality, commercial grade, clean composition"
    
    # Negative prompts to avoid inappropriate content
    NEGATIVE_PROMPTS = [
        "no human faces", "no portraits", "no people",
        "no marijuana leaves", "no smoking", "no recreational drugs",
        "no living rooms", "no home interiors", "no furniture",
        "no keyboards", "no computers", "no office equipment"
    ]
    
    # Industry-specific templates
    FOOD_NUTRITION = {
        "base": f"{BASE_STYLE}, food product photography, appetizing presentation",
        "tincture": "amber glass tincture bottle with dropper, {product_name}, hemp leaf logo, white label, kitchen counter setting, natural lighting, wellness product",
        "powder": "protein powder container or packet, {product_name}, scoop with powder visible, fitness setting, green and white color scheme, nutritional supplement",
        "oil": "culinary hemp oil bottle, {product_name}, kitchen setting with fresh ingredients, golden-green oil color, gourmet food product",
        "snack": "packaged hemp snack product, {product_name}, healthy snack presentation, visible hemp seeds or ingredients, natural packaging",
        "beverage": "hemp-infused beverage bottle or can, {product_name}, refreshing drink presentation, condensation on container, lifestyle product"
    }
    
    COSMETICS_BEAUTY = {
        "base": f"{BASE_STYLE}, luxury beauty product, elegant packaging, spa aesthetic",
        "moisturizer": "elegant moisturizer jar or bottle, {product_name}, hemp seed oil branding, white or green packaging, cream texture visible, premium skincare",
        "serum": "glass serum bottle with dropper, {product_name}, golden or clear serum visible, minimalist beauty packaging, botanical elements",
        "balm": "lip balm or body balm container, {product_name}, natural cosmetic product, hemp leaf icon, sustainable packaging",
        "soap": "artisan hemp soap bar, {product_name}, natural texture visible, eco-friendly wrapper, spa setting, handcrafted appearance",
        "shampoo": "hemp shampoo bottle, {product_name}, shower or bathroom setting, green botanical design, sulfate-free labeling"
    }
    
    MEDICAL_HEALTHCARE = {
        "base": f"{BASE_STYLE}, clinical presentation, pharmaceutical grade, medical aesthetic",
        "cbd_oil": "pharmaceutical CBD oil bottle, {product_name}, measured dropper, dosage information visible, white medical packaging, lab quality",
        "capsules": "supplement bottle with capsules, {product_name}, gel caps visible through container, clinical white background, dosage label",
        "topical": "medical hemp cream or gel tube, {product_name}, pharmaceutical packaging, pain relief branding, clinical presentation",
        "patch": "transdermal hemp patch packaging, {product_name}, medical device appearance, clear dosage information, sterile packaging",
        "spray": "medical spray bottle, {product_name}, measured dose dispenser, pharmaceutical labeling, clinical white design"
    }
    
    TEXTILES_FASHION = {
        "base": f"{BASE_STYLE}, fashion photography, textile focus, sustainable materials",
        "fabric": "hemp fabric roll or swatch, {product_name}, texture detail visible, natural fiber appearance, fashion atelier setting",
        "clothing": "hemp clothing item on hanger or mannequin, {product_name}, sustainable fashion tag, natural colors, eco-friendly branding",
        "rope": "coiled hemp rope or twine, {product_name}, natural fiber texture, marine or industrial setting, strong durable appearance",
        "canvas": "hemp canvas material, {product_name}, artist or industrial application, durable textile, natural beige color",
        "yarn": "skeins of hemp yarn, {product_name}, craft setting, natural fiber texture, knitting or weaving context"
    }
    
    CONSTRUCTION_MATERIALS = {
        "base": f"{BASE_STYLE}, industrial photography, construction context, durability focus",
        "hempcrete": "hempcrete blocks stacked, {product_name}, construction site setting, sustainable building material, texture visible",
        "insulation": "hemp insulation batts or rolls, {product_name}, cross-section view, energy efficient branding, installation context",
        "board": "hemp fiber board sheets, {product_name}, construction material, smooth finish, industrial strength appearance",
        "composite": "hemp composite material sample, {product_name}, engineering material, technical specifications visible, industrial application"
    }
    
    AGRICULTURE_GARDENING = {
        "base": f"{BASE_STYLE}, agricultural setting, organic farming, sustainable agriculture",
        "biochar": "hemp biochar in burlap sack, {product_name}, rich black granules, soil amendment product, garden setting",
        "mulch": "hemp mulch spread in garden, {product_name}, natural brown color, plant beds visible, sustainable gardening",
        "fertilizer": "organic hemp fertilizer bag, {product_name}, granular or pellet form visible, farm or garden context",
        "growing_medium": "hemp growing medium in pot or bag, {product_name}, seedlings or plants visible, greenhouse setting"
    }
    
    PLASTICS_POLYMERS = {
        "base": f"{BASE_STYLE}, industrial materials, sustainable plastics, technical photography",
        "pellets": "hemp plastic pellets in container, {product_name}, raw material form, injection molding context, natural beige color",
        "sheet": "hemp plastic sheet material, {product_name}, smooth surface, flexibility demonstration, eco-friendly branding",
        "packaging": "hemp-based packaging products, {product_name}, biodegradable labels, sustainable packaging examples",
        "filament": "3D printing filament spool, {product_name}, hemp-based material, maker space setting, sustainable manufacturing"
    }
    
    @classmethod
    def get_prompt(cls, industry: str, product_type: str, product_name: str, 
                   additional_details: str = "") -> str:
        """
        Get a complete prompt for image generation
        
        Args:
            industry: Industry category (food, cosmetics, medical, etc.)
            product_type: Specific product type (tincture, moisturizer, etc.)
            product_name: Name of the product
            additional_details: Any additional details to include
            
        Returns:
            Complete prompt string
        """
        # Get industry templates
        industry_templates = {
            "food": cls.FOOD_NUTRITION,
            "cosmetics": cls.COSMETICS_BEAUTY,
            "medical": cls.MEDICAL_HEALTHCARE,
            "textiles": cls.TEXTILES_FASHION,
            "construction": cls.CONSTRUCTION_MATERIALS,
            "agriculture": cls.AGRICULTURE_GARDENING,
            "plastics": cls.PLASTICS_POLYMERS
        }
        
        templates = industry_templates.get(industry.lower(), cls.FOOD_NUTRITION)
        
        # Get specific template or fall back to base
        template = templates.get(product_type.lower(), templates["base"])
        
        # Format with product name
        prompt = template.format(product_name=product_name)
        
        # Add additional details
        if additional_details:
            prompt += f", {additional_details}"
        
        # Add negative prompts
        prompt += ", " + ", ".join(cls.NEGATIVE_PROMPTS)
        
        return prompt
    
    @classmethod
    def get_product_type_from_name(cls, product_name: str, description: str = "") -> str:
        """
        Infer product type from product name and description
        """
        name_lower = product_name.lower()
        desc_lower = description.lower() if description else ""
        combined = f"{name_lower} {desc_lower}"
        
        # Check for specific product types
        type_keywords = {
            "tincture": ["tincture", "drops", "sublingual"],
            "powder": ["powder", "protein powder", "supplement powder"],
            "oil": ["oil", "hemp oil", "seed oil"],
            "moisturizer": ["moisturizer", "cream", "lotion", "facial"],
            "serum": ["serum", "face oil", "treatment"],
            "capsules": ["capsule", "caps", "pill", "tablet", "softgel"],
            "fabric": ["fabric", "textile", "cloth", "material"],
            "hempcrete": ["hempcrete", "concrete", "block"],
            "biochar": ["biochar", "char", "carbon"],
            "pellets": ["pellet", "resin", "polymer", "plastic"]
        }
        
        for product_type, keywords in type_keywords.items():
            if any(keyword in combined for keyword in keywords):
                return product_type
        
        return "general"

# Example usage and testing
if __name__ == "__main__":
    templates = HempImagePromptTemplates()
    
    # Test cases
    test_products = [
        ("CBG Focus Tincture", "medical", "tincture"),
        ("Hemp Protein Powder", "food", "powder"),
        ("Hemp Seed Moisturizer", "cosmetics", "moisturizer"),
        ("Hempcrete Building Blocks", "construction", "hempcrete"),
        ("Hemp Biochar Soil Amendment", "agriculture", "biochar")
    ]
    
    print("Generated Prompts:\n")
    for name, industry, product_type in test_products:
        prompt = templates.get_prompt(industry, product_type, name)
        print(f"Product: {name}")
        print(f"Prompt: {prompt}\n")
        print("-" * 80 + "\n")