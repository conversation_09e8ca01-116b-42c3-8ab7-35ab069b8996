#!/usr/bin/env python3
"""
Run Company Enrichment Pipeline
- Enriches existing companies with missing data
- Links orphaned products to companies
- Creates new companies from products without matches
"""

import os
import sys
import logging
from datetime import datetime

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'logs/company_enrichment_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def main():
    """Run all company enrichment processes"""
    logger.info("Starting company enrichment pipeline...")
    
    try:
        # Step 1: Run the specialized company enrichment agent
        logger.info("\n=== Step 1: Running Company Enrichment Agent ===")
        from src.agents.specialized.company_enrichment_agent import CompanyEnrichmentAgent
        
        enrichment_agent = CompanyEnrichmentAgent()
        
        # Enrich existing companies (web scraping, metadata extraction)
        logger.info("Enriching companies with missing data...")
        enrichment_agent.run_enrichment(limit=50)
        
        # Link products by name matching
        logger.info("Linking products to companies by name matching...")
        enrichment_agent.link_products_by_name_matching()
        
    except Exception as e:
        logger.error(f"Error in company enrichment agent: {str(e)}")
        logger.info("Continuing with smart company enrichment...")
    
    try:
        # Step 2: Run smart company enrichment (AI-based matching)
        logger.info("\n=== Step 2: Running Smart Company Enrichment ===")
        from src.agents.smart_company_enrichment import SmartCompanyEnrichment
        
        smart_enricher = SmartCompanyEnrichment()
        
        # First, link existing companies to products
        logger.info("Matching products to existing companies...")
        smart_enricher.match_products_to_companies()
        
        # Then create new companies for unmatched products
        logger.info("Creating companies for unmatched products...")
        smart_enricher.create_new_companies_from_products()
        
    except Exception as e:
        logger.error(f"Error in smart company enrichment: {str(e)}")
    
    # Step 3: Generate summary report
    logger.info("\n=== Generating Summary Report ===")
    try:
        import psycopg2
        from psycopg2.extras import RealDictCursor
        from dotenv import load_dotenv
        
        load_dotenv()
        DATABASE_URL = os.getenv('DATABASE_URL', '').replace('pooler.supabase.com:6543', 'db.ktoqznqmlnxrtvubewyz.supabase.co:5432')
        
        with psycopg2.connect(DATABASE_URL, cursor_factory=RealDictCursor) as conn:
            with conn.cursor() as cur:
                # Get updated statistics
                cur.execute("""
                    WITH company_stats AS (
                        SELECT 
                            COUNT(*) as total_companies,
                            COUNT(CASE WHEN website IS NOT NULL THEN 1 END) as with_website,
                            COUNT(CASE WHEN country IS NOT NULL THEN 1 END) as with_country,
                            COUNT(CASE WHEN founded_year IS NOT NULL THEN 1 END) as with_founded_year,
                            COUNT(CASE WHEN verified = true THEN 1 END) as verified
                        FROM hemp_companies
                    ),
                    product_stats AS (
                        SELECT 
                            COUNT(DISTINCT company_id) as companies_with_products,
                            COUNT(*) as total_relationships
                        FROM hemp_company_products
                    )
                    SELECT * FROM company_stats, product_stats
                """)
                
                stats = cur.fetchone()
                
                logger.info("\n=== Final Statistics ===")
                logger.info(f"Total Companies: {stats['total_companies']}")
                logger.info(f"With Website: {stats['with_website']} ({stats['with_website']/stats['total_companies']*100:.1f}%)")
                logger.info(f"With Country: {stats['with_country']} ({stats['with_country']/stats['total_companies']*100:.1f}%)")
                logger.info(f"With Founded Year: {stats['with_founded_year']} ({stats['with_founded_year']/stats['total_companies']*100:.1f}%)")
                logger.info(f"Verified: {stats['verified']} ({stats['verified']/stats['total_companies']*100:.1f}%)")
                logger.info(f"With Products: {stats['companies_with_products']} ({stats['companies_with_products']/stats['total_companies']*100:.1f}%)")
                logger.info(f"Total Product Links: {stats['total_relationships']}")
                
    except Exception as e:
        logger.error(f"Error generating summary: {str(e)}")
    
    logger.info("\n✅ Company enrichment pipeline completed!")

if __name__ == "__main__":
    main()