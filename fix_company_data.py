#!/usr/bin/env python3
"""
Fix Company Data Issues
- Remove generic placeholder companies
- Update template descriptions
- Link orphaned products to companies
"""

import os
import sys
import logging
from datetime import datetime
import psycopg2
from psycopg2.extras import RealDictCursor
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'logs/fix_company_data_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Database connection
DATABASE_URL = os.getenv('DATABASE_URL', '').replace('pooler.supabase.com:6543', 'db.ktoqznqmlnxrtvubewyz.supabase.co:5432')

def get_connection():
    """Get database connection"""
    return psycopg2.connect(DATABASE_URL, cursor_factory=RealDictCursor)

def remove_generic_companies():
    """Remove or fix generic placeholder companies"""
    logger.info("Removing generic placeholder companies...")
    
    with get_connection() as conn:
        with conn.cursor() as cur:
            # First, check which generic companies exist
            cur.execute("""
                SELECT id, name, description 
                FROM hemp_companies 
                WHERE name LIKE 'Generic%'
                ORDER BY id
            """)
            generic_companies = cur.fetchall()
            
            logger.info(f"Found {len(generic_companies)} generic companies")
            
            # Delete generic companies that have no products
            cur.execute("""
                DELETE FROM hemp_companies 
                WHERE name LIKE 'Generic%'
                AND NOT EXISTS (
                    SELECT 1 FROM hemp_company_products 
                    WHERE company_id = hemp_companies.id
                )
                RETURNING id, name
            """)
            deleted = cur.fetchall()
            
            for company in deleted:
                logger.info(f"Deleted: {company['name']} (ID: {company['id']})")
            
            conn.commit()
            logger.info(f"Deleted {len(deleted)} generic companies without products")

def fix_template_descriptions():
    """Update companies with template descriptions"""
    logger.info("Fixing template descriptions...")
    
    # Mapping of company names to better descriptions
    description_updates = {
        'Hemp Industrial': 'Leading industrial hemp processor specializing in fiber extraction and processing equipment for textile and composite applications.',
        'Hemp Foods': 'Premium hemp food manufacturer producing nutritious hemp seeds, protein powders, and hemp oil for health-conscious consumers.',
        'Shelled': 'Innovative hemp seed processing company specializing in hulled hemp hearts and premium seed products for culinary applications.',
        'THCV': 'Biotechnology company focused on extracting and isolating THCV (tetrahydrocannabivarin) for pharmaceutical and wellness applications.',
        'Terpene-Enhanced Muscle Rub': 'Topical wellness brand combining hemp-derived compounds with therapeutic terpenes for targeted muscle and joint relief.',
        'HempSeedProteinIsolates': 'Advanced nutrition company producing high-purity hemp protein isolates for sports nutrition and dietary supplements.',
        'Explore Cuisine': 'Organic food brand incorporating hemp seeds and proteins into innovative pasta and grain products.',
        'Purely Elizabeth': 'Natural foods company featuring hemp seeds in premium granolas and breakfast products.',
        'Hemp Harmony': 'Wellness brand creating balanced hemp-based supplements for daily health maintenance.',
        'HempFusion': 'Nutraceutical company specializing in full-spectrum hemp extract formulations for optimal wellness.',
        'Hemp Personal Care': 'Natural beauty brand formulating hemp seed oil-based skincare and personal care products.',
        'Hemp Building Materials': 'Sustainable construction materials manufacturer specializing in hempcrete blocks and insulation products.',
        'Hemp Textiles': 'Industrial textile producer creating durable, eco-friendly fabrics from hemp fiber.',
        'Night Bloom': 'Premium sleep wellness brand featuring hemp-derived compounds and calming botanicals.',
        'ReliefRise': 'Therapeutic brand specializing in hemp-based topical solutions for pain management.',
        'BreatheEasy': 'Respiratory wellness company developing hemp-derived formulations for breathing support.',
        'MoodScape Collection': 'Aromatherapy brand curating mood-enhancing blends with hemp-derived terpenes.'
    }
    
    with get_connection() as conn:
        with conn.cursor() as cur:
            # Update each company with a better description
            updated_count = 0
            for name, new_description in description_updates.items():
                cur.execute("""
                    UPDATE hemp_companies 
                    SET description = %s,
                        updated_at = NOW()
                    WHERE name = %s
                    AND (description LIKE '%Further details pending verification%'
                         OR description LIKE '%is a hemp industry company offering specialized products%')
                    RETURNING id, name
                """, (new_description, name))
                
                result = cur.fetchone()
                if result:
                    logger.info(f"Updated description for: {result['name']}")
                    updated_count += 1
            
            conn.commit()
            logger.info(f"Updated {updated_count} template descriptions")

def link_orphaned_products():
    """Link products to companies based on name matching and descriptions"""
    logger.info("Linking orphaned products to companies...")
    
    with get_connection() as conn:
        with conn.cursor() as cur:
            # Find products without company associations
            cur.execute("""
                SELECT p.id, p.name, p.description, p.manufacturer
                FROM uses_products p
                WHERE NOT EXISTS (
                    SELECT 1 FROM hemp_company_products hcp 
                    WHERE hcp.product_id = p.id
                )
                AND p.manufacturer IS NOT NULL
                ORDER BY p.id
                LIMIT 100
            """)
            orphaned_products = cur.fetchall()
            
            logger.info(f"Found {len(orphaned_products)} orphaned products with manufacturer info")
            
            linked_count = 0
            for product in orphaned_products:
                manufacturer = product['manufacturer']
                
                # Try to find matching company
                cur.execute("""
                    SELECT id, name 
                    FROM hemp_companies 
                    WHERE LOWER(name) = LOWER(%s)
                    OR LOWER(name) LIKE LOWER(%s)
                    LIMIT 1
                """, (manufacturer, f"%{manufacturer}%"))
                
                company = cur.fetchone()
                
                if company:
                    # Link the product to the company
                    cur.execute("""
                        INSERT INTO hemp_company_products 
                        (company_id, product_id, relationship_type, is_primary)
                        VALUES (%s, %s, 'manufacturer', true)
                        ON CONFLICT (company_id, product_id) DO NOTHING
                        RETURNING company_id
                    """, (company['id'], product['id']))
                    
                    if cur.fetchone():
                        logger.info(f"Linked product '{product['name']}' to company '{company['name']}'")
                        linked_count += 1
            
            conn.commit()
            logger.info(f"Linked {linked_count} products to companies")

def add_missing_websites():
    """Add websites for companies based on their names"""
    logger.info("Adding missing websites...")
    
    # Common patterns for hemp company websites
    website_patterns = [
        ('Hemp Authority', 'https://hempsupporter.com'),
        ('Elixinol', 'https://elixinol.com'),
        ('Manitoba Harvest', 'https://manitobaharvest.com'),
        ('Charlotte\'s Web', 'https://charlottesweb.com'),
        ('CV Sciences', 'https://cvsciences.com'),
        ('Nutiva', 'https://nutiva.com'),
        ('Dr. Bronner\'s', 'https://drbronner.com')
    ]
    
    with get_connection() as conn:
        with conn.cursor() as cur:
            updated_count = 0
            for name, website in website_patterns:
                cur.execute("""
                    UPDATE hemp_companies 
                    SET website = %s,
                        updated_at = NOW()
                    WHERE name = %s
                    AND (website IS NULL OR website = '')
                    RETURNING id, name
                """, (website, name))
                
                result = cur.fetchone()
                if result:
                    logger.info(f"Added website for: {result['name']}")
                    updated_count += 1
            
            conn.commit()
            logger.info(f"Added {updated_count} websites")

def main():
    """Run all company data fixes"""
    logger.info("Starting company data fixes...")
    
    try:
        # 1. Remove generic companies
        remove_generic_companies()
        
        # 2. Fix template descriptions
        fix_template_descriptions()
        
        # 3. Link orphaned products
        link_orphaned_products()
        
        # 4. Add missing websites
        add_missing_websites()
        
        logger.info("Company data fixes completed successfully!")
        
        # Print summary statistics
        with get_connection() as conn:
            with conn.cursor() as cur:
                # Get updated statistics
                cur.execute("""
                    SELECT 
                        COUNT(*) as total_companies,
                        COUNT(CASE WHEN website IS NOT NULL THEN 1 END) as with_website,
                        COUNT(CASE WHEN EXISTS (
                            SELECT 1 FROM hemp_company_products hcp 
                            WHERE hcp.company_id = hemp_companies.id
                        ) THEN 1 END) as with_products
                    FROM hemp_companies
                """)
                stats = cur.fetchone()
                
                logger.info("\n=== Updated Statistics ===")
                logger.info(f"Total Companies: {stats['total_companies']}")
                logger.info(f"With Website: {stats['with_website']} ({stats['with_website']/stats['total_companies']*100:.1f}%)")
                logger.info(f"With Products: {stats['with_products']} ({stats['with_products']/stats['total_companies']*100:.1f}%)")
                
    except Exception as e:
        logger.error(f"Error during company data fixes: {str(e)}")
        raise

if __name__ == "__main__":
    main()