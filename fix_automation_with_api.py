#!/usr/bin/env python3
"""
Update automation scripts to use Supabase REST API instead of direct database connection
This solves the IPv6 connectivity issue in WSL2
"""

import os
import subprocess

print("🔧 Updating Automation Scripts to Use Supabase API")
print("=" * 50)

# Update the fix_all_templates.py to use REST API
template_fixer_api = '''#!/usr/bin/env python3
"""
Fix all remaining template descriptions using Supabase REST API
"""

import os
import requests
import json
from datetime import datetime
import random

# Supabase configuration
SUPABASE_URL = "https://ktoqznqmlnxrtvubewyz.supabase.co"
SUPABASE_ANON_KEY = os.getenv('SUPABASE_ANON_KEY')
if not SUPABASE_ANON_KEY:
    print("ERROR: SUPABASE_ANON_KEY environment variable not set")
    print("Get it from: https://supabase.com/dashboard/project/ktoqznqmlnxrtvubewyz/settings/api")
    exit(1)

headers = {
    "apikey": SUPABASE_ANON_KEY,
    "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
    "Content-Type": "application/json"
}

def generate_product_description(name, industry_id):
    """Generate a meaningful description based on product name and industry"""
    
    name_lower = name.lower()
    
    # Industry-specific templates
    templates = {
        'fiber': [
            f"High-quality {name} designed for sustainable textile manufacturing",
            f"Premium {name} offering superior strength and durability",
            f"Eco-friendly {name} perfect for industrial applications"
        ],
        'oil': [
            f"Pure {name} extracted using advanced processing methods",
            f"Nutrient-rich {name} for health and wellness applications",
            f"Versatile {name} suitable for cosmetic and therapeutic use"
        ],
        'food': [
            f"Nutritious {name} packed with essential nutrients",
            f"Organic {name} cultivated using sustainable farming practices",
            f"Premium quality {name} for culinary applications"
        ],
        'construction': [
            f"Durable {name} engineered for modern construction needs",
            f"Sustainable {name} offering excellent structural properties",
            f"High-performance {name} for eco-friendly building projects"
        ]
    }
    
    # Determine category
    if any(word in name_lower for word in ['fiber', 'textile', 'fabric', 'yarn']):
        category = 'fiber'
    elif any(word in name_lower for word in ['oil', 'extract', 'essence']):
        category = 'oil'
    elif any(word in name_lower for word in ['food', 'protein', 'seed', 'flour']):
        category = 'food'
    elif any(word in name_lower for word in ['concrete', 'panel', 'insulation', 'board']):
        category = 'construction'
    else:
        # Generic templates
        templates['generic'] = [
            f"Innovative {name} designed for sustainable applications",
            f"High-quality {name} manufactured to industry standards",
            f"Versatile {name} suitable for various commercial uses"
        ]
        category = 'generic'
    
    return random.choice(templates[category])

def fix_template_descriptions():
    """Fix template descriptions using Supabase REST API"""
    
    print("🔍 Finding products with template descriptions...")
    
    # Query for template descriptions
    response = requests.get(
        f"{SUPABASE_URL}/rest/v1/uses_products",
        headers=headers,
        params={
            "select": "id,name,description,industry_id",
            "or": "(description.ilike.%sustainable solution%,description.ilike.%essential component%,description.ilike.%{"*"}%)",
            "limit": "100"
        }
    )
    
    if response.status_code != 200:
        print(f"❌ Error fetching products: {response.text}")
        return
    
    products = response.json()
    print(f"Found {len(products)} products with template descriptions")
    
    if not products:
        print("✅ No more template descriptions to fix!")
        return
    
    # Fix each product
    fixed_count = 0
    for product in products:
        try:
            new_description = generate_product_description(product['name'], product['industry_id'])
            
            # Update the product
            update_response = requests.patch(
                f"{SUPABASE_URL}/rest/v1/uses_products",
                headers=headers,
                params={"id": f"eq.{product['id']}"},
                json={"description": new_description}
            )
            
            if update_response.status_code == 204:
                fixed_count += 1
                print(f"  ✅ Fixed: {product['name']}")
            else:
                print(f"  ❌ Failed to update {product['name']}: {update_response.text}")
                
        except Exception as e:
            print(f"  ❌ Error processing {product['name']}: {e}")
    
    print(f"\\n✅ Fixed {fixed_count} template descriptions")

if __name__ == "__main__":
    fix_template_descriptions()
'''

# Write the new template fixer
with open('fix_all_templates_api.py', 'w') as f:
    f.write(template_fixer_api)
os.chmod('fix_all_templates_api.py', 0o755)

# Update cron_automation.sh to set API key
cron_update = '''
# Add this after the DATABASE_URL export
export SUPABASE_ANON_KEY="YOUR_ANON_KEY_HERE"

# Replace the template fixer call with:
python fix_all_templates_api.py >> logs/template_fixer_$(date +%Y%m%d_%H%M%S).log 2>&1
'''

print("\n✅ Created fix_all_templates_api.py")
print("\n📝 Next steps:")
print("1. Get your Supabase anon key from:")
print("   https://supabase.com/dashboard/project/ktoqznqmlnxrtvubewyz/settings/api")
print("")
print("2. Update cron_automation.sh to include:")
print(cron_update)
print("")
print("3. For the patent mining agent, consider using MCP tools or")
print("   the Supabase REST API similarly")
print("")
print("Alternative: Use a proxy/tunnel service to provide IPv4 connectivity")