#!/usr/bin/env python3
"""Quick test of agents to verify they're working"""

import os
import sys
import psycopg2

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from agents.specialized.patent_mining_agent_simple import SimplifiedPatentMiningAgent
from agents.specialized.academic_research_agent import AcademicResearchAgent

def test_agents():
    database_url = os.environ.get('DATABASE_URL')
    if not database_url:
        print("❌ DATABASE_URL not set")
        return
    
    print("🧪 Testing Fixed Agents...")
    print("=" * 50)
    
    # Test Patent Agent
    print("\n1. Testing Patent Mining Agent...")
    try:
        patent_agent = SimplifiedPatentMiningAgent()
        # Just process first patent
        if patent_agent.patent_examples:
            patent = patent_agent.patent_examples[0]
            products = patent_agent.generate_products_from_patent(patent)
            print(f"   ✓ Generated {len(products)} products from patent")
            
            # Try to save one
            if products:
                success = patent_agent.save_product(products[0])
                if success:
                    print(f"   ✓ Successfully saved: {products[0]['name']}")
                else:
                    print(f"   ❌ Failed to save product")
        else:
            print("   ❌ No patents loaded")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test Academic Agent
    print("\n2. Testing Academic Research Agent...")
    try:
        academic_agent = AcademicResearchAgent()
        # Test with one topic
        topic = academic_agent.research_topics[0]
        papers = academic_agent.search_pubmed(topic, max_results=1)
        print(f"   ✓ Found {len(papers)} papers for '{topic}'")
        
        if papers:
            products = academic_agent.generate_products_from_paper(papers[0], topic)
            print(f"   ✓ Generated {len(products)} products from paper")
            
            if products:
                success = academic_agent.save_product(products[0])
                if success:
                    print(f"   ✓ Successfully saved: {products[0]['name']}")
                else:
                    print(f"   ❌ Failed to save product")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Check database
    print("\n3. Verifying Database...")
    try:
        conn = psycopg2.connect(database_url)
        cur = conn.cursor()
        
        cur.execute("""
            SELECT COUNT(*), source_agent 
            FROM uses_products 
            WHERE created_at >= CURRENT_TIMESTAMP - INTERVAL '5 minutes'
            GROUP BY source_agent
        """)
        
        recent = cur.fetchall()
        if recent:
            print("   Recent additions:")
            for count, agent in recent:
                print(f"   - {agent}: {count} products")
        else:
            print("   ❌ No recent additions")
        
        conn.close()
    except Exception as e:
        print(f"   ❌ Database error: {e}")
    
    print("\n✅ Agent test complete!")

if __name__ == "__main__":
    test_agents()