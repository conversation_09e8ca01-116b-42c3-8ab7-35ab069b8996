#!/bin/bash
# Activate Company Enrichment Agent

echo "🏢 Activating Company Enrichment Agent..."

# Check if virtual environment exists
if [ -d "venv_agents" ]; then
    echo "✓ Using existing virtual environment"
    source venv_agents/bin/activate
elif [ -d "venv_dedup" ]; then
    echo "✓ Using dedup virtual environment"
    source venv_dedup/bin/activate
else
    echo "❌ No virtual environment found. Creating one..."
    python3 -m venv venv_agents
    source venv_agents/bin/activate
    
    # Install required packages
    pip install psycopg2-binary requests beautifulsoup4 python-dotenv
fi

# Set environment variables
export DATABASE_URL="${DATABASE_URL:-postgresql://postgres:$<EMAIL>:5432/postgres}"

# Run the enrichment agent
echo "🔄 Running company enrichment..."
python3 src/agents/specialized/company_enrichment_agent.py

echo "✅ Company enrichment completed!"