#!/usr/bin/env python3
"""
Run patent mining agent directly - bypasses API issues
Uses service role key to write directly to database
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.agents.specialized.patent_mining_agent_api import APIPatentMiningAgent

# Set service role key
os.environ['SUPABASE_ANON_KEY'] = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt0b3F6bnFtbG54cnR2dWJld3l6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODQ5MTc3NiwiZXhwIjoyMDY0MDY3Nzc2fQ.hxEiOy6xebv7I4CYJEU49oCEXRvmU6iz2l8Kg-NnvTs"

print("Starting Patent Mining Agent with service role key...")
print("=" * 60)

# Run the agent
agent = APIPatentMiningAgent()

# Process all 124 patents
results = agent.run_discovery(max_patents=124)

print("\n" + "=" * 60)
print(f"Patent mining complete!")
print(f"New products added: {results['products_saved']}")
print(f"Duplicates skipped: {results['duplicates']}")
print(f"Errors: {results['errors']}")