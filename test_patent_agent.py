#!/usr/bin/env python3
"""
Test run of patent mining agent with limited scope
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.agents.specialized.patent_mining_agent import PatentMiningAgent

class TestPatentAgent(PatentMiningAgent):
    def __init__(self):
        super().__init__()
        # Override with just 3 search terms for testing
        self.search_terms = [
            "hemp fiber composite",
            "hemp battery",
            "hemp medical device"
        ]
        print(f"Test mode: Limited to {len(self.search_terms)} search terms")

if __name__ == "__main__":
    print("🧪 Running Patent Mining Agent in TEST MODE")
    print("This will search for a limited set of patents to verify functionality")
    print("-" * 60)
    
    agent = TestPatentAgent()
    results = agent.run_discovery_cycle()
    
    print(f"\n📊 Test Results:")
    print(f"Products discovered in this test: {results}")