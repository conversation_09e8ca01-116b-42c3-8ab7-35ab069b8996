-- Comprehensive query to find ALL products needing image fixes
-- Total affected: 5,379 out of 6,157 products (87.36%)
-- Generated: 2025-07-25 11:16:55


-- Get ALL products that need image fixes (5,379 total)
WITH products_needing_fixes AS (
    SELECT 
        id,
        name,
        description,
        image_url,
        ai_generated_image_url,
        industry_sub_category_id,
        plant_part_id,
        CASE 
            WHEN image_url IS NULL OR image_url = '' THEN 'missing'
            WHEN image_url LIKE '%placeholder%' OR image_url LIKE '%unknown%' 
                OR image_url LIKE '%fallback%' OR image_url LIKE '%default%' THEN 'placeholder'
            WHEN image_url LIKE '%unsplash%' AND image_url NOT LIKE '%hemp%' THEN 'generic_stock'
            WHEN image_url LIKE '%face%' OR image_url LIKE '%portrait%' THEN 'face_portrait'
            WHEN image_url LIKE '%marijuana%' OR image_url LIKE '%smoking%' THEN 'marijuana'
            WHEN image_url NOT LIKE '%hemp%' AND image_url NOT LIKE '%cbd%' 
                AND image_url NOT LIKE '%product%' AND image_url NOT LIKE '%/generated/%' THEN 'unknown_quality'
            ELSE 'review_needed'
        END as issue_type
    FROM uses_products
    WHERE (
        image_url IS NULL 
        OR image_url = ''
        OR image_url LIKE '%placeholder%'
        OR image_url LIKE '%unknown%'
        OR image_url LIKE '%fallback%'
        OR image_url LIKE '%default%'
        OR (image_url LIKE '%unsplash%' AND image_url NOT LIKE '%hemp%')
        OR image_url LIKE '%face%'
        OR image_url LIKE '%portrait%'
        OR image_url LIKE '%marijuana%'
        OR image_url LIKE '%smoking%'
        OR (
            image_url NOT LIKE '%hemp%' 
            AND image_url NOT LIKE '%cbd%'
            AND image_url NOT LIKE '%product%'
            AND image_url NOT LIKE '%/generated/%'
            AND image_url NOT LIKE '%replicate%'
        )
    )
)
SELECT * FROM products_needing_fixes
ORDER BY 
    CASE issue_type
        WHEN 'missing' THEN 1
        WHEN 'placeholder' THEN 2
        WHEN 'marijuana' THEN 3
        WHEN 'face_portrait' THEN 4
        WHEN 'unknown_quality' THEN 5
        ELSE 6
    END,
    name;
