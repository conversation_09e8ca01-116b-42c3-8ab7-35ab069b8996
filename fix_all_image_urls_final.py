#!/usr/bin/env python3
"""Fix all image URLs to use the correct generated images."""

import os
import sys
import re

# Setup path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Try to import from virtual environment
venv_path = os.path.join(os.path.dirname(__file__), 'venv_dedup')
if os.path.exists(venv_path):
    site_packages = os.path.join(venv_path, 'lib', 'python3.10', 'site-packages')
    if os.path.exists(site_packages):
        sys.path.insert(0, site_packages)

try:
    import psycopg2
except ImportError:
    print("Installing psycopg2...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "psycopg2-binary"])
    import psycopg2

# Load environment variables
def load_env():
    env_file = '.env'
    if os.path.exists(env_file):
        with open(env_file, 'r') as f:
            for line in f:
                if '=' in line and not line.strip().startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value.strip('"').strip("'")

load_env()

def clean_filename(name):
    """Clean product name for filename matching."""
    # Remove special characters and normalize
    name = re.sub(r'[^\w\s-]', '', name)
    name = re.sub(r'[-\s]+', ' ', name)
    return name.strip()

def find_generated_image(product_id, product_name):
    """Find the generated image file for a product."""
    # Clean the product name
    clean_name = clean_filename(product_name)
    
    # Check for files in generated_images directory
    if not os.path.exists('generated_images'):
        return None
    
    # Try different filename patterns
    possible_patterns = [
        f"{product_id}_{clean_name}.png",
        f"{product_id}_{product_name}.png",
        # Try with underscores instead of spaces
        f"{product_id}_{clean_name.replace(' ', '_')}.png",
        f"{product_id}_{product_name.replace(' ', '_')}.png",
    ]
    
    for pattern in possible_patterns:
        file_path = os.path.join('generated_images', pattern)
        if os.path.exists(file_path):
            return f"/generated_images/{pattern}"
    
    # Try to find by product ID prefix
    for filename in os.listdir('generated_images'):
        if filename.startswith(f"{product_id}_") and filename.endswith('.png'):
            return f"/generated_images/{filename}"
    
    return None

def main():
    database_url = os.environ.get('DATABASE_URL')
    if not database_url:
        print("ERROR: DATABASE_URL not found")
        return
    
    try:
        conn = psycopg2.connect(database_url)
        cur = conn.cursor()
        
        print("FIXING IMAGE URLs TO USE GENERATED IMAGES")
        print("=" * 80)
        
        # Get all products
        cur.execute("SELECT id, name, image_url FROM uses_products ORDER BY id")
        products = cur.fetchall()
        
        stats = {
            'total': len(products),
            'already_correct': 0,
            'fixed': 0,
            'no_image_found': 0,
            'errors': 0
        }
        
        updates = []
        
        for product_id, name, current_url in products:
            # Check if already using generated image
            if current_url and '/generated_images/' in current_url:
                stats['already_correct'] += 1
                continue
            
            # Find the generated image
            generated_url = find_generated_image(product_id, name)
            
            if generated_url:
                updates.append((generated_url, product_id))
                stats['fixed'] += 1
                print(f"✓ ID {product_id}: {name[:50]}")
                print(f"  New URL: {generated_url}")
            else:
                stats['no_image_found'] += 1
                if stats['no_image_found'] <= 10:  # Only show first 10
                    print(f"✗ ID {product_id}: {name[:50]} - No generated image found")
        
        # Apply updates
        if updates:
            print(f"\n{'='*80}")
            print(f"Updating {len(updates)} products...")
            
            for url, product_id in updates:
                try:
                    cur.execute(
                        "UPDATE uses_products SET image_url = %s WHERE id = %s",
                        (url, product_id)
                    )
                except Exception as e:
                    print(f"Error updating product {product_id}: {e}")
                    stats['errors'] += 1
            
            conn.commit()
            print(f"✓ Successfully updated {stats['fixed']} products")
        
        # Final statistics
        print(f"\n{'='*80}")
        print("FINAL STATISTICS:")
        print(f"Total products: {stats['total']}")
        print(f"Already using generated images: {stats['already_correct']}")
        print(f"Fixed to use generated images: {stats['fixed']}")
        print(f"No generated image found: {stats['no_image_found']}")
        print(f"Errors: {stats['errors']}")
        
        # Show final distribution
        cur.execute("""
            SELECT 
                CASE 
                    WHEN image_url LIKE '/generated_images/%' THEN 'Generated Images'
                    WHEN image_url LIKE '%supabase.co%' THEN 'Supabase'
                    WHEN image_url LIKE 'http%' THEN 'External'
                    ELSE 'Other'
                END as type,
                COUNT(*) as count
            FROM uses_products
            GROUP BY type
            ORDER BY count DESC
        """)
        
        print(f"\n{'='*80}")
        print("NEW IMAGE URL DISTRIBUTION:")
        for row in cur.fetchall():
            print(f"{row[0]}: {row[1]} products")
        
        cur.close()
        conn.close()
        
        print(f"\n{'='*80}")
        print("✅ DONE! Your images should now be displaying correctly.")
        print("\nNext steps:")
        print("1. Refresh your browser (Ctrl+Shift+R)")
        print("2. Check the Products page")
        print("3. Images should now be showing!")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()