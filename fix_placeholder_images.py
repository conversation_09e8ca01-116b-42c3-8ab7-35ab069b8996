#!/usr/bin/env python3
"""
Fix placeholder images in the hemp database by generating appropriate AI images
"""

import os
from supabase import create_client, Client
from dotenv import load_dotenv
import time

# Load environment variables
load_dotenv()

# Initialize Supabase client
url = os.getenv("VITE_SUPABASE_URL")
key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
supabase: Client = create_client(url, key)

# Products that need image updates
products_to_fix = [
    {
        "id": 2013,
        "name": "CBG Focus Booster Tincture",
        "prompt": "Professional product photo of a premium CBG tincture bottle with dropper, amber glass bottle with white label showing 'CBG Focus Booster', lion's mane mushroom and hemp leaves in background, clean medical aesthetic, studio lighting"
    },
    {
        "id": 2008,
        "name": "CBG Focus Nasal Spray",
        "prompt": "Modern pharmaceutical nasal spray bottle for CBG hemp extract, sleek white medical dispenser with green accents, hemp leaf logo, clean clinical background, professional product photography"
    },
    {
        "id": 2011,
        "name": "CBG Focus Tincture",
        "prompt": "Premium CBG tincture bottle in pharmaceutical setting, dark amber glass with precision dropper, hemp extract label, laboratory quality presentation, soft professional lighting"
    },
    {
        "id": 2015,
        "name": "Full Spectrum Hemp Hydration Powder",
        "prompt": "Athletic hydration powder packet with hemp CBD branding, electrolyte powder visible, sports water bottle nearby, green and blue color scheme, fitness lifestyle product photo"
    },
    {
        "id": 85,
        "name": "Hemp Denim",
        "prompt": "Sustainable hemp denim fabric close-up showing texture, folded hemp-cotton blend jeans, natural blue denim color, eco-friendly textile, fashion product photography"
    },
    {
        "id": 1995,
        "name": "Hemp Fiber-Reinforced Concrete Blocks",
        "prompt": "Stack of hemp fiber reinforced concrete blocks on construction site, showing fibrous texture, sustainable building materials, industrial strength blocks, architectural photography"
    },
    {
        "id": 1988,
        "name": "Hemp Root Biochar Fertilizer",
        "prompt": "Black biochar fertilizer made from hemp roots in burlap sack, rich dark granules visible, garden setting with healthy plants, organic farming product, natural lighting"
    },
    {
        "id": 2019,
        "name": "Hemp Root Biodegradable Packaging Foam",
        "prompt": "White biodegradable packaging foam peanuts made from hemp, eco-friendly shipping materials, cardboard box filled with hemp foam, sustainable packaging solution"
    },
    {
        "id": 1990,
        "name": "Hemp Root Composite Bioplastic",
        "prompt": "Hemp composite bioplastic sheets and pellets, sustainable material samples, industrial raw materials, beige natural color, manufacturing materials photography"
    },
    {
        "id": 128,
        "name": "Hemp Seed Oil Facial Moisturizer",
        "prompt": "Elegant facial moisturizer jar with hemp seed oil, premium skincare packaging, white cream texture visible, green botanical elements, luxury beauty product photo"
    },
    {
        "id": 129,
        "name": "Hemp Seed Oil Facial Serum",
        "prompt": "Glass dropper bottle of hemp seed oil facial serum, golden oil visible through clear glass, minimalist skincare packaging, botanical beauty product, soft natural lighting"
    },
    {
        "id": 134,
        "name": "Hemp Shower Towel",
        "prompt": "Luxurious hemp-cotton blend shower towel folded neatly, natural beige color, spa bathroom setting, sustainable textile, showing soft texture, lifestyle product photo"
    },
    {
        "id": 1994,
        "name": "THC-Free Focus Tincture",
        "prompt": "Professional THC-free CBD tincture for focus, clear glass bottle with measured dropper, brain health supplements nearby, clean wellness product photography"
    },
    {
        "id": 2014,
        "name": "THCV Metabolic Gel Caps",
        "prompt": "Bottle of THCV metabolic support gel capsules, green translucent capsules visible, fitness and metabolism theme, dietary supplement product photo, clean white background"
    }
]

def generate_image_prompt(product_name, description, plant_part, industry):
    """Generate a better image prompt based on product details"""
    # This function would create more specific prompts based on the product category
    # For now, we'll use the manual prompts above
    pass

def update_product_images():
    """Update products with placeholder images"""
    
    print("Starting to update placeholder images...")
    
    for product in products_to_fix:
        try:
            # Here we would normally call an AI image generation service
            # For now, we'll just update the prompt in the database
            
            # Add the image generation to queue
            result = supabase.table("ai_image_generation_queue").insert({
                "product_id": product["id"],
                "product_name": product["name"],
                "prompt": product["prompt"],
                "priority": "high",
                "status": "pending",
                "requested_by": "fix_placeholder_script"
            }).execute()
            
            print(f"✓ Added to queue: {product['name']} (ID: {product['id']})")
            
            # Small delay to avoid rate limiting
            time.sleep(0.5)
            
        except Exception as e:
            print(f"✗ Error processing {product['name']}: {str(e)}")
    
    print("\nCompleted adding products to image generation queue!")

if __name__ == "__main__":
    update_product_images()