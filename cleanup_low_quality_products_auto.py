#!/usr/bin/env python3
"""
Automatically clean up low-quality products added by agents
Focus on July 8, 2025 products from problematic agents
"""

import psycopg2
import os
from datetime import datetime

def cleanup_low_quality_products():
    """Remove low-quality products automatically"""
    database_url = os.environ.get('DATABASE_URL')
    
    if not database_url:
        print("❌ Error: DATABASE_URL not set")
        return
    
    try:
        conn = psycopg2.connect(database_url)
        cur = conn.cursor()
        
        print("🧹 Analyzing low-quality products...")
        
        # Identify low-quality products from July 8 by agent
        cur.execute("""
            SELECT id, name, description, source_agent
            FROM uses_products