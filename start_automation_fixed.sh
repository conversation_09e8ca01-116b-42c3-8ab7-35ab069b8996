#!/bin/bash
# Hemp Database Automation Script with IPv4 Fix

echo "🚀 Starting Hemp Database Automation (Fixed)..."
echo "============================================"

# Activate virtual environment
source venv_dedup/bin/activate

# Use direct connection 
export DATABASE_URL="postgresql://postgres:$<EMAIL>:5432/postgres"

# Set up logging directory
mkdir -p logs

# Function to run agents with error handling
run_agent() {
    local agent_name=$1
    local agent_path=$2
    local log_file="logs/${agent_name}_$(date +%Y%m%d_%H%M%S).log"
    
    echo "Running $agent_name..."
    python $agent_path >> $log_file 2>&1
    
    if [ $? -eq 0 ]; then
        echo "✅ $agent_name completed successfully"
    else
        echo "❌ $agent_name failed - check $log_file"
    fi
}

echo "🔄 Starting automation cycle at $(date)"

# Run patent mining agent with new patents
echo "🔍 Running patent discovery with 100 patents..."
run_agent "patent_mining" "src/agents/specialized/patent_mining_agent_simple.py"

# Run template fixing
echo "📊 Fixing template descriptions..."
run_agent "template_fixer" "fix_all_templates.py"

echo "✅ Automation cycle complete!"