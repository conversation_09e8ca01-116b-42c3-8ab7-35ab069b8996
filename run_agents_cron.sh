#!/usr/bin/env bash
# Automated agent runner for cron

# Set up paths
PROJECT_DIR="/home/<USER>/projects/HQz-Ai-DB-MCP-3"
LOG_DIR="$PROJECT_DIR/logs"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# Change to project directory
cd $PROJECT_DIR

# Load environment variables
source .env

# Activate virtual environment
source venv_dedup/bin/activate

# Create logs directory if it doesn't exist
mkdir -p $LOG_DIR

# Log start time
echo "=== Starting agent run at $(date) ===" >> $LOG_DIR/automation.log

# Run the coordinator
python src/agents/mega_agent_coordinator_v2.py --continuous 0.5 >> $LOG_DIR/automation.log 2>&1

# Log completion
echo "=== Completed agent run at $(date) ===" >> $LOG_DIR/automation.log
echo "" >> $LOG_DIR/automation.log