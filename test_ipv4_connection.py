#!/usr/bin/env python3
import os
import psycopg2
from urllib.parse import urlparse
import socket
import subprocess

# Test different approaches to connect

print("Testing database connections...")
print("=" * 50)

# First, let's see what addresses are available
print("\n1. Checking DNS resolution:")
try:
    result = subprocess.run(['getent', 'hosts', 'db.ktoqznqmlnxrtvubewyz.supabase.co'], 
                          capture_output=True, text=True)
    print(f"   getent result: {result.stdout.strip()}")
except Exception as e:
    print(f"   Error: {e}")

# Try using the API endpoint instead
print("\n2. Testing API endpoint:")
api_host = "ktoqznqmlnxrtvubewyz.supabase.co"
try:
    addrs = socket.getaddrinfo(api_host, 443)
    for addr in addrs:
        if addr[0] == socket.AF_INET:
            print(f"   Found IPv4: {addr[4][0]}")
            break
except Exception as e:
    print(f"   Error: {e}")

# Test different connection methods
print("\n3. Testing database connections:")

# Method 1: Direct connection with original URL
print("\n   a) Original URL (will fail due to IPv6):")
url1 = "postgresql://postgres:%<EMAIL>:5432/postgres"
try:
    parsed = urlparse(url1)
    conn = psycopg2.connect(
        host=parsed.hostname,
        port=parsed.port or 5432,
        database=parsed.path[1:],
        user=parsed.username,
        password=parsed.password,
        sslmode='require'
    )
    print("      ✅ Connected!")
    conn.close()
except Exception as e:
    print(f"      ❌ Failed: {e}")

# Method 2: Pooler endpoint variations
pooler_configs = [
    "postgresql://postgres.ktoqznqmlnxrtvubewyz:%<EMAIL>:5432/postgres",
    "postgresql://postgres.ktoqznqmlnxrtvubewyz:%<EMAIL>:6543/postgres",
    "postgresql://postgres:%<EMAIL>:5432/postgres.ktoqznqmlnxrtvubewyz",
]

for i, url in enumerate(pooler_configs, 1):
    print(f"\n   b.{i}) Pooler config {i}:")
    try:
        parsed = urlparse(url)
        conn = psycopg2.connect(
            host=parsed.hostname,
            port=parsed.port or 5432,
            database=parsed.path[1:],
            user=parsed.username,
            password=parsed.password,
            sslmode='require'
        )
        print("      ✅ Connected!")
        conn.close()
    except Exception as e:
        print(f"      ❌ Failed: {str(e)[:100]}...")

print("\n" + "=" * 50)
print("Recommendation: Use Supabase API or MCP tools instead of direct database connection")