#!/bin/bash

# Setup automated cron jobs for Hemp Database Expansion
# This script sets up hourly automation to reach 10,000 products

echo "🚀 Setting up Hemp Database Automation Cron Jobs"
echo "=============================================="

# Get the full path to the project
PROJECT_DIR="$(cd "$(dirname "$0")" && pwd)"

# Create cron script
cat > "$PROJECT_DIR/run_hourly_automation.sh" << 'EOF'
#!/bin/bash
# Hourly automation runner for Hemp Database

PROJECT_DIR="/home/<USER>/projects/HQz-Ai-DB-MCP-3"
LOG_DIR="$PROJECT_DIR/logs"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# Load environment
source "$PROJECT_DIR/venv_dedup/bin/activate"
export DATABASE_URL="postgresql://postgres.ktoqznqmlnxrtvubewyz:%<EMAIL>:6543/postgres"
# Load API key from .env file
source "$PROJECT_DIR/.env"
# DEEPSEEK_API_KEY loaded from .env

# Check if already running
if pgrep -f "mega_agent_coordinator_v2.py" > /dev/null; then
    echo "$(date): Automation already running, skipping..." >> "$LOG_DIR/cron_automation.log"
    exit 0
fi

# Run the coordinator for 55 minutes (to allow 5 min buffer)
echo "$(date): Starting hourly automation cycle" >> "$LOG_DIR/cron_automation.log"
cd "$PROJECT_DIR"
timeout 55m python src/agents/mega_agent_coordinator_v2.py --continuous 0.9 >> "$LOG_DIR/automation_hourly_$TIMESTAMP.log" 2>&1

# Log completion
echo "$(date): Hourly automation cycle completed" >> "$LOG_DIR/cron_automation.log"

# Generate quick stats
python -c "
import psycopg2
import os
conn = psycopg2.connect(os.environ['DATABASE_URL'])
cur = conn.cursor()
cur.execute('SELECT COUNT(*) FROM uses_products')
total = cur.fetchone()[0]
cur.execute('SELECT COUNT(*) FROM uses_products WHERE created_at >= NOW() - INTERVAL %s', ('1 hour',))
new_hour = cur.fetchone()[0]
print(f'Total products: {total:,} | Added this hour: {new_hour}')
cur.close()
conn.close()
" >> "$LOG_DIR/cron_automation.log"
EOF

# Make the script executable
chmod +x "$PROJECT_DIR/run_hourly_automation.sh"

# Add to crontab
echo ""
echo "📝 Adding cron job to run every hour..."

# Check if cron job already exists
if crontab -l 2>/dev/null | grep -q "run_hourly_automation.sh"; then
    echo "⚠️  Cron job already exists!"
else
    # Add the cron job
    (crontab -l 2>/dev/null; echo "0 * * * * $PROJECT_DIR/run_hourly_automation.sh") | crontab -
    echo "✅ Cron job added successfully!"
fi

echo ""
echo "📋 Current cron jobs:"
crontab -l | grep hemp || echo "No hemp-related cron jobs found"

echo ""
echo "✅ Setup complete!"
echo ""
echo "🎯 Automation will run every hour at :00"
echo "📊 Logs will be saved to: $PROJECT_DIR/logs/"
echo "🔍 Monitor progress: tail -f $PROJECT_DIR/logs/cron_automation.log"
echo ""
echo "⏱️  Expected timeline to 10,000 products:"
echo "   Current rate: ~300 products/hour"
echo "   Products needed: ~8,764"
echo "   Time to goal: ~29 hours"
echo ""
echo "💡 To check automation status:"
echo "   python monitor_automation.py"
echo ""
echo "🛑 To stop automation:"
echo "   crontab -e  # Then remove the hemp automation line"