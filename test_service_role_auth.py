#!/usr/bin/env python3
"""
Test service role authentication with Supabase
"""

import requests
import json

# Service role key
service_role_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt0b3F6bnFtbG54cnR2dWJld3l6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODQ5MTc3NiwiZXhwIjoyMDY0MDY3Nzc2fQ.hxEiOy6xebv7I4CYJEU49oCEXRvmU6iz2l8Kg-NnvTs"
supabase_url = "https://ktoqznqmlnxrtvubewyz.supabase.co"

print("Testing service role authentication...")
print("=" * 60)

# Test 1: Basic query with service role
print("\n1. Testing basic query with service role key:")
headers = {
    "apikey": service_role_key,
    "Authorization": f"Bearer {service_role_key}",
    "Content-Type": "application/json"
}

response = requests.get(
    f"{supabase_url}/rest/v1/uses_products",
    headers=headers,
    params={"select": "count", "limit": "1"}
)

print(f"   Status: {response.status_code}")
if 'content-range' in response.headers:
    print(f"   Total products: {response.headers['content-range'].split('/')[1]}")
else:
    print(f"   Response: {response.text}")

# Test 2: Try to insert a test product
print("\n2. Testing insert with service role key:")
test_product = {
    "name": "Test Service Role Product",
    "description": "Testing service role authentication",
    "industry_sub_category_id": 5,
    "plant_part_id": 1,
    "confidence_score": 0.99,
    "source_type": "ai_agent",
    "source_agent": "Test Script",
    "benefits_advantages": '{"Testing authentication"}',
    "technical_specifications": '{"test": true}'
}

response = requests.post(
    f"{supabase_url}/rest/v1/uses_products",
    headers=headers,
    json=test_product
)

print(f"   Status: {response.status_code}")
if response.status_code in [200, 201, 204]:
    print("   ✅ Insert successful! Service role key is working.")
    
    # Clean up - delete the test product
    delete_response = requests.delete(
        f"{supabase_url}/rest/v1/uses_products",
        headers=headers,
        params={"name": f"eq.{test_product['name']}"}
    )
    print(f"   Cleanup status: {delete_response.status_code}")
else:
    print(f"   ❌ Insert failed: {response.text}")

# Test 3: Check RLS policies
print("\n3. Checking RLS policies on uses_products table:")
response = requests.get(
    f"{supabase_url}/rest/v1/uses_products",
    headers=headers,
    params={"select": "id", "limit": "1"}
)

print(f"   Read access: {'✅ Allowed' if response.status_code == 200 else '❌ Denied'}")

print("\n" + "=" * 60)
print("Service role key test complete.")