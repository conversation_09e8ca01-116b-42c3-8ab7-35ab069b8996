import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load .env
dotenv.config({ path: join(__dirname, '.env') });

const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

const supabase = createClient(supabaseUrl, supabaseKey);

// Better descriptions for generic brands based on their products
const brandDescriptions = {
  'AromaBoost': 'Premium terpene diffuser and aromatherapy brand specializing in hemp-derived terpene blends for wellness and mood enhancement.',
  'AuraBloom': 'Innovative terpene inhalation therapy brand focused on cognitive enhancement and mental clarity through targeted hemp terpene formulations.',
  'AuraBoost': 'Customizable terpene wellness brand offering diffuser blends, roll-ons, and inhalation products for mood and energy enhancement.',
  'CannaBloom': 'Advanced topical CBD brand utilizing liposome technology for targeted delivery of hemp-derived compounds in skincare and pain relief.',
  'DermaBloom': 'Hemp terpene skincare brand specializing in anti-aging serums and facial treatments enhanced with botanical terpenes.',
  'NeuroBloom': 'Nootropic supplement brand combining hemp-derived compounds with cognitive enhancers for mental performance.',
  'SonoBloom': 'Sleep wellness brand offering hemp-enhanced sleep aids with melatonin and CBG for natural sleep support.',
  'AromaSculpt': 'Artisanal terpene aromatherapy brand featuring sculpted diffuser blends and clay face masks infused with hemp terpenes.',
  'Aura Balance': 'Subscription-based terpene wellness brand providing curated monthly selections of mood-balancing diffuser blends.',
  'AuraMist': 'Customizable terpene inhalation system brand offering personalized wellness mists for various therapeutic needs.',
  'AuraShine': 'Hemp terpene skincare brand focused on brightening and radiance-enhancing serums for glowing skin.',
  'AuraSync': 'Precision terpene therapy brand creating synchronized inhalation blends for balanced wellness.',
  'BreatheEasy': 'Respiratory wellness brand specializing in targeted terpene inhalation systems and nasal sprays for breathing support.',
  'CannaCalm': 'Therapeutic hemp brand offering topical patches, sleep serums, and relief products for stress and pain management.',
  'CannaFocus': 'Cognitive enhancement brand creating hemp-based nootropic supplements for mental clarity and focus.',
  'CannaSleep': 'Sleep optimization brand developing bio-rhythm supplements and harmonizer patches with hemp compounds.',
  'DermaHemp': 'Clinical-grade hemp skincare brand featuring micro-needle delivery systems and rejuvenating serums.',
  'DermaRenew': 'Anti-aging hemp skincare brand specializing in terpene-infused facial serums for skin renewal.',
  'FocusFlow': 'Comprehensive cognitive wellness brand with brain mists, nootropic chews, and focus-enhancing hemp products.',
  'HempEase': 'Muscle recovery brand offering targeted micro-encapsulation technology in hemp-based topical rubs.',
  'HempFiberSkin': 'Innovative skincare brand utilizing hemp fiber technology for sustainable beauty products.',
  'HempHydrate': 'Hydration-focused brand combining hemp compounds with electrolytes and adaptogens in beverages and serums.',
  'HempLeaf': 'Diversified hemp brand offering everything from biodegradable packaging to herbal teas and skincare.',
  'HempRenew': 'Hair and scalp wellness brand featuring hemp stem cell extracts and biotin-enhanced formulations.',
  'HempRoot': 'Comprehensive hemp brand spanning construction materials to personal care with root-to-product philosophy.',
  'HempShine': 'Hair care brand specializing in CBD and biotin microemulsion technology for healthy, shiny hair.',
  'HempZen': 'Holistic wellness brand offering energy gels, enzyme cleaners, and zero-waste hemp packaging solutions.',
  'Mindful Mist': 'Aromatherapy brand creating room and linen sprays with targeted terpene profiles for mindful living.',
  'MoodScape Collection': 'Curated aromatherapy brand offering mood-specific terpene diffuser blend collections.',
  'MuscleMend': 'Sports recovery brand featuring rapid relief roll-ons and hemp terpene topicals for athletes.',
  'Night Bloom': 'Evening wellness brand specializing in terpene-infused sleep balms for restful nights.',
  'RejuveHemp': 'Anti-aging skincare brand harnessing hemp compounds for cellular rejuvenation.',
  'RejuveSkin': 'Premium hemp terpene serum brand focused on skin rejuvenation and vitality.',
  'ReliefRise': 'Pain management brand offering terpene-infused muscle rubs for natural relief.',
  'RespireEasy': 'Respiratory health brand with nasal sprays and inhalers enhanced with hemp terpenes.',
  'SkinRenew': 'Hemp-based skincare brand specializing in renewal serums for all skin types.',
  'SleepScape': 'Sleep environment brand creating diffuser blends and pillow mists for optimal rest.',
  'SleepSound': 'Comprehensive sleep solution brand with roll-ons, diffusers, and pillow mists.',
  'SleepWell': 'Innovative sleep technology brand combining hemp patches, sleep masks, and sublingual sprays.',
  'SonoCalm': 'High-tech sleep brand integrating CBD with ultrasonic technology in sleep masks.',
  'TerpFusion': 'Hydrating skincare brand fusing hemp terpenes with moisturizing botanicals.',
  'TerpeneFusion': 'Therapeutic balm brand specializing in terpene-enhanced topical applications.',
  'Tranquil': 'Relaxation brand offering dawn roll-ons, foot soaks, and calming hemp products.'
};

async function improveGenericBrands() {
  console.log('🌟 IMPROVING GENERIC BRAND COMPANIES');
  console.log('='.repeat(60));

  let updatedCount = 0;

  for (const [brandName, description] of Object.entries(brandDescriptions)) {
    try {
      // Update description and mark as brand
      const { data: updated } = await supabase
        .from('hemp_companies')
        .update({ 
          description,
          company_type: 'brand',
          verified: false // These are inferred brands, not verified companies
        })
        .eq('name', brandName)
        .select();

      if (updated && updated.length > 0) {
        updatedCount++;
        console.log(`✅ Updated ${brandName}`);
      }
    } catch (error) {
      console.error(`❌ Error updating ${brandName}: ${error.message}`);
    }
  }

  console.log(`\n✅ Updated ${updatedCount} generic brands with better descriptions`);

  // Also update the remaining placeholder descriptions
  console.log('\n📝 Updating remaining placeholder descriptions...');
  
  const { data: placeholderCompanies } = await supabase
    .from('hemp_companies')
    .select('id, name')
    .or('description.like.%is a hemp product brand%,description.like.%identified from product names%')
    .limit(100);

  if (placeholderCompanies && placeholderCompanies.length > 0) {
    console.log(`Found ${placeholderCompanies.length} companies with placeholder descriptions`);
    
    for (const company of placeholderCompanies) {
      // Generate a better generic description
      const description = `${company.name} is a hemp industry company offering specialized products and solutions. Further details pending verification.`;
      
      try {
        await supabase
          .from('hemp_companies')
          .update({ description })
          .eq('id', company.id);
          
        console.log(`✅ Updated placeholder for ${company.name}`);
      } catch (error) {
        console.error(`❌ Error updating ${company.name}: ${error.message}`);
      }
    }
  }

  console.log('\n✅ Generic brand improvement complete!');
}

improveGenericBrands().catch(console.error);