#!/usr/bin/env python3
"""
Comprehensive audit of all product images to find inappropriate content
"""

import os
import sys
import json
from datetime import datetime

# SQL queries to find all problematic images
AUDIT_QUERIES = {
    "missing_images": """
        SELECT COUNT(*) as count
        FROM uses_products
        WHERE image_url IS NULL 
        OR image_url = ''
    """,
    
    "placeholder_images": """
        SELECT id, name, image_url
        FROM uses_products
        WHERE (
            image_url LIKE '%placeholder%'
            OR image_url LIKE '%unknown%'
            OR image_url LIKE '%fallback%'
            OR image_url LIKE '%default%'
            OR image_url LIKE '%missing%'
            OR image_url LIKE '%no-image%'
            OR image_url LIKE '%coming-soon%'
        )
        AND image_url IS NOT NULL
        ORDER BY name
    """,
    
    "face_portrait_images": """
        SELECT id, name, image_url
        FROM uses_products
        WHERE (
            -- Unsplash portrait/face URLs
            image_url LIKE '%unsplash.com/photo-%' 
            AND (
                image_url LIKE '%face%'
                OR image_url LIKE '%portrait%'
                OR image_url LIKE '%person%'
                OR image_url LIKE '%people%'
                OR image_url LIKE '%headshot%'
                OR image_url LIKE '%selfie%'
                -- Common Unsplash portrait photo IDs
                OR image_url LIKE '%1507003211169%'
                OR image_url LIKE '%1517841905240%'
                OR image_url LIKE '%1506794778202%'
                OR image_url LIKE '%1531891153868%'
            )
        )
        OR (
            -- Other face/portrait patterns
            image_url LIKE '%/face/%'
            OR image_url LIKE '%/faces/%'
            OR image_url LIKE '%/portrait/%'
            OR image_url LIKE '%/headshot/%'
            OR image_url LIKE '%/person/%'
            OR image_url LIKE '%/people/%'
        )
        ORDER BY name
    """,
    
    "interior_room_images": """
        SELECT id, name, image_url
        FROM uses_products
        WHERE (
            image_url LIKE '%living%room%'
            OR image_url LIKE '%bedroom%'
            OR image_url LIKE '%kitchen%'
            OR image_url LIKE '%bathroom%'
            OR image_url LIKE '%interior%'
            OR image_url LIKE '%furniture%'
            OR image_url LIKE '%couch%'
            OR image_url LIKE '%sofa%'
            OR image_url LIKE '%house%'
            OR image_url LIKE '%home%decor%'
            OR image_url LIKE '%indoor%'
            OR image_url LIKE '%office%space%'
        )
        AND image_url IS NOT NULL
        ORDER BY name
    """,
    
    "keyboard_computer_images": """
        SELECT id, name, image_url
        FROM uses_products
        WHERE (
            image_url LIKE '%keyboard%'
            OR image_url LIKE '%computer%'
            OR image_url LIKE '%laptop%'
            OR image_url LIKE '%desktop%'
            OR image_url LIKE '%mouse%'
            OR image_url LIKE '%monitor%'
            OR image_url LIKE '%screen%'
            OR image_url LIKE '%tech%'
            OR image_url LIKE '%typing%'
            OR image_url LIKE '%workspace%'
            OR image_url LIKE '%macbook%'
            OR image_url LIKE '%pc%'
        )
        AND image_url IS NOT NULL
        ORDER BY name
    """,
    
    "marijuana_cannabis_images": """
        SELECT id, name, image_url
        FROM uses_products
        WHERE (
            image_url LIKE '%marijuana%'
            OR image_url LIKE '%weed%'
            OR image_url LIKE '%joint%'
            OR image_url LIKE '%smoking%'
            OR image_url LIKE '%bong%'
            OR image_url LIKE '%cannabis%'
            OR image_url LIKE '%420%'
            OR image_url LIKE '%blunt%'
            OR image_url LIKE '%pipe%'
            OR image_url LIKE '%smoke%'
            OR image_url LIKE '%recreational%'
            OR image_url LIKE '%thc%'
        )
        AND image_url IS NOT NULL
        ORDER BY name
    """,
    
    "stock_photo_indicators": """
        SELECT id, name, image_url
        FROM uses_products
        WHERE (
            -- Stock photo sites
            image_url LIKE '%shutterstock%'
            OR image_url LIKE '%gettyimages%'
            OR image_url LIKE '%istockphoto%'
            OR image_url LIKE '%123rf%'
            OR image_url LIKE '%dreamstime%'
            OR image_url LIKE '%depositphotos%'
            OR image_url LIKE '%alamy%'
            OR image_url LIKE '%pixabay%'
            OR image_url LIKE '%pexels%'
            OR image_url LIKE '%freepik%'
            -- Generic stock photo patterns
            OR image_url LIKE '%stock-photo%'
            OR image_url LIKE '%stockphoto%'
            OR image_url LIKE '%stock_photo%'
        )
        AND image_url IS NOT NULL
        ORDER BY name
    """,
    
    "duplicate_images": """
        WITH image_usage AS (
            SELECT 
                image_url,
                COUNT(*) as usage_count,
                array_agg(id ORDER BY name) as product_ids,
                array_agg(name ORDER BY name) as product_names
            FROM uses_products
            WHERE image_url IS NOT NULL
            AND image_url != ''
            GROUP BY image_url
            HAVING COUNT(*) > 2  -- Images used more than twice
        )
        SELECT 
            image_url,
            usage_count,
            product_ids[1:5] as sample_product_ids,  -- First 5 products
            product_names[1:5] as sample_product_names
        FROM image_usage
        ORDER BY usage_count DESC
    """,
    
    "non_hemp_keywords": """
        SELECT id, name, image_url
        FROM uses_products
        WHERE image_url IS NOT NULL
        AND (
            -- Check if URL lacks hemp-related keywords
            image_url NOT LIKE '%hemp%'
            AND image_url NOT LIKE '%cbd%'
            AND image_url NOT LIKE '%cannabinoid%'
            AND image_url NOT LIKE '%industrial%'
            AND image_url NOT LIKE '%sustainable%'
            AND image_url NOT LIKE '%fiber%'
            AND image_url NOT LIKE '%textile%'
            AND image_url NOT LIKE '%extract%'
            AND image_url NOT LIKE '%oil%'
            AND image_url NOT LIKE '%seed%'
        )
        AND (
            -- But contains generic/unrelated terms
            image_url LIKE '%generic%'
            OR image_url LIKE '%abstract%'
            OR image_url LIKE '%background%'
            OR image_url LIKE '%texture%'
            OR image_url LIKE '%pattern%'
            OR image_url LIKE '%gradient%'
            OR image_url LIKE '%wallpaper%'
        )
        ORDER BY name
    """,
    
    "suspicious_ai_images": """
        SELECT id, name, ai_generated_image_url, image_url
        FROM uses_products
        WHERE ai_generated_image_url IS NOT NULL
        AND (
            -- Replicate URLs with potential issues
            ai_generated_image_url LIKE '%face%'
            OR ai_generated_image_url LIKE '%portrait%'
            OR ai_generated_image_url LIKE '%person%'
            OR ai_generated_image_url LIKE '%marijuana%'
            OR ai_generated_image_url LIKE '%smoking%'
            OR ai_generated_image_url LIKE '%keyboard%'
            OR ai_generated_image_url LIKE '%computer%'
            OR ai_generated_image_url LIKE '%interior%'
            OR ai_generated_image_url LIKE '%living%'
        )
        ORDER BY name
    """
}

def generate_comprehensive_audit_sql():
    """Generate a comprehensive SQL audit script"""
    
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    sql_content = f"""-- Comprehensive Hemp Database Image Audit
-- Generated: {timestamp}
-- This script identifies ALL products with potentially inappropriate images

-- ========================================
-- SUMMARY STATISTICS
-- ========================================

SELECT 'OVERALL IMAGE STATISTICS' as report_section;
SELECT 
    COUNT(*) as total_products,
    COUNT(CASE WHEN image_url IS NOT NULL AND image_url != '' THEN 1 END) as has_image,
    COUNT(CASE WHEN image_url IS NULL OR image_url = '' THEN 1 END) as missing_image,
    COUNT(CASE WHEN ai_generated_image_url IS NOT NULL THEN 1 END) as has_ai_image,
    ROUND(100.0 * COUNT(CASE WHEN image_url IS NULL OR image_url = '' THEN 1 END) / COUNT(*), 2) as missing_percentage
FROM uses_products;

"""
    
    # Add each audit query
    for audit_name, query in AUDIT_QUERIES.items():
        section_title = audit_name.replace('_', ' ').upper()
        sql_content += f"""
-- ========================================
-- {section_title}
-- ========================================

SELECT '';
SELECT '{section_title}' as report_section;
{query}

"""
    
    # Add a final summary query
    sql_content += """
-- ========================================
-- PRODUCTS NEEDING IMMEDIATE ATTENTION
-- ========================================

SELECT '';
SELECT 'PRODUCTS NEEDING IMMEDIATE IMAGE REPLACEMENT' as report_section;
SELECT 
    id,
    name,
    CASE 
        WHEN image_url IS NULL OR image_url = '' THEN 'Missing Image'
        WHEN image_url LIKE '%placeholder%' OR image_url LIKE '%unknown%' THEN 'Placeholder Image'
        WHEN image_url LIKE '%face%' OR image_url LIKE '%portrait%' THEN 'Contains Face/Portrait'
        WHEN image_url LIKE '%keyboard%' OR image_url LIKE '%computer%' THEN 'Contains Computer/Tech'
        WHEN image_url LIKE '%marijuana%' OR image_url LIKE '%smoking%' THEN 'Contains Cannabis/Smoking'
        WHEN image_url LIKE '%interior%' OR image_url LIKE '%room%' THEN 'Contains Interior/Room'
        ELSE 'Other Issue'
    END as issue_type,
    image_url
FROM uses_products
WHERE (
    -- Missing images
    image_url IS NULL OR image_url = ''
    -- Placeholders
    OR image_url LIKE '%placeholder%'
    OR image_url LIKE '%unknown%'
    OR image_url LIKE '%fallback%'
    -- Inappropriate content
    OR image_url LIKE '%face%'
    OR image_url LIKE '%portrait%'
    OR image_url LIKE '%person%'
    OR image_url LIKE '%keyboard%'
    OR image_url LIKE '%computer%'
    OR image_url LIKE '%marijuana%'
    OR image_url LIKE '%smoking%'
    OR image_url LIKE '%interior%'
    OR image_url LIKE '%room%'
    OR image_url LIKE '%living%'
    OR image_url LIKE '%office%'
)
ORDER BY 
    CASE 
        WHEN image_url IS NULL OR image_url = '' THEN 1
        WHEN image_url LIKE '%placeholder%' OR image_url LIKE '%unknown%' THEN 2
        ELSE 3
    END,
    name
LIMIT 100;

-- ========================================
-- FINAL COUNT OF PROBLEMATIC IMAGES
-- ========================================

SELECT '';
SELECT 'TOTAL PROBLEMATIC IMAGES BY CATEGORY' as report_section;
SELECT 
    COUNT(CASE WHEN image_url IS NULL OR image_url = '' THEN 1 END) as missing_images,
    COUNT(CASE WHEN image_url LIKE '%placeholder%' OR image_url LIKE '%unknown%' OR image_url LIKE '%fallback%' THEN 1 END) as placeholder_images,
    COUNT(CASE WHEN image_url LIKE '%face%' OR image_url LIKE '%portrait%' OR image_url LIKE '%person%' THEN 1 END) as face_images,
    COUNT(CASE WHEN image_url LIKE '%keyboard%' OR image_url LIKE '%computer%' OR image_url LIKE '%laptop%' THEN 1 END) as computer_images,
    COUNT(CASE WHEN image_url LIKE '%marijuana%' OR image_url LIKE '%smoking%' OR image_url LIKE '%cannabis%' THEN 1 END) as cannabis_images,
    COUNT(CASE WHEN image_url LIKE '%interior%' OR image_url LIKE '%room%' OR image_url LIKE '%furniture%' THEN 1 END) as interior_images
FROM uses_products;
"""
    
    return sql_content

def main():
    # Generate the audit SQL
    sql_content = generate_comprehensive_audit_sql()
    
    # Save to file
    output_file = "comprehensive_image_audit.sql"
    with open(output_file, 'w') as f:
        f.write(sql_content)
    
    print(f"Generated comprehensive image audit: {output_file}")
    print("\nThis audit will check for:")
    print("- Missing images")
    print("- Placeholder/default images")
    print("- Face/portrait images")
    print("- Interior/room images")
    print("- Keyboard/computer images")
    print("- Marijuana/cannabis/smoking images")
    print("- Stock photo indicators")
    print("- Duplicate images (used >2 times)")
    print("- Non-hemp related generic images")
    print("- Suspicious AI-generated images")
    print("\nRun this SQL file to get a complete audit of all image issues.")

if __name__ == "__main__":
    main()