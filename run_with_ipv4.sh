#!/bin/bash
# Run Python scripts with IPv4 forced

# Export environment variables to force IPv4
export PGHOST_AF=inet
export DATABASE_URL="postgresql://postgres:$<EMAIL>:5432/postgres?connect_timeout=10"

# Get the IPv4 address for the host
IPV4_HOST=$(dig +short db.ktoqznqmlnxrtvubewyz.supabase.co A | head -1)
if [ ! -z "$IPV4_HOST" ]; then
    echo "Using IPv4 address: $IPV4_HOST"
    export DATABASE_URL="*************************************************/postgres?sslmode=require"
fi

# Run the command passed as arguments
exec "$@"