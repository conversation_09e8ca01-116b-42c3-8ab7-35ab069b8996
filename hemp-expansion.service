[Unit]
Description=Hemp Database Expansion Agents
After=network.target

[Service]
Type=simple
User=hempquarterz
WorkingDirectory=/home/<USER>/projects/HQz-Ai-DB-MCP-3
Environment="DATABASE_URL=postgresql://postgres:%<EMAIL>:5432/postgres"
ExecStart=/home/<USER>/projects/HQz-Ai-DB-MCP-3/venv_dedup/bin/python /home/<USER>/projects/HQz-Ai-DB-MCP-3/src/agents/mega_agent_coordinator_v2.py --continuous 24
Restart=always
RestartSec=300
StandardOutput=append:/home/<USER>/projects/HQz-Ai-DB-MCP-3/logs/systemd_expansion.log
StandardError=append:/home/<USER>/projects/HQz-Ai-DB-MCP-3/logs/systemd_expansion_error.log

[Install]
WantedBy=multi-user.target