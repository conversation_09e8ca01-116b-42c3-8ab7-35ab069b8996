#!/usr/bin/env python3
"""
Identify and fix products with inappropriate images (faces, interiors, etc.)
"""

import os
from supabase import create_client, Client
from dotenv import load_dotenv
from image_quality_validator import HempImageValidator, HempImagePromptGenerator
import time

# Load environment variables
load_dotenv()

# Initialize Supabase client
url = os.getenv("VITE_SUPABASE_URL")
key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

if not url or not key:
    print("Error: Missing environment variables!")
    print(f"VITE_SUPABASE_URL: {'Found' if url else 'Missing'}")
    print(f"SUPABASE_SERVICE_ROLE_KEY: {'Found' if key else 'Missing'}")
    print("\nMake sure your .env file contains:")
    print("VITE_SUPABASE_URL=https://your-project.supabase.co")
    print("SUPABASE_SERVICE_ROLE_KEY=your-service-role-key")
    exit(1)

supabase: Client = create_client(url, key)

# Initialize validator and generator
validator = HempImageValidator()
generator = HempImagePromptGenerator()

def get_industry_name(industry_id):
    """Get industry name from ID"""
    industry_map = {
        1: "textiles",
        6: "medical", 
        9: "construction",
        15: "cosmetics",
        18: "agriculture",
        40: "agriculture"
    }
    return industry_map.get(industry_id, "general")

def get_plant_part_name(plant_part_id):
    """Get plant part name from ID"""
    part_map = {
        1: "flower",
        2: "fiber", 
        4: "stem",
        6: "root",
        7: "seed"
    }
    return part_map.get(plant_part_id, "hemp")

def scan_for_inappropriate_images():
    """Scan database for products with inappropriate images"""
    
    print("Scanning for inappropriate images...")
    
    # Get products with AI-generated images
    response = supabase.table("uses_products").select(
        "id, name, description, image_url, ai_generated_image_url, "
        "plant_part_id, industry_sub_category_id"
    ).not_.is_("image_url", "null").limit(1000).execute()
    
    products = response.data
    problematic_products = []
    
    for product in products:
        # Validate the current image
        validation = validator.validate_image(
            product['image_url'],
            product['name'],
            product['description'] or ""
        )
        
        if validation.issue_type.value != "valid":
            problematic_products.append({
                "product": product,
                "issue": validation
            })
    
    print(f"\nFound {len(problematic_products)} products with inappropriate images")
    
    # Group by issue type
    issue_counts = {}
    for item in problematic_products:
        issue_type = item['issue'].issue_type.value
        if issue_type not in issue_counts:
            issue_counts[issue_type] = []
        issue_counts[issue_type].append(item)
    
    # Display summary
    print("\nIssue Summary:")
    for issue_type, items in issue_counts.items():
        print(f"- {issue_type}: {len(items)} products")
    
    return problematic_products

def generate_fix_queue(problematic_products):
    """Generate image regeneration queue for problematic products"""
    
    print("\nGenerating fix queue...")
    fix_queue = []
    
    for item in problematic_products[:50]:  # Process first 50 for now
        product = item['product']
        issue = item['issue']
        
        # Get industry and plant part
        industry = get_industry_name(product['industry_sub_category_id'])
        plant_part = get_plant_part_name(product['plant_part_id'])
        
        # Generate new prompt
        new_prompt = generator.generate_prompt(
            product['name'],
            product['description'] or "",
            industry,
            plant_part
        )
        
        fix_queue.append({
            "product_id": product['id'],
            "product_name": product['name'],
            "old_image_url": product['image_url'],
            "issue_type": issue.issue_type.value,
            "new_prompt": new_prompt,
            "priority": "high" if issue.issue_type.value in ["face_portrait", "interior_room"] else "medium"
        })
    
    return fix_queue

def update_image_generation_queue(fix_queue):
    """Add items to the image generation queue"""
    
    print("\nUpdating image generation queue...")
    
    for item in fix_queue:
        try:
            # Add to queue
            result = supabase.table("ai_image_generation_queue").insert({
                "product_id": item["product_id"],
                "product_name": item["product_name"],
                "prompt": item["new_prompt"],
                "priority": item["priority"],
                "status": "pending",
                "requested_by": "fix_inappropriate_images",
                "metadata": {
                    "issue_type": item["issue_type"],
                    "old_image_url": item["old_image_url"]
                }
            }).execute()
            
            print(f"✓ Queued: {item['product_name']} ({item['issue_type']})")
            time.sleep(0.2)  # Rate limiting
            
        except Exception as e:
            print(f"✗ Error queuing {item['product_name']}: {str(e)}")
    
    print(f"\nCompleted! Added {len(fix_queue)} items to regeneration queue.")

def main():
    """Main execution"""
    
    # Step 1: Scan for problems
    problematic_products = scan_for_inappropriate_images()
    
    if not problematic_products:
        print("No problematic images found!")
        return
    
    # Step 2: Generate fixes
    fix_queue = generate_fix_queue(problematic_products)
    
    # Step 3: Show what will be fixed
    print("\n" + "="*50)
    print("PROPOSED FIXES:")
    print("="*50)
    
    for i, item in enumerate(fix_queue[:10]):  # Show first 10
        print(f"\n{i+1}. {item['product_name']}")
        print(f"   Issue: {item['issue_type']}")
        print(f"   New prompt: {item['new_prompt'][:100]}...")
    
    if len(fix_queue) > 10:
        print(f"\n... and {len(fix_queue) - 10} more products")
    
    # Step 4: Confirm and execute
    response = input("\nProceed with fixing these images? (y/n): ")
    if response.lower() == 'y':
        update_image_generation_queue(fix_queue)
    else:
        print("Operation cancelled.")

if __name__ == "__main__":
    main()