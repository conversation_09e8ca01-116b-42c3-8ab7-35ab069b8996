const links = [
  "/",
  "/dashboard",
  "/companies", 
  "/research",
  "/login",
  "/products",
  "/plant-type/2",
  "/plant-type/3", 
  "/plant-type/4",
  "/plant-type/1",
  "/products-explorer",
  "/hemp-dex-enhanced",
  "/plant-parts",
  "/industries",
  "/hemp-companies",
  "/search",
  "/about",
  "/sitemap",
  "/privacy",
  "/terms",
  "/contact"
];

const results = {
  working: [],
  notFound: []
};

async function checkLink(link) {
  try {
    const response = await fetch(`http://localhost:5173${link}`, {
      method: 'HEAD',
      redirect: 'follow'
    });
    
    if (response.status === 404) {
      results.notFound.push(link);
    } else {
      results.working.push(link);
    }
  } catch (error) {
    console.error(`Error checking ${link}:`, error.message);
    results.notFound.push(link);
  }
}

async function checkAllLinks() {
  console.log('Checking all links for 404 errors...\n');
  
  for (const link of links) {
    await checkLink(link);
  }
  
  console.log('\n=== 404 NOT FOUND LINKS ===');
  if (results.notFound.length === 0) {
    console.log('No 404 links found!');
  } else {
    results.notFound.forEach(link => {
      console.log(`❌ ${link}`);
    });
  }
  
  console.log(`\n=== SUMMARY ===`);
  console.log(`✅ Working links: ${results.working.length}`);
  console.log(`❌ 404 links: ${results.notFound.length}`);
}

checkAllLinks();