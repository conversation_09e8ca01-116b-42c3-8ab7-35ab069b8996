#!/bin/bash
# Hemp Database Automation Script - Continuous Product Expansion

echo "🚀 Starting Hemp Database Automation..."
echo "============================================"
echo "Target: 10,000 products (Currently: ~1,504)"
echo "============================================"

# Activate virtual environment
source venv_dedup/bin/activate

# Set up logging directory
mkdir -p logs

# Function to run agents with error handling
run_agent() {
    local agent_name=$1
    local agent_path=$2
    local log_file="logs/${agent_name}_$(date +%Y%m%d_%H%M%S).log"
    
    echo "Running $agent_name..."
    python $agent_path >> $log_file 2>&1
    
    if [ $? -eq 0 ]; then
        echo "✅ $agent_name completed successfully"
    else
        echo "❌ $agent_name failed - check $log_file"
    fi
}

# Main automation loop
while true; do
    echo ""
    echo "🔄 Starting new automation cycle at $(date)"
    
    # Run quality improvement scripts
    echo "📊 Running quality improvements..."
    run_agent "template_fixer" "fix_template_descriptions.py"
    
    # Run company enrichment
    echo "🏢 Running company enrichment..."
    run_agent "company_enrichment" "run_company_enrichment.py"
    
    # Run expansion agents
    echo "🔍 Running discovery agents..."
    run_agent "patent_mining" "src/agents/specialized/patent_mining_agent_simple.py"
    
    # Run target industry script
    echo "🎯 Targeting underrepresented industries..."
    run_agent "industry_targeting" "target_underrepresented_industries.py"
    
    # Run mega coordinator with continuous mode
    echo "🤖 Running mega agent coordinator..."
    python src/agents/mega_agent_coordinator_v2.py --continuous 0.5 >> logs/mega_coordinator_$(date +%Y%m%d_%H%M%S).log 2>&1 &
    MEGA_PID=$!
    
    # Let mega coordinator run for 30 minutes
    sleep 1800
    
    # Stop mega coordinator gracefully
    kill -SIGTERM $MEGA_PID 2>/dev/null
    
    # Generate progress report
    echo "📈 Generating progress report..."
    python src/agents/mega_agent_coordinator_v2.py --report
    
    # Wait before next cycle (30 minutes)
    echo "⏳ Waiting 30 minutes before next cycle..."
    sleep 1800
    
done