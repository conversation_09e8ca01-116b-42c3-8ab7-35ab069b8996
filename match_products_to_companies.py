#!/usr/bin/env python3
"""
Match orphaned products to companies using intelligent matching
"""

import os
import sys
from supabase import create_client, Client
from datetime import datetime
import re
from difflib import SequenceMatcher

# Initialize Supabase client
url = os.environ.get("VITE_SUPABASE_URL")
key = os.environ.get("SUPABASE_SERVICE_ROLE_KEY")

if not url or not key:
    print("Error: Missing environment variables")
    sys.exit(1)

supabase: Client = create_client(url, key)

def get_orphaned_products():
    """Get products without company associations"""
    try:
        # Get products without primary_company_id
        response = supabase.table('uses_products').select('*').is_('primary_company_id', 'null').execute()
        return response.data
    except Exception as e:
        print(f"Error getting orphaned products: {e}")
        return []

def get_all_companies():
    """Get all companies for matching"""
    try:
        response = supabase.table('hemp_companies').select('*').execute()
        return response.data
    except Exception as e:
        print(f"Error getting companies: {e}")
        return []

def fuzzy_match(str1, str2, threshold=0.8):
    """Fuzzy string matching with threshold"""
    return SequenceMatcher(None, str1.lower(), str2.lower()).ratio() >= threshold

def extract_brand_from_product(product_name):
    """Extract potential brand name from product name"""
    # Common patterns
    patterns = [
        r'^([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\s+(?:Hemp|CBD|CBG|THC)',  # "Brand Name Hemp..."
        r'^([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\s+\d+',  # "Brand Name 500mg..."
        r'^([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\s+(?:Premium|Organic|Natural)',  # "Brand Premium..."
        r'^([A-Z][a-z]+(?:\s+&\s+[A-Z][a-z]+)*)',  # "Brand & Brand"
        r'^([A-Z][a-z]+\'s)',  # "Brand's"
    ]
    
    for pattern in patterns:
        match = re.match(pattern, product_name)
        if match:
            return match.group(1)
    
    # Check for known brand indicators
    words = product_name.split()
    if len(words) > 1:
        # First word might be brand if it's capitalized and not a common word
        common_words = {'hemp', 'cbd', 'cbg', 'thc', 'organic', 'natural', 'premium', 'the', 'a', 'an'}
        if words[0].lower() not in common_words and words[0][0].isupper():
            return words[0]
    
    return None

def match_product_to_company(product, companies):
    """Try to match a product to a company"""
    product_name = product.get('name', '')
    product_desc = product.get('description', '')
    
    matches = []
    
    # Extract potential brand from product name
    brand = extract_brand_from_product(product_name)
    
    for company in companies:
        company_name = company.get('name', '')
        score = 0
        match_type = []
        
        # Direct name match in product name
        if company_name.lower() in product_name.lower():
            score += 1.0
            match_type.append('name_in_product')
        
        # Brand extraction match
        if brand and fuzzy_match(brand, company_name, 0.85):
            score += 0.9
            match_type.append('brand_match')
        
        # Company name in description
        if company_name.lower() in product_desc.lower():
            score += 0.7
            match_type.append('name_in_description')
        
        # Partial word matching
        company_words = set(company_name.lower().split())
        product_words = set(product_name.lower().split())
        common_words = company_words.intersection(product_words)
        if len(common_words) >= 2:
            score += 0.5 * len(common_words) / len(company_words)
            match_type.append('partial_match')
        
        # Special cases
        if company_name == "Charlotte's Web" and "charlotte" in product_name.lower():
            score += 1.0
            match_type.append('special_case')
        
        if score > 0:
            matches.append({
                'company': company,
                'score': score,
                'match_types': match_type
            })
    
    # Sort by score and return best match
    matches.sort(key=lambda x: x['score'], reverse=True)
    
    if matches and matches[0]['score'] >= 0.7:
        return matches[0]
    
    return None

def create_company_product_relationship(product_id, company_id, relationship_type='manufacturer', is_primary=True):
    """Create relationship between company and product"""
    try:
        # Check if relationship already exists
        existing = supabase.table('hemp_company_products').select('*').eq('product_id', product_id).eq('company_id', company_id).execute()
        
        if not existing.data:
            # Create new relationship
            data = {
                'company_id': company_id,
                'product_id': product_id,
                'relationship_type': relationship_type,
                'is_primary': is_primary
            }
            
            supabase.table('hemp_company_products').insert(data).execute()
            return True
        
        return False
        
    except Exception as e:
        print(f"Error creating relationship: {e}")
        return False

def main():
    print("🔗 Product-Company Matching Process")
    print("=" * 60)
    
    # Get data
    orphaned_products = get_orphaned_products()
    companies = get_all_companies()
    
    print(f"\nFound {len(orphaned_products)} orphaned products")
    print(f"Found {len(companies)} companies for matching")
    
    if not orphaned_products:
        print("No orphaned products to match!")
        return
    
    # Match products
    matched_count = 0
    relationship_count = 0
    no_match = []
    
    print("\nMatching products to companies...")
    
    for i, product in enumerate(orphaned_products):
        print(f"\n[{i+1}/{len(orphaned_products)}] {product['name']}")
        
        match = match_product_to_company(product, companies)
        
        if match:
            company = match['company']
            print(f"  ✅ Matched to: {company['name']} (score: {match['score']:.2f})")
            print(f"     Match types: {', '.join(match['match_types'])}")
            
            # Update product with primary company
            try:
                supabase.table('uses_products').update({
                    'primary_company_id': company['id']
                }).eq('id', product['id']).execute()
                
                matched_count += 1
                
                # Also create relationship
                if create_company_product_relationship(product['id'], company['id']):
                    relationship_count += 1
                    
            except Exception as e:
                print(f"  ❌ Error updating product: {e}")
        else:
            print(f"  ❓ No match found")
            no_match.append(product['name'])
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 MATCHING SUMMARY")
    print("=" * 60)
    print(f"Total orphaned products: {len(orphaned_products)}")
    print(f"Successfully matched: {matched_count}")
    print(f"New relationships created: {relationship_count}")
    print(f"No match found: {len(no_match)}")
    
    if matched_count > 0:
        match_rate = (matched_count / len(orphaned_products)) * 100
        print(f"\n✅ Match rate: {match_rate:.1f}%")
    
    if no_match:
        print(f"\n❓ Products without matches ({len(no_match)}):")
        for product in no_match[:10]:
            print(f"  - {product}")
        if len(no_match) > 10:
            print(f"  ... and {len(no_match) - 10} more")

if __name__ == "__main__":
    main()