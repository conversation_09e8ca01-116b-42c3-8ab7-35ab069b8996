import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load .env from the correct path
dotenv.config({ path: join(__dirname, '.env') });

const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing required environment variables:');
  console.error('VITE_SUPABASE_URL:', supabaseUrl);
  console.error('SUPABASE_SERVICE_ROLE_KEY:', supabaseKey ? '[SET]' : '[NOT SET]');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Generic company name patterns
const genericPatterns = [
  /^[A-Z][a-z]+(?:Boost|Bloom|Aura|Balance|Plus|Pro|Max|Ultra)$/,
  /^(?:Hemp|Cannabis|CBD)\s+(?:Co|Company|Corp|Inc|LLC)$/,
  /^Generic\s+\w+$/,
  /^Test\s+\w+$/,
  /^\w+\s+Brand$/,
];

// Known legitimate hemp companies
const knownLegitimate = new Set([
  'Manitoba Harvest', 'HempFlax', 'GenCanna', 'HempWood',
  "Charlotte's Web", 'Elixinol', 'CV Sciences', 'Hemp Inc',
  'Nutiva', "Bob's Red Mill", "Dr. Bronner's", 'Patagonia'
]);

async function validateCompanies() {
  console.log('🔍 COMPANY VALIDATION REPORT');
  console.log('='.repeat(60));

  // Get all companies
  const { data: companies, error } = await supabase
    .from('hemp_companies')
    .select('*')
    .order('name');

  if (error) {
    console.error('Error fetching companies:', error);
    return;
  }

  console.log(`\nTotal companies: ${companies.length}`);

  let issues = {
    genericNames: [],
    noWebsite: [],
    noDescription: [],
    placeholderDesc: [],
    noType: [],
    duplicates: [],
  };

  let qualityScores = [];

  // Analyze each company
  for (const company of companies) {
    let score = 50; // Base score
    let companyIssues = [];

    // Check name quality
    const isGeneric = genericPatterns.some(pattern => pattern.test(company.name));
    if (isGeneric) {
      issues.genericNames.push(company.name);
      companyIssues.push('generic name');
      score -= 20;
    }

    if (knownLegitimate.has(company.name)) {
      score += 20;
    }

    // Check website
    if (!company.website) {
      issues.noWebsite.push(company.name);
      companyIssues.push('no website');
      score -= 10;
    } else {
      score += 15;
    }

    // Check description
    if (!company.description) {
      issues.noDescription.push(company.name);
      companyIssues.push('no description');
      score -= 10;
    } else if (
      company.description.includes('is a hemp product brand') ||
      company.description.includes('identified from product names') ||
      company.description.includes('Generic')
    ) {
      issues.placeholderDesc.push(company.name);
      companyIssues.push('placeholder description');
      score -= 15;
    } else if (company.description.length > 50) {
      score += 10;
    }

    // Check type
    if (!company.company_type) {
      issues.noType.push(company.name);
      companyIssues.push('no type');
      score -= 5;
    }

    // Verified status
    if (company.verified) {
      score += 20;
    }

    qualityScores.push({ name: company.name, score: Math.max(0, Math.min(100, score)), issues: companyIssues });
  }

  // Find duplicates
  for (let i = 0; i < companies.length; i++) {
    for (let j = i + 1; j < companies.length; j++) {
      const name1 = companies[i].name.toLowerCase();
      const name2 = companies[j].name.toLowerCase();
      
      if (name1 === name2 || 
          name1.replace(/\s+/g, '') === name2.replace(/\s+/g, '') ||
          (name1.includes(name2) || name2.includes(name1))) {
        issues.duplicates.push(`${companies[i].name} ↔ ${companies[j].name}`);
      }
    }
  }

  // Calculate average quality score
  const avgScore = qualityScores.reduce((sum, c) => sum + c.score, 0) / qualityScores.length;

  // Print results
  console.log(`\n📊 QUALITY METRICS`);
  console.log(`Average Quality Score: ${avgScore.toFixed(1)}/100`);
  console.log(`Valid Companies: ${qualityScores.filter(c => c.score >= 70).length}`);
  console.log(`Needs Improvement: ${qualityScores.filter(c => c.score < 70).length}`);

  console.log(`\n❌ ISSUES FOUND`);
  console.log(`Generic Names: ${issues.genericNames.length}`);
  console.log(`No Website: ${issues.noWebsite.length}`);
  console.log(`No Description: ${issues.noDescription.length}`);
  console.log(`Placeholder Descriptions: ${issues.placeholderDesc.length}`);
  console.log(`No Company Type: ${issues.noType.length}`);
  console.log(`Potential Duplicates: ${issues.duplicates.length}`);

  // Show sample issues
  if (issues.genericNames.length > 0) {
    console.log(`\n🏷️ Sample Generic Names:`);
    issues.genericNames.slice(0, 5).forEach(name => console.log(`  - ${name}`));
  }

  if (issues.duplicates.length > 0) {
    console.log(`\n👥 Sample Duplicates:`);
    issues.duplicates.slice(0, 5).forEach(dup => console.log(`  - ${dup}`));
  }

  // Show lowest quality companies
  console.log(`\n📉 Lowest Quality Companies:`);
  qualityScores
    .sort((a, b) => a.score - b.score)
    .slice(0, 10)
    .forEach(c => {
      console.log(`  - ${c.name} (Score: ${c.score}/100)`);
      if (c.issues.length > 0) {
        console.log(`    Issues: ${c.issues.join(', ')}`);
      }
    });

  // Show highest quality companies
  console.log(`\n📈 Highest Quality Companies:`);
  qualityScores
    .sort((a, b) => b.score - a.score)
    .slice(0, 5)
    .forEach(c => {
      console.log(`  - ${c.name} (Score: ${c.score}/100)`);
    });

  // Check product-company relationships
  const { count: productsWithCompany } = await supabase
    .from('uses_products')
    .select('*', { count: 'exact', head: true })
    .not('primary_company_id', 'is', null);

  const { count: totalProducts } = await supabase
    .from('uses_products')
    .select('*', { count: 'exact', head: true });

  console.log(`\n📦 PRODUCT-COMPANY LINKS`);
  console.log(`Products with companies: ${productsWithCompany}/${totalProducts} (${(productsWithCompany/totalProducts*100).toFixed(1)}%)`);
  console.log(`Products without companies: ${totalProducts - productsWithCompany}`);
}

validateCompanies().catch(console.error);