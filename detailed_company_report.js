import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load .env
dotenv.config({ path: join(__dirname, '.env') });

const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

const supabase = createClient(supabaseUrl, supabaseKey);

async function generateDetailedReport() {
  console.log('📊 DETAILED COMPANY VALIDATION REPORT');
  console.log('='.repeat(70));
  console.log(`Generated: ${new Date().toLocaleString()}`);
  console.log('='.repeat(70));

  // Fetch all companies
  const { data: companies } = await supabase
    .from('hemp_companies')
    .select('*')
    .order('name');

  // Calculate statistics
  const stats = {
    total: companies.length,
    withWebsite: companies.filter(c => c.website).length,
    withoutWebsite: companies.filter(c => !c.website).length,
    verified: companies.filter(c => c.verified).length,
    withPlaceholderDesc: companies.filter(c => 
      c.description?.includes('is a hemp product brand') ||
      c.description?.includes('identified from product names')
    ).length,
    withoutType: companies.filter(c => !c.company_type).length,
    byType: {}
  };

  // Count by type
  companies.forEach(c => {
    const type = c.company_type || 'Not specified';
    stats.byType[type] = (stats.byType[type] || 0) + 1;
  });

  // Print overall statistics
  console.log('\n📈 OVERALL STATISTICS');
  console.log('-'.repeat(50));
  console.log(`Total Companies: ${stats.total}`);
  console.log(`Verified Companies: ${stats.verified} (${(stats.verified/stats.total*100).toFixed(1)}%)`);
  console.log(`With Website: ${stats.withWebsite} (${(stats.withWebsite/stats.total*100).toFixed(1)}%)`);
  console.log(`Without Website: ${stats.withoutWebsite} (${(stats.withoutWebsite/stats.total*100).toFixed(1)}%)`);
  console.log(`Placeholder Descriptions: ${stats.withPlaceholderDesc} (${(stats.withPlaceholderDesc/stats.total*100).toFixed(1)}%)`);
  console.log(`Missing Type: ${stats.withoutType} (${(stats.withoutType/stats.total*100).toFixed(1)}%)`);

  console.log('\n📊 COMPANIES BY TYPE');
  console.log('-'.repeat(50));
  Object.entries(stats.byType)
    .sort((a, b) => b[1] - a[1])
    .forEach(([type, count]) => {
      console.log(`${type}: ${count} companies`);
    });

  // Show high-quality companies
  console.log('\n✨ TOP QUALITY COMPANIES (Verified with Website)');
  console.log('-'.repeat(70));
  const topCompanies = companies
    .filter(c => c.verified && c.website && c.description && !c.description.includes('is a hemp product brand'))
    .slice(0, 10);

  topCompanies.forEach(c => {
    console.log(`\n${c.name}`);
    console.log(`  Type: ${c.company_type || 'Not specified'}`);
    console.log(`  Website: ${c.website}`);
    console.log(`  Founded: ${c.founded_year || 'Unknown'}`);
    console.log(`  Description: ${c.description.substring(0, 150)}...`);
  });

  // Show companies needing improvement
  console.log('\n\n⚠️  COMPANIES NEEDING IMPROVEMENT');
  console.log('-'.repeat(70));
  
  // Generic named companies
  const genericNames = companies.filter(c => 
    /^[A-Z][a-z]+(?:Boost|Bloom|Aura|Balance|Plus|Pro|Max|Ultra)$/.test(c.name) ||
    c.name.includes('Generic')
  );

  console.log('\n🏷️ Generic/Suspicious Names:');
  genericNames.forEach(c => {
    console.log(`  - ${c.name} (Verified: ${c.verified ? 'Yes' : 'No'}, Website: ${c.website ? 'Yes' : 'No'})`);
  });

  // Companies without websites but with products
  const { data: productCounts } = await supabase
    .from('hemp_company_products')
    .select('company_id')
    .order('company_id');

  const companyProductCounts = {};
  productCounts?.forEach(p => {
    companyProductCounts[p.company_id] = (companyProductCounts[p.company_id] || 0) + 1;
  });

  const noWebsiteButProducts = companies
    .filter(c => !c.website && companyProductCounts[c.id] > 0)
    .sort((a, b) => (companyProductCounts[b.id] || 0) - (companyProductCounts[a.id] || 0))
    .slice(0, 10);

  console.log('\n🌐 No Website but Has Products:');
  noWebsiteButProducts.forEach(c => {
    console.log(`  - ${c.name} (${companyProductCounts[c.id]} products)`);
  });

  // Placeholder descriptions
  const placeholderDescs = companies
    .filter(c => c.description?.includes('is a hemp product brand'))
    .slice(0, 10);

  console.log('\n📝 Placeholder Descriptions:');
  placeholderDescs.forEach(c => {
    console.log(`  - ${c.name}`);
  });

  // Product coverage
  const { count: totalProducts } = await supabase
    .from('uses_products')
    .select('*', { count: 'exact', head: true });

  const { count: productsWithCompany } = await supabase
    .from('uses_products')
    .select('*', { count: 'exact', head: true })
    .not('primary_company_id', 'is', null);

  console.log('\n\n📦 PRODUCT-COMPANY COVERAGE');
  console.log('-'.repeat(50));
  console.log(`Total Products: ${totalProducts}`);
  console.log(`Products with Companies: ${productsWithCompany} (${(productsWithCompany/totalProducts*100).toFixed(1)}%)`);
  console.log(`Products without Companies: ${totalProducts - productsWithCompany}`);

  // Recommendations
  console.log('\n\n💡 RECOMMENDATIONS');
  console.log('-'.repeat(70));
  console.log('1. Priority Actions:');
  console.log('   - Add websites for companies with products but no website');
  console.log('   - Replace placeholder descriptions with real company information');
  console.log('   - Review and potentially remove generic-named companies');
  console.log('   - Add company types for the 88 companies missing this data');
  
  console.log('\n2. Data Quality Improvements:');
  console.log('   - Verify the 5 potential duplicate company pairs');
  console.log('   - Add founding years and location data where possible');
  console.log('   - Consider merging similar companies (e.g., WAMA and WAMA Underwear)');
  
  console.log('\n3. Product Matching:');
  console.log('   - Focus on matching the remaining 120 products');
  console.log('   - Review generic industry companies and replace with real ones');
  console.log('   - Consider creating a "Unknown Manufacturer" company for orphaned products');

  console.log('\n='.repeat(70));
  console.log('Report Complete');
}

generateDetailedReport().catch(console.error);