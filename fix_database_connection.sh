#!/bin/bash
# Fix database connection for automation scripts by using the pooler endpoint

echo "🔧 Fixing Database Connection for Automation Scripts"
echo "==================================================="
echo ""
echo "The issue: Supabase database only has IPv6 addresses, but WSL2 doesn't support IPv6"
echo "The solution: Use the Supabase pooler endpoint which has IPv4 addresses"
echo ""

# Update the cron automation script
echo "Updating cron_automation.sh..."
sed -i 's|db\.ktoqznqmlnxrtvubewyz\.supabase\.co|aws-0-us-east-1.pooler.supabase.com|g' cron_automation.sh

# Update the expansion agents script
echo "Updating start_expansion_agents.sh..."
sed -i 's|db\.ktoqznqmlnxrtvubewyz\.supabase\.co|aws-0-us-east-1.pooler.supabase.com|g' start_expansion_agents.sh

# Update any Python scripts that might have hardcoded URLs
echo "Checking Python scripts for hardcoded database URLs..."
grep -r "db.ktoqznqmlnxrtvubewyz.supabase.co" --include="*.py" . 2>/dev/null | while read -r line; do
    file=$(echo "$line" | cut -d: -f1)
    echo "  Updating: $file"
    sed -i 's|db\.ktoqznqmlnxrtvubewyz\.supabase\.co|aws-0-us-east-1.pooler.supabase.com|g' "$file"
done

# Test the connection
echo ""
echo "Testing new connection..."
export DATABASE_URL="postgresql://postgres:%<EMAIL>:5432/postgres"

python3 -c "
import psycopg2
from urllib.parse import urlparse
import os

try:
    url = urlparse(os.getenv('DATABASE_URL'))
    conn = psycopg2.connect(
        host=url.hostname,
        port=url.port or 5432,
        database=url.path[1:],
        user=url.username,
        password=url.password,
        sslmode='require'
    )
    conn.close()
    print('✅ Connection successful!')
except Exception as e:
    print(f'❌ Connection failed: {e}')
"

echo ""
echo "Done! The automation scripts should now be able to connect to the database."
echo "The cron jobs will use the new connection string on their next run."