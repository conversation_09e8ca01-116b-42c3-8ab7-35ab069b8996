<!DOCTYPE html>
<html>
<head>
    <title>Image Fix Verification</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            background-color: #0a0a0a;
            color: #e0e0e0;
        }
        .container { max-width: 1200px; margin: 0 auto; }
        .status { 
            padding: 20px; 
            margin: 20px 0; 
            border-radius: 8px; 
            background: #1a1a1a;
            border: 1px solid #333;
        }
        .success { border-color: #10b981; background: #064e3b; }
        .error { border-color: #ef4444; background: #7f1d1d; }
        .warning { border-color: #f59e0b; background: #78350f; }
        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .product-card {
            background: #1a1a1a;
            border: 1px solid #333;
            border-radius: 8px;
            padding: 15px;
        }
        .product-card img {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .field-info {
            font-size: 12px;
            color: #9ca3af;
            margin-top: 10px;
        }
        .image-status {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            margin-left: 10px;
        }
        .image-working { background: #10b981; color: white; }
        .image-broken { background: #ef4444; color: white; }
        h1 { color: #10b981; }
        .stats { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: #1a1a1a;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #333;
            text-align: center;
        }
        .stat-value { font-size: 2em; font-weight: bold; color: #10b981; }
        .stat-label { color: #9ca3af; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Image Display Fix Verification</h1>
        <div id="status" class="status">Checking system status...</div>
        <div id="stats" class="stats"></div>
        <h2>Sample Products</h2>
        <div id="products" class="product-grid"></div>
    </div>

    <script>
        async function checkSystem() {
            const statusDiv = document.getElementById('status');
            const productsDiv = document.getElementById('products');
            const statsDiv = document.getElementById('stats');
            
            try {
                // Test API endpoints
                const testEndpoints = [
                    { url: 'http://localhost:5173/api/hemp-products?limit=10', name: 'Express API' },
                    { url: 'http://localhost:5173/', name: 'Vite Frontend' }
                ];
                
                let allWorking = true;
                let statusHTML = '<h3>System Status</h3>';
                
                for (const endpoint of testEndpoints) {
                    try {
                        const response = await fetch(endpoint.url);
                        if (response.ok) {
                            statusHTML += `<p>✅ ${endpoint.name}: Working</p>`;
                        } else {
                            statusHTML += `<p>❌ ${endpoint.name}: HTTP ${response.status}</p>`;
                            allWorking = false;
                        }
                    } catch (e) {
                        statusHTML += `<p>❌ ${endpoint.name}: Not responding</p>`;
                        allWorking = false;
                    }
                }
                
                // Fetch products
                const response = await fetch('http://localhost:5173/api/hemp-products?limit=20');
                const data = await response.json();
                const products = Array.isArray(data) ? data : (data.products || []);
                
                if (!products.length) {
                    statusDiv.className = 'status warning';
                    statusDiv.innerHTML = statusHTML + '<p>⚠️ No products returned from API</p>';
                    return;
                }
                
                // Analyze products
                let stats = {
                    total: products.length,
                    withImageUrl: 0,
                    withImage_url: 0,
                    withBoth: 0,
                    withNone: 0,
                    workingImages: 0
                };
                
                // Display products
                productsDiv.innerHTML = '';
                for (const product of products) {
                    // Count field presence
                    const hasImageUrl = !!product.imageUrl;
                    const hasImage_url = !!product.image_url;
                    
                    if (hasImageUrl) stats.withImageUrl++;
                    if (hasImage_url) stats.withImage_url++;
                    if (hasImageUrl && hasImage_url) stats.withBoth++;
                    if (!hasImageUrl && !hasImage_url) stats.withNone++;
                    
                    // Get the actual image URL
                    const imageUrl = product.imageUrl || product.image_url || '/images/unknown-hemp-image.png';
                    
                    // Create product card
                    const card = document.createElement('div');
                    card.className = 'product-card';
                    
                    const img = document.createElement('img');
                    img.src = imageUrl;
                    img.alt = product.name;
                    img.onload = () => {
                        stats.workingImages++;
                        updateStats();
                    };
                    img.onerror = () => {
                        img.src = '/images/unknown-hemp-image.png';
                    };
                    
                    card.innerHTML = `
                        <h3>${product.name}</h3>
                        <div class="field-info">
                            <p>imageUrl: ${hasImageUrl ? '✅' : '❌'} ${hasImageUrl ? 'Present' : 'Missing'}</p>
                            <p>image_url: ${hasImage_url ? '✅' : '❌'} ${hasImage_url ? 'Present' : 'Missing'}</p>
                            <p>URL: ${imageUrl.substring(0, 50)}...</p>
                        </div>
                    `;
                    card.prepend(img);
                    productsDiv.appendChild(card);
                }
                
                // Update status
                statusDiv.className = allWorking ? 'status success' : 'status warning';
                statusDiv.innerHTML = statusHTML;
                
                // Show stats
                function updateStats() {
                    statsDiv.innerHTML = `
                        <div class="stat-card">
                            <div class="stat-value">${stats.total}</div>
                            <div class="stat-label">Total Products</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">${stats.withImageUrl}</div>
                            <div class="stat-label">With imageUrl</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">${stats.withImage_url}</div>
                            <div class="stat-label">With image_url</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">${stats.workingImages}</div>
                            <div class="stat-label">Images Loaded</div>
                        </div>
                    `;
                }
                updateStats();
                
                // Summary
                if (stats.withImageUrl > 0 || stats.withImage_url > 0) {
                    statusDiv.innerHTML += `<p>✅ <strong>Images should now be displaying correctly!</strong></p>`;
                } else {
                    statusDiv.innerHTML += `<p>❌ <strong>No image URLs found in API response</strong></p>`;
                }
                
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = `
                    <h3>Error</h3>
                    <p>${error.message}</p>
                    <p>Make sure both servers are running:</p>
                    <ul>
                        <li>Backend: npm run dev (in HempResourceHub folder)</li>
                        <li>Frontend: npx vite (in HempResourceHub folder)</li>
                    </ul>
                `;
            }
        }
        
        // Run check on load
        checkSystem();
        
        // Auto-refresh every 5 seconds
        setInterval(checkSystem, 5000);
    </script>
</body>
</html>