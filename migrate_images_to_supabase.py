#!/usr/bin/env python3
"""
Migrate all generated images to Supabase Storage
This will upload all images and update database URLs
"""
import os
import sys
from pathlib import Path
from datetime import datetime
import mimetypes
from dotenv import load_dotenv
from supabase import create_client, Client
import psycopg2
from psycopg2.extras import execute_batch

load_dotenv()

# Initialize Supabase client
SUPABASE_URL = os.getenv('SUPABASE_URL') or os.getenv('VITE_SUPABASE_URL')
SUPABASE_KEY = os.getenv('SUPABASE_SERVICE_ROLE_KEY') or os.getenv('SUPABASE_ANON_KEY') or os.getenv('VITE_SUPABASE_ANON_KEY')

if not SUPABASE_URL or not SUPABASE_KEY:
    print("❌ Error: Supabase credentials not found in environment")
    sys.exit(1)

supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

# Database connection
DATABASE_URL = os.getenv('DATABASE_URL')
if not DATABASE_URL:
    print("❌ Error: DATABASE_URL not found in environment")
    sys.exit(1)

class ImageMigrator:
    def __init__(self):
        self.supabase = supabase
        self.conn = psycopg2.connect(DATABASE_URL)
        self.cursor = self.conn.cursor()
        self.bucket_name = 'product-images'
        self.stats = {
            'total_images': 0,
            'uploaded': 0,
            'skipped': 0,
            'failed': 0,
            'db_updated': 0
        }
    
    def ensure_bucket_exists(self):
        """Create bucket if it doesn't exist"""
        try:
            buckets = self.supabase.storage.list_buckets()
            bucket_exists = any(b.name == self.bucket_name for b in buckets)
            
            if not bucket_exists:
                print(f"📦 Creating bucket: {self.bucket_name}")
                self.supabase.storage.create_bucket(
                    self.bucket_name, 
                    options={'public': True}
                )
                print("✅ Bucket created successfully")
            else:
                print(f"✅ Bucket '{self.bucket_name}' already exists")
        except Exception as e:
            print(f"❌ Error checking/creating bucket: {e}")
            sys.exit(1)
    
    def get_images_to_migrate(self):
        """Get all local generated images"""
        image_dir = Path('generated_images')
        if not image_dir.exists():
            print("❌ Error: generated_images directory not found")
            return []
        
        images = list(image_dir.glob('*.png'))
        print(f"📊 Found {len(images)} images to process")
        return images
    
    def upload_image(self, image_path: Path):
        """Upload single image to Supabase"""
        try:
            # Extract product ID from filename (e.g., "123_Product Name.png")
            filename = image_path.name
            product_id = filename.split('_')[0] if '_' in filename else None
            
            # Create organized path in storage
            storage_path = f"generated/{filename}"
            
            # Check if already exists
            try:
                existing = self.supabase.storage.from_(self.bucket_name).list(path='generated')
                if any(f['name'] == filename for f in existing):
                    print(f"⏭️  Skipping {filename} - already uploaded")
                    self.stats['skipped'] += 1
                    return self.get_public_url(storage_path), product_id
            except:
                pass  # If list fails, continue with upload
            
            # Read and upload image
            with open(image_path, 'rb') as f:
                image_data = f.read()
            
            response = self.supabase.storage.from_(self.bucket_name).upload(
                path=storage_path,
                file=image_data,
                file_options={"content-type": "image/png", "upsert": "true"}
            )
            
            print(f"✅ Uploaded: {filename}")
            self.stats['uploaded'] += 1
            
            # Get public URL
            public_url = self.get_public_url(storage_path)
            return public_url, product_id
            
        except Exception as e:
            print(f"❌ Failed to upload {image_path.name}: {e}")
            self.stats['failed'] += 1
            return None, None
    
    def get_public_url(self, storage_path: str):
        """Get public URL for uploaded image"""
        return self.supabase.storage.from_(self.bucket_name).get_public_url(storage_path)
    
    def update_database_urls(self, url_mappings):
        """Update database with new Supabase URLs"""
        print(f"\n📝 Updating {len(url_mappings)} database records...")
        
        try:
            # Prepare batch update data
            update_data = []
            for old_url, new_url, product_id in url_mappings:
                if product_id:
                    # Update by product ID (most reliable)
                    update_data.append((new_url, int(product_id)))
                else:
                    # Fallback: update by current URL
                    self.cursor.execute(
                        "UPDATE uses_products SET image_url = %s WHERE image_url = %s",
                        (new_url, old_url)
                    )
            
            # Batch update by product ID
            if update_data:
                execute_batch(
                    self.cursor,
                    "UPDATE uses_products SET image_url = %s WHERE id = %s",
                    update_data
                )
            
            self.conn.commit()
            self.stats['db_updated'] = len(url_mappings)
            print(f"✅ Updated {self.stats['db_updated']} database records")
            
        except Exception as e:
            print(f"❌ Database update failed: {e}")
            self.conn.rollback()
    
    def migrate_all(self):
        """Run the complete migration"""
        print("\n🚀 Starting image migration to Supabase Storage")
        print("=" * 60)
        
        # Ensure bucket exists
        self.ensure_bucket_exists()
        
        # Get images to migrate
        images = self.get_images_to_migrate()
        if not images:
            print("❌ No images found to migrate")
            return
        
        self.stats['total_images'] = len(images)
        
        # Upload images and collect URL mappings
        print(f"\n📤 Uploading images...")
        url_mappings = []
        
        for i, image_path in enumerate(images, 1):
            print(f"\n[{i}/{len(images)}] Processing {image_path.name}")
            
            # Current local URL
            old_url = f"/generated_images/{image_path.name}"
            
            # Upload and get new URL
            new_url, product_id = self.upload_image(image_path)
            
            if new_url:
                url_mappings.append((old_url, new_url, product_id))
        
        # Update database
        if url_mappings:
            self.update_database_urls(url_mappings)
        
        # Print summary
        self.print_summary()
        
        # Close connections
        self.cursor.close()
        self.conn.close()
    
    def print_summary(self):
        """Print migration summary"""
        print("\n" + "=" * 60)
        print("📊 MIGRATION SUMMARY")
        print("=" * 60)
        print(f"Total images found: {self.stats['total_images']}")
        print(f"Successfully uploaded: {self.stats['uploaded']}")
        print(f"Skipped (already exists): {self.stats['skipped']}")
        print(f"Failed uploads: {self.stats['failed']}")
        print(f"Database records updated: {self.stats['db_updated']}")
        print("\n✅ Migration complete!")
        
        if self.stats['uploaded'] > 0:
            print("\n📌 Next steps:")
            print("1. Verify images are loading correctly in the app")
            print("2. Run: git rm -r --cached generated_images/")
            print("3. Add 'generated_images/' to .gitignore")
            print("4. Commit and push to remove images from repository")

def main():
    """Run the migration"""
    # Confirm before proceeding
    print("⚠️  This will upload all generated images to Supabase Storage")
    print("   and update the database URLs. The process may take several minutes.")
    response = input("\nContinue? (yes/no): ")
    
    if response.lower() != 'yes':
        print("❌ Migration cancelled")
        return
    
    # Run migration
    migrator = ImageMigrator()
    migrator.migrate_all()

if __name__ == "__main__":
    main()