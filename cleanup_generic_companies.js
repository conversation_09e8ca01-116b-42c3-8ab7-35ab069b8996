import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load .env
dotenv.config({ path: join(__dirname, '.env') });

const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

const supabase = createClient(supabaseUrl, supabaseKey);

async function cleanupGenericCompanies() {
  console.log('🧹 GENERIC COMPANY CLEANUP');
  console.log('='.repeat(60));

  // List of generic companies to remove
  const genericToRemove = [
    'AromaBoost', 'AuraBloom', 'AuraBoost', '<PERSON>na<PERSON><PERSON>', 'Derma<PERSON>loom',
    'NeuroBloom', 'Sono<PERSON><PERSON>', 'AromaSculpt', 'Aura Balance', 'AuraMist',
    'AuraShine', 'AuraSync', 'BreatheEasy', 'CannaCalm', 'CannaFocus',
    'CannaSleep', 'DermaHemp', 'DermaRenew', 'FocusFlow', 'HempEase',
    'HempFiberSkin', 'HempHydrate', 'HempLeaf', 'HempRenew', 'HempRoot',
    'HempShine', 'HempZen', 'Mindful Mist', 'MoodScape Collection',
    'MuscleMend', 'Night Bloom', 'RejuveHemp', 'RejuveSkin', 'ReliefRise',
    'RespireEasy', 'SkinRenew', 'SleepScape', 'SleepSound', 'SleepWell',
    'SonoCalm', 'TerpFusion', 'TerpeneFusion', 'Tranquil'
  ];

  console.log(`\n🔍 Checking ${genericToRemove.length} generic companies...`);

  let deletedCount = 0;
  let hasProductsCount = 0;

  for (const companyName of genericToRemove) {
    try {
      // Get company
      const { data: company } = await supabase
        .from('hemp_companies')
        .select('id')
        .eq('name', companyName)
        .single();

      if (!company) {
        console.log(`⏭️  ${companyName} - already removed`);
        continue;
      }

      // Check for products
      const { data: products } = await supabase
        .from('uses_products')
        .select('id, name')
        .eq('primary_company_id', company.id);

      const { data: companyProducts } = await supabase
        .from('hemp_company_products')
        .select('id')
        .eq('company_id', company.id);

      if ((!products || products.length === 0) && (!companyProducts || companyProducts.length === 0)) {
        // No products, safe to delete
        await supabase
          .from('hemp_companies')
          .delete()
          .eq('id', company.id);
        
        deletedCount++;
        console.log(`✅ Deleted: ${companyName}`);
      } else {
        hasProductsCount++;
        console.log(`⚠️  ${companyName} has ${products?.length || 0} products - keeping`);
        
        // Show what products it has
        if (products && products.length > 0) {
          products.slice(0, 3).forEach(p => {
            console.log(`    - ${p.name}`);
          });
          if (products.length > 3) {
            console.log(`    ... and ${products.length - 3} more`);
          }
        }
      }
    } catch (error) {
      console.error(`❌ Error processing ${companyName}: ${error.message}`);
    }
  }

  console.log('\n📊 CLEANUP SUMMARY');
  console.log('='.repeat(60));
  console.log(`Deleted: ${deletedCount} companies`);
  console.log(`Kept (has products): ${hasProductsCount} companies`);

  // Also merge the last duplicate
  console.log('\n🔄 Merging remaining duplicate...');
  try {
    const { data: hempFoods } = await supabase
      .from('hemp_companies')
      .select('id')
      .eq('name', 'Hemp Foods')
      .single();

    const { data: hempFoodsAus } = await supabase
      .from('hemp_companies')
      .select('id')
      .eq('name', 'Hemp Foods Australia')
      .single();

    if (hempFoods && hempFoodsAus) {
      // Update relationships
      await supabase
        .from('hemp_company_products')
        .update({ company_id: hempFoods.id })
        .eq('company_id', hempFoodsAus.id);

      await supabase
        .from('uses_products')
        .update({ primary_company_id: hempFoods.id })
        .eq('primary_company_id', hempFoodsAus.id);

      // Delete duplicate
      await supabase
        .from('hemp_companies')
        .delete()
        .eq('id', hempFoodsAus.id);

      console.log('✅ Merged Hemp Foods Australia into Hemp Foods');
    }
  } catch (error) {
    console.error('❌ Error merging Hemp Foods duplicate:', error.message);
  }

  console.log('\n✅ Cleanup complete!');
}

cleanupGenericCompanies().catch(console.error);