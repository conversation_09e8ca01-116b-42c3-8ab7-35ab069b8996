#!/usr/bin/env python3
"""
Analyze quality issues in the hemp database, focusing on recent agent additions.
"""

import os
import psycopg2
from datetime import datetime, timed<PERSON>ta
from collections import defaultdict
import re

def analyze_quality_issues():
    """Perform comprehensive quality analysis of recent database entries."""
    
    conn = psycopg2.connect(os.environ['DATABASE_URL'])
    cur = conn.cursor()
    
    print("\n" + "="*80)
    print("🔍 HEMP DATABASE QUALITY ANALYSIS")
    print("="*80)
    
    # 1. Check for vague product names (with spaces and special characters)
    print("\n📝 VAGUE OR POORLY FORMATTED PRODUCT NAMES:")
    cur.execute("""
        SELECT id, name, source_agent, created_at
        FROM uses_products
        WHERE name ~ '\\s{2,}'  -- Multiple spaces
           OR name ~ '^\\s'     -- Leading spaces
           OR name ~ '\\s$'     -- Trailing spaces
           OR LENGTH(name) < 10  -- Very short names
           OR name ~ '[^\\w\\s\\-,&\\(\\)]'  -- Unusual characters
        ORDE<PERSON> BY created_at DESC
        LIMIT 20
    """)
    vague_names = cur.fetchall()
    
    print(f"Found {len(vague_names)} products with naming issues:")
    for id, name, agent, created in vague_names:
        print(f"  ID {id}: '{name}' (Agent: {agent}, Date: {created})")
    
    # 2. Check for generic/repetitive descriptions
    print("\n📋 GENERIC OR REPETITIVE DESCRIPTIONS:")
    cur.execute("""
        SELECT id, name, LEFT(description, 100) as desc_preview, source_agent
        FROM uses_products
        WHERE description LIKE '%based on academic research findings%'
           OR description LIKE '%developed using patented technology%'
           OR description LIKE '%This innovation leverages%'
           OR LENGTH(description) < 50
        ORDER BY created_at DESC
        LIMIT 15
    """)
    generic_descs = cur.fetchall()
    
    print(f"Found {len(generic_descs)} products with generic descriptions:")
    for id, name, desc, agent in generic_descs:
        print(f"  ID {id}: {name}")
        print(f"    Desc: {desc}...")
        print(f"    Agent: {agent}")
    
    # 3. Analyze agent performance trends
    print("\n📊 AGENT PERFORMANCE TRENDS:")
    cur.execute("""
        SELECT 
            source_agent,
            COUNT(*) as total_products,
            AVG(data_completeness_score) as avg_quality,
            COUNT(CASE WHEN created_at > NOW() - INTERVAL '7 days' THEN 1 END) as recent_count,
            AVG(CASE WHEN created_at > NOW() - INTERVAL '7 days' 
                THEN data_completeness_score END) as recent_quality
        FROM uses_products
        WHERE source_agent IS NOT NULL
        GROUP BY source_agent
        HAVING COUNT(*) > 10
        ORDER BY recent_count DESC
    """)
    agent_trends = cur.fetchall()
    
    for agent, total, avg_q, recent, recent_q in agent_trends:
        recent_q = float(recent_q or 0)
        avg_q = float(avg_q or 0)
        quality_change = "↓ DECLINING" if recent_q < avg_q - 0.1 else "→ STABLE" if abs(recent_q - avg_q) < 0.1 else "↑ IMPROVING"
        print(f"\n  {agent}:")
        print(f"    Total: {total} products")
        print(f"    Overall Quality: {avg_q:.2f}")
        print(f"    Recent (7d): {recent} products, Quality: {recent_q:.2f} {quality_change}")
    
    # 4. Check for duplicate patterns
    print("\n🔄 DUPLICATE PATTERNS:")
    cur.execute("""
        WITH name_patterns AS (
            SELECT 
                REGEXP_REPLACE(name, '\\s+', ' ', 'g') as normalized_name,
                COUNT(*) as count,
                STRING_AGG(DISTINCT source_agent::text, ', ') as agents
            FROM uses_products
            GROUP BY REGEXP_REPLACE(name, '\\s+', ' ', 'g')
            HAVING COUNT(*) > 1
        )
        SELECT * FROM name_patterns
        ORDER BY count DESC
        LIMIT 10
    """)
    duplicates = cur.fetchall()
    
    print(f"Found {len(duplicates)} duplicate name patterns:")
    for name, count, agents in duplicates:
        print(f"  '{name}': {count} occurrences (Agents: {agents})")
    
    # 5. Check for quality score anomalies
    print("\n⚠️  QUALITY SCORE ANOMALIES:")
    cur.execute("""
        SELECT 
            source_agent,
            COUNT(*) as zero_score_count,
            MIN(created_at) as first_occurrence,
            MAX(created_at) as last_occurrence
        FROM uses_products
        WHERE data_completeness_score = 0 OR data_completeness_score IS NULL
        GROUP BY source_agent
        HAVING COUNT(*) > 5
        ORDER BY zero_score_count DESC
    """)
    score_issues = cur.fetchall()
    
    print("Agents with many zero-quality products:")
    for agent, count, first, last in score_issues:
        print(f"  {agent}: {count} products with 0 score")
        print(f"    Period: {first} to {last}")
    
    # 6. Check recent automation effectiveness
    print("\n🤖 RECENT AUTOMATION ACTIVITY (Last 48 hours):")
    cur.execute("""
        SELECT 
            DATE(created_at) as date,
            COUNT(*) as products_added,
            COUNT(DISTINCT source_agent) as active_agents,
            AVG(data_completeness_score) as avg_quality
        FROM uses_products
        WHERE created_at > NOW() - INTERVAL '48 hours'
        GROUP BY DATE(created_at)
        ORDER BY date DESC
    """)
    recent_activity = cur.fetchall()
    
    for date, count, agents, quality in recent_activity:
        print(f"  {date}: {count} products by {agents} agents (Avg Quality: {quality:.2f})")
    
    # 7. Specific issues with recent agents
    print("\n🚨 ISSUES WITH RECENT PATENT/ACADEMIC AGENTS:")
    cur.execute("""
        SELECT 
            p.id, 
            p.name,
            p.plant_part_id,
            pp.name as plant_part,
            LEFT(p.description, 150) as desc_preview
        FROM uses_products p
        LEFT JOIN plant_parts pp ON p.plant_part_id = pp.id
        WHERE source_agent IN ('Academic Research Agent', 'Simplified Patent Mining Agent')
        AND created_at > NOW() - INTERVAL '7 days'
        ORDER BY created_at DESC
        LIMIT 20
    """)
    recent_agent_issues = cur.fetchall()
    
    print("Recent products showing quality issues:")
    for id, name, part_id, part_name, desc in recent_agent_issues:
        issues = []
        if '  ' in name or len(name.split()) < 3:
            issues.append("Poor naming")
        if part_name and part_name not in ['Hemp Bast (Fiber)', 'Hemp Seeds', 'Hemp Leaves', 'Hemp Flowers', 'Hemp Roots', 'Hemp Hurd (Shivs)']:
            issues.append(f"Wrong plant part: {part_name}")
        if 'based on academic research' in desc or 'developed using patented' in desc:
            issues.append("Generic description")
        
        if issues:
            print(f"\n  ID {id}: {name}")
            print(f"    Issues: {', '.join(issues)}")
            print(f"    Description: {desc}...")
    
    cur.close()
    conn.close()
    
    print("\n" + "="*80)
    print("💡 KEY FINDINGS:")
    print("="*80)
    print("""
1. NAMING ISSUES: Products with spaces in odd places, very short names, or strange characters
2. GENERIC DESCRIPTIONS: Many products use template-like descriptions from agents
3. ZERO QUALITY SCORES: Recent agents (Patent & Academic) all have 0 quality scores
4. WRONG PLANT PARTS: Some products assigned to "Cannabinoids" instead of proper parts
5. AGENT TEMPLATES: Clear evidence of template-based generation without customization
    """)

if __name__ == "__main__":
    analyze_quality_issues()