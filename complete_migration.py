#!/usr/bin/env python3
"""
Complete the remaining image migrations to Supabase
"""
import os
import sys
from pathlib import Path
from dotenv import load_dotenv
from supabase import create_client, Client
import psycopg2
import time

load_dotenv()

# Initialize connections
SUPABASE_URL = os.getenv('SUPABASE_URL') or os.getenv('VITE_SUPABASE_URL')
SUPABASE_KEY = os.getenv('SUPABASE_SERVICE_ROLE_KEY') or os.getenv('SUPABASE_ANON_KEY') or os.getenv('VITE_SUPABASE_ANON_KEY')
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

DATABASE_URL = os.getenv('DATABASE_URL')
conn = psycopg2.connect(DATABASE_URL)
cursor = conn.cursor()

# Get products still using local images
cursor.execute("""
    SELECT id, name, image_url 
    FROM uses_products 
    WHERE image_url LIKE '/generated_images/%'
    ORDER BY id
""")
products_to_update = cursor.fetchall()

print(f"📊 Found {len(products_to_update)} products still using local images")

if not products_to_update:
    print("✅ All images already migrated!")
    sys.exit(0)

# Check which local files exist
image_dir = Path('generated_images')
existing_files = {f.name for f in image_dir.glob('*.png')}

updated = 0
failed = 0

for product_id, name, local_url in products_to_update[:100]:  # Process 100 at a time
    filename = local_url.replace('/generated_images/', '')
    
    # Skip if file doesn't exist
    if filename not in existing_files:
        print(f"⏭️  Skipping {product_id}: {name} - file not found")
        continue
    
    # Upload to Supabase
    try:
        storage_path = f"generated/{filename}"
        image_path = image_dir / filename
        
        with open(image_path, 'rb') as f:
            image_data = f.read()
        
        # Upload with retry
        for attempt in range(3):
            try:
                response = supabase.storage.from_('product-images').upload(
                    path=storage_path,
                    file=image_data,
                    file_options={"content-type": "image/png", "upsert": "true"}
                )
                break
            except Exception as e:
                if attempt == 2:
                    raise e
                time.sleep(2)
        
        # Get public URL and update database
        public_url = supabase.storage.from_('product-images').get_public_url(storage_path)
        
        cursor.execute(
            "UPDATE uses_products SET image_url = %s WHERE id = %s",
            (public_url, product_id)
        )
        
        updated += 1
        print(f"✅ [{updated}] Updated {product_id}: {name[:30]}")
        
        # Commit every 10 updates
        if updated % 10 == 0:
            conn.commit()
            
    except Exception as e:
        failed += 1
        print(f"❌ Failed {product_id}: {str(e)}")
        continue

# Final commit
conn.commit()

print(f"\n📊 Migration Summary:")
print(f"✅ Successfully migrated: {updated}")
print(f"❌ Failed: {failed}")
print(f"⏭️  Skipped: {len(products_to_update) - updated - failed}")

cursor.close()
conn.close()