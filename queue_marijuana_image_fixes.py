#!/usr/bin/env python3
"""
Queue products with marijuana/smoking imagery for image regeneration
"""

import os
import sys
from datetime import datetime

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from hemp_image_prompt_templates import HempImagePromptTemplates

# Products found with marijuana/smoking imagery
products_to_fix = [
    {
        "id": 1697,
        "name": "Hemp Flower Smoking Blend",
        "description": "Pre-rolled herbal blends mixing hemp flowers with other smokable botanicals like damiana and mullein, offering a smooth, non-tobacco alternative for relaxation without non-non-non-non-non-psychoactive effects.",
        "industry_id": 18,  # Medical/Health
        "plant_part_id": 3,  # Flower
        "issue": "Contains 'smoking' in URL and name"
    },
    {
        "id": 1473,
        "name": "HempPlant Restore: Targeted Muscle & Joint Relief Roll-On",
        "description": "Combines hemp leaf extract with a precisely balanced blend of natural cooling agents and anti-inflammatory botanicals for a synergistic effect that surpasses the pain-relieving capabilities of either ingredient alone.",
        "industry_id": 18,
        "plant_part_id": 5,  # Leaf
        "issue": "May have inappropriate imagery"
    },
    {
        "id": 1618,
        "name": "Hemp Root & CBD Joint Pain Relief Patches – \"Targeted Relief\"",
        "description": "The incorporation of hemp root extract alongside CBD to enhance the analgesic effects of the CBD.",
        "industry_id": 18,
        "plant_part_id": 6,  # Root
        "issue": "Potential cannabis-related imagery"
    },
    {
        "id": 1450,
        "name": "HempRoot Muscle & Joint Relief Patch",
        "description": "Employs a novel micro-needle patch technology for enhanced and faster delivery of CBD and other active ingredients into the bloodstream.",
        "industry_id": 18,
        "plant_part_id": 6,
        "issue": "May contain inappropriate medical imagery"
    },
    {
        "id": 1561,
        "name": "HempRoot Muscle & Joint Relief Roll-On with Targeted CBD Delivery",
        "description": "A patented transdermal delivery system for CBD that increases absorption and reduces the need for high CBD concentrations.",
        "industry_id": 18,
        "plant_part_id": 6,
        "issue": "Potential cannabis imagery"
    },
    {
        "id": 1679,
        "name": "HempRoot Muscle & Joint Soothing Gel",
        "description": "A topical gel utilizing hemp root extract's natural anti-inflammatory and analgesic properties.",
        "industry_id": 18,
        "plant_part_id": 6,
        "issue": "May have cannabis-related imagery"
    },
    {
        "id": 1420,
        "name": "High-Potency CBD Oil 5000mg",
        "description": "Premium hemp-derived oil product extracted using sustainable methods.",
        "industry_id": 6,  # Medical/Health
        "plant_part_id": 1,  # Flower
        "issue": "High-potency CBD may have cannabis imagery"
    }
]

def get_plant_part_name(part_id):
    """Map plant part ID to name"""
    mapping = {
        1: "flower",
        3: "flower", 
        5: "leaf",
        6: "root"
    }
    return mapping.get(part_id, "hemp")

def get_industry_name(industry_id):
    """Map industry ID to category"""
    # All are medical/health products
    return "medical"

def generate_sql_inserts():
    """Generate SQL INSERT statements for the image generation queue"""
    
    templates = HempImagePromptTemplates()
    sql_statements = []
    
    print("Generating image regeneration queue for products with marijuana/smoking imagery...")
    print("=" * 80)
    
    for product in products_to_fix:
        # Determine product type
        product_type = templates.get_product_type_from_name(
            product["name"], 
            product["description"]
        )
        
        # Generate appropriate prompt
        industry = get_industry_name(product["industry_id"])
        plant_part = get_plant_part_name(product["plant_part_id"])
        
        prompt = templates.get_prompt(
            industry=industry,
            product_type=product_type,
            product_name=product["name"],
            additional_details=f"professional medical product, no smoking imagery, clinical presentation"
        )
        
        # Create SQL insert statement
        sql = f"""
-- Product: {product['name']} (ID: {product['id']})
-- Issue: {product['issue']}
INSERT INTO image_generation_queue (
    product_id,
    product_name,
    prompt,
    priority,
    status,
    requested_by,
    metadata
) VALUES (
    {product['id']},
    '{product['name'].replace("'", "''")}',
    '{prompt.replace("'", "''")}',
    9,
    'pending',
    'fix_marijuana_images',
    '{{"issue": "{product['issue']}", "previous_imagery": "marijuana/smoking related"}}'::jsonb
) ON CONFLICT (product_id) 
WHERE status = 'pending' 
DO UPDATE SET 
    prompt = EXCLUDED.prompt,
    priority = EXCLUDED.priority,
    updated_at = NOW();
"""
        sql_statements.append(sql)
        
        print(f"\nProduct: {product['name']}")
        print(f"Issue: {product['issue']}")
        print(f"New Prompt Preview: {prompt[:100]}...")
    
    return sql_statements

def main():
    """Generate and save SQL statements"""
    sql_statements = generate_sql_inserts()
    
    # Save to file
    output_file = "fix_marijuana_images.sql"
    with open(output_file, 'w') as f:
        f.write("-- SQL statements to queue products with marijuana/smoking imagery for regeneration\n")
        f.write(f"-- Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write("-- Total products: 7\n\n")
        f.write("BEGIN;\n\n")
        f.write("\n".join(sql_statements))
        f.write("\nCOMMIT;\n")
    
    print(f"\n{'=' * 80}")
    print(f"✅ Generated SQL file: {output_file}")
    print(f"📋 Total products to fix: {len(products_to_fix)}")
    print("\nNext steps:")
    print("1. Review the generated SQL statements")
    print("2. Execute the SQL to queue these products for image regeneration")
    print("3. Run your image generation process to create appropriate images")
    print("\nAll products will receive medical/clinical style prompts without any smoking or marijuana imagery.")

if __name__ == "__main__":
    main()