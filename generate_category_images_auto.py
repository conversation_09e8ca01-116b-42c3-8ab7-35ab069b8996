#!/usr/bin/env python3
"""
Auto-generate 5 test category images
Cost: ~$0.01
"""

import os
import json
import requests
import time

REPLICATE_API_TOKEN = os.getenv('REPLICATE_API_TOKEN')

# Test with just 5 images first
TEST_QUEUE = [
    {
        "category": "food_beverages",
        "subcategory": "hemp_seeds",
        "prompt": "Professional product photography of organic hemp seeds in a white ceramic bowl on white background, studio lighting, high quality, commercial style, no text",
        "filename": "hemp_food_hemp_seeds.jpg"
    },
    {
        "category": "health_wellness",
        "subcategory": "cbd_oil",
        "prompt": "Premium amber glass CBD oil bottle with black dropper cap, minimalist white background, professional product photography, wellness theme",
        "filename": "hemp_health_cbd_oil.jpg"
    },
    {
        "category": "textiles_fashion",
        "subcategory": "hemp_fabric",
        "prompt": "Natural beige hemp fabric textile roll, visible fiber texture, sustainable fashion, white background, commercial photography",
        "filename": "hemp_textiles_fabric.jpg"
    },
    {
        "category": "building_construction",
        "subcategory": "hempcrete",
        "prompt": "Stack of light gray hempcrete building blocks, construction materials, industrial product photography, white background",
        "filename": "hemp_building_hempcrete.jpg"
    },
    {
        "category": "cosmetics_personal",
        "subcategory": "hemp_skincare",
        "prompt": "Elegant white cosmetic jar with hemp leaf logo, premium skincare product, spa aesthetic, white background",
        "filename": "hemp_cosmetics_skincare.jpg"
    }
]

def generate_image(prompt):
    """Generate using Replicate API"""
    url = "https://api.replicate.com/v1/predictions"
    
    headers = {
        "Authorization": f"Token {REPLICATE_API_TOKEN}",
        "Content-Type": "application/json"
    }
    
    # Using SDXL for quality and cost efficiency
    data = {
        "version": "39ed52f2a78e934b3ba6e2a89f5b1c712de7dfea535525255b1aa35c5565e08b",
        "input": {
            "prompt": prompt,
            "negative_prompt": "text, watermark, logo, low quality, blurry",
            "width": 1024,
            "height": 1024,
            "num_outputs": 1,
            "num_inference_steps": 25,
            "guidance_scale": 7.5
        }
    }
    
    # Create prediction
    response = requests.post(url, headers=headers, json=data)
    if response.status_code != 201:
        return None
        
    prediction = response.json()
    
    # Poll for result
    max_attempts = 30
    for _ in range(max_attempts):
        time.sleep(2)
        status_response = requests.get(
            f"https://api.replicate.com/v1/predictions/{prediction['id']}",
            headers=headers
        )
        
        if status_response.status_code == 200:
            result = status_response.json()
            if result['status'] == 'succeeded':
                return result['output'][0]
            elif result['status'] == 'failed':
                return None
    
    return None

def download_and_save(url, filename):
    """Download image"""
    response = requests.get(url)
    if response.status_code == 200:
        os.makedirs('generated_category_images', exist_ok=True)
        filepath = f'generated_category_images/{filename}'
        with open(filepath, 'wb') as f:
            f.write(response.content)
        return filepath
    return None

def main():
    print("🌿 AUTO-GENERATING 5 TEST CATEGORY IMAGES")
    print("=" * 60)
    
    if not REPLICATE_API_TOKEN:
        print("❌ No Replicate token found")
        print("\n🆓 Try the free alternative instead:")
        print("python3 generate_category_images_free.py")
        return
    
    print("✅ Using Replicate API")
    print(f"💰 Estimated cost: ${len(TEST_QUEUE) * 0.0023:.4f}")
    print()
    
    results = []
    total_cost = 0
    
    for i, item in enumerate(TEST_QUEUE, 1):
        print(f"[{i}/5] Generating: {item['subcategory']}...")
        
        # Generate
        url = generate_image(item['prompt'])
        
        if url:
            # Download
            filepath = download_and_save(url, item['filename'])
            if filepath:
                print(f"✅ Saved: {filepath}")
                results.append(item)
                total_cost += 0.0023
            else:
                print(f"❌ Download failed")
        else:
            print(f"❌ Generation failed")
        
        # Brief pause between requests
        if i < len(TEST_QUEUE):
            time.sleep(1)
    
    # Summary
    print("\n" + "=" * 60)
    print(f"✅ Generated: {len(results)}/5 images")
    print(f"💰 Total cost: ${total_cost:.4f}")
    
    if results:
        # Save metadata
        with open('generated_category_images/test_results.json', 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"\n📁 Images saved in: generated_category_images/")
        print("\n🚀 Next: Upload these to Supabase and test the mapping")
    else:
        print("\n❌ No images generated. Check your Replicate token.")

if __name__ == "__main__":
    main()