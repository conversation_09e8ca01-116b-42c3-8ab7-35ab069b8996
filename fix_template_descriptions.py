#!/usr/bin/env python3
"""
Fix template descriptions in the database with unique, contextual content
"""

import os
import psycopg2
from psycopg2.extras import RealDictCursor
from datetime import datetime
import json

# Database connection
DATABASE_URL = os.environ.get('DATABASE_URL')

def get_connection():
    return psycopg2.connect(DATABASE_URL, cursor_factory=RealDictCursor)

def generate_unique_description(product_name, category_id):
    """Generate a unique description based on product name and category"""
    
    # Extract key information from product name
    name_lower = product_name.lower()
    
    # Base descriptions by product type
    if 'oil' in name_lower:
        base = f"{product_name} is a premium hemp-derived oil product utilizing advanced extraction techniques. "
        if 'seed' in name_lower:
            base += "Cold-pressed from carefully selected hemp seeds, this oil retains maximum nutritional value including omega-3 and omega-6 fatty acids. "
        elif 'cbd' in name_lower:
            base += "Rich in cannabidiol (CBD), this therapeutic oil is extracted using state-of-the-art CO2 supercritical extraction for purity and potency. "
        else:
            base += "This versatile oil combines traditional hemp processing with modern refinement techniques. "
    
    elif 'fiber' in name_lower or 'fibre' in name_lower:
        base = f"{product_name} represents an innovative application of hemp's natural fiber strength. "
        base += "Processed using sustainable methods, these fibers offer exceptional durability and environmental benefits. "
    
    elif 'patch' in name_lower:
        base = f"{product_name} delivers targeted relief through advanced transdermal technology. "
        base += "The patch provides controlled release of active compounds over extended periods, ensuring consistent therapeutic benefits. "
    
    elif 'serum' in name_lower or 'cream' in name_lower or 'lotion' in name_lower:
        base = f"{product_name} combines hemp-derived compounds with premium skincare ingredients. "
        base += "Formulated for optimal skin absorption, this product nourishes and protects while delivering hemp's natural benefits. "
    
    elif 'vape' in name_lower or 'cartridge' in name_lower:
        base = f"{product_name} offers a modern delivery method for hemp-derived compounds. "
        base += "Precision-engineered for consistent vapor production and optimal bioavailability of active ingredients. "
    
    elif 'phytoremediation' in name_lower:
        base = f"{product_name} leverages hemp's remarkable ability to clean contaminated soils. "
        base += "These specialized hemp varieties can absorb heavy metals and toxins while improving soil structure for future cultivation. "
    
    else:
        # Generic but contextual description
        base = f"{product_name} showcases the versatility of industrial hemp in modern applications. "
        base += "Developed through extensive research, this product meets industry standards while promoting sustainability. "
    
    # Add technical details
    base += "Manufactured under strict quality control standards with third-party testing for purity and consistency."
    
    return base

def fix_json_template_descriptions():
    """Fix all products with JSON template descriptions"""
    conn = get_connection()
    cur = conn.cursor()
    
    try:
        # Get all products with JSON template descriptions
        cur.execute("""
            SELECT id, name, description, industry_sub_category_id
            FROM uses_products
            WHERE description LIKE '%{"purity":%'
            ORDER BY id
        """)
        
        products = cur.fetchall()
        print(f"Found {len(products)} products with JSON template descriptions")
        
        updated_count = 0
        for product in products:
            new_description = generate_unique_description(product['name'], product['industry_sub_category_id'])
            
            cur.execute("""
                UPDATE uses_products
                SET description = %s,
                    updated_at = %s
                WHERE id = %s
            """, (new_description, datetime.now(), product['id']))
            
            updated_count += 1
            if updated_count % 10 == 0:
                print(f"Updated {updated_count} products...")
        
        conn.commit()
        print(f"Successfully updated {updated_count} JSON template descriptions")
        
    except Exception as e:
        conn.rollback()
        print(f"Error: {e}")
    finally:
        cur.close()
        conn.close()

def fix_cultural_template_descriptions():
    """Fix cultural template descriptions with more unique content"""
    conn = get_connection()
    cur = conn.cursor()
    
    try:
        # Get sample of cultural template products
        cur.execute("""
            SELECT id, name, description
            FROM uses_products
            WHERE description LIKE '%inspired by % traditions from%'
            ORDER BY id
            LIMIT 10
        """)
        
        products = cur.fetchall()
        print(f"\nSample of cultural template products:")
        
        for product in products:
            # Extract cultural information from existing description
            desc = product['description']
            culture_start = desc.find('inspired by ') + 12
            culture_end = desc.find(' traditions')
            culture = desc[culture_start:culture_end] if culture_start > 12 else 'traditional'
            
            # Extract region
            region_start = desc.find('from ') + 5
            region_end = desc.find('. Known')
            region = desc[region_start:region_end] if region_start > 5 else 'various regions'
            
            # Generate unique description
            product_type = product['name'].split()[-1].lower()  # Get last word (Material, Cloth, Fabric, etc.)
            
            new_desc = f"{product['name']} represents centuries of {culture} craftsmanship in hemp processing. "
            new_desc += f"Originating from {region}, this {product_type} showcases unique regional techniques "
            new_desc += f"that have been refined over generations. The traditional methods used in creating this "
            new_desc += f"{product_type} result in distinctive properties including enhanced durability, "
            new_desc += f"natural breathability, and cultural authenticity. Modern applications preserve these "
            new_desc += f"time-honored techniques while meeting contemporary quality standards."
            
            cur.execute("""
                UPDATE uses_products
                SET description = %s,
                    updated_at = %s
                WHERE id = %s
            """, (new_desc, datetime.now(), product['id']))
        
        conn.commit()
        print(f"Updated {len(products)} cultural template descriptions (sample)")
        
    except Exception as e:
        conn.rollback()
        print(f"Error: {e}")
    finally:
        cur.close()
        conn.close()

if __name__ == "__main__":
    print("Starting template description fixes...")
    fix_json_template_descriptions()
    fix_cultural_template_descriptions()
    print("\nTemplate description fix complete!")