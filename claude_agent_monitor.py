#!/usr/bin/env python3
"""
Claude Agent Activity Monitor
Tracks usage and results of Claude agents
"""

import os
import json
import time
from datetime import datetime
import glob

AGENTS_DIR = ".claude/agents"
AGENT_LOG_FILE = "logs/claude_agents.log"

def get_available_agents():
    """List all available Claude agents"""
    agents = []
    if os.path.exists(AGENTS_DIR):
        for file in glob.glob(f"{AGENTS_DIR}/*.md"):
            agent_name = os.path.basename(file).replace('.md', '')
            # Read agent metadata
            with open(file, 'r') as f:
                content = f.read()
                lines = content.split('\n')
                description = ""
                color = ""
                for line in lines:
                    if line.startswith('description:'):
                        description = line.replace('description:', '').strip()[:100] + "..."
                    elif line.startswith('color:'):
                        color = line.replace('color:', '').strip()
                
                agents.append({
                    'name': agent_name,
                    'description': description,
                    'color': color,
                    'file': file
                })
    return agents

def log_agent_activity(agent_name, action, details=""):
    """Log agent activity to file"""
    os.makedirs('logs', exist_ok=True)
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    log_entry = f"{timestamp} | {agent_name} | {action} | {details}\n"
    
    with open(AGENT_LOG_FILE, 'a') as f:
        f.write(log_entry)

def get_recent_activity():
    """Get recent agent activity from log"""
    if not os.path.exists(AGENT_LOG_FILE):
        return []
    
    with open(AGENT_LOG_FILE, 'r') as f:
        lines = f.readlines()
    
    return lines[-20:]  # Last 20 activities

def display_monitor():
    """Display agent monitoring dashboard"""
    os.system('clear' if os.name == 'posix' else 'cls')
    
    print("=" * 70)
    print("🤖 CLAUDE AGENT MONITOR".center(70))
    print("=" * 70)
    print(f"\n📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Available agents
    agents = get_available_agents()
    print(f"\n📋 AVAILABLE AGENTS ({len(agents)}):")
    print("-" * 70)
    for agent in agents:
        color_emoji = {
            'purple': '🟣',
            'blue': '🔵',
            'green': '🟢',
            'red': '🔴',
            'yellow': '🟡'
        }.get(agent['color'], '⚪')
        
        print(f"  {color_emoji} {agent['name']}")
        if agent['description']:
            print(f"     {agent['description']}")
    
    # Recent activity
    print(f"\n📜 RECENT AGENT ACTIVITY:")
    print("-" * 70)
    activities = get_recent_activity()
    if activities:
        for activity in activities:
            print(f"  {activity.strip()}")
    else:
        print("  No recent agent activity logged")
    
    # Instructions
    print(f"\n💡 HOW TO USE AGENTS:")
    print("-" * 70)
    print("  1. Agents are invoked using the Task tool in Claude")
    print("  2. Example: 'Use the database-quality-auditor agent to check for duplicates'")
    print("  3. Agents run on-demand, not as background processes")
    print("  4. Results are shown directly in the conversation")
    
    print(f"\n🛠️  AGENT MANAGEMENT:")
    print("-" * 70)
    print("  • Create new agent: Add .md file to .claude/agents/")
    print("  • View agent details: cat .claude/agents/<agent-name>.md")
    print("  • Log agent use: python claude_agent_monitor.py --log <agent> <action>")
    
    print("\n" + "=" * 70)

def main():
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == '--log':
        # Log agent activity
        if len(sys.argv) >= 4:
            agent_name = sys.argv[2]
            action = sys.argv[3]
            details = sys.argv[4] if len(sys.argv) > 4 else ""
            log_agent_activity(agent_name, action, details)
            print(f"✅ Logged: {agent_name} - {action}")
        else:
            print("Usage: python claude_agent_monitor.py --log <agent> <action> [details]")
    else:
        # Display monitor
        display_monitor()

if __name__ == "__main__":
    main()