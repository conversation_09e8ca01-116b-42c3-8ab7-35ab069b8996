-- Fix Short Product Descriptions - Batch 3
-- Updating products 21-35 (15 products)

BEGIN;

-- Update: Paper Hemp-Tech (ID: 8667)
-- Current: "Technical paper product" (23 chars)
UPDATE uses_products
SET 
    description = 'Advanced technical hemp paper product engineered from hemp leaves fibers. Superior strength and durability compared to traditional wood pulp papers. Acid-free composition ensures longevity while rapid renewability supports sustainable forestry alternatives. Perfect for specialty papers and technical documentation.',
    updated_at = CURRENT_TIMESTAMP
WHERE id = 8667;

-- Update: Fuel Hemp-Tech (ID: 6473)
-- Current: "Technical fuel" (14 chars)
UPDATE uses_products
SET 
    description = 'Innovative hemp biofuel product derived from hemp leaves for renewable energy generation. High energy density and clean combustion properties support carbon-neutral fuel alternatives. Sustainable cultivation ensures consistent supply without competing with food crops. Advancing the transition to renewable energy.',
    updated_at = CURRENT_TIMESTAMP
WHERE id = 6473;

-- Update: Hemp Brick-Premium (ID: 6463)
-- Current: "Premium brick material" (22 chars)
UPDATE uses_products
SET 
    description = 'Premium hemp brick construction material combining strength with sustainability. Manufactured from compressed hemp leaves, offering superior insulation and moisture regulation. Carbon-negative building solution supporting green construction standards. Ideal for eco-conscious architectural projects requiring durability and environmental responsibility.',
    updated_at = CURRENT_TIMESTAMP
WHERE id = 6463;

-- Update: Hemp Fiber-Superior (ID: 6453)
-- Current: "Superior fiber quality" (22 chars)
UPDATE uses_products
SET 
    description = 'Premium-grade hemp fiber showcasing exceptional quality and consistency. Carefully processed hemp leaves deliver outstanding tensile strength and natural breathability. The fiber''s inherent antimicrobial properties and sustainability credentials make it perfect for luxury textiles and eco-conscious fashion brands.',
    updated_at = CURRENT_TIMESTAMP
WHERE id = 6453;

-- Update: Filter Hemp-Superior (ID: 6458)
-- Current: "Superior filtration" (19 chars)
UPDATE uses_products
SET 
    description = 'Advanced hemp filtration product engineered from hemp leaves for superior purification performance. Natural porosity and adsorption properties effectively remove contaminants while maintaining flow rates. Biodegradable alternative to synthetic filters reduces environmental impact. Applications range from water treatment to air purification.',
    updated_at = CURRENT_TIMESTAMP
WHERE id = 6458;

-- Update: Nano HempBrick (ID: 6462)
-- Current: "Nano brick technology" (21 chars)
UPDATE uses_products
SET 
    description = 'Revolutionary nano-enhanced hemp brick technology utilizing advanced hemp leaves processing. Combines traditional building material strength with cutting-edge nanotechnology for superior performance. Enhanced thermal properties and structural integrity support next-generation sustainable construction. Leading innovation in green building materials.',
    updated_at = CURRENT_TIMESTAMP
WHERE id = 6462;

-- Update: Hemp Automotive-Tech (ID: 8652)
-- Current: "Automotive technology" (21 chars)
UPDATE uses_products
SET 
    description = 'High-tech hemp automotive component engineered from advanced hemp leaves composites. Delivers exceptional strength-to-weight ratio crucial for vehicle efficiency. Natural vibration dampening and impact resistance enhance passenger comfort and safety. Meeting stringent automotive industry standards for next-generation vehicles.',
    updated_at = CURRENT_TIMESTAMP
WHERE id = 8652;

-- Update: Primary Hemp-Automotive (ID: 6488)
-- Current: "Primary automotive component" (28 chars)
UPDATE uses_products
SET 
    description = 'Essential hemp-based automotive component manufactured from hemp leaves for modern vehicle applications. Combines lightweight properties with durability for improved fuel efficiency. Natural sound absorption and thermal insulation enhance cabin comfort. Suitable for interior panels, composites, and insulation systems.',
    updated_at = CURRENT_TIMESTAMP
WHERE id = 6488;

-- Update: Premium Hemp-Automotive (ID: 6465)
-- Current: "Premium automotive parts" (24 chars)
UPDATE uses_products
SET 
    description = 'Premium hemp automotive solution featuring superior quality hemp leaves materials. Advanced processing ensures exceptional performance in demanding vehicle applications. Sustainable alternative to synthetic components without compromising safety or durability. Perfect for luxury and performance vehicle manufacturers.',
    updated_at = CURRENT_TIMESTAMP
WHERE id = 6465;

-- Update: Eco Hemp-Automotive (ID: 8656)
-- Current: "Eco automotive solutions" (24 chars)
UPDATE uses_products
SET 
    description = 'Eco-friendly hemp automotive solution utilizing sustainable hemp leaves materials. Reduces vehicle carbon footprint while maintaining performance standards. Biodegradable components support end-of-life vehicle recycling. Perfect for manufacturers committed to sustainable mobility solutions.',
    updated_at = CURRENT_TIMESTAMP
WHERE id = 8656;

-- Update: Paper Hemp-Eco (ID: 6452)
-- Current: "Eco-friendly paper" (18 chars)
UPDATE uses_products
SET 
    description = 'Eco-conscious hemp paper solution manufactured from sustainably harvested hemp leaves. Tree-free alternative reduces deforestation while delivering quality comparable to premium papers. Chlorine-free processing minimizes environmental impact. Ideal for environmentally responsible printing and packaging needs.',
    updated_at = CURRENT_TIMESTAMP
WHERE id = 6452;

-- Update: Hemp Panel (ID: 8651)
-- Current: "Panel material" (14 chars)
UPDATE uses_products
SET 
    description = 'Durable hemp panel product manufactured from compressed hemp leaves fibers. Combines structural integrity with environmental benefits including carbon sequestration and renewable sourcing. Versatile building material suitable for interior and exterior applications. Supporting sustainable construction practices.',
    updated_at = CURRENT_TIMESTAMP
WHERE id = 8651;

-- Update: Hemp Nano-Tech (ID: 6459)
-- Current: "Nano technology" (15 chars)
UPDATE uses_products
SET 
    description = 'Cutting-edge hemp nanomaterial technology derived from hemp leaves for advanced applications. Proprietary processing creates high-performance graphene and nanocomposites with exceptional properties. Superior conductivity, strength, and sustainability compared to traditional nanomaterials. Enabling next-generation technology solutions.',
    updated_at = CURRENT_TIMESTAMP
WHERE id = 6459;

-- Update: Vehicle Hemp-Superior (ID: 6455)
-- Current: "Superior vehicle parts" (22 chars)
UPDATE uses_products
SET 
    description = 'Superior hemp vehicle component engineered from premium hemp leaves materials. Advanced manufacturing techniques deliver exceptional durability and performance. Lightweight construction improves fuel efficiency while maintaining safety standards. Perfect for automotive manufacturers seeking sustainable material solutions.',
    updated_at = CURRENT_TIMESTAMP
WHERE id = 6455;

-- Update: Energy Hemp-Superior (ID: 8658)
-- Current: "Superior energy solutions" (25 chars)
UPDATE uses_products
SET 
    description = 'Advanced hemp energy solution harnessing hemp leaves for sustainable power applications. Breakthrough technology enables efficient energy conversion and storage. Environmentally responsible alternative supporting renewable energy infrastructure. Suitable for various green energy implementations.',
    updated_at = CURRENT_TIMESTAMP
WHERE id = 8658;


-- Verify batch 3 updates
SELECT 
    id,
    name,
    LENGTH(description) as new_length,
    SUBSTRING(description, 1, 80) || '...' as description_preview
FROM uses_products
WHERE id IN (8667,6473,6463,6453,6458,6462,8652,6488,6465,8656,6452,8651,6459,6455,8658)
ORDER BY id;

-- Check overall progress
SELECT 
    'Batch 3 Complete' as status,
    COUNT(*) as total_products,
    COUNT(CASE WHEN LENGTH(description) < 100 THEN 1 END) as remaining_short,
    COUNT(CASE WHEN LENGTH(description) >= 200 THEN 1 END) as good_descriptions,
    ROUND(100.0 * COUNT(CASE WHEN LENGTH(description) >= 200 THEN 1 END) / COUNT(*), 2) as percent_good
FROM uses_products
WHERE description IS NOT NULL;

COMMIT;