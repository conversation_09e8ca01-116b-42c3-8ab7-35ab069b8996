#!/bin/bash
# Setup database proxy for IPv6 to IPv4 translation
# This script creates a local proxy to work around WSL2 IPv6 issues

echo "🔧 Setting up Database Proxy for IPv6 Connectivity"
echo "================================================="

# Check if socat is installed
if ! command -v socat &> /dev/null; then
    echo "❌ socat is not installed. Installing..."
    sudo apt-get update
    sudo apt-get install -y socat
fi

# Create systemd service for the proxy
echo "Creating proxy service..."

# Use the API endpoint which has IPv4
API_HOST="ktoqznqmlnxrtvubewyz.supabase.co"
API_IPV4=$(getent ahostsv4 $API_HOST | head -1 | awk '{print $1}')

if [ -z "$API_IPV4" ]; then
    echo "❌ Could not resolve IPv4 address for $API_HOST"
    echo "   The API approach is the best option for now."
    exit 1
fi

echo "✅ Found IPv4 address: $API_IPV4"

# Create a simple proxy script
cat > ~/database_proxy.sh << 'EOF'
#!/bin/bash
# Database proxy using SSH tunnel

echo "Starting database proxy..."
echo "This will create a local PostgreSQL port that forwards to Supabase"
echo ""
echo "Options:"
echo "1. Use SSH tunnel (requires a server with IPv6)"
echo "2. Use API-based agents (recommended)"
echo "3. Use ngrok (requires ngrok account)"
echo ""
echo "For now, please use the API-based agents:"
echo "  python src/agents/specialized/patent_mining_agent_api.py"
echo "  python fix_all_templates_api.py"
EOF

chmod +x ~/database_proxy.sh

echo ""
echo "✅ Setup complete!"
echo ""
echo "Since direct IPv6 connectivity isn't available in WSL2, we recommend:"
echo ""
echo "1. Use the API-based agents (RECOMMENDED):"
echo "   - patent_mining_agent_api.py"
echo "   - fix_all_templates_api.py"
echo ""
echo "2. Set up an SSH tunnel from a VPS with IPv6:"
echo "   ssh -L 5432:db.ktoqznqmlnxrtvubewyz.supabase.co:5432 user@your-vps"
echo ""
echo "3. Use Supabase CLI which handles connections properly:"
echo "   npx supabase db remote execute 'your query'"
echo ""
echo "The API approach is working and tested!"