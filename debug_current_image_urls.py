#!/usr/bin/env python3
"""Debug current image URLs in the database."""

import os
import subprocess
import json

# Read .env file
def load_env():
    if os.path.exists('.env'):
        with open('.env', 'r') as f:
            for line in f:
                if '=' in line and not line.strip().startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value.strip('"').strip("'")

load_env()

database_url = os.environ.get('DATABASE_URL')
if not database_url:
    print("ERROR: DATABASE_URL not found")
    exit(1)

# Check image URL patterns
query = """
SELECT 
    CASE 
        WHEN image_url LIKE '/generated_images/%' THEN 'Local Generated Images'
        WHEN image_url LIKE '/images/%' THEN 'Local Static Images'
        WHEN image_url LIKE '%supabase.co%' THEN 'Supabase Storage'
        WHEN image_url LIKE '%replicate.delivery%' THEN 'Replicate AI'
        WHEN image_url LIKE 'http%' THEN 'External URL'
        WHEN image_url IS NULL OR image_url = '' THEN 'No Image'
        ELSE 'Other'
    END as url_type,
    COUNT(*) as count,
    array_agg(DISTINCT substring(image_url from 1 for 100)) as samples
FROM uses_products
GROUP BY url_type
ORDER BY count DESC;
"""

result = subprocess.run(
    ['psql', database_url, '-t', '-A', '-F', '|', '-c', query],
    capture_output=True,
    text=True
)

if result.returncode == 0:
    print("CURRENT IMAGE URL DISTRIBUTION:")
    print("=" * 80)
    
    lines = result.stdout.strip().split('\n')
    total = 0
    
    for line in lines:
        if line:
            parts = line.split('|')
            if len(parts) >= 3:
                url_type = parts[0]
                count = int(parts[1])
                samples = parts[2]
                total += count
                
                print(f"\n{url_type}: {count} products")
                if samples and samples != '{}':
                    # Parse PostgreSQL array format
                    sample_list = samples.strip('{}').split(',')
                    print("  Sample URLs:")
                    for i, sample in enumerate(sample_list[:3]):
                        print(f"    - {sample.strip('\"')}")
    
    print(f"\n{'='*80}")
    print(f"TOTAL PRODUCTS: {total}")
    
    # Check specific problematic URLs
    print("\n\nCHECKING SPECIFIC PATTERNS:")
    print("-" * 80)
    
    specific_checks = [
        ("Products with /generated_images/ URLs", "image_url LIKE '/generated_images/%'"),
        ("Products with /images/ URLs", "image_url LIKE '/images/%'"),
        ("Products with Supabase URLs", "image_url LIKE '%supabase.co%'"),
        ("Products with NULL/empty URLs", "image_url IS NULL OR image_url = ''"),
    ]
    
    for desc, condition in specific_checks:
        check_query = f"SELECT COUNT(*) FROM uses_products WHERE {condition};"
        check_result = subprocess.run(
            ['psql', database_url, '-t', '-c', check_query],
            capture_output=True,
            text=True
        )
        if check_result.returncode == 0:
            count = int(check_result.stdout.strip())
            print(f"{desc}: {count}")

else:
    print(f"Error: {result.stderr}")

# Check if generated_images directory exists and has files
print("\n\nLOCAL FILE SYSTEM CHECK:")
print("-" * 80)
if os.path.exists('generated_images'):
    png_count = len([f for f in os.listdir('generated_images') if f.endswith('.png')])
    print(f"generated_images/ directory: {png_count} PNG files")
else:
    print("generated_images/ directory: NOT FOUND")

if os.path.exists('HempResourceHub/client/public/images'):
    img_count = len([f for f in os.listdir('HempResourceHub/client/public/images') if f.endswith(('.png', '.jpg', '.jpeg', '.webp'))])
    print(f"HempResourceHub/client/public/images/: {img_count} image files")
else:
    print("HempResourceHub/client/public/images/: NOT FOUND")