#!/usr/bin/env python3
"""
Direct image generator for products missing images
Bypasses the queue system and generates images directly
"""

import os
import psycopg2
from psycopg2.extras import RealDictCursor
import replicate
import time
from datetime import datetime

def generate_image_for_product(product):
    """Generate an image for a single product"""
    try:
        # Create prompt
        plant_part = product.get('plant_part_name', 'hemp')
        name = product['name']
        
        # Clean name for prompt
        clean_name = name.replace('™', '').replace('®', '').strip()
        
        prompt = f"Professional product photography of {clean_name}, made from {plant_part}, clean white background, studio lighting, high quality, commercial product shot"
        
        print(f"🎨 Generating image for: {name}")
        print(f"   Prompt: {prompt[:80]}...")
        
        # Generate image using Replicate
        output = replicate.run(
            "stability-ai/stable-diffusion:db21e45d3f7023abc2a46ee38a23973f6dce16bb082a930b0c49861f96d1e5bf",
            input={
                "prompt": prompt,
                "image_dimensions": "512x512",
                "num_outputs": 1,
                "guidance_scale": 7.5,
                "num_inference_steps": 25
            }
        )
        
        # Get the image URL
        if output and len(output) > 0:
            # Handle FileOutput type
            if hasattr(output[0], '__str__'):
                image_url = str(output[0])
            else:
                image_url = output[0]
            print(f"   ✅ Generated: {image_url}")
            return image_url
        else:
            print(f"   ❌ No output from Replicate")
            return None
            
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")
        return None

def main():
    print("🖼️  DIRECT IMAGE GENERATOR")
    print("=" * 60)
    
    # Connect to database
    conn = psycopg2.connect(os.environ['DATABASE_URL'])
    cur = conn.cursor(cursor_factory=RealDictCursor)
    
    # Get products without images
    print("\n📊 Finding products without images...")
    cur.execute("""
        SELECT 
            p.id,
            p.name,
            p.description,
            pp.name as plant_part_name
        FROM uses_products p
        JOIN plant_parts pp ON p.plant_part_id = pp.id
        WHERE p.image_url IS NULL 
        AND p.ai_generated_image_url IS NULL 
        AND p.original_image_url IS NULL
        AND p.name IS NOT NULL
        AND LENGTH(p.name) > 0
        ORDER BY p.created_at DESC
        LIMIT 20
    """)
    
    products = cur.fetchall()
    print(f"Found {len(products)} products needing images")
    
    if not products:
        print("✅ All products have images!")
        return
    
    # Generate images
    successful = 0
    failed = 0
    
    print(f"\n🚀 Generating images for {len(products)} products...")
    
    for i, product in enumerate(products, 1):
        print(f"\n[{i}/{len(products)}] {product['name']}")
        
        # Generate image
        image_url = generate_image_for_product(product)
        
        if image_url:
            # Update database
            try:
                cur.execute("""
                    UPDATE uses_products 
                    SET 
                        ai_generated_image_url = %s,
                        image_source = 'ai_generated',
                        updated_at = NOW()
                    WHERE id = %s
                """, (image_url, product['id']))
                
                conn.commit()
                successful += 1
                print(f"   ✅ Saved to database")
                
            except Exception as e:
                conn.rollback()
                print(f"   ❌ Database error: {e}")
                failed += 1
        else:
            failed += 1
        
        # Rate limiting
        if i < len(products):
            print("   ⏸️  Waiting 2 seconds...")
            time.sleep(2)
    
    # Final report
    print(f"\n📊 GENERATION COMPLETE")
    print(f"=" * 60)
    print(f"Successful: {successful}/{len(products)}")
    print(f"Failed: {failed}")
    print(f"Estimated cost: ${successful * 0.002:.2f}")
    
    # Check remaining
    cur.execute("""
        SELECT COUNT(*) 
        FROM uses_products 
        WHERE image_url IS NULL 
        AND ai_generated_image_url IS NULL 
        AND original_image_url IS NULL
    """)
    remaining = cur.fetchone()['count']
    print(f"\n📍 Remaining products without images: {remaining:,}")
    
    cur.close()
    conn.close()

if __name__ == "__main__":
    # Check environment
    if not os.environ.get('DATABASE_URL'):
        print("❌ DATABASE_URL not set")
        exit(1)
    if not os.environ.get('REPLICATE_API_TOKEN'):
        print("❌ REPLICATE_API_TOKEN not set")
        exit(1)
        
    main()