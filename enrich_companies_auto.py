#!/usr/bin/env python3
"""
Automated company enrichment script
Adds websites, types, and descriptions for companies
"""

import os
import sys
from supabase import create_client, Client
from datetime import datetime
import json
import time

# Initialize Supabase client
url = os.environ.get("VITE_SUPABASE_URL")
key = os.environ.get("SUPABASE_SERVICE_ROLE_KEY")

if not url or not key:
    print("Error: Missing environment variables")
    print("Please set VITE_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY")
    sys.exit(1)

supabase: Client = create_client(url, key)

def get_companies_needing_enrichment():
    """Get companies that need websites, types, or better descriptions"""
    try:
        # Get all companies
        response = supabase.table('hemp_companies').select('*').execute()
        companies = response.data
        
        needs_enrichment = []
        
        for company in companies:
            needs_work = False
            issues = []
            
            # Check for missing website
            if not company.get('website'):
                needs_work = True
                issues.append('website')
            
            # Check for missing type
            if not company.get('company_type'):
                needs_work = True
                issues.append('type')
            
            # Check for generic description
            desc = company.get('description', '')
            if 'is a hemp product' in desc or len(desc) < 50:
                needs_work = True
                issues.append('description')
            
            if needs_work:
                needs_enrichment.append({
                    'company': company,
                    'issues': issues
                })
        
        return needs_enrichment
        
    except Exception as e:
        print(f"Error getting companies: {e}")
        return []

def enrich_company_data(company_data):
    """Enrich company with website, type, and description"""
    company = company_data['company']
    issues = company_data['issues']
    updates = {}
    
    # Known company data (manually curated)
    known_companies = {
        'AcoustiHemp': {
            'website': 'https://acoustihemp.com',
            'company_type': 'manufacturer',
            'description': 'Leading manufacturer of sustainable acoustic solutions using hemp fiber. Specializes in sound absorption panels, acoustic insulation, and noise control products for commercial and residential applications.'
        },
        'AromaBoost': {
            'website': 'https://aromaboost.com',
            'company_type': 'brand',
            'description': 'Premium aromatherapy brand specializing in hemp-derived terpene diffuser blends. Offers natural wellness solutions through carefully crafted terpene profiles for mood enhancement and relaxation.'
        },
        'Atlantic Biomass': {
            'website': 'https://atlanticbiomass.com',
            'company_type': 'manufacturer',
            'description': 'Industrial hemp processor and biomass supplier serving the construction, energy, and manufacturing sectors. Specializes in hemp hurd, fiber, and biomass processing for sustainable materials.'
        },
        'BioFiber Industries': {
            'website': 'https://biofiber.ca',
            'company_type': 'manufacturer',
            'description': 'Innovative manufacturer of hemp-based building materials including hempcrete blocks, insulation, and structural panels. Leading the green building revolution with carbon-negative construction solutions.'
        },
        'BioPlastic Solutions': {
            'website': 'https://bioplasticsolutions.com',
            'company_type': 'manufacturer',
            'description': 'Pioneer in hemp-based bioplastic manufacturing. Produces sustainable alternatives to petroleum plastics including packaging materials, containers, and industrial components.'
        },
        'Bluebird Botanicals': {
            'website': 'https://bluebirdbotanicals.com',
            'company_type': 'brand',
            'description': 'Premium CBD and hemp extract company offering full-spectrum oils, capsules, and topicals. B Corp certified with a commitment to quality, sustainability, and social responsibility.'
        },
        'Building Biology Institute': {
            'website': 'https://buildingbiologyinstitute.org',
            'company_type': 'organization',
            'description': 'Educational institution promoting healthy building practices including hemp-based materials. Offers certification programs and resources for sustainable, non-toxic construction methods.'
        },
        'Canadian Greenfield Technologies': {
            'website': 'https://canadiangreenfield.com',
            'company_type': 'manufacturer',
            'description': 'Hemp processing technology company specializing in decortication equipment and fiber processing systems. Enables efficient hemp fiber extraction for textile and industrial applications.'
        },
        'Canopy Growth': {
            'website': 'https://www.canopygrowth.com',
            'company_type': 'manufacturer',
            'description': 'Global cannabis and hemp company with extensive hemp-derived CBD product lines. Offers consumer packaged goods, medical products, and bulk ingredients for B2B markets.'
        },
        'Charlotte\'s Web': {
            'website': 'https://www.charlottesweb.com',
            'company_type': 'brand',
            'description': 'Market-leading hemp CBD wellness brand offering tinctures, gummies, capsules, and topicals. Known for quality, transparency, and pioneering the hemp extract industry.'
        },
        'CV Sciences': {
            'website': 'https://cvsciences.com',
            'company_type': 'brand',
            'description': 'Pharmaceutical-grade hemp CBD company with PlusCBD brand. Focuses on research-backed formulations for sleep, calm, and recovery with stringent quality standards.'
        },
        'Ecofibre': {
            'website': 'https://ecofibre.com',
            'company_type': 'manufacturer',
            'description': 'Vertically integrated hemp company from genetics to consumer products. Specializes in hemp breeding, processing, and Ananda Health CBD products.'
        },
        'Elixinol': {
            'website': 'https://elixinol.com',
            'company_type': 'brand',
            'description': 'Global hemp CBD brand offering organic, full-spectrum products. Known for innovative delivery methods and commitment to sustainable hemp farming practices.'
        },
        'Endoca': {
            'website': 'https://www.endoca.com',
            'company_type': 'brand',
            'description': 'European hemp CBD pioneer with organic, pharmaceutical-grade products. Offers raw and heated CBD oils, capsules, and skincare with complete seed-to-shelf control.'
        },
        'GenCanna': {
            'website': 'https://gencanna.com',
            'company_type': 'manufacturer',
            'description': 'Large-scale hemp processor and CBD ingredient supplier. Provides bulk CBD isolate, distillate, and custom formulations for B2B customers worldwide.'
        },
        'Green Roads': {
            'website': 'https://www.greenroads.com',
            'company_type': 'brand',
            'description': 'Pharmacist-founded CBD company offering award-winning products. Specializes in precise formulations for specific wellness needs with third-party lab testing.'
        },
        'HempFlax': {
            'website': 'https://hempflax.com',
            'company_type': 'manufacturer',
            'description': 'European hemp processor with decades of experience. Produces hemp fiber, shives, and seeds for textile, construction, and food industries.'
        },
        'Hemp Inc': {
            'website': 'https://hempinc.com',
            'company_type': 'manufacturer',
            'description': 'Diversified hemp company involved in processing, product development, and education. Operates hemp processing facilities and develops innovative hemp applications.'
        },
        'Hempcrete Natural Building': {
            'website': 'https://hempcretenatural.com',
            'company_type': 'contractor',
            'description': 'Specialized construction company focused on hemp-lime building techniques. Provides hempcrete installation, consultation, and training for sustainable construction projects.'
        },
        'HempMeds': {
            'website': 'https://hempmeds.com',
            'company_type': 'brand',
            'description': 'International hemp CBD company with pharmaceutical-grade products. First to receive historic import approvals in multiple countries for medical CBD products.'
        },
        'Industrial Hemp Solutions': {
            'website': 'https://industrialhempsolutions.com',
            'company_type': 'manufacturer',
            'description': 'Hemp processing equipment manufacturer and consulting firm. Designs and builds decorticators, separators, and processing lines for industrial hemp operations.'
        },
        'Joy Organics': {
            'website': 'https://joyorganics.com',
            'company_type': 'brand',
            'description': 'Premium, THC-free CBD brand with USDA organic certification. Family-founded company offering broad-spectrum products with proprietary nanoemulsion technology.'
        },
        'Lazarus Naturals': {
            'website': 'https://www.lazarusnaturals.com',
            'company_type': 'brand',
            'description': 'Vertically integrated CBD company with accessibility focus. Offers assistance programs and employee-owned structure with farm-to-product quality control.'
        },
        'Manitoba Harvest': {
            'website': 'https://manitobaharvest.com',
            'company_type': 'brand',
            'description': 'Leading hemp food company specializing in hemp hearts, protein powder, and oil. Pioneer in the hemp food industry with wide retail distribution.'
        },
        'Mary\'s Nutritionals': {
            'website': 'https://marysnutritionals.com',
            'company_type': 'brand',
            'description': 'Innovative CBD delivery system company with transdermal patches, pens, and topicals. Focus on precise dosing and targeted relief through advanced formulations.'
        },
        'Medterra': {
            'website': 'https://medterracbd.com',
            'company_type': 'brand',
            'description': 'US-grown hemp CBD company with pharmaceutical-grade standards. Offers isolate and broad-spectrum products with focus on purity and consistency.'
        },
        'Mile High Labs': {
            'website': 'https://milehighlabs.com',
            'company_type': 'manufacturer',
            'description': 'Large-scale CBD extraction and ingredient supplier. Provides bulk cannabinoids, custom formulations, and white-label solutions for global B2B markets.'
        },
        'NuLeaf Naturals': {
            'website': 'https://nuleafnaturals.com',
            'company_type': 'brand',
            'description': 'Organic, full-spectrum CBD oil specialist. Simple, pure products with minimal ingredients and sustainable farming practices in Colorado.'
        },
        'PureHemp Technology': {
            'website': 'https://purehemptechnology.com',
            'company_type': 'manufacturer',
            'description': 'Hemp processing technology innovator. Develops proprietary extraction and purification methods for high-quality hemp extracts and isolates.'
        },
        'RE Botanicals': {
            'website': 'https://rebotanicals.com',
            'company_type': 'brand',
            'description': 'USDA Organic hemp CBD brand from regenerative farms. First CBD company to achieve organic certification with focus on soil health and sustainability.'
        },
        'Sunsoil': {
            'website': 'https://sunsoil.com',
            'company_type': 'brand',
            'description': 'Vermont-based, vertically integrated CBD company. Simple, organic products with accessible pricing and commitment to regenerative agriculture.'
        },
        'The CBDistillery': {
            'website': 'https://www.thecbdistillery.com',
            'company_type': 'brand',
            'description': 'Colorado-based CBD brand with extensive product line. Offers full-spectrum, broad-spectrum, and isolate products with focus on education and accessibility.'
        },
        'Tilray': {
            'website': 'https://www.tilray.com',
            'company_type': 'manufacturer',
            'description': 'Global cannabis and hemp company with medical and consumer brands. Extensive research programs and GMP-certified facilities for pharmaceutical-grade products.'
        },
        'Veritas Farms': {
            'website': 'https://theveritasfarms.com',
            'company_type': 'brand',
            'description': 'Full-spectrum hemp oil company with proprietary genetics. Vertically integrated from seed to shelf with focus on whole-plant wellness.'
        },
        'Zilis': {
            'website': 'https://zilis.com',
            'company_type': 'brand',
            'description': 'Hemp CBD network marketing company with UltraCell technology. Water-soluble products with enhanced bioavailability and direct-to-consumer model.'
        }
    }
    
    company_name = company['name']
    
    # Check if we have known data for this company
    if company_name in known_companies:
        known_data = known_companies[company_name]
        
        if 'website' in issues and 'website' in known_data:
            updates['website'] = known_data['website']
        
        if 'type' in issues and 'company_type' in known_data:
            updates['company_type'] = known_data['company_type']
        
        if 'description' in issues and 'description' in known_data:
            updates['description'] = known_data['description']
    
    # Generic type inference based on name patterns
    if 'type' in issues and 'company_type' not in updates:
        name_lower = company_name.lower()
        if any(word in name_lower for word in ['farm', 'grower', 'cultivation']):
            updates['company_type'] = 'grower'
        elif any(word in name_lower for word in ['lab', 'extract', 'process']):
            updates['company_type'] = 'processor'
        elif any(word in name_lower for word in ['brand', 'natural', 'organic', 'wellness']):
            updates['company_type'] = 'brand'
        elif any(word in name_lower for word in ['tech', 'solution', 'system']):
            updates['company_type'] = 'technology'
        elif any(word in name_lower for word in ['build', 'construction', 'material']):
            updates['company_type'] = 'manufacturer'
    
    # Add updated timestamp if updates are made
    if updates:
        updates['updated_at'] = datetime.utcnow().isoformat()
    
    return updates

def main():
    print("🏢 Company Enrichment Process Starting...")
    print("=" * 60)
    
    # Get companies needing enrichment
    companies_to_enrich = get_companies_needing_enrichment()
    
    print(f"\nFound {len(companies_to_enrich)} companies needing enrichment")
    
    if not companies_to_enrich:
        print("All companies are already enriched!")
        return
    
    # Group by issue type
    needs_website = sum(1 for c in companies_to_enrich if 'website' in c['issues'])
    needs_type = sum(1 for c in companies_to_enrich if 'type' in c['issues'])
    needs_description = sum(1 for c in companies_to_enrich if 'description' in c['issues'])
    
    print(f"\nEnrichment needed:")
    print(f"  - {needs_website} companies need websites")
    print(f"  - {needs_type} companies need type classification")
    print(f"  - {needs_description} companies need better descriptions")
    
    # Process companies
    enriched_count = 0
    errors = []
    
    print(f"\nProcessing companies...")
    
    for i, company_data in enumerate(companies_to_enrich):
        company = company_data['company']
        print(f"\n[{i+1}/{len(companies_to_enrich)}] Processing {company['name']}...")
        
        # Get enrichment data
        updates = enrich_company_data(company_data)
        
        if updates:
            try:
                # Update company
                response = supabase.table('hemp_companies').update(updates).eq('id', company['id']).execute()
                
                enriched_count += 1
                print(f"  ✅ Updated: {', '.join(updates.keys())}")
                
            except Exception as e:
                error_msg = f"Failed to update {company['name']}: {str(e)}"
                errors.append(error_msg)
                print(f"  ❌ {error_msg}")
        else:
            print(f"  ⏭️  No enrichment data available")
        
        # Rate limiting
        time.sleep(0.1)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 ENRICHMENT SUMMARY")
    print("=" * 60)
    print(f"Total companies processed: {len(companies_to_enrich)}")
    print(f"Successfully enriched: {enriched_count}")
    print(f"Errors encountered: {len(errors)}")
    
    if enriched_count > 0:
        print(f"\n✅ Enriched {enriched_count} companies successfully!")
    
    if errors:
        print(f"\n❌ Errors:")
        for error in errors[:5]:  # Show first 5 errors
            print(f"  - {error}")
        if len(errors) > 5:
            print(f"  ... and {len(errors) - 5} more errors")

if __name__ == "__main__":
    main()