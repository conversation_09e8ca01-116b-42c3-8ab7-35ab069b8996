#!/bin/bash

echo "=== Configuring Supabase MCP for Write Access ==="
echo ""
echo "Current Supabase MCP configuration:"
claude mcp list | grep supabase
echo ""

echo "To enable write access, you need to:"
echo ""
echo "1. Remove the current read-only configuration:"
echo "   claude mcp remove supabase"
echo ""
echo "2. Add Supabase MCP with write access:"
echo "   claude mcp add @supabase/mcp-server-supabase@latest \\"
echo "     --project-ref ktoqznqmlnxrtvubewyz \\"
echo "     --service-role-key \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt0b3F6bnFtbG54cnR2dWJld3l6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODQ5MTc3NiwiZXhwIjoyMDY0MDY3Nzc2fQ.hxEiOy6xebv7I4CYJEU49oCEXRvmU6iz2l8Kg-NnvTs\""
echo ""
echo "Alternative command (without read-only flag):"
echo "   claude mcp add @supabase/mcp-server-supabase@latest \\"
echo "     --args=\"--project-ref ktoqznqmlnxrtvubewyz\" \\"
echo "     --env=\"SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt0b3F6bnFtbG54cnR2dWJld3l6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODQ5MTc3NiwiZXhwIjoyMDY0MDY3Nzc2fQ.hxEiOy6xebv7I4CYJEU49oCEXRvmU6iz2l8Kg-NnvTs\""
echo ""
echo "After reconfiguring, restart Claude Code for changes to take effect."