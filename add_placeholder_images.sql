-- Add placeholder images for all products without images
-- These can be replaced with actual AI-generated images later

-- Hemp Fiber Processing Equipment
UPDATE uses_products 
SET 
  ai_generated_image_url = 'https://placehold.co/600x400/2E7D32/FFFFFF?text=Hemp+Fiber+Equipment',
  image_source = 'ai_generated',
  image_type = 'placeholder',
  updated_at = NOW()
WHERE id IN (3037, 3038, 3039, 3040, 3041, 3042)
  AND image_url IS NULL 
  AND ai_generated_image_url IS NULL;

-- Cultural/Regional Products
UPDATE uses_products 
SET 
  ai_generated_image_url = 'https://placehold.co/600x400/4A5D23/FFFFFF?text=' || REPLACE(name, ' ', '+'),
  image_source = 'ai_generated', 
  image_type = 'placeholder',
  updated_at = NOW()
WHERE source_agent = 'Regional/Cultural Agent'
  AND image_url IS NULL 
  AND ai_generated_image_url IS NULL;

-- Check results
SELECT COUNT(*) as updated_count
FROM uses_products
WHERE image_type = 'placeholder';