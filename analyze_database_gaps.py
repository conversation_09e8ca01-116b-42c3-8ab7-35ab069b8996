#!/usr/bin/env python3
"""
Comprehensive Database Gap Analysis for Hemp Products
Identifies missing categories and opportunities for expansion
"""

import os
import psycopg2
from psycopg2.extras import RealDictCursor
from collections import defaultdict
import json

# Database connection
DATABASE_URL = os.environ.get('DATABASE_URL')

def analyze_current_coverage():
    """Analyze current database coverage and identify gaps"""
    conn = psycopg2.connect(DATABASE_URL)
    cur = conn.cursor(cursor_factory=RealDictCursor)
    
    # 1. Product distribution by plant part
    cur.execute("""
        SELECT 
            pp.name as plant_part,
            COUNT(DISTINCT up.id) as product_count,
            COUNT(DISTINCT up.industry_sub_category_id) as industry_coverage
        FROM plant_parts pp
        LEFT JOIN uses_products up ON pp.id = up.plant_part_id
        GROUP BY pp.id, pp.name
        ORDER BY product_count DESC
    """)
    plant_part_coverage = cur.fetchall()
    
    # 2. Industry coverage analysis
    cur.execute("""
        SELECT 
            i.name as industry,
            COUNT(DISTINCT isc.id) as sub_categories,
            COUNT(DISTINCT up.id) as total_products
        FROM industries i
        LEFT JOIN industry_sub_categories isc ON i.id = isc.industry_id
        LEFT JOIN uses_products up ON isc.id = up.industry_sub_category_id
        GROUP BY i.id, i.name
        ORDER BY total_products DESC
    """)
    industry_coverage = cur.fetchall()
    
    # 3. Product name analysis for patterns
    cur.execute("SELECT name, description FROM uses_products")
    products = cur.fetchall()
    
    # Analyze product patterns
    product_types = defaultdict(int)
    application_areas = defaultdict(int)
    
    # Keywords that indicate specific categories
    type_keywords = [
        'composite', 'fiber', 'oil', 'extract', 'powder', 'material',
        'fabric', 'paper', 'plastic', 'resin', 'board', 'panel',
        'concrete', 'insulation', 'fuel', 'pellet', 'meal', 'protein',
        'cosmetic', 'cream', 'lotion', 'supplement', 'capsule'
    ]
    
    application_keywords = [
        'automotive', 'construction', 'textile', 'food', 'medical',
        'pharmaceutical', 'cosmetic', 'industrial', 'packaging',
        'agriculture', 'energy', 'electronics', 'aerospace', 'marine',
        'sports', 'furniture', 'paper', 'chemical', 'environmental'
    ]
    
    for product in products:
        name_lower = product['name'].lower()
        desc_lower = (product['description'] or '').lower()
        combined = name_lower + ' ' + desc_lower
        
        for keyword in type_keywords:
            if keyword in combined:
                product_types[keyword] += 1
        
        for keyword in application_keywords:
            if keyword in combined:
                application_areas[keyword] += 1
    
    # 4. Identify gaps based on research
    identified_gaps = {
        "Nanotechnology": [
            "Cellulose nanocrystals",
            "Graphene-like nanosheets", 
            "Quantum dots",
            "Nanofibers for electronics",
            "Nano-enhanced composites"
        ],
        "Electronics": [
            "Supercapacitors",
            "Flexible batteries",
            "Conductive textiles",
            "Semiconductors",
            "Display substrates",
            "Printed circuit boards"
        ],
        "Medical/Pharmaceutical": [
            "Drug delivery systems",
            "Surgical implants",
            "Wound dressings",
            "Bone scaffolds",
            "Dental materials"
        ],
        "Advanced Materials": [
            "3D printing filaments",
            "Smart textiles",
            "Shape-memory materials",
            "Piezoelectric materials",
            "Photovoltaic materials"
        ],
        "Traditional/Cultural": [
            "Religious ceremonial items",
            "Traditional medicines (by region)",
            "Cultural textiles",
            "Historical replicas",
            "Indigenous tools"
        ],
        "Specialized Industrial": [
            "Aerospace composites",
            "Marine applications",
            "Mining equipment",
            "Filtration systems",
            "Catalysts"
        ]
    }
    
    # 5. Regional specific products missing
    regional_gaps = {
        "Asian Traditional": [
            "Japanese temple ropes",
            "Chinese traditional medicine preparations",
            "Korean hanbok fabric",
            "Indian bhang preparations"
        ],
        "European Heritage": [
            "Viking sail cloth replicas",
            "Medieval armor padding",
            "Traditional ship rigging"
        ],
        "Modern Regional": [
            "EU-specific building standards materials",
            "North American automotive parts",
            "Australian agricultural applications"
        ]
    }
    
    conn.close()
    
    return {
        "current_coverage": {
            "total_products": len(products),
            "plant_parts": plant_part_coverage,
            "industries": industry_coverage,
            "product_types": dict(sorted(product_types.items(), key=lambda x: x[1], reverse=True)[:20]),
            "applications": dict(sorted(application_areas.items(), key=lambda x: x[1], reverse=True)[:20])
        },
        "identified_gaps": identified_gaps,
        "regional_gaps": regional_gaps,
        "expansion_potential": {
            "nanotechnology": 500,
            "electronics": 300, 
            "medical": 800,
            "traditional": 1000,
            "regional_specific": 2000,
            "patents_based": 3000,
            "emerging_tech": 1500,
            "industrial_specialized": 2000
        }
    }

if __name__ == "__main__":
    analysis = analyze_current_coverage()
    print(json.dumps(analysis, indent=2))
    
    # Calculate total expansion potential
    total_potential = sum(analysis['expansion_potential'].values())
    current_total = analysis['current_coverage']['total_products']
    
    print(f"\n=== EXPANSION SUMMARY ===")
    print(f"Current Products: {current_total}")
    print(f"Expansion Potential: {total_potential:,} products")
    print(f"Total Possible: {current_total + total_potential:,} products")