#!/usr/bin/env python3
"""
Comprehensive plan to fix ALL image issues in the hemp database
Total products needing attention: 5,379 out of 6,157 (87.36%)
"""

import os
from datetime import datetime

# Categories of products needing image fixes
IMAGE_FIX_CATEGORIES = {
    "missing_images": {
        "count": 5072,
        "percentage": 82.38,
        "priority": "CRITICAL",
        "solution": "Generate AI images with industry-specific prompts",
        "estimated_cost": "$2,536 (at $0.50 per image)"
    },
    "unknown_quality": {
        "count": 287,
        "percentage": 4.66,
        "priority": "HIGH",
        "solution": "Review and replace non-hemp related images",
        "action": "Manual review + AI generation"
    },
    "placeholder_images": {
        "count": 14,
        "percentage": 0.23,
        "priority": "HIGH",
        "solution": "Already queued for regeneration",
        "status": "IN PROGRESS"
    },
    "generic_stock": {
        "count": 4,
        "percentage": 0.06,
        "priority": "MEDIUM",
        "solution": "Replace with hemp-specific images"
    },
    "inappropriate_content": {
        "count": 2,
        "percentage": 0.03,
        "priority": "HIGH",
        "solution": "Replace marijuana/face images",
        "status": "7 already queued"
    }
}

# SQL to identify ALL products needing image fixes
COMPREHENSIVE_FIX_SQL = """
-- Get ALL products that need image fixes (5,379 total)
WITH products_needing_fixes AS (
    SELECT 
        id,
        name,
        description,
        image_url,
        ai_generated_image_url,
        industry_sub_category_id,
        plant_part_id,
        CASE 
            WHEN image_url IS NULL OR image_url = '' THEN 'missing'
            WHEN image_url LIKE '%placeholder%' OR image_url LIKE '%unknown%' 
                OR image_url LIKE '%fallback%' OR image_url LIKE '%default%' THEN 'placeholder'
            WHEN image_url LIKE '%unsplash%' AND image_url NOT LIKE '%hemp%' THEN 'generic_stock'
            WHEN image_url LIKE '%face%' OR image_url LIKE '%portrait%' THEN 'face_portrait'
            WHEN image_url LIKE '%marijuana%' OR image_url LIKE '%smoking%' THEN 'marijuana'
            WHEN image_url NOT LIKE '%hemp%' AND image_url NOT LIKE '%cbd%' 
                AND image_url NOT LIKE '%product%' AND image_url NOT LIKE '%/generated/%' THEN 'unknown_quality'
            ELSE 'review_needed'
        END as issue_type
    FROM uses_products
    WHERE (
        image_url IS NULL 
        OR image_url = ''
        OR image_url LIKE '%placeholder%'
        OR image_url LIKE '%unknown%'
        OR image_url LIKE '%fallback%'
        OR image_url LIKE '%default%'
        OR (image_url LIKE '%unsplash%' AND image_url NOT LIKE '%hemp%')
        OR image_url LIKE '%face%'
        OR image_url LIKE '%portrait%'
        OR image_url LIKE '%marijuana%'
        OR image_url LIKE '%smoking%'
        OR (
            image_url NOT LIKE '%hemp%' 
            AND image_url NOT LIKE '%cbd%'
            AND image_url NOT LIKE '%product%'
            AND image_url NOT LIKE '%/generated/%'
            AND image_url NOT LIKE '%replicate%'
        )
    )
)
SELECT * FROM products_needing_fixes
ORDER BY 
    CASE issue_type
        WHEN 'missing' THEN 1
        WHEN 'placeholder' THEN 2
        WHEN 'marijuana' THEN 3
        WHEN 'face_portrait' THEN 4
        WHEN 'unknown_quality' THEN 5
        ELSE 6
    END,
    name;
"""

# Batch processing strategy
BATCH_STRATEGY = {
    "batch_size": 100,  # Process 100 products at a time
    "total_batches": 54,  # 5,379 / 100
    "processing_order": [
        "placeholder_images",     # 14 products (already done)
        "inappropriate_content",  # 7 products (already done)
        "generic_stock",         # 4 products
        "unknown_quality",       # 287 products
        "missing_images"         # 5,072 products
    ],
    "daily_target": 500,  # Process 500 images per day
    "estimated_days": 11  # Complete in ~11 days
}

def generate_fix_summary():
    """Generate a summary report of all image issues"""
    
    print("=" * 80)
    print("HEMP DATABASE IMAGE CRISIS REPORT")
    print("=" * 80)
    print(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    total_products = 6157
    products_with_issues = 5379
    products_ok = 778
    
    print(f"📊 DATABASE STATISTICS:")
    print(f"   Total Products: {total_products:,}")
    print(f"   Products with Image Issues: {products_with_issues:,} ({products_with_issues/total_products*100:.1f}%)")
    print(f"   Products with Valid Images: {products_ok:,} ({products_ok/total_products*100:.1f}%)")
    print()
    
    print("🚨 CRITICAL ISSUES BREAKDOWN:")
    for category, details in IMAGE_FIX_CATEGORIES.items():
        print(f"\n   {category.upper().replace('_', ' ')}:")
        print(f"   - Count: {details['count']:,} products ({details['percentage']}%)")
        print(f"   - Priority: {details['priority']}")
        print(f"   - Solution: {details['solution']}")
        if 'status' in details:
            print(f"   - Status: {details['status']}")
        if 'estimated_cost' in details:
            print(f"   - Estimated Cost: {details['estimated_cost']}")
    
    print("\n" + "=" * 80)
    print("💰 COST ESTIMATE:")
    print(f"   - AI Image Generation: ~$2,690 (5,379 images × $0.50)")
    print(f"   - Time Required: ~11 days at 500 images/day")
    print(f"   - Manual Review Time: ~5 hours for unknown quality images")
    
    print("\n" + "=" * 80)
    print("🎯 RECOMMENDED ACTION PLAN:")
    print("   1. Complete existing queue (21 products) ✓")
    print("   2. Queue remaining problematic images (4 generic stock)")
    print("   3. Review and categorize 287 'unknown quality' images")
    print("   4. Batch process 5,072 missing images in groups of 100")
    print("   5. Implement validation to prevent future issues")
    
    print("\n" + "=" * 80)
    print("📋 NEXT IMMEDIATE STEPS:")
    print("   1. Run SQL to export all 5,379 products needing fixes")
    print("   2. Categorize by industry and plant part")
    print("   3. Generate appropriate prompts using template system")
    print("   4. Begin batch queuing with priority on visible products")

if __name__ == "__main__":
    generate_fix_summary()
    
    # Save the comprehensive SQL
    with open("fix_all_images.sql", "w") as f:
        f.write("-- Comprehensive query to find ALL products needing image fixes\n")
        f.write(f"-- Total affected: 5,379 out of 6,157 products (87.36%)\n")
        f.write(f"-- Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        f.write(COMPREHENSIVE_FIX_SQL)
    
    print("\n\n✅ Generated comprehensive fix plan and SQL query.")
    print("📄 SQL saved to: fix_all_images.sql")