#!/usr/bin/env python3
"""
Test product discovery to see if agents are working
"""

import os
import sys
import subprocess
from datetime import datetime

def test_working_product_adder():
    """Test the working product adder directly"""
    print("Testing Working Product Adder V2")
    print("=" * 60)
    
    try:
        # Run the product adder with a small count
        result = subprocess.run(
            [sys.executable, 'working_product_adder_v2.py', '--count', '1'],
            capture_output=True,
            text=True,
            timeout=30
        )
        
        print("STDOUT:")
        print(result.stdout)
        print("\nSTDERR:")
        print(result.stderr)
        print("\nReturn code:", result.returncode)
        
    except Exception as e:
        print(f"Error running product adder: {e}")

def check_recent_products():
    """Check if any products were added recently"""
    print("\n\nChecking Recent Product Additions")
    print("=" * 60)
    
    import psycopg2
    from dotenv import load_dotenv
    
    load_dotenv()
    
    try:
        conn = psycopg2.connect(os.getenv('DATABASE_URL'))
        cur = conn.cursor()
        
        # Check products added in last hour
        cur.execute("""
            SELECT source_agent, COUNT(*) as count, MAX(created_at) as last_added
            FROM uses_products
            WHERE created_at > NOW() - INTERVAL '1 hour'
            GROUP BY source_agent
            ORDER BY MAX(created_at) DESC
        """)
        
        recent = cur.fetchall()
        if recent:
            print("Products added in last hour:")
            for agent, count, last_time in recent:
                print(f"  {agent}: {count} products (last: {last_time})")
        else:
            print("No products added in last hour")
        
        # Check total by each agent
        cur.execute("""
            SELECT source_agent, COUNT(*) as total
            FROM uses_products
            WHERE source_agent LIKE '%working_product_adder%'
               OR source_agent LIKE '%ai_discovery%'
            GROUP BY source_agent
            ORDER BY COUNT(*) DESC
        """)
        
        print("\nTotal products by discovery agents:")
        for agent, total in cur.fetchall():
            print(f"  {agent}: {total} total products")
        
        cur.close()
        conn.close()
        
    except Exception as e:
        print(f"Database error: {e}")

def test_safe_automation():
    """Test the safe automation runner"""
    print("\n\nTesting Safe Automation Runner")
    print("=" * 60)
    
    try:
        from safe_automation_runner import SafeAutomationRunner
        
        runner = SafeAutomationRunner()
        
        # Check current quality
        metrics = runner.check_current_quality()
        print(f"Current quality metrics: {metrics}")
        
        # Try to run AI discovery
        print("\nAttempting AI discovery...")
        added = runner.run_ai_discovery(num_products=1)
        print(f"Products added: {added}")
        
        if hasattr(runner, 'close'):
            runner.close()
        
    except Exception as e:
        print(f"Error testing safe automation: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_working_product_adder()
    check_recent_products()
    test_safe_automation()