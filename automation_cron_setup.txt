# Hemp Database Automation - Cron Setup Instructions

To set up automated runs, add this to your crontab (run: crontab -e):

# Run hemp database automation every hour
0 * * * * cd /home/<USER>/projects/HQz-Ai-DB-MCP-3 && ./start_automation.sh >> logs/automation_cron.log 2>&1

# Alternative: Run specific agents at different intervals

# Run patent mining agent every 2 hours
0 */2 * * * cd /home/<USER>/projects/HQz-Ai-DB-MCP-3 && source venv_dedup/bin/activate && python src/agents/specialized/patent_mining_agent_simple.py >> logs/patent_cron.log 2>&1

# Run template fixer daily at 3 AM
0 3 * * * cd /home/<USER>/projects/HQz-Ai-DB-MCP-3 && source venv_dedup/bin/activate && python fix_template_descriptions.py >> logs/template_cron.log 2>&1

# Run mega coordinator continuously (restart if stopped)
*/30 * * * * cd /home/<USER>/projects/HQz-Ai-DB-MCP-3 && pgrep -f "mega_agent_coordinator_v2.py" || (source venv_dedup/bin/activate && python src/agents/mega_agent_coordinator_v2.py --continuous 0.5 >> logs/mega_cron.log 2>&1 &)

# Monitor and send daily reports
0 9 * * * cd /home/<USER>/projects/HQz-Ai-DB-MCP-3 && source venv_dedup/bin/activate && python src/agents/mega_agent_coordinator_v2.py --report >> logs/daily_report.log 2>&1