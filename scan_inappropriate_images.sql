-- SQL script to scan for and report inappropriate images in the database

-- 1. Summary of image issues
SELECT 'SUMMARY OF IMAGE ISSUES' as report_section;
SELECT 
    COUNT(*) FILTER (WHERE image_url IS NULL) as missing_images,
    COUNT(*) FILTER (WHERE image_url LIKE '%fallback%' OR image_url LIKE '%unknown%') as placeholder_images,
    COUNT(*) FILTER (WHERE image_url IS NOT NULL AND image_url NOT LIKE '%fallback%' AND image_url NOT LIKE '%unknown%') as has_images,
    COUNT(*) as total_products
FROM uses_products;

-- 2. Products with placeholder images
SELECT '' as blank;
SELECT 'PRODUCTS WITH PLACEHOLDER IMAGES' as report_section;
SELECT 
    id,
    name,
    substring(image_url, 1, 50) as image_url_preview
FROM uses_products
WHERE image_url LIKE '%unknown-hemp-image%'
OR image_url LIKE '%fallback%'
ORDER BY name
LIMIT 20;

-- 3. Check for duplicate images (same image used multiple times)
SELECT '' as blank;
SELECT 'DUPLICATE IMAGES (USED MORE THAN 3 TIMES)' as report_section;
WITH image_counts AS (
    SELECT 
        image_url,
        COUNT(*) as usage_count,
        array_agg(name ORDER BY name) as product_names
    FROM uses_products
    WHERE image_url IS NOT NULL
    GROUP BY image_url
    HAVING COUNT(*) > 3
)
SELECT 
    substring(image_url, 1, 60) as image_url_preview,
    usage_count,
    array_to_string(product_names[1:3], ', ') as example_products
FROM image_counts
ORDER BY usage_count DESC
LIMIT 10;

-- 4. Products that might have inappropriate images based on name patterns
SELECT '' as blank;
SELECT 'PRODUCTS THAT MAY NEED IMAGE REVIEW' as report_section;
SELECT 
    id,
    name,
    CASE 
        WHEN name LIKE '%Active Hemp%' THEN 'Active Hemp series - check for faces'
        WHEN name LIKE '%Additive Hemp%' THEN 'Additive Hemp series - check for interiors'
        WHEN name LIKE '%Advanced%Electronic%' THEN 'Electronic series - check for keyboards'
        WHEN name LIKE '%Adaptive Hemp%' THEN 'Adaptive series - review needed'
        ELSE 'Review recommended'
    END as potential_issue
FROM uses_products
WHERE (
    name LIKE '%Active Hemp%'
    OR name LIKE '%Additive Hemp%'
    OR name LIKE '%Electronic%'
    OR name LIKE '%Adaptive Hemp%'
)
AND image_url IS NOT NULL
ORDER BY name
LIMIT 30;

-- 5. Image generation queue status
SELECT '' as blank;
SELECT 'IMAGE GENERATION QUEUE STATUS' as report_section;
SELECT 
    status,
    COUNT(*) as count,
    MIN(created_at) as oldest_request,
    MAX(created_at) as newest_request
FROM image_generation_queue
GROUP BY status
ORDER BY status;

-- 6. Recently queued products
SELECT '' as blank;
SELECT 'RECENTLY QUEUED FOR IMAGE GENERATION' as report_section;
SELECT 
    p.id,
    p.name,
    iq.status,
    iq.priority,
    iq.created_at,
    substring(iq.prompt, 1, 80) as prompt_preview
FROM image_generation_queue iq
JOIN uses_products p ON iq.product_id = p.id
WHERE iq.created_at > NOW() - INTERVAL '1 day'
ORDER BY iq.created_at DESC
LIMIT 20;