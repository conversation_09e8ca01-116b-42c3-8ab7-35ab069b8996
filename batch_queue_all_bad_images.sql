-- Batch queue ALL products with bad/missing images for regeneration
-- This will queue 5,379 products in batches

-- First, queue the remaining generic stock photos and unknown quality images
INSERT INTO image_generation_queue (
    product_id,
    prompt,
    negative_prompt,
    priority,
    status,
    style_preset
)
SELECT 
    p.id,
    CASE 
        -- Food & Nutrition products
        WHEN isc.industry_id IN (1, 3, 5, 7) THEN 
            'Professional product photography of ' || p.name || ', food-grade hemp product, appetizing presentation, kitchen setting, natural lighting, sustainable packaging'
        
        -- Cosmetics & Beauty
        WHEN isc.industry_id IN (15, 17, 19) THEN
            'Elegant beauty product photo of ' || p.name || ', premium hemp skincare packaging, spa-like setting, soft natural lighting, luxury aesthetic'
        
        -- Medical & Healthcare
        WHEN isc.industry_id IN (6, 8, 10) THEN
            'Clinical product photo of ' || p.name || ', pharmaceutical-grade hemp product, medical packaging, clean white background, professional lighting'
        
        -- Textiles & Fashion
        WHEN isc.industry_id IN (12, 14, 16) THEN
            'Fashion photography of ' || p.name || ', hemp textile product, fabric texture visible, sustainable fashion context, studio lighting'
        
        -- Construction Materials
        WHEN isc.industry_id IN (9, 11, 13) THEN
            'Industrial product photo of ' || p.name || ', hemp construction material, showing strength and durability, architectural context'
        
        -- Agriculture
        WHEN isc.industry_id IN (18, 20, 40) THEN
            'Agricultural product photo of ' || p.name || ', hemp farming product, outdoor natural setting, sustainable agriculture context'
        
        -- Default for others
        ELSE
            'Professional product photography of ' || p.name || ', hemp-based product, sustainable materials, commercial quality, clean composition'
    END as prompt,
    'no human faces, no portraits, no people, no marijuana leaves, no cannabis imagery, no smoking, no recreational drugs, no keyboards, no computers, no living rooms, no furniture',
    CASE 
        WHEN p.image_url IS NULL OR p.image_url = '' THEN 8  -- High priority for missing
        WHEN p.image_url LIKE '%placeholder%' THEN 9         -- Highest for placeholders
        ELSE 7                                                -- Medium for unknown quality
    END as priority,
    'pending',
    'product_photography'
FROM uses_products p
LEFT JOIN industry_sub_categories isc ON p.industry_sub_category_id = isc.id
WHERE (
    -- Missing images
    p.image_url IS NULL OR p.image_url = ''
    -- Generic/unknown quality images
    OR (
        p.image_url IS NOT NULL
        AND p.image_url NOT LIKE '%hemp%'
        AND p.image_url NOT LIKE '%cbd%' 
        AND p.image_url NOT LIKE '%product%'
        AND p.image_url NOT LIKE '%/generated/%'
        AND p.image_url NOT LIKE '%replicate%'
    )
)
-- Exclude already queued products
AND NOT EXISTS (
    SELECT 1 FROM image_generation_queue iq 
    WHERE iq.product_id = p.id 
    AND iq.status = 'pending'
)
-- Process in batches of 100
LIMIT 100;

-- Check how many were queued
SELECT 
    COUNT(*) as queued_count,
    MIN(priority) as highest_priority,
    MAX(priority) as lowest_priority
FROM image_generation_queue
WHERE created_at > NOW() - INTERVAL '1 minute';