#!/usr/bin/env python3
"""
Continuously generate images for all products missing them
Runs until all products have images
"""

import os
import psycopg2
from psycopg2.extras import RealDictCursor
import replicate
import time
from datetime import datetime

def generate_image_for_product(product):
    """Generate an image for a single product"""
    try:
        # Create prompt
        plant_part = product.get('plant_part_name', 'hemp')
        name = product['name']
        
        # Clean name for prompt
        clean_name = name.replace('™', '').replace('®', '').strip()
        
        prompt = f"Professional product photography of {clean_name}, made from {plant_part}, clean white background, studio lighting, high quality, commercial product shot"
        
        print(f"   🎨 Generating: {name[:50]}...")
        
        # Generate image using Replicate
        output = replicate.run(
            "stability-ai/stable-diffusion:db21e45d3f7023abc2a46ee38a23973f6dce16bb082a930b0c49861f96d1e5bf",
            input={
                "prompt": prompt,
                "image_dimensions": "512x512",
                "num_outputs": 1,
                "guidance_scale": 7.5,
                "num_inference_steps": 25
            }
        )
        
        # Get the image URL
        if output and len(output) > 0:
            # Handle FileOutput type
            if hasattr(output[0], '__str__'):
                image_url = str(output[0])
            else:
                image_url = output[0]
            return image_url
        else:
            return None
            
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")
        return None

def main():
    print("🖼️  CONTINUOUS IMAGE GENERATOR")
    print("=" * 60)
    print("Will run until all products have images")
    print("Press Ctrl+C to stop")
    print("")
    
    # Connect to database
    conn = psycopg2.connect(os.environ['DATABASE_URL'])
    cur = conn.cursor(cursor_factory=RealDictCursor)
    
    total_generated = 0
    total_failed = 0
    start_time = datetime.now()
    batch_size = 20
    
    try:
        while True:
            # Get products without images
            cur.execute("""
                SELECT 
                    p.id,
                    p.name,
                    p.description,
                    pp.name as plant_part_name
                FROM uses_products p
                JOIN plant_parts pp ON p.plant_part_id = pp.id
                WHERE p.image_url IS NULL 
                AND p.ai_generated_image_url IS NULL 
                AND p.original_image_url IS NULL
                AND p.name IS NOT NULL
                AND LENGTH(p.name) > 0
                ORDER BY p.created_at DESC
                LIMIT %s
            """, (batch_size,))
            
            products = cur.fetchall()
            
            if not products:
                print("\n✅ ALL PRODUCTS HAVE IMAGES!")
                break
            
            # Check total remaining
            cur.execute("""
                SELECT COUNT(*) 
                FROM uses_products 
                WHERE image_url IS NULL 
                AND ai_generated_image_url IS NULL 
                AND original_image_url IS NULL
            """)
            total_remaining = cur.fetchone()['count']
            
            print(f"\n📊 Batch {(total_generated + total_failed) // batch_size + 1}")
            print(f"   Remaining: {total_remaining:,} products")
            print(f"   Processing: {len(products)} products")
            
            # Process batch
            batch_success = 0
            batch_failed = 0
            
            for i, product in enumerate(products, 1):
                # Generate image
                image_url = generate_image_for_product(product)
                
                if image_url:
                    # Update database
                    try:
                        cur.execute("""
                            UPDATE uses_products 
                            SET 
                                ai_generated_image_url = %s,
                                image_source = 'ai_generated',
                                updated_at = NOW()
                            WHERE id = %s
                        """, (image_url, product['id']))
                        
                        conn.commit()
                        batch_success += 1
                        print(f"   ✅ [{i}/{len(products)}] Saved")
                        
                    except Exception as e:
                        conn.rollback()
                        print(f"   ❌ [{i}/{len(products)}] DB error: {e}")
                        batch_failed += 1
                else:
                    batch_failed += 1
                    print(f"   ❌ [{i}/{len(products)}] Generation failed")
                
                # Rate limiting between images
                if i < len(products):
                    time.sleep(2)
            
            # Update totals
            total_generated += batch_success
            total_failed += batch_failed
            
            # Progress report
            elapsed = (datetime.now() - start_time).total_seconds()
            rate = total_generated / elapsed * 60 if elapsed > 0 else 0
            eta_minutes = (total_remaining - len(products)) / rate if rate > 0 else 0
            
            print(f"\n📈 Progress Update:")
            print(f"   Total generated: {total_generated:,}")
            print(f"   Total failed: {total_failed}")
            print(f"   Success rate: {total_generated/(total_generated+total_failed)*100:.1f}%")
            print(f"   Generation rate: {rate:.1f} images/minute")
            print(f"   ETA: {eta_minutes:.1f} minutes ({eta_minutes/60:.1f} hours)")
            print(f"   Estimated cost so far: ${total_generated * 0.002:.2f}")
            
            # Pause between batches
            print(f"\n⏸️  Pausing 10 seconds before next batch...")
            time.sleep(10)
            
    except KeyboardInterrupt:
        print("\n\n🛑 Stopped by user")
    except Exception as e:
        print(f"\n\n❌ Fatal error: {e}")
    finally:
        # Final report
        elapsed = (datetime.now() - start_time).total_seconds()
        print(f"\n📊 FINAL REPORT")
        print(f"=" * 60)
        print(f"Total images generated: {total_generated:,}")
        print(f"Total failed: {total_failed}")
        print(f"Time elapsed: {elapsed/60:.1f} minutes")
        print(f"Average rate: {total_generated/elapsed*60:.1f} images/minute")
        print(f"Total cost: ${total_generated * 0.002:.2f}")
        
        # Check final status
        cur.execute("""
            SELECT COUNT(*) 
            FROM uses_products 
            WHERE image_url IS NULL 
            AND ai_generated_image_url IS NULL 
            AND original_image_url IS NULL
        """)
        final_remaining = cur.fetchone()['count']
        print(f"\nProducts still without images: {final_remaining:,}")
        
        cur.close()
        conn.close()

if __name__ == "__main__":
    # Check environment
    if not os.environ.get('DATABASE_URL'):
        print("❌ DATABASE_URL not set")
        exit(1)
    if not os.environ.get('REPLICATE_API_TOKEN'):
        print("❌ REPLICATE_API_TOKEN not set")
        exit(1)
        
    main()