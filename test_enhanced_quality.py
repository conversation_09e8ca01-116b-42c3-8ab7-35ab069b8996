#!/usr/bin/env python3
"""
Test the enhanced quality improvement with real AI
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.agents.quality_improvement_agent import QualityImprovementAgent

def main():
    """Test quality improvement with real AI"""
    print("Testing Enhanced Quality Improvement with Real AI")
    print("=" * 60)
    
    agent = None
    try:
        agent = QualityImprovementAgent()
        
        # Get one low quality product to test
        products = agent.get_low_quality_products(1)
        
        if products:
            product = products[0]
            print(f"\nTesting with product: {product['name']}")
            print(f"Current description length: {len(product.get('description', ''))}")
            print(f"Current benefits: {len(product.get('benefits_advantages', []))}")
            print(f"Has tech specs: {'Yes' if product.get('technical_specifications') else 'No'}")
            print(f"Has manufacturing: {'Yes' if product.get('manufacturing_processes_summary') else 'No'}")
            
            print("\nRunning improvement...")
            result = agent.improve_product(product)
            
            print("\nResults:")
            print(f"Success: {result.get('success')}")
            print(f"Improvements made: {result.get('improvements_made', [])}")
            
            if result.get('success') and result.get('updates'):
                updates = result['updates']
                if 'description' in updates:
                    print(f"\nNew description ({len(updates['description'])} chars):")
                    print(updates['description'][:200] + "...")
                
                if 'benefits_advantages' in updates:
                    print(f"\nBenefits ({len(updates['benefits_advantages'])} items):")
                    for i, benefit in enumerate(updates['benefits_advantages'][:3]):
                        print(f"  {i+1}. {benefit}")
                    if len(updates['benefits_advantages']) > 3:
                        print(f"  ... and {len(updates['benefits_advantages']) - 3} more")
        else:
            print("No low quality products found to test")
            
    except Exception as e:
        print(f"\nError: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if agent:
            agent.close()

if __name__ == "__main__":
    main()