import psycopg2
from datetime import datetime, timedelta
import os
from urllib.parse import urlparse

# Parse DATABASE_URL
DATABASE_URL = "postgresql://postgres.ktoqznqmlnxrtvubewyz:$<EMAIL>:6543/postgres"

# Connect to database
try:
    conn = psycopg2.connect(DATABASE_URL)
    cur = conn.cursor()
    
    print("=== Hemp Database Status Report ===")
    print(f"Report generated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("-" * 50)
    
    # 1. Total product count
    cur.execute("SELECT COUNT(*) FROM uses_products;")
    total_count = cur.fetchone()[0]
    print(f"\n1. Total Products: {total_count}")
    
    # 2. Products added in last 24 hours
    yesterday = (datetime.now() - timedelta(days=1)).isoformat()
    cur.execute("""
        SELECT COUNT(*) 
        FROM uses_products 
        WHERE created_at >= %s;
    """, (yesterday,))
    recent_count = cur.fetchone()[0]
    print(f"\n2. Products Added in Last 24 Hours: {recent_count}")
    
    # 3. Count by source_agent
    print("\n3. Products by Source Agent:")
    cur.execute("""
        SELECT 
            COALESCE(source_agent, 'Unknown') as agent,
            COUNT(*) as count
        FROM uses_products
        GROUP BY source_agent
        ORDER BY count DESC;
    """)
    
    for row in cur.fetchall():
        agent, count = row
        print(f"   - {agent}: {count}")
    
    # 4. Recent products added by patent agents
    print("\n4. Recent Patent Agent Products (last 10):")
    cur.execute("""
        SELECT 
            name,
            description,
            created_at,
            source_agent
        FROM uses_products
        WHERE source_agent LIKE '%patent%' 
           OR source_agent LIKE '%Patent%'
        ORDER BY created_at DESC
        LIMIT 10;
    """)
    
    patent_products = cur.fetchall()
    if patent_products:
        for product in patent_products:
            name, description, created_at, agent = product
            print(f"   - {name}")
            print(f"     Description: {description[:100]}...")
            print(f"     Added: {created_at.strftime('%Y-%m-%d %H:%M')}")
            print(f"     Agent: {agent}")
    else:
        print("   No products found from patent agents")
    
    # Additional useful stats
    print("\n5. Additional Statistics:")
    
    # Products with images
    cur.execute("""
        SELECT COUNT(*) 
        FROM uses_products 
        WHERE image_url IS NOT NULL AND image_url != '';
    """)
    image_count = cur.fetchone()[0]
    print(f"   - Products with images: {image_count} ({image_count/total_count*100:.1f}%)")
    
    # Products with companies
    cur.execute("""
        SELECT COUNT(*) 
        FROM uses_products 
        WHERE primary_company_id IS NOT NULL;
    """)
    company_count = cur.fetchone()[0]
    print(f"   - Products with companies: {company_count} ({company_count/total_count*100:.1f}%)")
    
    # Recent activity summary
    print("\n6. Recent Activity (last 7 days):")
    cur.execute("""
        SELECT 
            DATE(created_at) as date,
            COUNT(*) as products_added
        FROM uses_products
        WHERE created_at >= CURRENT_DATE - INTERVAL '7 days'
        GROUP BY DATE(created_at)
        ORDER BY date DESC;
    """)
    
    for row in cur.fetchall():
        date, count = row
        print(f"   - {date}: {count} products")
    
    cur.close()
    conn.close()
    
except Exception as e:
    print(f"Error connecting to database: {e}")