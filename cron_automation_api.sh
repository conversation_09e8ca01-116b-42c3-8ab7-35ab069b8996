#!/bin/bash
# Fixed Cron automation script using API-based agents
# Works with WSL2 IPv6 limitations

echo "🚀 Starting Hemp Database Automation (API-Based)..."
echo "============================================"

# Set up environment
cd /home/<USER>/projects/HQz-Ai-DB-MCP-3
source venv_dedup/bin/activate

# Export API keys
export SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt0b3F6bnFtbG54cnR2dWJld3l6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODQ5MTc3NiwiZXhwIjoyMDY0MDY3Nzc2fQ.hxEiOy6xebv7I4CYJEU49oCEXRvmU6iz2l8Kg-NnvTs"

# Create log directory
mkdir -p logs

echo "🔄 Starting automation cycle at $(date)"

# Run patent mining with API
echo "🔍 Running patent discovery with API..."
python src/agents/specialized/patent_mining_agent_api.py >> logs/patent_mining_api_$(date +%Y%m%d_%H%M%S).log 2>&1

if [ $? -eq 0 ]; then
    echo "✅ Patent mining completed successfully"
else
    echo "❌ Patent mining failed - check logs"
fi

# Run template fixer with API
echo "📊 Fixing template descriptions with API..."
python fix_all_templates_api.py >> logs/template_fixer_api_$(date +%Y%m%d_%H%M%S).log 2>&1

if [ $? -eq 0 ]; then
    echo "✅ Template fixing completed successfully"
else
    echo "❌ Template fixing failed - check logs"
fi

# Get stats using MCP or API
echo "📈 Current database stats:"
python -c "
import requests
url = 'https://ktoqznqmlnxrtvubewyz.supabase.co'
headers = {
    'apikey': '$SUPABASE_ANON_KEY',
    'Authorization': 'Bearer $SUPABASE_ANON_KEY'
}

try:
    # Get product count
    response = requests.get(
        f'{url}/rest/v1/uses_products',
        headers=headers,
        params={'select': 'count', 'limit': '1'}
    )
    
    if 'content-range' in response.headers:
        total = response.headers['content-range'].split('/')[1]
        print(f'  Total products: {total}')
    else:
        print('  Could not get product count')
        
except Exception as e:
    print(f'  Error getting stats: {e}')
"

echo "✅ Automation cycle complete!"
echo ""

# Log to cron.log
echo "[$(date)] API automation cycle complete" >> logs/cron.log