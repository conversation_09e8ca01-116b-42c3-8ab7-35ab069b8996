#!/usr/bin/env python3
"""
Generate images for all products missing images
Runs in batches to avoid overwhelming the API
"""

import os
import time
import psycopg2
from psycopg2.extras import RealDictCursor
import subprocess
import sys

def get_products_without_images(limit=100):
    """Get products that need images"""
    conn = psycopg2.connect(os.environ['DATABASE_URL'])
    cur = conn.cursor(cursor_factory=RealDictCursor)
    
    cur.execute("""
        SELECT COUNT(*) 
        FROM uses_products 
        WHERE image_url IS NULL 
        AND ai_generated_image_url IS NULL 
        AND original_image_url IS NULL
    """)
    total = cur.fetchone()['count']
    
    cur.close()
    conn.close()
    
    return total

def generate_batch(batch_size=50):
    """Generate a batch of images"""
    print(f"\n🎨 Generating batch of {batch_size} images...")
    
    # Run the generation script
    result = subprocess.run([
        sys.executable, 
        "generate_product_images.py", 
        "--limit", 
        str(batch_size)
    ], capture_output=True, text=True)
    
    if result.returncode == 0:
        print("✅ Batch completed successfully")
        # Extract success count from output
        if "Successful:" in result.stdout:
            success_line = [line for line in result.stdout.split('\n') if "Successful:" in line]
            if success_line:
                print(f"   {success_line[0].strip()}")
        return True
    else:
        print(f"❌ Batch failed: {result.stderr}")
        return False

def main():
    print("🖼️  MISSING IMAGE GENERATOR")
    print("=" * 60)
    
    # Check how many products need images
    total_missing = get_products_without_images()
    print(f"\n📊 Products without images: {total_missing:,}")
    
    if total_missing == 0:
        print("✅ All products have images!")
        return
    
    # Calculate batches
    batch_size = 50
    total_batches = (total_missing + batch_size - 1) // batch_size
    estimated_cost = total_missing * 0.002  # ~$0.002 per image
    
    print(f"\n📋 Generation Plan:")
    print(f"   Batch size: {batch_size}")
    print(f"   Total batches: {total_batches}")
    print(f"   Estimated cost: ${estimated_cost:.2f}")
    print(f"   Estimated time: {total_batches * 2:.1f} minutes")
    
    # Confirm
    print(f"\n⚠️  This will generate {total_missing} images in {total_batches} batches.")
    response = input("Continue? (y/n): ")
    if response.lower() != 'y':
        print("Cancelled.")
        return
    
    # Process batches
    batches_completed = 0
    total_generated = 0
    
    print(f"\n🚀 Starting image generation...")
    start_time = time.time()
    
    while True:
        # Check remaining
        remaining = get_products_without_images()
        if remaining == 0:
            break
        
        print(f"\n📍 Batch {batches_completed + 1}/{total_batches}")
        print(f"   Remaining: {remaining:,} products")
        
        # Generate batch
        success = generate_batch(min(batch_size, remaining))
        
        if success:
            batches_completed += 1
            # Estimate generated (batch might be smaller at the end)
            generated_this_batch = min(batch_size, remaining)
            total_generated += generated_this_batch
            
            # Progress
            elapsed = time.time() - start_time
            rate = total_generated / elapsed if elapsed > 0 else 0
            eta = remaining / rate if rate > 0 else 0
            
            print(f"\n📈 Progress:")
            print(f"   Generated: {total_generated:,}/{total_missing:,} ({total_generated/total_missing*100:.1f}%)")
            print(f"   Rate: {rate*60:.1f} images/minute")
            print(f"   ETA: {eta/60:.1f} minutes")
        
        # Pause between batches (API rate limiting)
        if remaining > batch_size:
            print("\n⏸️  Pausing 30 seconds between batches...")
            time.sleep(30)
    
    # Final report
    elapsed = time.time() - start_time
    print(f"\n✅ GENERATION COMPLETE!")
    print(f"=" * 60)
    print(f"Total images generated: {total_generated:,}")
    print(f"Time taken: {elapsed/60:.1f} minutes")
    print(f"Average rate: {total_generated/elapsed*60:.1f} images/minute")
    print(f"Estimated cost: ${total_generated * 0.002:.2f}")

if __name__ == "__main__":
    # Check environment
    if not os.environ.get('DATABASE_URL'):
        print("❌ DATABASE_URL not set")
        sys.exit(1)
    if not os.environ.get('REPLICATE_API_TOKEN'):
        print("❌ REPLICATE_API_TOKEN not set")
        sys.exit(1)
    
    main()