import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load .env
dotenv.config({ path: join(__dirname, '.env') });

const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function addQualityScores() {
  console.log('🔧 Adding quality scores to companies...');
  
  try {
    // First, try to add the column if it doesn't exist
    const { error: alterError } = await supabase.rpc('exec_sql', {
      sql: `
        ALTER TABLE hemp_companies 
        ADD COLUMN IF NOT EXISTS quality_score DECIMAL(3,2) DEFAULT 0.50 
        CHECK (quality_score >= 0 AND quality_score <= 1);
      `
    });
    
    if (alterError && !alterError.message.includes('already exists')) {
      console.log('Note: Could not add column via RPC, it may already exist or require direct DB access');
    }

    // Fetch all companies
    const { data: companies, error: fetchError } = await supabase
      .from('hemp_companies')
      .select('*');

    if (fetchError) {
      console.error('Error fetching companies:', fetchError);
      return;
    }

    console.log(`Found ${companies.length} companies to update`);

    // Calculate and update quality scores
    let updated = 0;
    for (const company of companies) {
      const qualityScore = 
        (company.verified ? 0.3 : 0) +
        (company.website ? 0.2 : 0) +
        (company.description && !company.description.includes('is a hemp product brand') ? 0.2 : 0) +
        (company.company_type ? 0.1 : 0) +
        (company.founded_year ? 0.1 : 0) +
        (company.country ? 0.1 : 0);

      const { error: updateError } = await supabase
        .from('hemp_companies')
        .update({ quality_score: qualityScore })
        .eq('id', company.id);

      if (!updateError) {
        updated++;
        if (updated % 50 === 0) {
          console.log(`Updated ${updated} companies...`);
        }
      }
    }

    console.log(`\n✅ Successfully updated ${updated} companies with quality scores`);

    // Show quality distribution
    const { data: distribution } = await supabase
      .from('hemp_companies')
      .select('quality_score');

    if (distribution) {
      const high = distribution.filter(c => c.quality_score >= 0.8).length;
      const medium = distribution.filter(c => c.quality_score >= 0.5 && c.quality_score < 0.8).length;
      const low = distribution.filter(c => c.quality_score < 0.5).length;

      console.log('\n📊 Quality Score Distribution:');
      console.log(`High (80-100%): ${high} companies`);
      console.log(`Medium (50-79%): ${medium} companies`);
      console.log(`Low (<50%): ${low} companies`);
    }

  } catch (error) {
    console.error('Error:', error);
  }
}

addQualityScores();