#!/usr/bin/env python3
"""Monitor image generation progress"""

import requests
import os
from datetime import datetime

SUPABASE_URL = "https://ktoqznqmlnxrtvubewyz.supabase.co"
SERVICE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt0b3F6bnFtbG54cnR2dWJld3l6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODQ5MTc3NiwiZXhwIjoyMDY0MDY3Nzc2fQ.hxEiOy6xebv7I4CYJEU49oCEXRvmU6iz2l8Kg-NnvTs"

headers = {
    "apikey": SERVICE_KEY,
    "Authorization": f"Bearer {SERVICE_KEY}",
    "Content-Type": "application/json"
}

print("📊 IMAGE GENERATION MONITOR")
print("=" * 60)
print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

# Check recent updates
response = requests.get(
    f"{SUPABASE_URL}/rest/v1/uses_products",
    headers=headers,
    params={
        "select": "id,name,image_url,updated_at",
        "image_url": "not.is.null",
        "order": "updated_at.desc",
        "limit": "10"
    }
)

if response.status_code == 200:
    recent = response.json()
    print(f"\n📸 Recently Generated Images (Last 10):")
    for p in recent:
        if p.get('updated_at'):
            time_str = p['updated_at'][:19].replace('T', ' ')
            print(f"   ✅ {p['name'][:50]}")
            print(f"      Updated: {time_str}")
            print(f"      URL: {p['image_url'][:60]}...")

# Check totals
response = requests.get(
    f"{SUPABASE_URL}/rest/v1/uses_products",
    headers=headers,
    params={
        "select": "id",
        "image_url": "not.is.null",
        "limit": "10000"
    }
)

if response.status_code == 200:
    with_images = len(response.json())
    
    response = requests.get(
        f"{SUPABASE_URL}/rest/v1/uses_products",
        headers=headers,
        params={
            "select": "id",
            "or": "(image_url.is.null,image_url.eq.)",
            "limit": "10000"
        }
    )
    
    if response.status_code == 200:
        without_images = len(response.json())
        total = with_images + without_images
        percentage = (with_images / total * 100) if total > 0 else 0
        
        print(f"\n📈 Overall Progress:")
        print(f"   Total products: {total}")
        print(f"   With images: {with_images} ({percentage:.1f}%)")
        print(f"   Without images: {without_images}")
        print(f"   Progress bar: [{'=' * int(percentage/5)}{' ' * (20-int(percentage/5))}] {percentage:.1f}%")

# Check if generator is running
import subprocess
result = subprocess.run(['pgrep', '-f', 'safe_image_generator'], capture_output=True, text=True)
if result.returncode == 0:
    print(f"\n✅ Image generator is running (PID: {result.stdout.strip()})")
else:
    print(f"\n❌ Image generator is not running")

print("\n" + "=" * 60)