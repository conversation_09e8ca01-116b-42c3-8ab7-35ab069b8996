import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
dotenv.config({ path: join(__dirname, 'HempResourceHub', '.env') });

const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function addQualityScoreColumn() {
  console.log('Adding quality_score column to hemp_companies table...');
  
  try {
    // Read SQL file
    const sql = readFileSync('add_quality_score_column.sql', 'utf8');
    
    // Execute SQL
    const { data, error } = await supabase.rpc('exec_sql', {
      sql_query: sql
    });
    
    if (error) {
      // Try alternative approach - direct SQL via service role
      const response = await fetch(
        `${process.env.VITE_SUPABASE_URL}/rest/v1/rpc/exec_sql`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY,
            'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
          },
          body: JSON.stringify({ sql_query: sql })
        }
      );
      
      if (!response.ok) {
        // Final fallback - use direct table update to test
        const testUpdate = await supabase
          .from('hemp_companies')
          .update({ quality_score: 0.5 })
          .eq('id', 1);
          
        if (testUpdate.error) {
          console.error('Column does not exist. Please add it manually in Supabase dashboard.');
          console.log('\nSQL to execute:');
          console.log(sql);
          return;
        }
      }
    }
    
    console.log('✅ Quality score column added successfully!');
    
  } catch (error) {
    console.error('Error:', error);
    console.log('\nPlease add the column manually in Supabase dashboard:');
    console.log('ALTER TABLE hemp_companies ADD COLUMN quality_score DECIMAL(3,2) DEFAULT 0.50;');
  }
}

addQualityScoreColumn();