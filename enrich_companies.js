import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load .env
dotenv.config({ path: join(__dirname, '.env') });

const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Known legitimate hemp companies with verified data
const verifiedCompanies = {
  'Manitoba Harvest': {
    website: 'https://manitobaharvest.com',
    description: 'Leading hemp food manufacturer specializing in hemp hearts, protein powder, and hemp oil. Pioneer in the hemp food industry.',
    founded_year: 1998,
    country: 'Canada',
    company_type: 'manufacturer'
  },
  "Charlotte's Web": {
    website: 'https://www.charlottesweb.com',
    description: 'Premium hemp extract and CBD wellness products. Known for high-quality, full-spectrum hemp extracts.',
    founded_year: 2013,
    country: 'United States',
    company_type: 'manufacturer'
  },
  'HempFlax': {
    website: 'https://hempflax.com',
    description: 'European leader in hemp cultivation and processing. Produces hemp fiber, shives, and seeds for various industries.',
    founded_year: 1993,
    country: 'Netherlands',
    company_type: 'processor'
  },
  'Nutiva': {
    website: 'https://nutiva.com',
    description: 'Organic superfoods company offering hemp seeds, hemp protein, and hemp oil products.',
    founded_year: 1999,
    country: 'United States',
    company_type: 'manufacturer'
  },
  "Dr. Bronner's": {
    website: 'https://www.drbronner.com',
    description: 'Organic and fair trade personal care products, including hemp-based soaps and lotions.',
    founded_year: 1948,
    country: 'United States',
    company_type: 'manufacturer'
  },
  "Bob's Red Mill": {
    website: 'https://www.bobsredmill.com',
    description: 'Natural foods company offering hemp hearts and hemp protein powder among other whole grain products.',
    founded_year: 1978,
    country: 'United States',
    company_type: 'manufacturer'
  },
  'Elixinol': {
    website: 'https://elixinol.com',
    description: 'Global leader in hemp-derived CBD products for wellness and skincare.',
    founded_year: 2014,
    country: 'United States',
    company_type: 'manufacturer'
  },
  'Hemp Inc': {
    website: 'https://hempinc.com',
    description: 'Industrial hemp products company focused on processing, manufacturing, and marketing hemp-based products.',
    founded_year: 2008,
    country: 'United States',
    company_type: 'manufacturer'
  },
  'HempWood': {
    website: 'https://hempwood.com',
    description: 'Innovative manufacturer of hemp-based wood alternatives for flooring and building materials.',
    founded_year: 2017,
    country: 'United States',
    company_type: 'manufacturer'
  },
  'Patagonia': {
    website: 'https://www.patagonia.com',
    description: 'Outdoor clothing company pioneering the use of hemp fibers in sustainable apparel.',
    founded_year: 1973,
    country: 'United States',
    company_type: 'manufacturer'
  }
};

async function findAndMergeDuplicates(companies) {
  console.log('\n🔍 Looking for duplicate companies...');
  
  const mergedCount = { count: 0 };
  const processed = new Set();

  for (let i = 0; i < companies.length; i++) {
    if (processed.has(companies[i].id)) continue;

    const name1 = companies[i].name.toLowerCase().trim();

    for (let j = i + 1; j < companies.length; j++) {
      if (processed.has(companies[j].id)) continue;

      const name2 = companies[j].name.toLowerCase().trim();

      // Check for duplicates
      if (name1 === name2 || 
          name1.replace(/\s+/g, '') === name2.replace(/\s+/g, '') ||
          (name1.includes(name2) || name2.includes(name1)) && Math.abs(name1.length - name2.length) < 5) {
        
        console.log(`🔄 Merging '${companies[i].name}' and '${companies[j].name}'`);

        // Keep the one with more data
        const merged = mergeCompanyData(companies[i], companies[j]);

        try {
          // Update primary company
          await supabase
            .from('hemp_companies')
            .update(merged)
            .eq('id', companies[i].id);

          // Update product relationships
          await supabase
            .from('hemp_company_products')
            .update({ company_id: companies[i].id })
            .eq('company_id', companies[j].id);

          await supabase
            .from('uses_products')
            .update({ primary_company_id: companies[i].id })
            .eq('primary_company_id', companies[j].id);

          // Delete duplicate
          await supabase
            .from('hemp_companies')
            .delete()
            .eq('id', companies[j].id);

          processed.add(companies[j].id);
          mergedCount.count++;
        } catch (error) {
          console.error(`❌ Error merging companies: ${error.message}`);
        }
      }
    }
  }

  console.log(`✅ Merged ${mergedCount.count} duplicate companies`);
}

function mergeCompanyData(company1, company2) {
  const merged = { ...company1 };

  // Fields to merge
  const fieldsToMerge = [
    'description', 'website', 'country', 'city', 'state_province',
    'founded_year', 'company_type', 'logo_url'
  ];

  for (const field of fieldsToMerge) {
    if (!merged[field] && company2[field]) {
      merged[field] = company2[field];
    } else if (merged[field] && company2[field]) {
      // For description, keep the longer one
      if (field === 'description' && company2[field].length > merged[field].length) {
        merged[field] = company2[field];
      }
    }
  }

  // Merge verification status
  merged.verified = company1.verified || company2.verified;

  return merged;
}

async function enrichCompanies() {
  console.log('🏢 COMPANY DATA ENRICHMENT');
  console.log('='.repeat(60));

  // Fetch all companies
  console.log('\n📊 Fetching companies from database...');
  const { data: companies, error } = await supabase
    .from('hemp_companies')
    .select('*')
    .order('name');

  if (error) {
    console.error('Error fetching companies:', error);
    return;
  }

  console.log(`✅ Found ${companies.length} companies`);

  // Find and merge duplicates first
  await findAndMergeDuplicates(companies);

  // Re-fetch companies after merging
  const { data: updatedCompanies } = await supabase
    .from('hemp_companies')
    .select('*')
    .order('name');

  // Enrich companies
  console.log(`\n🔧 Enriching ${updatedCompanies.length} companies...`);
  let enrichedCount = 0;

  for (let i = 0; i < updatedCompanies.length; i++) {
    const company = updatedCompanies[i];
    console.log(`\n[${i + 1}/${updatedCompanies.length}] Processing ${company.name}...`);

    const enrichedData = {};

    // Check if it's a known verified company
    if (verifiedCompanies[company.name]) {
      Object.assign(enrichedData, verifiedCompanies[company.name]);
      enrichedData.verified = true;
      console.log(`✅ Found verified data for ${company.name}`);
    }

    // Only update description if current one is generic
    if (enrichedData.description && 
        (company.description?.includes('is a hemp product brand') ||
         company.description?.includes('identified from product names') ||
         !company.description)) {
      // Keep enriched description
    } else if (company.description && enrichedData.description) {
      // Keep existing description if it's not generic
      delete enrichedData.description;
    }

    // Clean up company type
    if (!company.company_type && enrichedData.company_type) {
      // Use enriched type
    } else if (company.company_type === 'Brand' && enrichedData.company_type) {
      // Replace generic 'Brand' with specific type
    } else {
      delete enrichedData.company_type;
    }

    if (Object.keys(enrichedData).length > 0) {
      try {
        await supabase
          .from('hemp_companies')
          .update(enrichedData)
          .eq('id', company.id);
        
        enrichedCount++;
        console.log(`✅ Updated ${company.name}`);
      } catch (error) {
        console.error(`❌ Error updating ${company.name}: ${error.message}`);
      }
    }
  }

  console.log(`\n✅ Enriched ${enrichedCount} companies`);

  // Generate summary report
  console.log('\n📊 ENRICHMENT SUMMARY');
  console.log('='.repeat(60));

  // Re-fetch for final stats
  const { data: finalCompanies } = await supabase
    .from('hemp_companies')
    .select('*');

  const withWebsites = finalCompanies.filter(c => c.website).length;
  const withDescriptions = finalCompanies.filter(c => 
    c.description && !c.description.includes('is a hemp product brand')
  ).length;
  const verified = finalCompanies.filter(c => c.verified).length;

  console.log(`Total Companies: ${finalCompanies.length}`);
  console.log(`With Websites: ${withWebsites} (${(withWebsites/finalCompanies.length*100).toFixed(1)}%)`);
  console.log(`With Quality Descriptions: ${withDescriptions} (${(withDescriptions/finalCompanies.length*100).toFixed(1)}%)`);
  console.log(`Verified: ${verified} (${(verified/finalCompanies.length*100).toFixed(1)}%)`);
}

enrichCompanies().catch(console.error);