#!/bin/bash
# Quick script to run company enrichment

echo "🏢 Starting Company Enrichment..."
echo "=================================="

# Activate virtual environment
if [ -d "venv_dedup" ]; then
    echo "✓ Activating virtual environment..."
    source venv_dedup/bin/activate
else
    echo "❌ Virtual environment not found!"
    echo "Creating new virtual environment..."
    python3 -m venv venv_enrichment
    source venv_enrichment/bin/activate
fi

# Install required packages if needed
echo "📦 Checking dependencies..."
pip install psycopg2-binary requests beautifulsoup4 python-dotenv --quiet

# Set database URL
export DATABASE_URL="${DATABASE_URL:-postgresql://postgres:$<EMAIL>:5432/postgres}"

# Run the enrichment
echo ""
echo "🔄 Running enrichment process..."
echo "This will:"
echo "  - Web scrape company websites"
echo "  - Extract metadata and social links"
echo "  - Link orphaned products"
echo "  - Estimate missing data"
echo ""

python3 run_company_enrichment.py

echo ""
echo "✅ Enrichment complete!"