#!/usr/bin/env python3
"""
Quick Quality Improvement Script
Runs a single batch with progress tracking
"""

import os
import sys
import json
from datetime import datetime

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.agents.quality_improvement_agent import QualityImprovementAgent

def main():
    """Run a quick batch of quality improvements"""
    print("Quick Quality Improvement - Single Batch")
    print("=" * 60)
    
    agent = None
    batch_size = 20  # Smaller batch for quick run
    
    try:
        agent = QualityImprovementAgent()
        
        # Check current status
        low_quality_products = agent.get_low_quality_products(1000)
        total_remaining = len(low_quality_products)
        
        print(f"Total low quality products remaining: {total_remaining}")
        print(f"Running batch of {batch_size} products...\n")
        
        # Run one batch
        start_time = datetime.now()
        results = agent.run_improvement_batch(batch_size=batch_size)
        end_time = datetime.now()
        
        # Calculate duration
        duration = (end_time - start_time).total_seconds()
        per_product = duration / results['products_processed'] if results['products_processed'] > 0 else 0
        
        # Display results
        print("\n" + "=" * 60)
        print("BATCH RESULTS")
        print("=" * 60)
        print(f"Products processed: {results['products_processed']}")
        print(f"Products improved: {results['products_improved']}")
        print(f"Success rate: {results['products_improved']/results['products_processed']*100:.1f}%")
        print(f"Total time: {duration:.1f} seconds")
        print(f"Time per product: {per_product:.1f} seconds")
        
        # Estimate remaining time
        if total_remaining > batch_size:
            estimated_total_time = (total_remaining * per_product) / 60
            print(f"\nEstimated time for all {total_remaining} products: {estimated_total_time:.1f} minutes")
        
        # Save results
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = f'logs/quick_quality_improvement_{timestamp}.json'
        
        os.makedirs('logs', exist_ok=True)
        with open(output_file, 'w') as f:
            json.dump({
                'batch_results': results,
                'total_remaining': total_remaining,
                'duration_seconds': duration,
                'per_product_seconds': per_product
            }, f, indent=2, default=str)
        
        print(f"\nResults saved to: {output_file}")
        
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        if agent:
            agent.close()

if __name__ == "__main__":
    main()