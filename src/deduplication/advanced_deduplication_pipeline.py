#!/usr/bin/env python3
"""
Advanced Deduplication Pipeline for Hemp Products Database
Implements multi-stage matching: exact, fuzzy, semantic, and component-based
Author: Database Enhancement Team
Date: January 2025
"""

import os
import re
import json
import logging
from typing import List, Dict, Tuple, Optional, Set
from dataclasses import dataclass, field
from datetime import datetime
import numpy as np
from rapidfuzz import fuzz, process
from sentence_transformers import SentenceTransformer
import torch
from sklearn.metrics.pairwise import cosine_similarity
import psycopg2
from psycopg2.extras import RealDictCursor
from urllib.parse import quote_plus
import hashlib

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class DuplicateCandidate:
    """Represents a potential duplicate match"""
    product_id: int
    name: str
    description: str
    plant_part_id: int
    industry_id: int
    match_type: str  # 'exact', 'fuzzy', 'semantic', 'component'
    similarity_score: float
    match_details: Dict = field(default_factory=dict)

@dataclass
class Product:
    """Product data structure"""
    id: int
    name: str
    description: Optional[str]
    plant_part_id: Optional[int]
    industry_sub_category_id: Optional[int]
    benefits_advantages: Optional[str]
    technical_specifications: Optional[str]
    keywords: Optional[List[str]]
    created_at: datetime
    confidence_score: Optional[float]

class AdvancedDeduplicator:
    """Multi-stage deduplication pipeline"""
    
    def __init__(self, db_url: str, model_name: str = 'all-MiniLM-L6-v2'):
        """Initialize deduplicator with database connection and ML model"""
        self.db_url = db_url
        self.conn = None
        self.cursor = None
        
        # Initialize sentence transformer for semantic matching
        logger.info(f"Loading sentence transformer model: {model_name}")
        self.semantic_model = SentenceTransformer(model_name)
        
        # Configuration
        self.config = {
            'exact_match': {
                'enabled': True,
                'weight': 1.0
            },
            'fuzzy_match': {
                'enabled': True,
                'threshold': 85,
                'weight': 0.9
            },
            'semantic_match': {
                'enabled': True,
                'threshold': 0.85,
                'weight': 0.8
            },
            'component_match': {
                'enabled': True,
                'threshold': 0.75,
                'weight': 0.7
            }
        }
        
        # Cache for embeddings
        self.embedding_cache = {}
        
    def connect(self):
        """Establish database connection"""
        try:
            self.conn = psycopg2.connect(self.db_url)
            self.cursor = self.conn.cursor(cursor_factory=RealDictCursor)
            logger.info("Database connection established")
        except Exception as e:
            logger.error(f"Failed to connect to database: {e}")
            raise
    
    def disconnect(self):
        """Close database connection"""
        if self.cursor:
            self.cursor.close()
        if self.conn:
            self.conn.close()
        logger.info("Database connection closed")
    
    def normalize_text(self, text: str) -> str:
        """Normalize text for comparison"""
        if not text:
            return ""
        
        # Convert to lowercase
        text = text.lower().strip()
        
        # Remove multiple spaces
        text = re.sub(r'\s+', ' ', text)
        
        # Remove special characters but keep important ones
        text = re.sub(r'[^a-z0-9\s\-\/&,.]', '', text)
        
        # Normalize common terms
        replacements = {
            'fibre': 'fiber',
            'centre': 'center',
            'colour': 'color',
            'utilising': 'utilizing',
            'optimised': 'optimized',
            'co2': 'carbon dioxide',
            'cbd': 'cannabidiol',
            'thc': 'tetrahydrocannabinol'
        }
        
        for old, new in replacements.items():
            text = text.replace(old, new)
        
        return text
    
    def extract_components(self, product: Product) -> Dict[str, Set[str]]:
        """Extract semantic components from product"""
        components = {
            'materials': set(),
            'processes': set(),
            'applications': set(),
            'properties': set(),
            'metrics': set()
        }
        
        # Combine all text fields
        text_fields = [
            product.name or '',
            product.description or '',
            product.benefits_advantages or '',
            product.technical_specifications or ''
        ]
        full_text = ' '.join(text_fields).lower()
        
        # Material patterns
        material_patterns = [
            r'hemp (?:fiber|fibre|hurd|seed|oil|extract)',
            r'(?:bio)?(?:plastic|polymer|resin|composite)',
            r'(?:nano)?(?:cellulose|graphene|carbon)',
            r'(?:organic|natural) \w+'
        ]
        
        # Process patterns
        process_patterns = [
            r'(?:cold|hot)?.?press(?:ed|ing)',
            r'extract(?:ed|ion)',
            r'(?:bio)?degrad(?:able|ation)',
            r'sustain(?:able|ability)',
            r'eco.?friendly'
        ]
        
        # Application patterns
        application_patterns = [
            r'(?:for|used in|application in) ([\w\s]+)',
            r'(?:construction|building|automotive|textile|food|cosmetic)',
            r'(?:industrial|commercial|residential)'
        ]
        
        # Extract using patterns
        for pattern in material_patterns:
            matches = re.findall(pattern, full_text)
            components['materials'].update(matches)
        
        for pattern in process_patterns:
            matches = re.findall(pattern, full_text)
            components['processes'].update(matches)
        
        for pattern in application_patterns:
            matches = re.findall(pattern, full_text)
            components['applications'].update(matches)
        
        # Extract metrics (numbers with units)
        metric_pattern = r'(\d+(?:\.\d+)?\s*(?:mm|cm|m|kg|g|mg|%|mpa|gpa))'
        metrics = re.findall(metric_pattern, full_text, re.IGNORECASE)
        components['metrics'].update(metrics)
        
        # Extract properties from keywords
        if product.keywords:
            components['properties'].update(product.keywords)
        
        return components
    
    def exact_match(self, product: Product, candidates: List[Product]) -> List[DuplicateCandidate]:
        """Stage 1: Exact matching on normalized name and key fields"""
        matches = []
        normalized_name = self.normalize_text(product.name)
        
        for candidate in candidates:
            if candidate.id == product.id:
                continue
            
            # Check exact name match
            if self.normalize_text(candidate.name) == normalized_name:
                # Additional checks for same plant part and industry
                if (candidate.plant_part_id == product.plant_part_id and 
                    candidate.industry_sub_category_id == product.industry_sub_category_id):
                    
                    matches.append(DuplicateCandidate(
                        product_id=candidate.id,
                        name=candidate.name,
                        description=candidate.description or '',
                        plant_part_id=candidate.plant_part_id,
                        industry_id=candidate.industry_sub_category_id,
                        match_type='exact',
                        similarity_score=1.0,
                        match_details={
                            'normalized_name': normalized_name,
                            'exact_match_fields': ['name', 'plant_part', 'industry']
                        }
                    ))
        
        return matches
    
    def fuzzy_match(self, product: Product, candidates: List[Product]) -> List[DuplicateCandidate]:
        """Stage 2: Fuzzy matching using token sort ratio"""
        matches = []
        threshold = self.config['fuzzy_match']['threshold']
        
        # Prepare name for fuzzy matching
        product_name = self.normalize_text(product.name)
        
        for candidate in candidates:
            if candidate.id == product.id:
                continue
            
            candidate_name = self.normalize_text(candidate.name)
            
            # Calculate fuzzy match score
            token_sort_score = fuzz.token_sort_ratio(product_name, candidate_name)
            token_set_score = fuzz.token_set_ratio(product_name, candidate_name)
            partial_score = fuzz.partial_ratio(product_name, candidate_name)
            
            # Use the highest score
            max_score = max(token_sort_score, token_set_score, partial_score)
            
            if max_score >= threshold:
                # Boost score if plant part and industry match
                boost = 0
                if candidate.plant_part_id == product.plant_part_id:
                    boost += 5
                if candidate.industry_sub_category_id == product.industry_sub_category_id:
                    boost += 5
                
                final_score = min(100, max_score + boost)
                
                if final_score >= threshold:
                    matches.append(DuplicateCandidate(
                        product_id=candidate.id,
                        name=candidate.name,
                        description=candidate.description or '',
                        plant_part_id=candidate.plant_part_id,
                        industry_id=candidate.industry_sub_category_id,
                        match_type='fuzzy',
                        similarity_score=final_score / 100.0,
                        match_details={
                            'token_sort_score': token_sort_score,
                            'token_set_score': token_set_score,
                            'partial_score': partial_score,
                            'boost_applied': boost
                        }
                    ))
        
        return matches
    
    def get_embedding(self, text: str) -> np.ndarray:
        """Get embedding for text with caching"""
        # Create cache key
        cache_key = hashlib.md5(text.encode()).hexdigest()
        
        if cache_key in self.embedding_cache:
            return self.embedding_cache[cache_key]
        
        # Generate embedding
        embedding = self.semantic_model.encode(text, convert_to_numpy=True)
        
        # Cache it
        self.embedding_cache[cache_key] = embedding
        
        return embedding
    
    def semantic_match(self, product: Product, candidates: List[Product]) -> List[DuplicateCandidate]:
        """Stage 3: Semantic matching using sentence embeddings"""
        matches = []
        threshold = self.config['semantic_match']['threshold']
        
        # Create product text representation
        product_text = f"{product.name} {product.description or ''} {product.benefits_advantages or ''}"
        product_text = self.normalize_text(product_text)
        
        if not product_text.strip():
            return matches
        
        # Get product embedding
        product_embedding = self.get_embedding(product_text)
        
        for candidate in candidates:
            if candidate.id == product.id:
                continue
            
            # Create candidate text
            candidate_text = f"{candidate.name} {candidate.description or ''} {candidate.benefits_advantages or ''}"
            candidate_text = self.normalize_text(candidate_text)
            
            if not candidate_text.strip():
                continue
            
            # Get candidate embedding
            candidate_embedding = self.get_embedding(candidate_text)
            
            # Calculate cosine similarity
            similarity = cosine_similarity(
                product_embedding.reshape(1, -1),
                candidate_embedding.reshape(1, -1)
            )[0][0]
            
            if similarity >= threshold:
                matches.append(DuplicateCandidate(
                    product_id=candidate.id,
                    name=candidate.name,
                    description=candidate.description or '',
                    plant_part_id=candidate.plant_part_id,
                    industry_id=candidate.industry_sub_category_id,
                    match_type='semantic',
                    similarity_score=float(similarity),
                    match_details={
                        'embedding_model': self.semantic_model._model_card_vars['name'],
                        'text_length_product': len(product_text.split()),
                        'text_length_candidate': len(candidate_text.split())
                    }
                ))
        
        return matches
    
    def component_match(self, product: Product, candidates: List[Product]) -> List[DuplicateCandidate]:
        """Stage 4: Component-based matching"""
        matches = []
        threshold = self.config['component_match']['threshold']
        
        # Extract product components
        product_components = self.extract_components(product)
        
        for candidate in candidates:
            if candidate.id == product.id:
                continue
            
            # Extract candidate components
            candidate_components = self.extract_components(candidate)
            
            # Calculate component overlap
            overlap_scores = {}
            
            for component_type in product_components:
                if not product_components[component_type]:
                    continue
                
                product_set = product_components[component_type]
                candidate_set = candidate_components[component_type]
                
                if product_set and candidate_set:
                    intersection = len(product_set & candidate_set)
                    union = len(product_set | candidate_set)
                    
                    if union > 0:
                        overlap_scores[component_type] = intersection / union
            
            # Calculate weighted average of overlap scores
            if overlap_scores:
                weights = {
                    'materials': 0.3,
                    'processes': 0.2,
                    'applications': 0.25,
                    'properties': 0.15,
                    'metrics': 0.1
                }
                
                weighted_score = sum(
                    overlap_scores.get(comp, 0) * weights.get(comp, 0)
                    for comp in weights
                )
                
                if weighted_score >= threshold:
                    matches.append(DuplicateCandidate(
                        product_id=candidate.id,
                        name=candidate.name,
                        description=candidate.description or '',
                        plant_part_id=candidate.plant_part_id,
                        industry_id=candidate.industry_sub_category_id,
                        match_type='component',
                        similarity_score=weighted_score,
                        match_details={
                            'overlap_scores': overlap_scores,
                            'shared_components': {
                                comp: list(product_components[comp] & candidate_components[comp])
                                for comp in product_components
                                if product_components[comp] & candidate_components[comp]
                            }
                        }
                    ))
        
        return matches
    
    def find_duplicates(self, product_id: int, limit: int = 50) -> List[DuplicateCandidate]:
        """Find all potential duplicates for a given product"""
        # Fetch the product
        self.cursor.execute(
            "SELECT * FROM uses_products WHERE id = %s",
            (product_id,)
        )
        product_data = self.cursor.fetchone()
        
        if not product_data:
            logger.error(f"Product {product_id} not found")
            return []
        
        product = Product(**product_data)
        
        # Fetch candidate products (same plant part or industry)
        self.cursor.execute("""
            SELECT * FROM uses_products 
            WHERE id != %s 
            AND (plant_part_id = %s OR industry_sub_category_id = %s)
            ORDER BY created_at DESC
            LIMIT %s
        """, (product_id, product.plant_part_id, product.industry_sub_category_id, limit))
        
        candidates_data = self.cursor.fetchall()
        candidates = [Product(**data) for data in candidates_data]
        
        logger.info(f"Processing {len(candidates)} candidates for product {product_id}")
        
        # Run all matching stages
        all_matches = []
        
        if self.config['exact_match']['enabled']:
            logger.info("Running exact match stage...")
            exact_matches = self.exact_match(product, candidates)
            all_matches.extend(exact_matches)
            logger.info(f"Found {len(exact_matches)} exact matches")
        
        if self.config['fuzzy_match']['enabled']:
            logger.info("Running fuzzy match stage...")
            fuzzy_matches = self.fuzzy_match(product, candidates)
            all_matches.extend(fuzzy_matches)
            logger.info(f"Found {len(fuzzy_matches)} fuzzy matches")
        
        if self.config['semantic_match']['enabled']:
            logger.info("Running semantic match stage...")
            semantic_matches = self.semantic_match(product, candidates)
            all_matches.extend(semantic_matches)
            logger.info(f"Found {len(semantic_matches)} semantic matches")
        
        if self.config['component_match']['enabled']:
            logger.info("Running component match stage...")
            component_matches = self.component_match(product, candidates)
            all_matches.extend(component_matches)
            logger.info(f"Found {len(component_matches)} component matches")
        
        # Deduplicate and rank matches
        unique_matches = self.deduplicate_and_rank(all_matches)
        
        return unique_matches
    
    def deduplicate_and_rank(self, matches: List[DuplicateCandidate]) -> List[DuplicateCandidate]:
        """Deduplicate matches and rank by weighted score"""
        # Group by product_id
        grouped = {}
        
        for match in matches:
            if match.product_id not in grouped:
                grouped[match.product_id] = []
            grouped[match.product_id].append(match)
        
        # For each product, calculate best weighted score
        final_matches = []
        
        for product_id, product_matches in grouped.items():
            # Calculate weighted score for each match type
            best_score = 0
            best_match = None
            
            for match in product_matches:
                weight = self.config[f"{match.match_type}_match"]['weight']
                weighted_score = match.similarity_score * weight
                
                if weighted_score > best_score:
                    best_score = weighted_score
                    best_match = match
            
            if best_match:
                # Add aggregated match details
                best_match.match_details['all_match_types'] = [
                    m.match_type for m in product_matches
                ]
                best_match.match_details['weighted_score'] = best_score
                final_matches.append(best_match)
        
        # Sort by weighted score
        final_matches.sort(key=lambda x: x.match_details['weighted_score'], reverse=True)
        
        return final_matches
    
    def merge_duplicates(self, primary_id: int, duplicate_ids: List[int], 
                        merge_strategy: str = 'comprehensive') -> bool:
        """Merge duplicate products into a primary product"""
        try:
            self.conn.begin()
            
            # Fetch all products
            all_ids = [primary_id] + duplicate_ids
            self.cursor.execute(
                "SELECT * FROM uses_products WHERE id = ANY(%s)",
                (all_ids,)
            )
            products = {p['id']: p for p in self.cursor.fetchall()}
            
            if primary_id not in products:
                raise ValueError(f"Primary product {primary_id} not found")
            
            primary = products[primary_id]
            
            if merge_strategy == 'comprehensive':
                # Merge all data, keeping the most complete information
                for dup_id in duplicate_ids:
                    if dup_id not in products:
                        continue
                    
                    dup = products[dup_id]
                    
                    # Merge description (keep longer one)
                    if dup['description'] and len(dup['description']) > len(primary['description'] or ''):
                        primary['description'] = dup['description']
                    
                    # Merge benefits
                    if dup['benefits_advantages'] and not primary['benefits_advantages']:
                        primary['benefits_advantages'] = dup['benefits_advantages']
                    
                    # Merge technical specs
                    if dup['technical_specifications'] and not primary['technical_specifications']:
                        primary['technical_specifications'] = dup['technical_specifications']
                    
                    # Merge keywords
                    if dup['keywords']:
                        primary_keywords = set(primary['keywords'] or [])
                        primary_keywords.update(dup['keywords'])
                        primary['keywords'] = list(primary_keywords)
                    
                    # Update references
                    # Move company associations
                    self.cursor.execute("""
                        UPDATE product_companies 
                        SET product_id = %s 
                        WHERE product_id = %s
                        AND NOT EXISTS (
                            SELECT 1 FROM product_companies pc2 
                            WHERE pc2.product_id = %s 
                            AND pc2.company_id = product_companies.company_id
                        )
                    """, (primary_id, dup_id, primary_id))
                    
                    # Move images
                    self.cursor.execute("""
                        UPDATE product_images 
                        SET product_id = %s 
                        WHERE product_id = %s
                        AND NOT EXISTS (
                            SELECT 1 FROM product_images pi2 
                            WHERE pi2.product_id = %s 
                            AND pi2.image_url = product_images.image_url
                        )
                    """, (primary_id, dup_id, primary_id))
                    
                    # Update canonical reference
                    self.cursor.execute("""
                        UPDATE uses_products 
                        SET canonical_product_id = %s,
                            verification_status = 'deprecated',
                            verification_notes = %s
                        WHERE id = %s
                    """, (
                        primary_id,
                        f"Merged into product {primary_id} on {datetime.now().isoformat()}",
                        dup_id
                    ))
            
            # Update primary product with merged data
            self.cursor.execute("""
                UPDATE uses_products 
                SET description = %s,
                    benefits_advantages = %s,
                    technical_specifications = %s,
                    keywords = %s,
                    confidence_score = %s,
                    verification_status = 'pending_review',
                    verification_notes = %s
                WHERE id = %s
            """, (
                primary['description'],
                primary['benefits_advantages'],
                primary['technical_specifications'],
                primary['keywords'],
                0.7,  # Set confidence to 0.7 for merged products
                f"Merged from products: {duplicate_ids} on {datetime.now().isoformat()}",
                primary_id
            ))
            
            self.conn.commit()
            logger.info(f"Successfully merged {len(duplicate_ids)} products into {primary_id}")
            return True
            
        except Exception as e:
            self.conn.rollback()
            logger.error(f"Failed to merge products: {e}")
            return False
    
    def batch_find_duplicates(self, sample_size: int = 1000) -> Dict[int, List[DuplicateCandidate]]:
        """Find duplicates for a batch of products"""
        # Get a sample of recent products
        self.cursor.execute("""
            SELECT id FROM uses_products 
            WHERE verification_status != 'deprecated'
            ORDER BY created_at DESC 
            LIMIT %s
        """, (sample_size,))
        
        product_ids = [row['id'] for row in self.cursor.fetchall()]
        
        logger.info(f"Processing {len(product_ids)} products for duplicate detection")
        
        all_duplicates = {}
        
        for i, product_id in enumerate(product_ids):
            if i % 100 == 0:
                logger.info(f"Progress: {i}/{len(product_ids)} products processed")
            
            duplicates = self.find_duplicates(product_id, limit=30)
            
            if duplicates:
                all_duplicates[product_id] = duplicates
        
        logger.info(f"Found {len(all_duplicates)} products with potential duplicates")
        
        return all_duplicates
    
    def generate_deduplication_report(self, duplicates: Dict[int, List[DuplicateCandidate]]) -> str:
        """Generate a detailed deduplication report"""
        report = []
        report.append("# Hemp Products Deduplication Report")
        report.append(f"Generated: {datetime.now().isoformat()}")
        report.append(f"Total products with duplicates: {len(duplicates)}\n")
        
        # Statistics
        total_duplicates = sum(len(dups) for dups in duplicates.values())
        match_type_counts = {'exact': 0, 'fuzzy': 0, 'semantic': 0, 'component': 0}
        
        for product_dups in duplicates.values():
            for dup in product_dups:
                match_type_counts[dup.match_type] += 1
        
        report.append("## Statistics")
        report.append(f"- Total duplicate candidates: {total_duplicates}")
        for match_type, count in match_type_counts.items():
            report.append(f"- {match_type.capitalize()} matches: {count}")
        
        report.append("\n## High Confidence Duplicates (>0.9 score)")
        
        high_confidence = []
        for product_id, dups in duplicates.items():
            for dup in dups:
                if dup.similarity_score > 0.9:
                    high_confidence.append((product_id, dup))
        
        high_confidence.sort(key=lambda x: x[1].similarity_score, reverse=True)
        
        for product_id, dup in high_confidence[:20]:  # Top 20
            report.append(f"\n### Product {product_id} ↔ {dup.product_id}")
            report.append(f"- Match Type: {dup.match_type}")
            report.append(f"- Similarity Score: {dup.similarity_score:.3f}")
            report.append(f"- Product Name: {dup.name}")
            
            if 'shared_components' in dup.match_details:
                report.append("- Shared Components:")
                for comp_type, components in dup.match_details['shared_components'].items():
                    if components:
                        report.append(f"  - {comp_type}: {', '.join(components[:5])}")
        
        return "\n".join(report)


def main():
    """Main execution function"""
    # Database connection
    db_url = os.getenv('DATABASE_URL')
    if not db_url:
        logger.error("DATABASE_URL environment variable not set")
        return
    
    # Initialize deduplicator
    dedup = AdvancedDeduplicator(db_url)
    
    try:
        dedup.connect()
        
        # Example: Find duplicates for a specific product
        product_id = 1234  # Replace with actual product ID
        logger.info(f"Finding duplicates for product {product_id}")
        duplicates = dedup.find_duplicates(product_id)
        
        if duplicates:
            logger.info(f"Found {len(duplicates)} potential duplicates:")
            for dup in duplicates[:5]:  # Show top 5
                logger.info(f"  - Product {dup.product_id}: {dup.name} "
                          f"(Score: {dup.similarity_score:.3f}, Type: {dup.match_type})")
        
        # Batch processing example
        logger.info("\nRunning batch duplicate detection...")
        all_duplicates = dedup.batch_find_duplicates(sample_size=100)
        
        # Generate report
        report = dedup.generate_deduplication_report(all_duplicates)
        
        # Save report
        with open('deduplication_report.md', 'w') as f:
            f.write(report)
        
        logger.info("Report saved to deduplication_report.md")
        
    finally:
        dedup.disconnect()


if __name__ == "__main__":
    main()