"""
Company Validation Pipeline - Validates and scores company data quality
Similar to the product quality control pipeline but for companies
"""
import os
import re
import requests
from typing import Dict, List, Tuple, Optional
from datetime import datetime
from urllib.parse import urlparse
import hashlib
from difflib import SequenceMatcher

class CompanyValidationPipeline:
    def __init__(self):
        # Suspicious/generic company name patterns
        self.generic_patterns = [
            r'^[A-Z][a-z]+(?:Boost|Bloom|Aura|Balance|Plus|Pro|Max|Ultra)$',  # AuraBoost, BloomPlus etc
            r'^(?:Hemp|Cannabis|CBD)\s+(?:Co|Company|Corp|Inc|LLC)$',  # Generic Hemp Co
            r'^Generic\s+\w+$',  # Generic Supplier etc
            r'^Test\s+\w+$',  # Test Company
            r'^\w+\s+Brand$',  # Something Brand
        ]
        
        # Known legitimate hemp companies
        self.known_legitimate = {
            'Manitoba Harvest', 'HempFlax', 'GenCanna', 'HempWood', 
            'Charlotte\'s Web', 'Elixinol', 'CV Sciences', 'Hemp Inc',
            'Nutiva', 'Bob\'s Red Mill', 'Dr<PERSON>\'s', 'Patagonia'
        }
        
        # Business entity suffixes
        self.business_suffixes = {
            'LLC', 'Inc', 'Corp', 'Corporation', 'Company', 'Co', 
            'Ltd', 'Limited', 'GmbH', 'AG', 'SA', 'BV', 'Pty'
        }
        
        # Industry-specific keywords
        self.hemp_keywords = {
            'hemp', 'cannabis', 'cbd', 'cannabinoid', 'fiber', 'textile',
            'nutrition', 'sustainable', 'organic', 'bio'
        }
        
    def validate_company(self, company: Dict) -> Dict:
        """
        Validate a single company and return validation results
        """
        results = {
            'company_id': company.get('id'),
            'company_name': company.get('name'),
            'is_valid': True,
            'quality_score': 0.0,
            'issues': [],
            'warnings': [],
            'suggestions': []
        }
        
        # Run validation checks
        self._check_name_quality(company, results)
        self._check_website_validity(company, results)
        self._check_description_quality(company, results)
        self._check_company_type(company, results)
        self._check_location_data(company, results)
        self._check_verification_status(company, results)
        
        # Calculate overall quality score
        results['quality_score'] = self._calculate_quality_score(results)
        
        # Determine if company is valid
        if len(results['issues']) > 0:
            results['is_valid'] = False
            
        return results
    
    def _check_name_quality(self, company: Dict, results: Dict):
        """Check if company name is legitimate"""
        name = company.get('name', '')
        
        if not name:
            results['issues'].append('Company name is missing')
            return
            
        # Check for generic patterns
        for pattern in self.generic_patterns:
            if re.match(pattern, name):
                results['warnings'].append(f'Company name "{name}" matches generic pattern')
                results['suggestions'].append('Consider verifying this is a real company')
                
        # Check if it's a known legitimate company
        if name in self.known_legitimate:
            results['quality_score'] += 20
            
        # Check for business suffix
        has_suffix = any(name.endswith(f' {suffix}') for suffix in self.business_suffixes)
        if has_suffix:
            results['quality_score'] += 5
            
        # Check name length and complexity
        if len(name) < 3:
            results['issues'].append('Company name too short')
        elif len(name) > 100:
            results['warnings'].append('Company name unusually long')
            
    def _check_website_validity(self, company: Dict, results: Dict):
        """Validate company website"""
        website = company.get('website', '')
        
        if not website:
            results['warnings'].append('No website provided')
            results['suggestions'].append('Add company website for better credibility')
            return
            
        # Check URL format
        try:
            parsed = urlparse(website)
            if not parsed.scheme:
                website = f'https://{website}'
                parsed = urlparse(website)
                
            if parsed.scheme not in ['http', 'https']:
                results['issues'].append('Invalid website URL scheme')
                return
                
            # Check if website is accessible
            try:
                response = requests.head(website, timeout=5, allow_redirects=True)
                if response.status_code == 200:
                    results['quality_score'] += 15
                elif response.status_code >= 400:
                    results['warnings'].append(f'Website returns error code {response.status_code}')
            except requests.RequestException:
                results['warnings'].append('Website is not accessible')
                
        except Exception as e:
            results['issues'].append(f'Invalid website URL format: {str(e)}')
            
    def _check_description_quality(self, company: Dict, results: Dict):
        """Check company description quality"""
        description = company.get('description', '')
        
        if not description:
            results['warnings'].append('No company description')
            results['suggestions'].append('Add a detailed company description')
            return
            
        # Check length
        if len(description) < 20:
            results['warnings'].append('Company description too short')
        elif len(description) > 50:
            results['quality_score'] += 10
            
        # Check for placeholder text
        placeholder_patterns = [
            r'is a hemp product brand',
            r'identified from product names',
            r'Generic.*company',
            r'TODO',
            r'TBD'
        ]
        
        for pattern in placeholder_patterns:
            if re.search(pattern, description, re.IGNORECASE):
                results['warnings'].append('Description appears to be placeholder text')
                results['suggestions'].append('Update with real company information')
                break
                
        # Check for hemp-related keywords
        desc_lower = description.lower()
        keyword_count = sum(1 for keyword in self.hemp_keywords if keyword in desc_lower)
        if keyword_count >= 2:
            results['quality_score'] += 5
            
    def _check_company_type(self, company: Dict, results: Dict):
        """Validate company type"""
        company_type = company.get('company_type', '')
        
        valid_types = ['manufacturer', 'distributor', 'retailer', 'brand', 
                      'processor', 'technology', 'research', 'farm']
        
        if not company_type:
            results['warnings'].append('Company type not specified')
            results['suggestions'].append('Specify company type (manufacturer, distributor, etc.)')
        elif company_type.lower() not in valid_types:
            results['warnings'].append(f'Unusual company type: {company_type}')
            
        if company_type.lower() in ['manufacturer', 'processor', 'farm']:
            results['quality_score'] += 5
            
    def _check_location_data(self, company: Dict, results: Dict):
        """Check company location information"""
        has_location = False
        
        if company.get('country'):
            has_location = True
            results['quality_score'] += 5
            
        if company.get('city') or company.get('state_province'):
            results['quality_score'] += 5
            
        if company.get('latitude') and company.get('longitude'):
            results['quality_score'] += 10
            
        if not has_location:
            results['warnings'].append('No location information provided')
            results['suggestions'].append('Add company location for better credibility')
            
    def _check_verification_status(self, company: Dict, results: Dict):
        """Check verification status and related data"""
        if company.get('verified'):
            results['quality_score'] += 20
            
            # Check if verification is backed by data
            if not company.get('website') and not company.get('description'):
                results['warnings'].append('Company marked as verified but lacks supporting data')
                
        if company.get('founded_year'):
            current_year = datetime.now().year
            founded_year = company.get('founded_year')
            
            if founded_year > current_year:
                results['issues'].append('Founded year is in the future')
            elif founded_year < 1900:
                results['warnings'].append('Founded year seems unusually old for hemp industry')
            else:
                results['quality_score'] += 5
                
    def _calculate_quality_score(self, results: Dict) -> float:
        """Calculate overall quality score (0-100)"""
        base_score = results.get('quality_score', 0)
        
        # Apply penalties
        base_score -= len(results['issues']) * 20
        base_score -= len(results['warnings']) * 5
        
        # Ensure score is between 0 and 100
        return max(0, min(100, base_score))
        
    def find_duplicate_companies(self, companies: List[Dict]) -> List[Tuple[Dict, Dict, float]]:
        """Find potential duplicate companies"""
        duplicates = []
        
        for i, company1 in enumerate(companies):
            for j, company2 in enumerate(companies[i+1:], i+1):
                similarity = self._calculate_similarity(company1, company2)
                
                if similarity > 0.8:  # 80% similarity threshold
                    duplicates.append((company1, company2, similarity))
                    
        return duplicates
        
    def _calculate_similarity(self, company1: Dict, company2: Dict) -> float:
        """Calculate similarity between two companies"""
        scores = []
        
        # Name similarity
        name1 = company1.get('name', '').lower()
        name2 = company2.get('name', '').lower()
        name_similarity = SequenceMatcher(None, name1, name2).ratio()
        scores.append(name_similarity * 0.5)  # Weight: 50%
        
        # Website similarity
        web1 = company1.get('website', '').lower()
        web2 = company2.get('website', '').lower()
        if web1 and web2:
            web_similarity = SequenceMatcher(None, web1, web2).ratio()
            scores.append(web_similarity * 0.3)  # Weight: 30%
            
        # Description similarity
        desc1 = company1.get('description', '').lower()
        desc2 = company2.get('description', '').lower()
        if desc1 and desc2:
            desc_similarity = SequenceMatcher(None, desc1[:200], desc2[:200]).ratio()
            scores.append(desc_similarity * 0.2)  # Weight: 20%
            
        return sum(scores)
        
    def generate_validation_report(self, companies: List[Dict]) -> Dict:
        """Generate comprehensive validation report for all companies"""
        report = {
            'total_companies': len(companies),
            'valid_companies': 0,
            'invalid_companies': 0,
            'average_quality_score': 0,
            'common_issues': {},
            'common_warnings': {},
            'potential_duplicates': [],
            'validation_results': []
        }
        
        total_score = 0
        
        # Validate each company
        for company in companies:
            result = self.validate_company(company)
            report['validation_results'].append(result)
            
            if result['is_valid']:
                report['valid_companies'] += 1
            else:
                report['invalid_companies'] += 1
                
            total_score += result['quality_score']
            
            # Track common issues
            for issue in result['issues']:
                report['common_issues'][issue] = report['common_issues'].get(issue, 0) + 1
                
            for warning in result['warnings']:
                report['common_warnings'][warning] = report['common_warnings'].get(warning, 0) + 1
                
        # Calculate average score
        if companies:
            report['average_quality_score'] = total_score / len(companies)
            
        # Find duplicates
        duplicates = self.find_duplicate_companies(companies)
        report['potential_duplicates'] = [
            {
                'company1': dup[0]['name'],
                'company2': dup[1]['name'],
                'similarity': dup[2]
            }
            for dup in duplicates
        ]
        
        return report