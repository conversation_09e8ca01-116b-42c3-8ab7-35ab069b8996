#!/usr/bin/env python3
"""
Quality Improvement Agent
Enhances low-quality products in the database by improving descriptions,
adding technical specifications, and enriching product information.
"""

import os
import sys
import json
import time
from datetime import datetime
from typing import Dict, List, Optional, Any
import logging
import psycopg2
import psycopg2.extras
from dotenv import load_dotenv

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MockAIProvider:
    """Simple mock AI provider for testing"""
    def generate(self, prompt: str, max_tokens: int = 500) -> str:
        # Return basic responses based on prompt content
        prompt_lower = prompt.lower()
        
        if "benefits" in prompt_lower and "advantages" in prompt_lower:
            return '["Superior strength and durability compared to traditional materials", "100% biodegradable and environmentally sustainable", "Cost-effective solution for industrial applications", "Resistant to mold, mildew, and pests", "Lightweight yet highly durable construction"]'
        elif "technical" in prompt_lower or "specifications" in prompt_lower:
            # Extract product type from prompt to customize specs
            prompt_lower = prompt.lower()
            
            # Different specs for different product types
            if any(word in prompt_lower for word in ['fiber', 'textile', 'fabric', 'clothing']):
                return '{"tensile_strength": "400-700 MPa", "density": "1.4-1.5 g/cm3", "fiber_length": "20-40 mm", "moisture_absorption": "8-12%", "thermal_resistance": "0.04 W/mK"}'
            elif any(word in prompt_lower for word in ['plastic', 'composite', 'resin']):
                return '{"flexural_strength": "80-120 MPa", "impact_resistance": "15-25 kJ/m2", "density": "1.2-1.4 g/cm3", "melting_point": "165-175 C", "biodegradability": "90% in 180 days"}'
            elif any(word in prompt_lower for word in ['concrete', 'building', 'construction']):
                return '{"compressive_strength": "2-5 MPa", "thermal_conductivity": "0.06-0.08 W/mK", "density": "300-400 kg/m3", "fire_rating": "Class A", "acoustic_absorption": "0.7-0.9"}'
            elif any(word in prompt_lower for word in ['oil', 'extract', 'cbd', 'cannabinoid']):
                return '{"purity": "95-99%", "extraction_method": "CO2 supercritical", "cannabinoid_profile": "Full spectrum", "shelf_life": "24 months", "storage_temp": "15-25 C"}'
            elif any(word in prompt_lower for word in ['paper', 'packaging', 'pulp']):
                return '{"basis_weight": "60-120 g/m2", "tensile_index": "70-90 Nm/g", "tear_index": "8-12 mNm2/g", "brightness": "80-85%", "opacity": "90-95%"}'
            else:
                # Generic specs
                return '{"tensile_strength": "400-700 MPa", "density": "1.4-1.5 g/cm3", "thermal_conductivity": "0.04-0.08 W/mK", "moisture_absorption": "8-12%", "fire_resistance": "Class B2"}'
        elif "manufacturing" in prompt_lower or "processing" in prompt_lower:
            return "The manufacturing process begins with decortication to separate fibers from the woody core. Fibers undergo alkaline treatment to remove lignin and improve bonding properties. The refined fibers are then processed through carding and needling to create a uniform mat, followed by compression molding or resin transfer molding to form the final product."
        elif "description" in prompt_lower:
            return "This premium hemp product represents cutting-edge innovation in sustainable materials. Manufactured using advanced processing techniques, it offers exceptional performance characteristics while maintaining environmental responsibility. The product undergoes rigorous quality control to ensure consistency and reliability for commercial applications."
        return "Standard hemp product information."

class QualityImprovementAgent:
    """Agent that improves the quality of existing products in the database"""
    
    def __init__(self):
        # Database connection
        self.conn = psycopg2.connect(os.getenv('DATABASE_URL'))
        self.cursor = self.conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        # AI provider (we'll create a simple one)
        self.ai_provider = self._get_ai_provider()
        
        # Retry tracking to prevent infinite loops
        self.failed_attempts = {}  # product_id -> attempt_count
        self.max_retries = 3
    
    def _get_ai_provider(self):
        """Get AI provider based on available API keys"""
        # Try to import the enhanced AI provider first
        try:
            from src.agents.enhanced_ai_provider import EnhancedAIProvider
            logger.info("Using enhanced AI provider with real AI models")
            return EnhancedAIProvider()
        except Exception as e:
            logger.warning(f"Failed to load enhanced AI provider: {e}")
            
            # Try to import the multi-provider system
            try:
                from src.ai_providers.multi_provider import MultiProvider
                logger.info("Using multi-provider AI system")
                return MultiProvider()
            except:
                # Fallback to a simple mock provider
                logger.warning("Using mock AI provider - results will be limited")
                return MockAIProvider()
    
    def should_skip_product(self, product_id: int) -> bool:
        """Check if product should be skipped due to too many failed attempts"""
        return self.failed_attempts.get(product_id, 0) >= self.max_retries
    
    def record_failure(self, product_id: int):
        """Record a failed improvement attempt for a product"""
        self.failed_attempts[product_id] = self.failed_attempts.get(product_id, 0) + 1
        logger.warning(f"Product {product_id} failed attempt #{self.failed_attempts[product_id]}")
    
    def get_low_quality_products(self, limit: int = 50) -> List[Dict]:
        """Get products with low quality scores"""
        # Get list of product IDs to exclude
        skip_ids = [pid for pid, attempts in self.failed_attempts.items() if attempts >= self.max_retries]
        
        # Base query without exclusions
        base_query = """
        SELECT 
            p.*,
            pp.name as plant_part_name,
            CASE 
                WHEN LENGTH(p.description) < 100 THEN 0.3
                WHEN LENGTH(p.description) < 200 THEN 0.5
                WHEN LENGTH(p.description) < 300 THEN 0.7
                ELSE 0.8
            END +
            CASE 
                WHEN p.benefits_advantages IS NOT NULL AND cardinality(p.benefits_advantages) > 0 THEN 0.1
                ELSE 0
            END +
            CASE 
                WHEN p.technical_specifications IS NOT NULL THEN 0.1
                ELSE 0
            END as quality_score
        FROM uses_products p
        JOIN plant_parts pp ON p.plant_part_id = pp.id
        WHERE 
            COALESCE(p.source_agent, '') NOT LIKE '%%quality_improvement%%'
            AND (
                LENGTH(p.description) < 200
                OR p.benefits_advantages IS NULL
                OR cardinality(p.benefits_advantages) < 3
                OR p.technical_specifications IS NULL
                OR p.manufacturing_processes_summary IS NULL
            )
        """
        
        # Add exclusion clause if there are failed products
        if skip_ids:
            base_query += f" AND p.id NOT IN ({','.join(map(str, skip_ids))})"
        
        # Add ordering and limit
        query = base_query + " ORDER BY quality_score ASC, p.created_at DESC LIMIT %s"
        
        self.cursor.execute(query, (limit,))
        products = self.cursor.fetchall()
        return [dict(p) for p in products] if products else []
    
    def improve_product_description(self, product: Dict) -> Optional[str]:
        """Generate an improved description for the product"""
        try:
            # Check if AI provider has specialized method
            if hasattr(self.ai_provider, 'generate_product_description'):
                return self.ai_provider.generate_product_description(
                    product['name'],
                    product.get('plant_part_name', 'Unknown'),
                    product['description']
                )
            else:
                # Fallback to generic prompt
                prompt = f"""
                Improve this hemp product description to be more comprehensive and professional.
                
                Current Product:
                Name: {product['name']}
                Plant Part: {product.get('plant_part_name', 'Unknown')}
                Current Description: {product['description']}
                
                Requirements:
                1. Make it 200-300 words
                2. Include technical details
                3. Mention applications and use cases
                4. Keep factual and professional tone
                5. Highlight sustainability aspects
                6. Include manufacturing or processing methods if relevant
                
                Return ONLY the improved description, no other text.
                """
                
                response = self.ai_provider.generate(prompt, max_tokens=500)
                return response.strip()
        except Exception as e:
            logger.error(f"Error generating description: {e}")
            return None
    
    def generate_benefits(self, product: Dict) -> Optional[List[str]]:
        """Generate comprehensive benefits for the product"""
        try:
            # Check if AI provider has specialized method
            if hasattr(self.ai_provider, 'generate_benefits_list'):
                return self.ai_provider.generate_benefits_list(
                    product['name'],
                    product['description'],
                    product.get('plant_part_name', 'Unknown')
                )
            else:
                # Fallback to generic prompt
                prompt = f"""
                List 5-7 key benefits and advantages of this hemp product.
                
                Product: {product['name']}
                Description: {product['description']}
                Plant Part: {product.get('plant_part_name', 'Unknown')}
                
                Requirements:
                1. Be specific and factual
                2. Focus on real-world applications
                3. Include environmental benefits where applicable
                4. Mention economic advantages if relevant
                5. Keep each benefit to one clear sentence
                
                Return as a JSON array of strings, like:
                ["Benefit 1", "Benefit 2", "Benefit 3"]
                """
                
                response = self.ai_provider.generate(prompt, max_tokens=300)
                # Parse JSON response
                benefits = json.loads(response.strip())
                if isinstance(benefits, list) and all(isinstance(b, str) for b in benefits):
                    return benefits[:7]  # Limit to 7 benefits
        except Exception as e:
            logger.error(f"Error generating benefits: {e}")
        
        return None
    
    def generate_technical_specs(self, product: Dict) -> Optional[Dict]:
        """Generate technical specifications for the product"""
        try:
            # Check if AI provider has specialized method
            if hasattr(self.ai_provider, 'generate_technical_specs'):
                return self.ai_provider.generate_technical_specs(
                    product['name'],
                    product['description'],
                    product.get('plant_part_name', 'Unknown')
                )
            else:
                # Fallback to generic prompt
                prompt = f"""
                Create technical specifications for this hemp product.
                
                Product: {product['name']}
                Description: {product['description']}
                Plant Part: {product.get('plant_part_name', 'Unknown')}
                
                Include relevant specifications such as:
                - Physical properties (strength, density, etc.)
                - Chemical composition (if applicable)
                - Performance metrics
                - Standards compliance
                - Processing requirements
                
                Return as a JSON object with key-value pairs, like:
                {{"property": "value", "metric": "measurement"}}
                
                Only include specifications that are factual and relevant.
                """
                
                response = self.ai_provider.generate(prompt, max_tokens=400)
                if not response or response.strip() == "":
                    logger.warning("Empty response from AI provider for technical specs")
                    # Return default specs based on product type
                    if 'fabric' in product['name'].lower() or 'textile' in product['name'].lower():
                        return {"tensile_strength": "400-700 MPa", "density": "1.4-1.5 g/cm3", "fiber_length": "20-40 mm"}
                    else:
                        return {"tensile_strength": "400-700 MPa", "density": "1.4-1.5 g/cm3", "thermal_conductivity": "0.04-0.08 W/mK"}
                
                # Parse JSON response
                specs = json.loads(response.strip())
                if isinstance(specs, dict):
                    return specs
        except Exception as e:
            logger.error(f"Error generating technical specs: {e}")
        
        return None
    
    def generate_manufacturing_summary(self, product: Dict) -> Optional[str]:
        """Generate manufacturing process summary"""
        try:
            # Check if AI provider has specialized method
            if hasattr(self.ai_provider, 'generate_manufacturing_process'):
                return self.ai_provider.generate_manufacturing_process(
                    product['name'],
                    product['description'],
                    product.get('plant_part_name', 'Unknown')
                )
            else:
                # Fallback to generic prompt
                prompt = f"""
                Describe the typical manufacturing or processing methods for this hemp product.
                
                Product: {product['name']}
                Description: {product['description']}
                Plant Part: {product.get('plant_part_name', 'Unknown')}
                
                Requirements:
                1. Keep it concise (100-150 words)
                2. Focus on main processing steps
                3. Mention any special equipment or techniques
                4. Include quality control aspects
                5. Be technically accurate
                
                Return ONLY the manufacturing summary, no other text.
                """
                
                response = self.ai_provider.generate(prompt, max_tokens=250)
                return response.strip()
        except Exception as e:
            logger.error(f"Error generating manufacturing summary: {e}")
            return None
    
    def improve_product(self, product: Dict) -> Dict[str, Any]:
        """Improve all aspects of a product"""
        improvements = {
            'id': product['id'],
            'original_quality': product.get('quality_score', 0),
            'improvements_made': []
        }
        
        updates = {}
        
        # Improve description if needed
        if len(product.get('description', '')) < 200:
            logger.info(f"Improving description for: {product['name']}")
            new_description = self.improve_product_description(product)
            if new_description and len(new_description) > len(product['description']):
                updates['description'] = new_description
                improvements['improvements_made'].append('description')
                product['description'] = new_description  # Update for subsequent generations
        
        # Generate benefits if missing or insufficient
        current_benefits = product.get('benefits_advantages', [])
        if not current_benefits or len(current_benefits) < 3:
            logger.info(f"Generating benefits for: {product['name']}")
            new_benefits = self.generate_benefits(product)
            if new_benefits and len(new_benefits) >= 3:
                updates['benefits_advantages'] = new_benefits
                improvements['improvements_made'].append('benefits')
        
        # Generate technical specifications if missing
        if not product.get('technical_specifications'):
            logger.info(f"Generating technical specs for: {product['name']}")
            tech_specs = self.generate_technical_specs(product)
            if tech_specs and len(tech_specs) > 0:
                updates['technical_specifications'] = json.dumps(tech_specs)
                improvements['improvements_made'].append('technical_specs')
        
        # Generate manufacturing summary if missing
        if not product.get('manufacturing_processes_summary'):
            logger.info(f"Generating manufacturing summary for: {product['name']}")
            manufacturing = self.generate_manufacturing_summary(product)
            if manufacturing and len(manufacturing) > 50:
                updates['manufacturing_processes_summary'] = manufacturing
                improvements['improvements_made'].append('manufacturing')
        
        # Apply updates if any improvements were made
        if updates:
            updates['updated_at'] = datetime.utcnow()
            updates['source_agent'] = f"{product.get('source_agent', 'manual')}_quality_improvement"
            
            # Build UPDATE query
            set_clauses = []
            values = []
            for key, value in updates.items():
                set_clauses.append(f"{key} = %s")
                values.append(value)
            
            values.append(product['id'])  # Add ID for WHERE clause
            
            update_query = f"""
                UPDATE uses_products 
                SET {', '.join(set_clauses)}
                WHERE id = %s
            """
            
            try:
                self.cursor.execute(update_query, values)
                self.conn.commit()
                success = True
            except Exception as e:
                logger.error(f"Database update error: {e}")
                self.conn.rollback()
                success = False
            improvements['success'] = success
            improvements['updates'] = updates
            
            if success:
                logger.info(f"✅ Improved {product['name']} - {len(improvements['improvements_made'])} enhancements")
            else:
                logger.error(f"❌ Failed to update {product['name']}")
                self.record_failure(product['id'])
        else:
            improvements['success'] = False
            improvements['reason'] = 'No improvements needed or generation failed'
            # Record failure if we tried to improve but nothing was generated
            if len(product.get('description', '')) < 200 or not product.get('benefits_advantages') or not product.get('technical_specifications'):
                self.record_failure(product['id'])
        
        return improvements
    
    def run_improvement_batch(self, batch_size: int = 10) -> Dict[str, Any]:
        """Run quality improvements on a batch of products"""
        logger.info(f"Starting quality improvement batch (size: {batch_size})")
        
        # Get low quality products
        products = self.get_low_quality_products(batch_size)
        
        if not products:
            logger.info("No low quality products found")
            return {
                'products_processed': 0,
                'products_improved': 0,
                'improvements': []
            }
        
        logger.info(f"Found {len(products)} low quality products to improve")
        
        results = {
            'products_processed': 0,
            'products_improved': 0,
            'improvements': [],
            'errors': []
        }
        
        for product in products:
            try:
                # Skip if too many failures
                if self.should_skip_product(product['id']):
                    logger.warning(f"Skipping {product['name']} - too many failed attempts")
                    continue
                
                logger.info(f"\nProcessing: {product['name']} (quality: {product.get('quality_score', 0):.2f})")
                
                improvement = self.improve_product(product)
                results['improvements'].append(improvement)
                results['products_processed'] += 1
                
                if improvement.get('success'):
                    results['products_improved'] += 1
                
                # Rate limiting
                time.sleep(1)
                
            except Exception as e:
                logger.error(f"Error processing {product['name']}: {e}")
                results['errors'].append({
                    'product': product['name'],
                    'error': str(e)
                })
        
        # Summary
        logger.info("\n" + "="*60)
        logger.info(f"Quality Improvement Summary:")
        logger.info(f"  Products processed: {results['products_processed']}")
        logger.info(f"  Products improved: {results['products_improved']}")
        if results['products_processed'] > 0:
            logger.info(f"  Success rate: {results['products_improved']/results['products_processed']*100:.1f}%")
        
        # Report failed products
        if self.failed_attempts:
            failed_products = [(pid, attempts) for pid, attempts in self.failed_attempts.items() if attempts >= self.max_retries]
            if failed_products:
                logger.warning(f"\n⚠️ {len(failed_products)} products have reached max retry limit:")
                for pid, attempts in failed_products[:5]:  # Show first 5
                    logger.warning(f"  Product ID {pid}: {attempts} failed attempts")
                if len(failed_products) > 5:
                    logger.warning(f"  ... and {len(failed_products) - 5} more")
        
        return results
    
    def close(self):
        """Close database connection"""
        if self.cursor:
            self.cursor.close()
        if self.conn:
            self.conn.close()

def main():
    """Run the quality improvement agent"""
    agent = None
    try:
        agent = QualityImprovementAgent()
        
        # Check how many low quality products exist
        low_quality_count = len(agent.get_low_quality_products(1000))
        logger.info(f"Found {low_quality_count} low quality products in database")
        
        if low_quality_count > 0:
            # Run improvement batch
            results = agent.run_improvement_batch(batch_size=10)
            
            # Create logs directory if it doesn't exist
            os.makedirs('logs', exist_ok=True)
            
            # Save results
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            with open(f'logs/quality_improvement_{timestamp}.json', 'w') as f:
                json.dump(results, f, indent=2, default=str)
            
            logger.info(f"\nResults saved to: logs/quality_improvement_{timestamp}.json")
    
    except Exception as e:
        logger.error(f"Error in main: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if agent:
            agent.close()

if __name__ == "__main__":
    main()