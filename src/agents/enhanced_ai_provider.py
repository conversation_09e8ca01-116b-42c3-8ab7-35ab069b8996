#!/usr/bin/env python3
"""
Enhanced AI Provider for Quality Improvement
Uses real AI providers with fallback and specialized prompts
"""

import json
import logging
from typing import Optional, List, Dict
import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from ai_providers.multi_provider import MultiProvider
except ImportError:
    try:
        from src.ai_providers.multi_provider import MultiProvider
    except ImportError:
        # Try absolute import
        sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
        from ai_providers.multi_provider import MultiProvider

logger = logging.getLogger(__name__)


class EnhancedAIProvider:
    """Enhanced AI provider that uses real AI models for quality improvement"""
    
    def __init__(self):
        self.provider = MultiProvider()
        self.system_prompt = """You are a hemp product expert assistant helping improve product descriptions and specifications.
        You must provide factual, professional, and detailed information about hemp products.
        Focus on industrial and commercial applications, avoiding any recreational or psychoactive references.
        Always emphasize sustainability, environmental benefits, and practical applications."""
    
    def generate(self, prompt: str, max_tokens: int = 500) -> str:
        """Generate response using real AI providers"""
        try:
            # Use the multi-provider to generate response
            response = self.provider.generate(
                prompt=prompt,
                system_prompt=self.system_prompt,
                temperature=0.7,
                max_tokens=max_tokens
            )
            
            if response.success:
                return response.content.strip()
            else:
                logger.error(f"AI generation failed: {response.error}")
                # Fallback to basic response
                return self._get_fallback_response(prompt)
                
        except Exception as e:
            logger.error(f"Error in AI generation: {e}")
            return self._get_fallback_response(prompt)
    
    def _get_fallback_response(self, prompt: str) -> str:
        """Provide fallback responses when AI fails"""
        prompt_lower = prompt.lower()
        
        if "benefits" in prompt_lower and "advantages" in prompt_lower:
            return json.dumps([
                "Superior strength and durability compared to traditional materials",
                "100% biodegradable and environmentally sustainable",
                "Cost-effective solution for industrial applications",
                "Resistant to mold, mildew, and pests",
                "Lightweight yet highly durable construction",
                "Carbon-negative production process",
                "Versatile applications across multiple industries"
            ])
        
        elif "technical" in prompt_lower or "specifications" in prompt_lower:
            # Extract product context
            if any(word in prompt_lower for word in ['fiber', 'textile', 'fabric']):
                return json.dumps({
                    "tensile_strength": "250-750 MPa",
                    "density": "1.4-1.5 g/cm³",
                    "fiber_length": "15-50 mm",
                    "moisture_absorption": "8-12%",
                    "thermal_resistance": "0.04-0.06 W/mK",
                    "breaking_elongation": "1.6-4.0%"
                })
            elif any(word in prompt_lower for word in ['plastic', 'composite']):
                return json.dumps({
                    "flexural_strength": "40-80 MPa",
                    "impact_resistance": "15-25 kJ/m²",
                    "density": "1.2-1.4 g/cm³",
                    "melting_point": "165-175°C",
                    "biodegradability": "90% in 180 days",
                    "UV_resistance": "Good"
                })
            else:
                return json.dumps({
                    "tensile_strength": "Variable",
                    "density": "Product-specific",
                    "sustainability_rating": "A+",
                    "biodegradability": "100%",
                    "carbon_footprint": "Negative"
                })
        
        elif "manufacturing" in prompt_lower or "processing" in prompt_lower:
            return """The manufacturing process utilizes state-of-the-art technology to transform raw hemp materials into high-quality products. 
            The process begins with careful selection and preparation of hemp fibers, followed by specialized treatment to enhance performance characteristics. 
            Advanced processing techniques ensure consistent quality while maintaining the natural benefits of hemp. 
            Quality control measures are implemented at every stage to guarantee product reliability and performance."""
        
        elif "description" in prompt_lower:
            return """This innovative hemp product represents the latest advancement in sustainable materials technology. 
            Engineered for superior performance and environmental responsibility, it offers an ideal solution for modern industrial applications. 
            The product combines the natural strength and versatility of hemp with cutting-edge processing techniques to deliver exceptional value. 
            Suitable for a wide range of commercial applications, it provides a sustainable alternative to traditional materials while maintaining or exceeding performance standards."""
        
        return "Premium hemp product with exceptional quality and sustainability features."
    
    def generate_product_description(self, product_name: str, plant_part: str, current_desc: str) -> str:
        """Generate an enhanced product description"""
        prompt = f"""Create a comprehensive, professional product description for this hemp product.

Product Name: {product_name}
Plant Part: {plant_part}
Current Description: {current_desc}

Requirements:
1. Write 200-300 words
2. Include specific technical details and applications
3. Mention sustainability and environmental benefits
4. Describe typical use cases and industries
5. Maintain professional, factual tone
6. Avoid any recreational or psychoactive references

Provide ONLY the improved description text, no additional formatting or explanation."""

        return self.generate(prompt, max_tokens=500)
    
    def generate_benefits_list(self, product_name: str, description: str, plant_part: str) -> List[str]:
        """Generate a list of product benefits"""
        prompt = f"""List 5-7 specific benefits and advantages of this hemp product.

Product: {product_name}
Description: {description}
Plant Part: {plant_part}

Requirements:
1. Be specific and factual
2. Focus on practical, real-world advantages
3. Include environmental and sustainability benefits
4. Mention economic or performance advantages
5. Keep each benefit to one clear sentence

Return ONLY a JSON array of benefit strings, like:
["Benefit 1", "Benefit 2", "Benefit 3", ...]"""

        response = self.generate(prompt, max_tokens=300)
        try:
            benefits = json.loads(response)
            if isinstance(benefits, list):
                return benefits[:7]  # Limit to 7 benefits
        except:
            logger.error(f"Failed to parse benefits JSON: {response}")
        
        # Fallback
        return [
            "Environmentally sustainable and renewable resource",
            "Superior strength-to-weight ratio",
            "Natural resistance to pests and diseases",
            "Carbon-negative production process",
            "Versatile applications across industries"
        ]
    
    def generate_technical_specs(self, product_name: str, description: str, plant_part: str) -> Dict:
        """Generate technical specifications"""
        prompt = f"""Create detailed technical specifications for this hemp product.

Product: {product_name}
Description: {description}
Plant Part: {plant_part}

Include relevant specifications such as:
- Physical properties (strength, density, dimensions)
- Chemical composition (if applicable)
- Performance metrics
- Environmental specifications
- Processing requirements

Return ONLY a JSON object with specification key-value pairs, like:
{{"property": "value", "metric": "measurement"}}"""

        response = self.generate(prompt, max_tokens=400)
        try:
            specs = json.loads(response)
            if isinstance(specs, dict):
                return specs
        except:
            logger.error(f"Failed to parse technical specs JSON: {response}")
        
        # Fallback based on plant part
        if "fiber" in plant_part.lower() or "bast" in plant_part.lower():
            return {
                "tensile_strength": "250-750 MPa",
                "density": "1.4-1.5 g/cm³",
                "fiber_length": "15-50 mm",
                "moisture_content": "10-12%"
            }
        elif "seed" in plant_part.lower():
            return {
                "protein_content": "25-35%",
                "oil_content": "25-35%",
                "fiber_content": "10-15%",
                "omega_ratio": "3:1 (omega-6:omega-3)"
            }
        else:
            return {
                "sustainability_rating": "A+",
                "biodegradability": "100%",
                "renewable_resource": "Yes",
                "carbon_footprint": "Negative"
            }
    
    def generate_manufacturing_process(self, product_name: str, description: str, plant_part: str) -> str:
        """Generate manufacturing process description"""
        prompt = f"""Describe the typical manufacturing or processing methods for this hemp product.

Product: {product_name}
Description: {description}
Plant Part: {plant_part}

Requirements:
1. Keep it concise (100-150 words)
2. Focus on main processing steps
3. Mention quality control measures
4. Include any special techniques or equipment
5. Be technically accurate

Provide ONLY the manufacturing process description, no additional formatting."""

        return self.generate(prompt, max_tokens=250)