#!/usr/bin/env python3
"""
API-Based Hybrid Product Agent - Discovers hemp combined with other materials
Focuses on composite materials, blended products, and multi-material innovations
"""

import json
import time
import requests
from datetime import datetime
from typing import Dict, List, Optional, Set
import os
import re
import random
from difflib import SequenceMatcher

class APIHybridProductAgent:
    """Hybrid hemp product discovery with duplicate prevention"""
    
    def __init__(self):
        self.name = "API Hybrid Product Agent"
        self.version = "1.0.0"
        self.discovered_count = 0
        
        # Supabase configuration
        self.supabase_url = "https://ktoqznqmlnxrtvubewyz.supabase.co"
        self.service_role_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt0b3F6bnFtbG54cnR2dWJld3l6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODQ5MTc3NiwiZXhwIjoyMDY0MDY3Nzc2fQ.hxEiOy6xebv7I4CYJEU49oCEXRvmU6iz2l8Kg-NnvTs"
        
        self.headers = {
            "apikey": self.service_role_key,
            "Authorization": f"Bearer {self.service_role_key}",
            "Content-Type": "application/json",
            "Prefer": "return=minimal"
        }
        
        # Cache for existing products
        self.existing_products_cache = set()
        self.normalized_cache = {}
        self.load_existing_products()
        
        # Hybrid material combinations
        self.hybrid_combinations = [
            # Hemp + Natural Fibers
            {
                "category": "natural_fiber_blends",
                "subcategory": "plant_fibers",
                "combinations": [
                    {"name": "Hemp-Cotton Blend", "partner": "Cotton", "ratio": "50/50"},
                    {"name": "Hemp-Bamboo Composite", "partner": "Bamboo", "ratio": "60/40"},
                    {"name": "Hemp-Jute Hybrid", "partner": "Jute", "ratio": "70/30"},
                    {"name": "Hemp-Flax Blend", "partner": "Flax", "ratio": "55/45"},
                    {"name": "Hemp-Sisal Composite", "partner": "Sisal", "ratio": "65/35"}
                ]
            },
            {
                "category": "natural_fiber_blends",
                "subcategory": "animal_fibers",
                "combinations": [
                    {"name": "Hemp-Wool Blend", "partner": "Wool", "ratio": "40/60"},
                    {"name": "Hemp-Silk Hybrid", "partner": "Silk", "ratio": "80/20"},
                    {"name": "Hemp-Alpaca Mix", "partner": "Alpaca", "ratio": "50/50"},
                    {"name": "Hemp-Cashmere Blend", "partner": "Cashmere", "ratio": "70/30"},
                    {"name": "Hemp-Mohair Composite", "partner": "Mohair", "ratio": "60/40"}
                ]
            },
            # Hemp + Polymers
            {
                "category": "polymer_composites",
                "subcategory": "thermoplastics",
                "combinations": [
                    {"name": "Hemp-PP Composite", "partner": "Polypropylene", "ratio": "30/70"},
                    {"name": "Hemp-PE Blend", "partner": "Polyethylene", "ratio": "25/75"},
                    {"name": "Hemp-PLA Hybrid", "partner": "PLA Bioplastic", "ratio": "40/60"},
                    {"name": "Hemp-ABS Composite", "partner": "ABS Plastic", "ratio": "20/80"},
                    {"name": "Hemp-Nylon Blend", "partner": "Nylon", "ratio": "35/65"}
                ]
            },
            {
                "category": "polymer_composites",
                "subcategory": "thermosets",
                "combinations": [
                    {"name": "Hemp-Epoxy Composite", "partner": "Epoxy Resin", "ratio": "45/55"},
                    {"name": "Hemp-Polyester Hybrid", "partner": "Polyester Resin", "ratio": "50/50"},
                    {"name": "Hemp-Vinyl Ester Blend", "partner": "Vinyl Ester", "ratio": "40/60"},
                    {"name": "Hemp-Phenolic Composite", "partner": "Phenolic Resin", "ratio": "35/65"},
                    {"name": "Hemp-Polyurethane Mix", "partner": "Polyurethane", "ratio": "30/70"}
                ]
            },
            # Hemp + Metals
            {
                "category": "metal_composites",
                "subcategory": "light_metals",
                "combinations": [
                    {"name": "Hemp-Aluminum Matrix", "partner": "Aluminum", "ratio": "15/85"},
                    {"name": "Hemp-Magnesium Composite", "partner": "Magnesium", "ratio": "20/80"},
                    {"name": "Hemp-Titanium Hybrid", "partner": "Titanium", "ratio": "10/90"},
                    {"name": "Hemp-Zinc Alloy", "partner": "Zinc", "ratio": "25/75"},
                    {"name": "Hemp-Copper Blend", "partner": "Copper", "ratio": "18/82"}
                ]
            },
            {
                "category": "metal_composites",
                "subcategory": "steel_alloys",
                "combinations": [
                    {"name": "Hemp-Steel Reinforcement", "partner": "Steel", "ratio": "12/88"},
                    {"name": "Hemp-Stainless Composite", "partner": "Stainless Steel", "ratio": "8/92"},
                    {"name": "Hemp-Iron Matrix", "partner": "Cast Iron", "ratio": "15/85"},
                    {"name": "Hemp-Alloy Steel Mix", "partner": "Alloy Steel", "ratio": "10/90"},
                    {"name": "Hemp-Carbon Steel Hybrid", "partner": "Carbon Steel", "ratio": "14/86"}
                ]
            },
            # Hemp + Ceramics
            {
                "category": "ceramic_composites",
                "subcategory": "traditional_ceramics",
                "combinations": [
                    {"name": "Hemp-Clay Composite", "partner": "Clay", "ratio": "30/70"},
                    {"name": "Hemp-Porcelain Blend", "partner": "Porcelain", "ratio": "20/80"},
                    {"name": "Hemp-Terracotta Mix", "partner": "Terracotta", "ratio": "35/65"},
                    {"name": "Hemp-Earthenware Hybrid", "partner": "Earthenware", "ratio": "25/75"},
                    {"name": "Hemp-Stoneware Composite", "partner": "Stoneware", "ratio": "28/72"}
                ]
            },
            {
                "category": "ceramic_composites",
                "subcategory": "advanced_ceramics",
                "combinations": [
                    {"name": "Hemp-Alumina Composite", "partner": "Aluminum Oxide", "ratio": "15/85"},
                    {"name": "Hemp-Zirconia Hybrid", "partner": "Zirconium Oxide", "ratio": "10/90"},
                    {"name": "Hemp-Silicon Carbide", "partner": "SiC", "ratio": "12/88"},
                    {"name": "Hemp-Boron Nitride Mix", "partner": "BN", "ratio": "8/92"},
                    {"name": "Hemp-Graphene Blend", "partner": "Graphene", "ratio": "5/95"}
                ]
            },
            # Hemp + Glass
            {
                "category": "glass_composites",
                "subcategory": "fiber_glass",
                "combinations": [
                    {"name": "Hemp-Fiberglass Hybrid", "partner": "Glass Fiber", "ratio": "40/60"},
                    {"name": "Hemp-E-Glass Composite", "partner": "E-Glass", "ratio": "35/65"},
                    {"name": "Hemp-S-Glass Blend", "partner": "S-Glass", "ratio": "30/70"},
                    {"name": "Hemp-AR-Glass Mix", "partner": "AR-Glass", "ratio": "45/55"},
                    {"name": "Hemp-C-Glass Composite", "partner": "C-Glass", "ratio": "38/62"}
                ]
            },
            {
                "category": "glass_composites",
                "subcategory": "glass_matrix",
                "combinations": [
                    {"name": "Hemp-Glass Powder", "partner": "Glass Powder", "ratio": "25/75"},
                    {"name": "Hemp-Glass Bead Mix", "partner": "Glass Beads", "ratio": "20/80"},
                    {"name": "Hemp-Recycled Glass", "partner": "Recycled Glass", "ratio": "30/70"},
                    {"name": "Hemp-Tempered Glass", "partner": "Tempered Glass", "ratio": "15/85"},
                    {"name": "Hemp-Laminated Glass", "partner": "Laminated Glass", "ratio": "10/90"}
                ]
            },
            # Hemp + Rubber
            {
                "category": "rubber_composites",
                "subcategory": "natural_rubber",
                "combinations": [
                    {"name": "Hemp-Natural Rubber", "partner": "Natural Rubber", "ratio": "35/65"},
                    {"name": "Hemp-Latex Blend", "partner": "Latex", "ratio": "25/75"},
                    {"name": "Hemp-Guayule Mix", "partner": "Guayule Rubber", "ratio": "40/60"},
                    {"name": "Hemp-Chicle Composite", "partner": "Chicle", "ratio": "30/70"},
                    {"name": "Hemp-Balata Hybrid", "partner": "Balata", "ratio": "28/72"}
                ]
            },
            {
                "category": "rubber_composites",
                "subcategory": "synthetic_rubber",
                "combinations": [
                    {"name": "Hemp-SBR Composite", "partner": "Styrene-Butadiene", "ratio": "20/80"},
                    {"name": "Hemp-EPDM Blend", "partner": "EPDM Rubber", "ratio": "25/75"},
                    {"name": "Hemp-Neoprene Mix", "partner": "Neoprene", "ratio": "22/78"},
                    {"name": "Hemp-Silicone Hybrid", "partner": "Silicone Rubber", "ratio": "18/82"},
                    {"name": "Hemp-Nitrile Composite", "partner": "Nitrile Rubber", "ratio": "24/76"}
                ]
            },
            # Hemp + Concrete
            {
                "category": "concrete_composites",
                "subcategory": "structural_concrete",
                "combinations": [
                    {"name": "Hemp-Portland Cement", "partner": "Portland Cement", "ratio": "25/75"},
                    {"name": "Hemp-Fly Ash Concrete", "partner": "Fly Ash", "ratio": "30/70"},
                    {"name": "Hemp-Slag Cement", "partner": "Slag Cement", "ratio": "35/65"},
                    {"name": "Hemp-Silica Fume Mix", "partner": "Silica Fume", "ratio": "20/80"},
                    {"name": "Hemp-Limestone Concrete", "partner": "Limestone", "ratio": "40/60"}
                ]
            },
            {
                "category": "concrete_composites",
                "subcategory": "specialty_concrete",
                "combinations": [
                    {"name": "Hemp-Geopolymer", "partner": "Geopolymer", "ratio": "28/72"},
                    {"name": "Hemp-Ferrocement", "partner": "Ferrocement", "ratio": "15/85"},
                    {"name": "Hemp-Pervious Concrete", "partner": "Pervious Mix", "ratio": "32/68"},
                    {"name": "Hemp-Lightweight Mix", "partner": "Lightweight Aggregate", "ratio": "45/55"},
                    {"name": "Hemp-Fiber Concrete", "partner": "Fiber Reinforced", "ratio": "38/62"}
                ]
            },
            # Hemp + Wood
            {
                "category": "wood_composites",
                "subcategory": "engineered_wood",
                "combinations": [
                    {"name": "Hemp-MDF Composite", "partner": "MDF", "ratio": "40/60"},
                    {"name": "Hemp-OSB Blend", "partner": "OSB", "ratio": "35/65"},
                    {"name": "Hemp-Plywood Hybrid", "partner": "Plywood", "ratio": "30/70"},
                    {"name": "Hemp-Particleboard", "partner": "Particleboard", "ratio": "45/55"},
                    {"name": "Hemp-LVL Composite", "partner": "LVL", "ratio": "25/75"}
                ]
            },
            {
                "category": "wood_composites",
                "subcategory": "solid_wood",
                "combinations": [
                    {"name": "Hemp-Hardwood Blend", "partner": "Hardwood", "ratio": "20/80"},
                    {"name": "Hemp-Softwood Mix", "partner": "Softwood", "ratio": "25/75"},
                    {"name": "Hemp-Bamboo Lumber", "partner": "Bamboo Lumber", "ratio": "50/50"},
                    {"name": "Hemp-Cork Composite", "partner": "Cork", "ratio": "40/60"},
                    {"name": "Hemp-Reclaimed Wood", "partner": "Reclaimed Wood", "ratio": "35/65"}
                ]
            },
            # Hemp + Foam
            {
                "category": "foam_composites",
                "subcategory": "rigid_foam",
                "combinations": [
                    {"name": "Hemp-PU Foam", "partner": "Polyurethane Foam", "ratio": "30/70"},
                    {"name": "Hemp-XPS Composite", "partner": "Extruded Polystyrene", "ratio": "25/75"},
                    {"name": "Hemp-EPS Blend", "partner": "Expanded Polystyrene", "ratio": "35/65"},
                    {"name": "Hemp-PIR Foam", "partner": "Polyisocyanurate", "ratio": "20/80"},
                    {"name": "Hemp-Phenolic Foam", "partner": "Phenolic Foam", "ratio": "28/72"}
                ]
            },
            {
                "category": "foam_composites",
                "subcategory": "flexible_foam",
                "combinations": [
                    {"name": "Hemp-Memory Foam", "partner": "Memory Foam", "ratio": "15/85"},
                    {"name": "Hemp-Latex Foam", "partner": "Latex Foam", "ratio": "40/60"},
                    {"name": "Hemp-EVA Foam", "partner": "EVA Foam", "ratio": "22/78"},
                    {"name": "Hemp-Neoprene Foam", "partner": "Neoprene Foam", "ratio": "18/82"},
                    {"name": "Hemp-PVC Foam", "partner": "PVC Foam", "ratio": "25/75"}
                ]
            }
        ]
        
        # Product templates by category
        self.product_templates = {
            "natural_fiber_blends": [
                "{} Textile Material",
                "{} Fabric Blend",
                "{} Natural Composite"
            ],
            "polymer_composites": [
                "{} Polymer Composite",
                "{} Plastic Blend",
                "{} Reinforced Polymer"
            ],
            "metal_composites": [
                "{} Metal Matrix Composite",
                "{} Reinforced Alloy",
                "{} Hybrid Metal Material"
            ],
            "ceramic_composites": [
                "{} Ceramic Composite",
                "{} Advanced Ceramic",
                "{} Hybrid Ceramic Material"
            ],
            "glass_composites": [
                "{} Glass Composite",
                "{} Reinforced Glass",
                "{} Glass Fiber Hybrid"
            ],
            "rubber_composites": [
                "{} Rubber Compound",
                "{} Elastomer Blend",
                "{} Reinforced Rubber"
            ],
            "concrete_composites": [
                "{} Concrete Mix",
                "{} Cementitious Composite",
                "{} Reinforced Concrete"
            ],
            "wood_composites": [
                "{} Wood Composite",
                "{} Engineered Lumber",
                "{} Hybrid Wood Product"
            ],
            "foam_composites": [
                "{} Foam Composite",
                "{} Cellular Material",
                "{} Hybrid Insulation"
            ]
        }
        
        # Industry mapping varies by hybrid type
        self.industry_mappings = {
            "natural_fiber_blends": 2,     # Textiles
            "polymer_composites": 4,       # Materials/Plastics
            "metal_composites": 19,        # Industrial
            "ceramic_composites": 5,       # Construction
            "glass_composites": 4,         # Materials
            "rubber_composites": 6,        # Automotive
            "concrete_composites": 5,      # Construction
            "wood_composites": 5,          # Construction
            "foam_composites": 4           # Materials
        }

    def load_existing_products(self):
        """Load existing products into cache"""
        print("Loading existing products into cache...")
        offset = 0
        limit = 1000
        
        while True:
            response = requests.get(
                f"{self.supabase_url}/rest/v1/uses_products",
                headers=self.headers,
                params={
                    "select": "name",
                    "order": "id",
                    "offset": offset,
                    "limit": limit
                }
            )
            
            if response.status_code != 200 or not response.json():
                break
                
            batch = response.json()
            for product in batch:
                name = product['name']
                self.existing_products_cache.add(name.lower())
                normalized = self.normalize_name(name)
                self.normalized_cache[normalized.lower()] = name
            
            if len(batch) < limit:
                break
            offset += limit
        
        print(f"Loaded {len(self.existing_products_cache)} existing products")

    def normalize_name(self, name: str) -> str:
        """Normalize product name"""
        name = ' '.join(name.split())
        name = re.sub(r'\b(\w+)\s+\1\b', r'\1', name)
        name = name.replace('Hemp-Based', 'Hemp')
        name = name.replace('Hemp-Derived', 'Hemp')
        name = re.sub(r'(\w+)\s+\1$', r'\1', name)
        return name.strip()

    def check_duplicate_advanced(self, name: str, threshold: float = 0.85) -> bool:
        """Advanced duplicate detection"""
        name_lower = name.lower()
        normalized = self.normalize_name(name)
        normalized_lower = normalized.lower()
        
        if name_lower in self.existing_products_cache:
            return True
        
        if normalized_lower in self.normalized_cache:
            print(f"  ⚠️  Similar to existing: {self.normalized_cache[normalized_lower]}")
            return True
        
        for existing in self.existing_products_cache:
            similarity = SequenceMatcher(None, normalized_lower, existing).ratio()
            if similarity > threshold:
                print(f"  ⚠️  Too similar ({similarity:.2f}) to existing product")
                return True
        
        return False

    def get_plant_part_for_category(self, category: str) -> int:
        """Get appropriate plant part ID for hybrid category"""
        mapping = {
            "natural_fiber_blends": 1,    # Fiber for textiles
            "polymer_composites": 1,      # Fiber for reinforcement
            "metal_composites": 1,        # Fiber for MMC
            "ceramic_composites": 1,      # Fiber for CMC
            "glass_composites": 1,        # Fiber for GFRP
            "rubber_composites": 1,       # Fiber for reinforcement
            "concrete_composites": 3,     # Stem/hurds for aggregate
            "wood_composites": 3,         # Stem for structure
            "foam_composites": 3          # Stem for cellular structure
        }
        return mapping.get(category, 1)

    def generate_hybrid_description(self, combo_name: str, partner: str, ratio: str,
                                   category: str, subcategory: str) -> str:
        """Generate detailed hybrid product description"""
        descriptions = {
            "natural_fiber_blends": f"Advanced {combo_name} engineered for {subcategory.replace('_', ' ')} applications. "
                                   f"This {ratio} blend combines hemp's strength and sustainability with {partner}'s "
                                   f"unique properties, creating superior textile materials for modern applications.",
            
            "polymer_composites": f"High-performance {combo_name} designed for {subcategory.replace('_', ' ')} manufacturing. "
                                 f"The {ratio} ratio optimizes hemp fiber reinforcement in {partner} matrix, "
                                 f"delivering enhanced mechanical properties with reduced environmental impact.",
            
            "metal_composites": f"Innovative {combo_name} developed for {subcategory.replace('_', ' ')} industries. "
                               f"Hemp fibers ({ratio} blend) reinforce {partner} matrix, creating lightweight "
                               f"metal matrix composites with excellent strength-to-weight ratios.",
            
            "ceramic_composites": f"Advanced {combo_name} formulated for {subcategory.replace('_', ' ')} applications. "
                                 f"Hemp integration ({ratio}) in {partner} enhances toughness and thermal "
                                 f"shock resistance while maintaining ceramic's inherent properties.",
            
            "glass_composites": f"Hybrid {combo_name} engineered for {subcategory.replace('_', ' ')} products. "
                               f"The {ratio} combination of hemp and {partner} creates composite materials "
                               f"with superior impact resistance and environmental sustainability.",
            
            "rubber_composites": f"Reinforced {combo_name} optimized for {subcategory.replace('_', ' ')} uses. "
                                f"Hemp fibers ({ratio} blend) enhance {partner}'s elasticity and durability "
                                f"while adding natural antimicrobial and UV-resistant properties.",
            
            "concrete_composites": f"Sustainable {combo_name} developed for {subcategory.replace('_', ' ')} construction. "
                                  f"Hemp aggregate ({ratio}) in {partner} creates lightweight, insulating "
                                  f"concrete with excellent carbon sequestration properties.",
            
            "wood_composites": f"Engineered {combo_name} designed for {subcategory.replace('_', ' ')} applications. "
                              f"The {ratio} blend of hemp and {partner} produces composite lumber with "
                              f"enhanced dimensional stability and environmental resistance.",
            
            "foam_composites": f"Innovative {combo_name} created for {subcategory.replace('_', ' ')} insulation. "
                              f"Hemp reinforcement ({ratio}) in {partner} foam improves structural integrity "
                              f"while maintaining excellent thermal and acoustic properties."
        }
        
        return descriptions.get(category, f"Hybrid {combo_name} material. {ratio} blend of hemp and {partner}.")

    def generate_hybrid_benefits(self, category: str, partner: str) -> str:
        """Generate benefits based on hybrid combination"""
        base_benefits = ["Hybrid advantages", "Sustainable blend", "Enhanced properties"]
        
        category_benefits = {
            "natural_fiber_blends": ["Superior drape", "Moisture management", "Natural comfort"],
            "polymer_composites": ["Reduced weight", "Improved stiffness", "Bio-content"],
            "metal_composites": ["Vibration dampening", "Fatigue resistance", "Thermal management"],
            "ceramic_composites": ["Crack resistance", "Thermal shock resistance", "Lightweight"],
            "glass_composites": ["Impact absorption", "Electrical insulation", "Weather resistance"],
            "rubber_composites": ["Enhanced elasticity", "Tear resistance", "Natural properties"],
            "concrete_composites": ["Carbon negative", "Thermal mass", "Breathability"],
            "wood_composites": ["Moisture stability", "Pest resistance", "Formaldehyde-free"],
            "foam_composites": ["Fire resistance", "Acoustic absorption", "Structural support"]
        }
        
        # Add partner-specific benefit
        partner_benefit = f"Combined with {partner}"
        
        benefits = base_benefits + category_benefits.get(category, ["Material innovation"])
        benefits.append(partner_benefit)
        return "{" + ",".join([f'"{b}"' for b in benefits[:6]]) + "}"  # Limit to 6 benefits

    def create_hybrid_products(self, hybrid_group: Dict) -> List[Dict]:
        """Create products from hybrid combination group"""
        products = []
        category = hybrid_group["category"]
        subcategory = hybrid_group["subcategory"]
        
        for combo in hybrid_group["combinations"]:
            # Generate 1-2 variations per combination
            num_variations = random.randint(1, 2)
            
            for i in range(num_variations):
                templates = self.product_templates.get(category, ["{} Hybrid Material"])
                template = random.choice(templates)
                
                # Create product name
                if i == 0:
                    product_name = template.format(combo["name"])
                else:
                    modifiers = ["Advanced", "Premium", "High-Performance", "Engineered", "Sustainable"]
                    modifier = random.choice(modifiers)
                    product_name = template.format(f"{modifier} {combo['name']}")
                
                product_name = self.normalize_name(product_name)
                
                # Check for duplicates
                if self.check_duplicate_advanced(product_name):
                    continue
                
                # Determine plant part
                plant_part_id = self.get_plant_part_for_category(category)
                
                # Get industry ID
                industry_id = self.industry_mappings.get(category, 4)
                
                # Calculate confidence score (0.8-0.95 for hybrids - higher confidence)
                confidence_score = round(0.8 + (random.random() * 0.15), 2)
                
                product_data = {
                    'name': product_name,
                    'description': self.generate_hybrid_description(
                        combo["name"], combo["partner"], combo["ratio"], category, subcategory
                    ),
                    'industry_sub_category_id': industry_id,
                    'plant_part_id': plant_part_id,
                    'confidence_score': confidence_score,
                    'image_url': None,
                    'benefits_advantages': self.generate_hybrid_benefits(category, combo["partner"]),
                    'technical_specifications': json.dumps({
                        "category": category,
                        "subcategory": subcategory,
                        "material_type": "Hybrid Composite",
                        "base_material": combo["name"],
                        "partner_material": combo["partner"],
                        "blend_ratio": combo["ratio"],
                        "processing_methods": ["Compression molding", "Extrusion", "Pultrusion"],
                        "applications": subcategory.replace('_', ' ').title()
                    }),
                    'source_type': 'ai_agent',
                    'source_agent': self.name,
                    'source_url': f"Hybrid: {category}/{subcategory}"
                }
                
                products.append(product_data)
                
                # Add to cache immediately
                self.existing_products_cache.add(product_name.lower())
                self.normalized_cache[product_name.lower()] = product_name
        
        return products

    def save_product_api(self, product_data: Dict) -> bool:
        """Save product using REST API"""
        try:
            response = requests.post(
                f"{self.supabase_url}/rest/v1/uses_products",
                headers=self.headers,
                json=product_data
            )
            
            if response.status_code in [200, 201, 204]:
                self.discovered_count += 1
                return True
            else:
                print(f"Failed to save product: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"Error saving product: {e}")
            return False

    def run_discovery(self) -> Dict:
        """Run hybrid product discovery"""
        start_time = time.time()
        total_saved = 0
        total_duplicates = 0
        total_errors = 0
        
        print(f"\n🔀 {self.name} - Starting Discovery Cycle")
        print(f"Hybrid categories: {len(self.hybrid_combinations)}")
        print("=" * 60)
        
        for idx, hybrid_group in enumerate(self.hybrid_combinations, 1):
            category = hybrid_group["category"]
            subcategory = hybrid_group["subcategory"]
            num_combos = len(hybrid_group["combinations"])
            
            print(f"\n[{idx}/{len(self.hybrid_combinations)}] {category.title().replace('_', ' ')} - {subcategory.title().replace('_', ' ')}")
            print(f"  Combinations: {num_combos}")
            
            try:
                # Create products from hybrid group
                products = self.create_hybrid_products(hybrid_group)
                print(f"  Generated {len(products)} product variations")
                
                # Save each product
                saved = 0
                for product in products:
                    if self.save_product_api(product):
                        saved += 1
                        total_saved += 1
                        print(f"  ✅ {product['name']}")
                    else:
                        total_errors += 1
                
                # Track duplicates prevented
                expected = num_combos * 1.5  # Average expected variations
                prevented = max(0, expected - len(products))
                total_duplicates += int(prevented)
                
                print(f"  Saved {saved} new products")
                
                # Rate limiting
                time.sleep(0.5)
                
            except Exception as e:
                print(f"  ❌ Error processing category: {e}")
                total_errors += 1
        
        # Summary
        duration = time.time() - start_time
        print(f"\n{'=' * 60}")
        print(f"Discovery Complete!")
        print(f"  Duration: {duration:.1f} seconds")
        print(f"  Categories processed: {len(self.hybrid_combinations)}")
        print(f"  New products saved: {total_saved}")
        print(f"  Duplicates prevented: ~{total_duplicates}")
        print(f"  Errors: {total_errors}")
        
        return {
            'agent': self.name,
            'duration': duration,
            'categories_processed': len(self.hybrid_combinations),
            'products_saved': total_saved,
            'duplicates_prevented': total_duplicates,
            'errors': total_errors
        }


if __name__ == "__main__":
    agent = APIHybridProductAgent()
    results = agent.run_discovery()
    print(f"\nResults: {json.dumps(results, indent=2)}")