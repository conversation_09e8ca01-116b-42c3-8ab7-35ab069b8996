#!/usr/bin/env python3
"""
API-Based Sports Equipment Agent - Discovers hemp applications in athletic gear
Focuses on performance equipment, protective gear, and sporting goods
"""

import json
import time
import requests
from datetime import datetime
from typing import Dict, List, Optional, Set
import os
import re
import random
from difflib import SequenceMatcher

class APISportsEquipmentAgent:
    """Sports equipment hemp product discovery with duplicate prevention"""
    
    def __init__(self):
        self.name = "API Sports Equipment Agent"
        self.version = "1.0.0"
        self.discovered_count = 0
        
        # Supabase configuration
        self.supabase_url = "https://ktoqznqmlnxrtvubewyz.supabase.co"
        self.service_role_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt0b3F6bnFtbG54cnR2dWJld3l6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODQ5MTc3NiwiZXhwIjoyMDY0MDY3Nzc2fQ.hxEiOy6xebv7I4CYJEU49oCEXRvmU6iz2l8Kg-NnvTs"
        
        self.headers = {
            "apikey": self.service_role_key,
            "Authorization": f"Bearer {self.service_role_key}",
            "Content-Type": "application/json",
            "Prefer": "return=minimal"
        }
        
        # Cache for existing products
        self.existing_products_cache = set()
        self.normalized_cache = {}
        self.load_existing_products()
        
        # Sports equipment database
        self.sports_equipment = [
            # Team Sports Equipment
            {
                "category": "team_sports",
                "subcategory": "field_sports",
                "equipment": [
                    {"name": "Soccer Ball", "description": "Professional match ball"},
                    {"name": "Football Shell", "description": "American football outer casing"},
                    {"name": "Rugby Ball", "description": "International standard ball"},
                    {"name": "Field Hockey Stick", "description": "Composite stick shaft"},
                    {"name": "Lacrosse Head", "description": "Stick head mesh and frame"}
                ]
            },
            {
                "category": "team_sports",
                "subcategory": "court_sports",
                "equipment": [
                    {"name": "Basketball Net", "description": "Hoop netting material"},
                    {"name": "Volleyball", "description": "Indoor/outdoor ball"},
                    {"name": "Tennis Racket Grip", "description": "Handle wrap material"},
                    {"name": "Badminton Shuttlecock", "description": "Feather alternative"},
                    {"name": "Table Tennis Paddle", "description": "Paddle face material"}
                ]
            },
            # Individual Sports Gear
            {
                "category": "individual_sports",
                "subcategory": "track_field",
                "equipment": [
                    {"name": "Running Shoe Insole", "description": "Cushioning footbed"},
                    {"name": "Pole Vault Pole", "description": "Flexible vaulting pole"},
                    {"name": "Javelin Shaft", "description": "Throwing spear body"},
                    {"name": "Discus Ring", "description": "Disc rim material"},
                    {"name": "Shot Put Grip", "description": "Texture coating"}
                ]
            },
            {
                "category": "individual_sports",
                "subcategory": "gymnastics",
                "equipment": [
                    {"name": "Balance Beam Cover", "description": "Non-slip surface"},
                    {"name": "Parallel Bars Grip", "description": "Hand grip material"},
                    {"name": "Gymnastics Mat", "description": "Landing cushion"},
                    {"name": "Vault Table Surface", "description": "Spring board cover"},
                    {"name": "Rings Straps", "description": "Suspension straps"}
                ]
            },
            # Water Sports
            {
                "category": "water_sports",
                "subcategory": "surfing_boarding",
                "equipment": [
                    {"name": "Surfboard Core", "description": "Board foam alternative"},
                    {"name": "Wakeboard Binding", "description": "Foot strap material"},
                    {"name": "Paddleboard Deck", "description": "Standing surface"},
                    {"name": "Kiteboard Fins", "description": "Directional fins"},
                    {"name": "Skim Board Shell", "description": "Board outer layer"}
                ]
            },
            {
                "category": "water_sports",
                "subcategory": "swimming_diving",
                "equipment": [
                    {"name": "Swim Cap", "description": "Waterproof head cover"},
                    {"name": "Diving Board Surface", "description": "Non-slip coating"},
                    {"name": "Pool Noodle", "description": "Flotation device"},
                    {"name": "Kickboard", "description": "Swimming training aid"},
                    {"name": "Pull Buoy", "description": "Leg float device"}
                ]
            },
            # Winter Sports
            {
                "category": "winter_sports",
                "subcategory": "skiing_snowboarding",
                "equipment": [
                    {"name": "Ski Core", "description": "Ski internal structure"},
                    {"name": "Snowboard Base", "description": "Board bottom layer"},
                    {"name": "Ski Pole Shaft", "description": "Pole body material"},
                    {"name": "Binding Strap", "description": "Boot attachment"},
                    {"name": "Wax Alternative", "description": "Eco-friendly ski wax"}
                ]
            },
            {
                "category": "winter_sports",
                "subcategory": "ice_sports",
                "equipment": [
                    {"name": "Hockey Stick Blade", "description": "Stick blade composite"},
                    {"name": "Figure Skate Boot", "description": "Boot shell material"},
                    {"name": "Curling Broom Head", "description": "Sweeping surface"},
                    {"name": "Speed Skate Frame", "description": "Blade mounting frame"},
                    {"name": "Goalie Pad Shell", "description": "Protective pad outer"}
                ]
            },
            # Combat Sports
            {
                "category": "combat_sports",
                "subcategory": "striking_arts",
                "equipment": [
                    {"name": "Boxing Glove Padding", "description": "Impact absorption"},
                    {"name": "Punching Bag Shell", "description": "Heavy bag exterior"},
                    {"name": "Hand Wraps", "description": "Protective wrapping"},
                    {"name": "Shin Guards", "description": "Leg protection"},
                    {"name": "Head Guard Padding", "description": "Cranial protection"}
                ]
            },
            {
                "category": "combat_sports",
                "subcategory": "grappling_arts",
                "equipment": [
                    {"name": "Wrestling Mat", "description": "Competition surface"},
                    {"name": "Judo Gi Material", "description": "Uniform fabric"},
                    {"name": "BJJ Belt", "description": "Rank indicator belt"},
                    {"name": "MMA Shorts", "description": "Fight shorts material"},
                    {"name": "Grappling Dummy Fill", "description": "Training dummy stuffing"}
                ]
            },
            # Outdoor Recreation
            {
                "category": "outdoor_recreation",
                "subcategory": "camping_hiking",
                "equipment": [
                    {"name": "Backpack Frame", "description": "Pack support structure"},
                    {"name": "Tent Fabric", "description": "Shelter material"},
                    {"name": "Sleeping Bag Shell", "description": "Outer bag material"},
                    {"name": "Trekking Pole", "description": "Walking stick shaft"},
                    {"name": "Camping Chair Frame", "description": "Portable seat structure"}
                ]
            },
            {
                "category": "outdoor_recreation",
                "subcategory": "climbing_mountaineering",
                "equipment": [
                    {"name": "Climbing Rope", "description": "Dynamic safety rope"},
                    {"name": "Harness Webbing", "description": "Support strap material"},
                    {"name": "Carabiner Gate", "description": "Clip mechanism part"},
                    {"name": "Chalk Bag", "description": "Grip powder container"},
                    {"name": "Crash Pad Shell", "description": "Boulder mat cover"}
                ]
            },
            # Fitness Equipment
            {
                "category": "fitness",
                "subcategory": "strength_training",
                "equipment": [
                    {"name": "Resistance Band", "description": "Elastic exercise band"},
                    {"name": "Yoga Mat", "description": "Exercise floor mat"},
                    {"name": "Kettlebell Coating", "description": "Weight grip coating"},
                    {"name": "Weight Plate Core", "description": "Barbell plate material"},
                    {"name": "Pull-up Bar Grip", "description": "Bar grip wrapping"}
                ]
            },
            {
                "category": "fitness",
                "subcategory": "cardio_equipment",
                "equipment": [
                    {"name": "Jump Rope Handle", "description": "Rope grip handle"},
                    {"name": "Exercise Ball Shell", "description": "Stability ball surface"},
                    {"name": "Foam Roller Core", "description": "Massage roller base"},
                    {"name": "Agility Ladder", "description": "Speed training tool"},
                    {"name": "Medicine Ball Cover", "description": "Weighted ball exterior"}
                ]
            },
            # Protective Gear
            {
                "category": "protective_gear",
                "subcategory": "helmets_headgear",
                "equipment": [
                    {"name": "Bike Helmet Liner", "description": "Impact absorption layer"},
                    {"name": "Batting Helmet Shell", "description": "Baseball head protection"},
                    {"name": "Equestrian Helmet", "description": "Riding head protection"},
                    {"name": "Climbing Helmet", "description": "Rock climbing headgear"},
                    {"name": "Skateboard Helmet Padding", "description": "Skate protection liner"}
                ]
            },
            {
                "category": "protective_gear",
                "subcategory": "body_protection",
                "equipment": [
                    {"name": "Chest Protector", "description": "Torso impact guard"},
                    {"name": "Elbow Pads", "description": "Joint protection"},
                    {"name": "Knee Guards", "description": "Knee impact protection"},
                    {"name": "Mouth Guard", "description": "Dental protection"},
                    {"name": "Athletic Cup", "description": "Groin protection"}
                ]
            }
        ]
        
        # Product templates by category
        self.product_templates = {
            "team_sports": [
                "Athletic Hemp {} Equipment",
                "Professional Hemp {} Gear",
                "Sports-Grade Hemp {} Component"
            ],
            "individual_sports": [
                "Performance Hemp {} Equipment",
                "Competition Hemp {} Gear",
                "Athletic Hemp {} Material"
            ],
            "water_sports": [
                "Marine Hemp {} Equipment",
                "Aquatic Hemp {} Gear",
                "Water-Resistant Hemp {} Component"
            ],
            "winter_sports": [
                "Cold-Weather Hemp {} Equipment",
                "Winter Sports Hemp {} Gear",
                "Snow Hemp {} Component"
            ],
            "combat_sports": [
                "Combat Hemp {} Equipment",
                "Martial Arts Hemp {} Gear",
                "Fighting Sports Hemp {} Material"
            ],
            "outdoor_recreation": [
                "Outdoor Hemp {} Equipment",
                "Adventure Hemp {} Gear",
                "Recreation Hemp {} Component"
            ],
            "fitness": [
                "Fitness Hemp {} Equipment",
                "Training Hemp {} Gear",
                "Exercise Hemp {} Material"
            ],
            "protective_gear": [
                "Protective Hemp {} Equipment",
                "Safety Hemp {} Gear",
                "Impact-Resistant Hemp {} Component"
            ]
        }
        
        # Industry mapping
        self.industry_id = 13  # Sports and Recreation

    def load_existing_products(self):
        """Load existing products into cache"""
        print("Loading existing products into cache...")
        offset = 0
        limit = 1000
        
        while True:
            response = requests.get(
                f"{self.supabase_url}/rest/v1/uses_products",
                headers=self.headers,
                params={
                    "select": "name",
                    "order": "id",
                    "offset": offset,
                    "limit": limit
                }
            )
            
            if response.status_code != 200 or not response.json():
                break
                
            batch = response.json()
            for product in batch:
                name = product['name']
                self.existing_products_cache.add(name.lower())
                normalized = self.normalize_name(name)
                self.normalized_cache[normalized.lower()] = name
            
            if len(batch) < limit:
                break
            offset += limit
        
        print(f"Loaded {len(self.existing_products_cache)} existing products")

    def normalize_name(self, name: str) -> str:
        """Normalize product name"""
        name = ' '.join(name.split())
        name = re.sub(r'\b(\w+)\s+\1\b', r'\1', name)
        name = name.replace('Hemp-Based', 'Hemp')
        name = name.replace('Hemp-Derived', 'Hemp')
        name = re.sub(r'(\w+)\s+\1$', r'\1', name)
        return name.strip()

    def check_duplicate_advanced(self, name: str, threshold: float = 0.85) -> bool:
        """Advanced duplicate detection"""
        name_lower = name.lower()
        normalized = self.normalize_name(name)
        normalized_lower = normalized.lower()
        
        if name_lower in self.existing_products_cache:
            return True
        
        if normalized_lower in self.normalized_cache:
            print(f"  ⚠️  Similar to existing: {self.normalized_cache[normalized_lower]}")
            return True
        
        for existing in self.existing_products_cache:
            similarity = SequenceMatcher(None, normalized_lower, existing).ratio()
            if similarity > threshold:
                print(f"  ⚠️  Too similar ({similarity:.2f}) to existing product")
                return True
        
        return False

    def get_plant_part_for_category(self, category: str) -> int:
        """Get appropriate plant part ID for sports category"""
        mapping = {
            "team_sports": 1,         # Fiber for durability
            "individual_sports": 3,   # Stem for structure
            "water_sports": 1,        # Fiber for water resistance
            "winter_sports": 3,       # Stem for rigidity
            "combat_sports": 1,       # Fiber for flexibility
            "outdoor_recreation": 7,  # Whole plant for versatility
            "fitness": 1,            # Fiber for elasticity
            "protective_gear": 3     # Stem for impact resistance
        }
        return mapping.get(category, 7)

    def generate_sports_description(self, equip_name: str, equip_desc: str, 
                                  category: str, subcategory: str) -> str:
        """Generate detailed sports equipment description"""
        descriptions = {
            "team_sports": f"Professional-grade {equip_name} designed for {subcategory.replace('_', ' ')} competition. "
                          f"{equip_desc}. Engineered with hemp materials to provide superior performance, "
                          f"durability, and sustainability for team sports applications.",
            
            "individual_sports": f"High-performance {equip_name} optimized for {subcategory.replace('_', ' ')} athletes. "
                               f"{equip_desc}. Hemp-based construction delivers exceptional strength-to-weight "
                               f"ratio and responsive performance for competitive sports.",
            
            "water_sports": f"Marine-grade {equip_name} engineered for {subcategory.replace('_', ' ')} activities. "
                           f"{equip_desc}. Water-resistant hemp materials provide buoyancy, durability, "
                           f"and eco-friendly performance in aquatic environments.",
            
            "winter_sports": f"Cold-weather {equip_name} developed for {subcategory.replace('_', ' ')} performance. "
                            f"{equip_desc}. Hemp composites maintain flexibility and strength in extreme "
                            f"temperatures while reducing environmental impact.",
            
            "combat_sports": f"Impact-rated {equip_name} designed for {subcategory.replace('_', ' ')} training. "
                            f"{equip_desc}. Hemp materials provide optimal shock absorption and durability "
                            f"for martial arts and combat sports safety.",
            
            "outdoor_recreation": f"Adventure-ready {equip_name} built for {subcategory.replace('_', ' ')} enthusiasts. "
                                 f"{equip_desc}. Sustainable hemp construction withstands outdoor elements "
                                 f"while maintaining lightweight portability.",
            
            "fitness": f"Training-optimized {equip_name} engineered for {subcategory.replace('_', ' ')} workouts. "
                      f"{equip_desc}. Hemp-based materials offer antimicrobial properties and superior "
                      f"grip for intense fitness sessions.",
            
            "protective_gear": f"Safety-certified {equip_name} developed for {subcategory.replace('_', ' ')} protection. "
                              f"{equip_desc}. Advanced hemp composites provide impact resistance and "
                              f"comfort for athletic safety equipment."
        }
        
        return descriptions.get(category, f"Sports {equip_name}. {equip_desc}.")

    def generate_sports_benefits(self, category: str, subcategory: str) -> str:
        """Generate benefits based on sports application"""
        base_benefits = ["Enhanced performance", "Sustainable materials", "Professional quality"]
        
        category_benefits = {
            "team_sports": ["Competition approved", "Weather resistant", "Long-lasting durability"],
            "individual_sports": ["Lightweight design", "Energy return", "Precision engineering"],
            "water_sports": ["Water repellent", "UV resistant", "Salt water compatible"],
            "winter_sports": ["Cold weather flexibility", "Snow/ice grip", "Thermal stability"],
            "combat_sports": ["Impact absorption", "Tear resistant", "Antimicrobial properties"],
            "outdoor_recreation": ["All-weather durability", "Packable design", "Natural materials"],
            "fitness": ["Sweat resistant", "Non-slip grip", "Easy maintenance"],
            "protective_gear": ["Shock dissipation", "Breathable comfort", "Certified protection"]
        }
        
        benefits = base_benefits + category_benefits.get(category, ["Athletic performance"])
        return "{" + ",".join([f'"{b}"' for b in benefits]) + "}"

    def create_sports_products(self, equipment_group: Dict) -> List[Dict]:
        """Create products from sports equipment group"""
        products = []
        category = equipment_group["category"]
        subcategory = equipment_group["subcategory"]
        
        for equipment in equipment_group["equipment"]:
            # Generate 1-2 variations per equipment
            num_variations = random.randint(1, 2)
            
            for i in range(num_variations):
                templates = self.product_templates.get(category, ["Sports Hemp {} Equipment"])
                template = random.choice(templates)
                
                # Create product name
                if i == 0:
                    product_name = template.format(equipment["name"])
                else:
                    modifiers = ["Pro", "Elite", "Competition", "Training", "Premium"]
                    modifier = random.choice(modifiers)
                    product_name = template.format(f"{modifier} {equipment['name']}")
                
                product_name = self.normalize_name(product_name)
                
                # Check for duplicates
                if self.check_duplicate_advanced(product_name):
                    continue
                
                # Determine plant part
                plant_part_id = self.get_plant_part_for_category(category)
                
                # Calculate confidence score (0.7-0.9 for sports)
                confidence_score = round(0.7 + (random.random() * 0.2), 2)
                
                product = {
                    'name': product_name,
                    'description': self.generate_sports_description(
                        equipment["name"], equipment["description"], category, subcategory
                    ),
                    'industry_sub_category_id': self.industry_id,
                    'plant_part_id': plant_part_id,
                    'confidence_score': confidence_score,
                    'image_url': None,
                    'benefits_advantages': self.generate_sports_benefits(category, subcategory),
                    'technical_specifications': json.dumps({
                        "category": category,
                        "subcategory": subcategory,
                        "sport_type": subcategory.replace('_', ' '),
                        "equipment_class": equipment["name"],
                        "performance_grade": "Professional",
                        "certifications": ["ISO", "CE", "ASTM"]
                    }),
                    'source_type': 'ai_agent',
                    'source_agent': self.name,
                    'source_url': f"Sports: {category}/{subcategory}"
                }
                
                products.append(product)
                
                # Add to cache immediately
                self.existing_products_cache.add(product_name.lower())
                self.normalized_cache[product_name.lower()] = product_name
        
        return products

    def save_product_api(self, product_data: Dict) -> bool:
        """Save product using REST API"""
        try:
            response = requests.post(
                f"{self.supabase_url}/rest/v1/uses_products",
                headers=self.headers,
                json=product_data
            )
            
            if response.status_code in [200, 201, 204]:
                self.discovered_count += 1
                return True
            else:
                print(f"Failed to save product: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"Error saving product: {e}")
            return False

    def run_discovery(self) -> Dict:
        """Run sports equipment discovery"""
        start_time = time.time()
        total_saved = 0
        total_duplicates = 0
        total_errors = 0
        
        print(f"\n⚽ {self.name} - Starting Discovery Cycle")
        print(f"Equipment categories: {len(self.sports_equipment)}")
        print("=" * 60)
        
        for idx, equipment_group in enumerate(self.sports_equipment, 1):
            category = equipment_group["category"]
            subcategory = equipment_group["subcategory"]
            num_equipment = len(equipment_group["equipment"])
            
            print(f"\n[{idx}/{len(self.sports_equipment)}] {category.title().replace('_', ' ')} - {subcategory.title().replace('_', ' ')}")
            print(f"  Equipment types: {num_equipment}")
            
            try:
                # Create products from equipment group
                products = self.create_sports_products(equipment_group)
                print(f"  Generated {len(products)} product variations")
                
                # Save each product
                saved = 0
                for product in products:
                    if self.save_product_api(product):
                        saved += 1
                        total_saved += 1
                        print(f"  ✅ {product['name']}")
                    else:
                        total_errors += 1
                
                # Track duplicates prevented
                expected = num_equipment * 1.5  # Average expected variations
                prevented = max(0, expected - len(products))
                total_duplicates += int(prevented)
                
                print(f"  Saved {saved} new products")
                
                # Rate limiting
                time.sleep(0.5)
                
            except Exception as e:
                print(f"  ❌ Error processing category: {e}")
                total_errors += 1
        
        # Summary
        duration = time.time() - start_time
        print(f"\n{'=' * 60}")
        print(f"Discovery Complete!")
        print(f"  Duration: {duration:.1f} seconds")
        print(f"  Categories processed: {len(self.sports_equipment)}")
        print(f"  New products saved: {total_saved}")
        print(f"  Duplicates prevented: ~{total_duplicates}")
        print(f"  Errors: {total_errors}")
        
        return {
            'agent': self.name,
            'duration': duration,
            'categories_processed': len(self.sports_equipment),
            'products_saved': total_saved,
            'duplicates_prevented': total_duplicates,
            'errors': total_errors
        }


if __name__ == "__main__":
    agent = APISportsEquipmentAgent()
    results = agent.run_discovery()
    print(f"\nResults: {json.dumps(results, indent=2)}")