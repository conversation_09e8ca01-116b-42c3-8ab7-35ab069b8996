#!/usr/bin/env python3
"""
API-Based Patent Mining Agent - Fixed Authentication
Uses proper service role authentication to bypass RLS
"""

import json
import time
import requests
from datetime import datetime
from typing import Dict, List, Optional
import os
import re
import random
import hashlib

try:
    from .expanded_patent_database import get_all_patents, get_patent_count
except ImportError:
    from expanded_patent_database import get_all_patents, get_patent_count

class APIPatentMiningAgent:
    """Patent mining using Supabase REST API with proper service role auth"""
    
    def __init__(self):
        self.name = "API Patent Mining Agent"
        self.version = "3.0.1"
        self.discovered_count = 0
        
        # Supabase configuration
        self.supabase_url = "https://ktoqznqmlnxrtvubewyz.supabase.co"
        self.service_role_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt0b3F6bnFtbG54cnR2dWJld3l6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODQ5MTc3NiwiZXhwIjoyMDY0MDY3Nzc2fQ.hxEiOy6xebv7I4CYJEU49oCEXRvmU6iz2l8Kg-NnvTs"
        
        # Set up headers with service role key
        self.headers = {
            "apikey": self.service_role_key,
            "Authorization": f"Bearer {self.service_role_key}",
            "Content-Type": "application/json",
            "Prefer": "return=minimal"  # Don't return the created record
        }
        
        # Load expanded patent database
        self.patent_examples = get_all_patents()
        print(f"Loaded {len(self.patent_examples)} patents from expanded database")
        
        # Product generation templates
        self.product_templates = {
            "composites": [
                "Hemp Fiber {} Composite Material",
                "High-Strength Hemp {} Composite",
                "Reinforced Hemp {} Panel"
            ],
            "energy": [
                "Hemp-Based {} Energy Storage Device",
                "Hemp Carbon {} Electrode",
                "Hemp-Derived {} Battery Component"
            ],
            "construction": [
                "Hemp {} Building Material",
                "Structural Hemp {} Product",
                "Hemp-Based {} Insulation"
            ],
            "plastics": [
                "Biodegradable Hemp {} Plastic",
                "Hemp Biopolymer {} Material",
                "Sustainable Hemp {} Packaging"
            ],
            "textiles": [
                "Premium Hemp {} Textile",
                "Hemp Fiber {} Fabric",
                "Technical Hemp {} Material"
            ],
            "nanotechnology": [
                "Hemp-Derived {} Nanomaterial",
                "Hemp Nano{} Product",
                "Advanced Hemp {} Nanostructure"
            ],
            "pharmaceutical": [
                "Hemp-Based {} Pharmaceutical",
                "Cannabinoid {} Formulation",
                "Medical Hemp {} Product"
            ],
            "food": [
                "Hemp {} Food Product",
                "Nutritional Hemp {} Supplement",
                "Hemp-Based {} Ingredient"
            ],
            "automotive": [
                "Hemp {} Automotive Component",
                "Vehicle Hemp {} Part",
                "Hemp-Based {} Auto Material"
            ],
            "filtration": [
                "Hemp {} Filtration System",
                "Hemp-Based {} Filter",
                "Hemp Fiber {} Membrane"
            ],
            "cosmetics": [
                "Hemp {} Beauty Product",
                "Hemp-Based {} Cosmetic",
                "Natural Hemp {} Care"
            ],
            "paper": [
                "Hemp {} Paper Product",
                "Sustainable Hemp {} Paper",
                "Hemp-Based {} Packaging"
            ],
            "agriculture": [
                "Hemp {} Agricultural Product",
                "Hemp-Based {} Growing Medium",
                "Organic Hemp {} Solution"
            ],
            "electronics": [
                "Hemp {} Electronic Component",
                "Hemp-Based {} Circuit Material",
                "Conductive Hemp {} Product"
            ],
            "medical": [
                "Medical Hemp {} Device",
                "Hemp-Based {} Medical Supply",
                "Therapeutic Hemp {} Product"
            ]
        }
        
        # Industry mapping for database
        self.industry_mapping = {
            "composites": 6,      # Construction Materials
            "energy": 15,         # Energy
            "construction": 6,    # Construction Materials
            "plastics": 12,       # Packaging
            "textiles": 4,        # Apparel and Fashion
            "nanotechnology": 18, # Advanced Materials
            "pharmaceutical": 9,  # Health and Wellness
            "food": 8,           # Food and Beverage
            "automotive": 14,     # Automotive
            "filtration": 5,      # Industrial Textiles
            "cosmetics": 9,       # Health and Wellness
            "paper": 13,         # Paper and Pulp
            "agriculture": 16,    # Agriculture
            "electronics": 17,    # Electronics
            "medical": 9         # Health and Wellness
        }
        
        # Plant part mapping
        self.plant_part_mapping = {
            "fiber": 1,
            "seed": 2,
            "oil": 2,
            "extract": 6,
            "whole": 7,
            "stem": 3,
            "leaf": 4,
            "root": 5
        }

    def check_duplicate_api(self, name: str) -> bool:
        """Check if product already exists using REST API"""
        try:
            # Check by exact name
            response = requests.get(
                f"{self.supabase_url}/rest/v1/uses_products",
                headers=self.headers,
                params={
                    "name": f"eq.{name}",
                    "select": "id",
                    "limit": "1"
                }
            )
            
            if response.status_code == 200 and response.json():
                return True
                
            return False
            
        except Exception as e:
            print(f"Error checking duplicate: {e}")
            return False

    def save_product_api(self, product_data: Dict) -> bool:
        """Save product using REST API with service role authentication"""
        try:
            # Use the service role key in headers
            response = requests.post(
                f"{self.supabase_url}/rest/v1/uses_products",
                headers=self.headers,
                json=product_data
            )
            
            if response.status_code in [200, 201, 204]:
                self.discovered_count += 1
                return True
            else:
                print(f"Failed to save product: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"Error saving product: {e}")
            return False

    def generate_product_description(self, patent: Dict, product_name: str, category: str) -> str:
        """Generate meaningful product description based on patent"""
        base_descriptions = {
            "composites": f"Advanced composite material incorporating hemp fibers for enhanced strength and sustainability. Based on {patent.get('title', 'patent technology')}, this product offers superior mechanical properties while maintaining environmental benefits.",
            "energy": f"Innovative energy storage solution utilizing hemp-derived materials. Leveraging {patent.get('title', 'patent technology')}, this product provides efficient energy storage with sustainable materials.",
            "construction": f"Sustainable building material made from hemp components. Developed using {patent.get('title', 'patent technology')}, ideal for eco-friendly construction projects.",
            "plastics": f"Biodegradable plastic alternative derived from hemp biomass. Based on {patent.get('title', 'patent technology')}, this material offers comparable performance with environmental benefits.",
            "textiles": f"High-performance textile product utilizing hemp fibers. Incorporating {patent.get('title', 'patent technology')} for enhanced durability and comfort.",
            "nanotechnology": f"Cutting-edge nanomaterial derived from hemp processing. Using {patent.get('title', 'patent technology')}, this product enables advanced applications at the nanoscale.",
            "pharmaceutical": f"Pharmaceutical-grade product derived from hemp compounds. Developed through {patent.get('title', 'patent technology')} for therapeutic applications.",
            "food": f"Nutritious food product made from hemp ingredients. Based on {patent.get('title', 'patent technology')}, providing high-quality nutrition from sustainable sources.",
            "automotive": f"Automotive component manufactured using hemp materials. Leveraging {patent.get('title', 'patent technology')} for lightweight, durable vehicle parts.",
            "filtration": f"Advanced filtration system utilizing hemp fiber properties. Based on {patent.get('title', 'patent technology')} for efficient separation and purification.",
            "cosmetics": f"Natural cosmetic product formulated with hemp-derived ingredients. Using {patent.get('title', 'patent technology')} for skin-friendly beauty solutions.",
            "paper": f"Sustainable paper product made from hemp fibers. Developed through {patent.get('title', 'patent technology')} as an eco-friendly alternative to wood pulp.",
            "agriculture": f"Agricultural solution incorporating hemp-based materials. Based on {patent.get('title', 'patent technology')} for sustainable farming practices.",
            "electronics": f"Electronic component utilizing hemp-derived conductive materials. Leveraging {patent.get('title', 'patent technology')} for sustainable electronics.",
            "medical": f"Medical device or supply incorporating hemp materials. Developed using {patent.get('title', 'patent technology')} for healthcare applications."
        }
        
        return base_descriptions.get(category, f"Innovative hemp-based product developed using {patent.get('title', 'patent technology')}. Designed for sustainable applications in the {category} industry.")

    def extract_products_from_patent(self, patent: Dict) -> List[Dict]:
        """Extract potential products from a patent"""
        products = []
        patent_id = patent.get('number', 'Unknown')
        title = patent.get('title', 'Hemp innovation')
        category = patent.get('category', 'general')
        
        # Generate 3-12 product variations per patent
        num_products = random.randint(3, 12)
        templates = self.product_templates.get(category, ["Hemp {} Product"])
        
        # Extract key terms from patent title
        title_words = re.findall(r'\b[A-Za-z]+\b', title)
        key_terms = [w for w in title_words if len(w) > 4 and w.lower() not in ['hemp', 'method', 'system', 'apparatus', 'device']]
        
        for i in range(num_products):
            template = random.choice(templates)
            
            # Generate unique modifier
            if key_terms:
                modifier = random.choice(key_terms).title()
            else:
                modifiers = ["Advanced", "Premium", "Enhanced", "Optimized", "Refined", 
                           "Specialized", "Professional", "Industrial", "Commercial", "Innovative"]
                modifier = random.choice(modifiers)
            
            # Add variation
            if i > 0:
                variations = ["Plus", "Pro", "Elite", "Ultra", "Max", "Prime", "Select", "Premium"]
                modifier = f"{modifier} {random.choice(variations)}"
            
            product_name = template.format(modifier)
            
            # Ensure uniqueness within this batch
            attempt = 0
            while any(p['name'] == product_name for p in products) and attempt < 5:
                modifier = f"{modifier} {random.choice(['X', 'Z', 'Q', 'V', 'Neo'])}"
                product_name = template.format(modifier)
                attempt += 1
            
            # Determine plant part based on category
            if category in ["fiber", "textiles", "composites", "paper"]:
                plant_part_id = 1  # Fiber
            elif category in ["food", "oil", "cosmetics"]:
                plant_part_id = 2  # Seed/Oil
            elif category in ["pharmaceutical", "medical"]:
                plant_part_id = 6  # Extract
            elif category in ["construction", "agriculture"]:
                plant_part_id = 3  # Stem
            else:
                plant_part_id = 7  # Whole Plant
            
            # Calculate confidence score (0.7-0.95)
            confidence_score = round(0.7 + (random.random() * 0.25), 2)
            
            product = {
                'name': product_name,
                'description': self.generate_product_description(patent, product_name, category),
                'industry_sub_category_id': self.industry_mapping.get(category, 5),
                'plant_part_id': plant_part_id,
                'confidence_score': confidence_score,
                'image_url': None,
                'benefits_advantages': "{" + ",".join([
                    '"Sustainable and eco-friendly"',
                    f'"Based on patented technology ({patent_id})"',
                    '"High-performance materials"',
                    '"Reduced environmental impact"',
                    '"Cost-effective production"'
                ]) + "}",
                'technical_specifications': json.dumps({
                    "patent_reference": patent_id,
                    "category": category,
                    "innovation_type": patent.get('type', 'product')
                }),
                'source_type': 'ai_agent',
                'source_agent': self.name,
                'source_url': f"Patent: {patent_id}"
            }
            
            products.append(product)
        
        return products

    def run_discovery(self, max_patents: Optional[int] = None) -> Dict:
        """Run patent discovery process using REST API"""
        start_time = time.time()
        total_saved = 0
        total_duplicates = 0
        total_errors = 0
        
        print(f"\n🔍 {self.name} - Starting Discovery Cycle")
        print(f"Using service role key for authentication")
        print("=" * 60)
        
        # Process patents
        patents_to_process = self.patent_examples[:max_patents] if max_patents else self.patent_examples
        
        for idx, patent in enumerate(patents_to_process, 1):
            print(f"\n[{idx}/{len(patents_to_process)}] Processing patent: {patent.get('number', 'Unknown')}")
            print(f"  Title: {patent.get('title', 'No title')}")
            
            try:
                # Extract products from patent
                products = self.extract_products_from_patent(patent)
                print(f"  Generated {len(products)} product variations")
                
                # Save each product
                saved = 0
                for product in products:
                    # Check for duplicates
                    if self.check_duplicate_api(product['name']):
                        total_duplicates += 1
                        continue
                    
                    # Save product
                    if self.save_product_api(product):
                        saved += 1
                        total_saved += 1
                        print(f"  ✅ Saved: {product['name']}")
                    else:
                        total_errors += 1
                        print(f"  ❌ Error saving {product['name']}")
                
                print(f"  Saved {saved} new products from this patent")
                
                # Rate limiting
                time.sleep(0.5)
                
            except Exception as e:
                print(f"  ❌ Error processing patent: {e}")
                total_errors += 1
        
        # Summary
        duration = time.time() - start_time
        print(f"\n{'=' * 60}")
        print(f"Discovery Complete!")
        print(f"  Duration: {duration:.1f} seconds")
        print(f"  Patents processed: {len(patents_to_process)}")
        print(f"  New products saved: {total_saved}")
        print(f"  Duplicates skipped: {total_duplicates}")
        print(f"  Errors: {total_errors}")
        print(f"  Total products in system: ~{1106 + total_saved}")
        
        return {
            'agent': self.name,
            'duration': duration,
            'patents_processed': len(patents_to_process),
            'products_saved': total_saved,
            'duplicates': total_duplicates,
            'errors': total_errors
        }


if __name__ == "__main__":
    agent = APIPatentMiningAgent()
    results = agent.run_discovery(max_patents=10)  # Test with 10 patents
    print(f"\nResults: {json.dumps(results, indent=2)}")