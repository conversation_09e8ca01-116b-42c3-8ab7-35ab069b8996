#!/usr/bin/env python3
"""
API-Based Regional/Cultural Agent - Discovers traditional and regional hemp products
Explores cultural uses and regional variations
"""

import json
import time
import requests
from datetime import datetime
from typing import Dict, List, Optional, Set
import os
import re
import random
from difflib import SequenceMatcher

class APIRegionalCulturalAgent:
    """Regional and cultural hemp product discovery with duplicate prevention"""
    
    def __init__(self):
        self.name = "API Regional Cultural Agent"
        self.version = "1.0.0"
        self.discovered_count = 0
        
        # Supabase configuration
        self.supabase_url = "https://ktoqznqmlnxrtvubewyz.supabase.co"
        self.service_role_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt0b3F6bnFtbG54cnR2dWJld3l6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODQ5MTc3NiwiZXhwIjoyMDY0MDY3Nzc2fQ.hxEiOy6xebv7I4CYJEU49oCEXRvmU6iz2l8Kg-NnvTs"
        
        self.headers = {
            "apikey": self.service_role_key,
            "Authorization": f"Bearer {self.service_role_key}",
            "Content-Type": "application/json",
            "Prefer": "return=minimal"
        }
        
        # Cache for existing products
        self.existing_products_cache = set()
        self.normalized_cache = {}
        self.load_existing_products()
        
        # Regional and cultural database
        self.regional_products = [
            # Asian Traditional Uses
            {
                "region": "Japan",
                "culture": "Japanese",
                "traditional_name": "Asa",
                "products": [
                    {"name": "Shichimi Togarashi", "use": "spice blend", "category": "food"},
                    {"name": "Asanomi", "use": "traditional seed snack", "category": "food"},
                    {"name": "Asa-no-ha", "use": "textile pattern", "category": "textiles"},
                    {"name": "Noren", "use": "door curtain", "category": "textiles"},
                    {"name": "Waraji", "use": "traditional sandals", "category": "apparel"},
                    {"name": "Shimenawa", "use": "sacred rope", "category": "cultural"},
                    {"name": "Asa Paper", "use": "traditional paper", "category": "paper"}
                ]
            },
            {
                "region": "China",
                "culture": "Chinese",
                "traditional_name": "Ma",
                "products": [
                    {"name": "Ma Ren Wan", "use": "traditional medicine", "category": "pharmaceutical"},
                    {"name": "Huoma You", "use": "cooking oil", "category": "food"},
                    {"name": "Ma Bian", "use": "hemp rope", "category": "industrial"},
                    {"name": "Ma Bu", "use": "traditional fabric", "category": "textiles"},
                    {"name": "Hemp Tea", "use": "medicinal tea", "category": "food"},
                    {"name": "Ma Zhi", "use": "traditional paper", "category": "paper"}
                ]
            },
            {
                "region": "Korea",
                "culture": "Korean",
                "traditional_name": "Sam",
                "products": [
                    {"name": "Sambe", "use": "traditional fabric", "category": "textiles"},
                    {"name": "Sam-ssi", "use": "seed oil", "category": "food"},
                    {"name": "Sampo", "use": "burial cloth", "category": "cultural"},
                    {"name": "Hemp Kimchi", "use": "fermented food", "category": "food"}
                ]
            },
            {
                "region": "India",
                "culture": "Indian",
                "traditional_name": "Bhang/Ganja",
                "products": [
                    {"name": "Bhang Lassi", "use": "traditional drink", "category": "food"},
                    {"name": "Hemp Chutney", "use": "condiment", "category": "food"},
                    {"name": "Vijaya", "use": "ayurvedic medicine", "category": "pharmaceutical"},
                    {"name": "Hemp Rope", "use": "traditional binding", "category": "industrial"},
                    {"name": "Hemp Dhoti", "use": "traditional garment", "category": "apparel"}
                ]
            },
            # European Traditional Uses
            {
                "region": "Eastern Europe",
                "culture": "Slavic",
                "traditional_name": "Konopie",
                "products": [
                    {"name": "Hemp Seed Soup", "use": "traditional dish", "category": "food"},
                    {"name": "Konopne Maslo", "use": "hemp butter", "category": "food"},
                    {"name": "Hemp Twine", "use": "farming tool", "category": "agricultural"},
                    {"name": "Traditional Hemp Shirt", "use": "peasant clothing", "category": "apparel"},
                    {"name": "Hemp Sacks", "use": "grain storage", "category": "agricultural"}
                ]
            },
            {
                "region": "Italy",
                "culture": "Italian",
                "traditional_name": "Canapa",
                "products": [
                    {"name": "Canapa Flour", "use": "baking ingredient", "category": "food"},
                    {"name": "Hemp Pasta", "use": "traditional food", "category": "food"},
                    {"name": "Carmagnola Rope", "use": "marine cordage", "category": "industrial"},
                    {"name": "Hemp Linen", "use": "household textiles", "category": "textiles"}
                ]
            },
            {
                "region": "France",
                "culture": "French",
                "traditional_name": "Chanvre",
                "products": [
                    {"name": "Chènevis", "use": "bird feed", "category": "agricultural"},
                    {"name": "Chanvre Paper", "use": "fine paper", "category": "paper"},
                    {"name": "Hemp Sail Canvas", "use": "maritime", "category": "textiles"},
                    {"name": "Cordage de Chanvre", "use": "rope making", "category": "industrial"}
                ]
            },
            # Americas Traditional Uses
            {
                "region": "Mexico",
                "culture": "Mexican",
                "traditional_name": "Cáñamo",
                "products": [
                    {"name": "Hemp Tortillas", "use": "traditional food", "category": "food"},
                    {"name": "Ixtle Hemp", "use": "fiber crafts", "category": "textiles"},
                    {"name": "Hemp Sandals", "use": "traditional footwear", "category": "apparel"}
                ]
            },
            {
                "region": "Native American",
                "culture": "Indigenous",
                "traditional_name": "Various",
                "products": [
                    {"name": "Hemp Bowstring", "use": "hunting tool", "category": "cultural"},
                    {"name": "Hemp Medicine Bag", "use": "ceremonial item", "category": "cultural"},
                    {"name": "Hemp Fishing Net", "use": "fishing tool", "category": "industrial"},
                    {"name": "Hemp Moccasins", "use": "footwear", "category": "apparel"}
                ]
            },
            # Middle Eastern Uses
            {
                "region": "Middle East",
                "culture": "Arabic",
                "traditional_name": "Qinnab",
                "products": [
                    {"name": "Hemp Halva", "use": "sweet confection", "category": "food"},
                    {"name": "Hemp Coffee", "use": "beverage", "category": "food"},
                    {"name": "Traditional Hemp Rope", "use": "construction", "category": "industrial"},
                    {"name": "Hemp Tent Fabric", "use": "nomadic shelter", "category": "textiles"}
                ]
            },
            # African Traditional Uses
            {
                "region": "Africa",
                "culture": "Various African",
                "traditional_name": "Dagga/Others",
                "products": [
                    {"name": "Hemp Basket", "use": "storage container", "category": "cultural"},
                    {"name": "Hemp Fish Trap", "use": "fishing tool", "category": "industrial"},
                    {"name": "Traditional Hemp Mat", "use": "floor covering", "category": "textiles"},
                    {"name": "Hemp Ceremonial Cloth", "use": "ritual item", "category": "cultural"}
                ]
            }
        ]
        
        # Product templates by category
        self.product_templates = {
            "food": [
                "Traditional {} Hemp {} Product",
                "{} Regional Hemp {}",
                "Artisanal {} Hemp {}"
            ],
            "textiles": [
                "{} Traditional Hemp {} Fabric",
                "Heritage {} Hemp Textile",
                "{} Cultural Hemp {} Material"
            ],
            "pharmaceutical": [
                "Traditional {} Hemp {} Medicine",
                "{} Herbal Hemp {} Remedy",
                "Ancient {} Hemp {} Treatment"
            ],
            "industrial": [
                "Traditional {} Hemp {} Tool",
                "{} Crafted Hemp {}",
                "Heritage {} Hemp {} Product"
            ],
            "cultural": [
                "{} Ceremonial Hemp {}",
                "Traditional {} Hemp {} Artifact",
                "{} Cultural Hemp {} Item"
            ],
            "apparel": [
                "Traditional {} Hemp {} Garment",
                "{} Style Hemp {} Clothing",
                "Heritage {} Hemp {} Wear"
            ],
            "agricultural": [
                "{} Agricultural Hemp {}",
                "Traditional {} Hemp {} Tool",
                "{} Farming Hemp {} Product"
            ],
            "paper": [
                "{} Traditional Hemp {} Paper",
                "Artisanal {} Hemp {} Sheet",
                "Heritage {} Hemp {} Document"
            ]
        }
        
        # Industry mapping
        self.industry_mapping = {
            "food": 8,           # Food and Beverage
            "textiles": 4,       # Apparel and Fashion
            "pharmaceutical": 9, # Health and Wellness
            "industrial": 5,     # Industrial Textiles
            "cultural": 20,      # Arts and Crafts
            "apparel": 4,        # Apparel and Fashion
            "agricultural": 16,  # Agriculture
            "paper": 13         # Paper and Pulp
        }

    def load_existing_products(self):
        """Load existing products into cache"""
        print("Loading existing products into cache...")
        offset = 0
        limit = 1000
        
        while True:
            response = requests.get(
                f"{self.supabase_url}/rest/v1/uses_products",
                headers=self.headers,
                params={
                    "select": "name",
                    "order": "id",
                    "offset": offset,
                    "limit": limit
                }
            )
            
            if response.status_code != 200 or not response.json():
                break
                
            batch = response.json()
            for product in batch:
                name = product['name']
                self.existing_products_cache.add(name.lower())
                normalized = self.normalize_name(name)
                self.normalized_cache[normalized.lower()] = name
            
            if len(batch) < limit:
                break
            offset += limit
        
        print(f"Loaded {len(self.existing_products_cache)} existing products")

    def normalize_name(self, name: str) -> str:
        """Normalize product name"""
        name = ' '.join(name.split())
        name = re.sub(r'\b(\w+)\s+\1\b', r'\1', name)
        name = name.replace('Hemp-Based', 'Hemp')
        name = name.replace('Hemp-Derived', 'Hemp')
        name = re.sub(r'(\w+)\s+\1$', r'\1', name)
        return name.strip()

    def check_duplicate_advanced(self, name: str, threshold: float = 0.85) -> bool:
        """Advanced duplicate detection"""
        name_lower = name.lower()
        normalized = self.normalize_name(name)
        normalized_lower = normalized.lower()
        
        if name_lower in self.existing_products_cache:
            return True
        
        if normalized_lower in self.normalized_cache:
            print(f"  ⚠️  Similar to existing: {self.normalized_cache[normalized_lower]}")
            return True
        
        for existing in self.existing_products_cache:
            similarity = SequenceMatcher(None, normalized_lower, existing).ratio()
            if similarity > threshold:
                print(f"  ⚠️  Too similar ({similarity:.2f}) to existing product")
                return True
        
        return False

    def get_plant_part_for_category(self, category: str) -> int:
        """Get appropriate plant part ID for category"""
        mapping = {
            "food": 2,           # Seed
            "textiles": 1,       # Fiber
            "pharmaceutical": 6, # Flower/Extract
            "industrial": 3,     # Stem
            "cultural": 7,       # Whole Plant
            "apparel": 1,        # Fiber
            "agricultural": 7,   # Whole Plant
            "paper": 1          # Fiber
        }
        return mapping.get(category, 7)

    def generate_cultural_description(self, region: str, culture: str, traditional_name: str, 
                                    product_name: str, use: str) -> str:
        """Generate culturally-aware product description"""
        descriptions = [
            f"Traditional {culture} {product_name} from {region}. Known locally as '{traditional_name}', "
            f"this heritage product has been used for {use} for centuries. Represents authentic cultural practices.",
            
            f"Authentic {region} hemp product traditionally used for {use}. Part of {culture} heritage, "
            f"where hemp is known as '{traditional_name}'. Combines ancient wisdom with modern applications.",
            
            f"Heritage {product_name} from {region}'s {culture} traditions. Locally called '{traditional_name}', "
            f"this product showcases traditional {use} techniques passed down through generations.",
            
            f"Cultural {product_name} representing {culture} craftsmanship from {region}. "
            f"Traditional name '{traditional_name}' reflects its deep cultural significance for {use}."
        ]
        
        return random.choice(descriptions)

    def generate_cultural_benefits(self, region: str, culture: str, category: str) -> str:
        """Generate benefits based on cultural context"""
        base_benefits = [
            f"Authentic {culture} heritage product",
            "Traditionally crafted",
            f"Cultural significance in {region}"
        ]
        
        category_benefits = {
            "food": ["Traditional nutrition", "Time-tested recipe"],
            "textiles": ["Durable natural fibers", "Traditional weaving"],
            "pharmaceutical": ["Ancient healing wisdom", "Natural remedy"],
            "industrial": ["Proven traditional design", "Sustainable materials"],
            "cultural": ["Ceremonial importance", "Cultural preservation"],
            "apparel": ["Traditional comfort", "Natural breathability"],
            "agricultural": ["Time-tested farming tool", "Sustainable agriculture"],
            "paper": ["Archival quality", "Traditional craftsmanship"]
        }
        
        benefits = base_benefits + category_benefits.get(category, ["Authentic traditional product"])
        return "{" + ",".join([f'"{b}"' for b in benefits]) + "}"

    def create_products_from_region(self, regional_data: Dict) -> List[Dict]:
        """Create products from regional/cultural data"""
        products = []
        region = regional_data["region"]
        culture = regional_data["culture"]
        traditional_name = regional_data["traditional_name"]
        
        for item in regional_data["products"]:
            # Generate 1-2 variations per traditional product
            num_variations = random.randint(1, 2)
            
            for i in range(num_variations):
                category = item["category"]
                templates = self.product_templates.get(category, ["{} {} Hemp Product"])
                template = random.choice(templates)
                
                # Create product name
                if i == 0:
                    # First variation - close to traditional
                    product_name = template.format(culture, item["name"])
                else:
                    # Additional variation - modernized
                    modifiers = ["Modern", "Premium", "Artisanal", "Heritage", "Authentic"]
                    modifier = random.choice(modifiers)
                    product_name = template.format(modifier, item["name"])
                
                product_name = self.normalize_name(product_name)
                
                # Check for duplicates
                if self.check_duplicate_advanced(product_name):
                    continue
                
                # Determine plant part
                plant_part_id = self.get_plant_part_for_category(category)
                
                # Calculate confidence score (0.65-0.9 for cultural products)
                confidence_score = round(0.65 + (random.random() * 0.25), 2)
                
                product = {
                    'name': product_name,
                    'description': self.generate_cultural_description(
                        region, culture, traditional_name, item["name"], item["use"]
                    ),
                    'industry_sub_category_id': self.industry_mapping.get(category, 20),
                    'plant_part_id': plant_part_id,
                    'confidence_score': confidence_score,
                    'image_url': None,
                    'benefits_advantages': self.generate_cultural_benefits(region, culture, category),
                    'technical_specifications': json.dumps({
                        "region": region,
                        "culture": culture,
                        "traditional_name": traditional_name,
                        "traditional_use": item["use"],
                        "category": category
                    }),
                    'source_type': 'ai_agent',
                    'source_agent': self.name,
                    'source_url': f"Cultural: {region}/{culture}"
                }
                
                products.append(product)
                
                # Add to cache immediately
                self.existing_products_cache.add(product_name.lower())
                self.normalized_cache[product_name.lower()] = product_name
        
        return products

    def save_product_api(self, product_data: Dict) -> bool:
        """Save product using REST API"""
        try:
            response = requests.post(
                f"{self.supabase_url}/rest/v1/uses_products",
                headers=self.headers,
                json=product_data
            )
            
            if response.status_code in [200, 201, 204]:
                self.discovered_count += 1
                return True
            else:
                print(f"Failed to save product: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"Error saving product: {e}")
            return False

    def run_discovery(self) -> Dict:
        """Run regional/cultural discovery"""
        start_time = time.time()
        total_saved = 0
        total_duplicates = 0
        total_errors = 0
        
        print(f"\n🌍 {self.name} - Starting Discovery Cycle")
        print(f"Regions to explore: {len(self.regional_products)}")
        print("=" * 60)
        
        for idx, regional_data in enumerate(self.regional_products, 1):
            region = regional_data["region"]
            culture = regional_data["culture"]
            num_traditional = len(regional_data["products"])
            
            print(f"\n[{idx}/{len(self.regional_products)}] {region} ({culture} Culture)")
            print(f"  Traditional products: {num_traditional}")
            print(f"  Local name for hemp: {regional_data['traditional_name']}")
            
            try:
                # Create products from regional data
                products = self.create_products_from_region(regional_data)
                print(f"  Generated {len(products)} product variations")
                
                # Save each product
                saved = 0
                for product in products:
                    if self.save_product_api(product):
                        saved += 1
                        total_saved += 1
                        print(f"  ✅ {product['name']}")
                    else:
                        total_errors += 1
                
                # Track duplicates prevented
                expected = num_traditional * 1.5  # Average expected variations
                prevented = max(0, expected - len(products))
                total_duplicates += int(prevented)
                
                print(f"  Saved {saved} new products")
                
                # Rate limiting
                time.sleep(0.5)
                
            except Exception as e:
                print(f"  ❌ Error processing region: {e}")
                total_errors += 1
        
        # Summary
        duration = time.time() - start_time
        print(f"\n{'=' * 60}")
        print(f"Discovery Complete!")
        print(f"  Duration: {duration:.1f} seconds")
        print(f"  Regions explored: {len(self.regional_products)}")
        print(f"  New products saved: {total_saved}")
        print(f"  Duplicates prevented: ~{total_duplicates}")
        print(f"  Errors: {total_errors}")
        
        return {
            'agent': self.name,
            'duration': duration,
            'regions_explored': len(self.regional_products),
            'products_saved': total_saved,
            'duplicates_prevented': total_duplicates,
            'errors': total_errors
        }


if __name__ == "__main__":
    agent = APIRegionalCulturalAgent()
    results = agent.run_discovery()
    print(f"\nResults: {json.dumps(results, indent=2)}")