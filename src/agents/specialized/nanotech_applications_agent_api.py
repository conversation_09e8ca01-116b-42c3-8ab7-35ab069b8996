#!/usr/bin/env python3
"""
API-Based Nanotech Applications Agent - Discovers hemp in nanotechnology
Focuses on quantum dots, nanofibers, nanocomposites, and advanced nanomaterials
"""

import json
import time
import requests
from datetime import datetime
from typing import Dict, List, Optional, Set
import os
import re
import random
from difflib import SequenceMatcher

class APINanotechApplicationsAgent:
    """Nanotechnology hemp applications discovery with duplicate prevention"""
    
    def __init__(self):
        self.name = "API Nanotech Applications Agent"
        self.version = "1.0.0"
        self.discovered_count = 0
        
        # Supabase configuration
        self.supabase_url = "https://ktoqznqmlnxrtvubewyz.supabase.co"
        self.service_role_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt0b3F6bnFtbG54cnR2dWJld3l6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODQ5MTc3NiwiZXhwIjoyMDY0MDY3Nzc2fQ.hxEiOy6xebv7I4CYJEU49oCEXRvmU6iz2l8Kg-NnvTs"
        
        self.headers = {
            "apikey": self.service_role_key,
            "Authorization": f"Bearer {self.service_role_key}",
            "Content-Type": "application/json",
            "Prefer": "return=minimal"
        }
        
        # Cache for existing products
        self.existing_products_cache = set()
        self.normalized_cache = {}
        self.load_existing_products()
        
        # Nanotechnology applications database
        self.nanotech_applications = [
            # Nanofibers & Nanofilaments
            {
                "category": "nanofibers",
                "subcategory": "electrospun_fibers",
                "applications": [
                    {"name": "Electrospun Nanofiber", "description": "Ultra-fine fiber material"},
                    {"name": "Hollow Nanofiber", "description": "Tubular nanostructure"},
                    {"name": "Core-Shell Nanofiber", "description": "Dual-layer fiber structure"},
                    {"name": "Porous Nanofiber", "description": "High surface area fiber"},
                    {"name": "Aligned Nanofiber Mat", "description": "Directional fiber assembly"}
                ]
            },
            {
                "category": "nanofibers",
                "subcategory": "carbon_nanotubes",
                "applications": [
                    {"name": "Single-Wall Nanotube", "description": "SWNT hemp derivative"},
                    {"name": "Multi-Wall Nanotube", "description": "MWNT nested structure"},
                    {"name": "Functionalized Nanotube", "description": "Modified CNT surface"},
                    {"name": "Nanotube Array", "description": "Organized CNT forest"},
                    {"name": "Nanotube Yarn", "description": "Twisted CNT filament"}
                ]
            },
            # Nanoparticles & Quantum Dots
            {
                "category": "nanoparticles",
                "subcategory": "quantum_dots",
                "applications": [
                    {"name": "Carbon Quantum Dot", "description": "Fluorescent nanoparticle"},
                    {"name": "Graphene Quantum Dot", "description": "2D material fragment"},
                    {"name": "Photoluminescent Dot", "description": "Light-emitting particle"},
                    {"name": "Quantum Dot LED", "description": "QD display material"},
                    {"name": "Biocompatible Quantum Dot", "description": "Medical imaging particle"}
                ]
            },
            {
                "category": "nanoparticles",
                "subcategory": "metal_nanoparticles",
                "applications": [
                    {"name": "Silver Nanoparticle Carrier", "description": "Antimicrobial delivery"},
                    {"name": "Gold Nanoparticle Template", "description": "Catalyst support"},
                    {"name": "Magnetic Nanoparticle", "description": "Targeted delivery system"},
                    {"name": "Plasmonic Nanoparticle", "description": "Optical enhancement"},
                    {"name": "Core-Shell Nanoparticle", "description": "Protected metal core"}
                ]
            },
            # Nanocomposites
            {
                "category": "nanocomposites",
                "subcategory": "polymer_nanocomposites",
                "applications": [
                    {"name": "Nanofiber Reinforced Polymer", "description": "Enhanced matrix material"},
                    {"name": "Clay Nanocomposite", "description": "Barrier property enhancement"},
                    {"name": "Graphene Nanocomposite", "description": "Conductive polymer blend"},
                    {"name": "Nanocellulose Composite", "description": "Bio-based reinforcement"},
                    {"name": "Hybrid Nanocomposite", "description": "Multi-filler system"}
                ]
            },
            {
                "category": "nanocomposites",
                "subcategory": "ceramic_nanocomposites",
                "applications": [
                    {"name": "Ceramic Matrix Nanocomposite", "description": "High-temp resistant"},
                    {"name": "Bioactive Glass Composite", "description": "Bone regeneration material"},
                    {"name": "Piezoelectric Nanocomposite", "description": "Energy harvesting"},
                    {"name": "Photocatalytic Composite", "description": "Self-cleaning surface"},
                    {"name": "Thermal Barrier Composite", "description": "Insulation material"}
                ]
            },
            # Nanomembranes & Films
            {
                "category": "nanomembranes",
                "subcategory": "filtration_membranes",
                "applications": [
                    {"name": "Ultrafiltration Membrane", "description": "Selective separation"},
                    {"name": "Reverse Osmosis Film", "description": "Water purification"},
                    {"name": "Gas Separation Membrane", "description": "Molecular sieving"},
                    {"name": "Pervaporation Membrane", "description": "Chemical separation"},
                    {"name": "Nanoporous Membrane", "description": "Size-selective filter"}
                ]
            },
            {
                "category": "nanomembranes",
                "subcategory": "barrier_films",
                "applications": [
                    {"name": "Oxygen Barrier Film", "description": "Gas impermeable layer"},
                    {"name": "Moisture Barrier Coating", "description": "Water vapor protection"},
                    {"name": "UV Blocking Film", "description": "Radiation protection"},
                    {"name": "Antimicrobial Film", "description": "Active surface coating"},
                    {"name": "Self-Healing Film", "description": "Damage recovery coating"}
                ]
            },
            # Nanoelectronics
            {
                "category": "nanoelectronics",
                "subcategory": "molecular_electronics",
                "applications": [
                    {"name": "Molecular Switch", "description": "Single molecule device"},
                    {"name": "Organic Transistor", "description": "Flexible electronics"},
                    {"name": "Molecular Memory", "description": "Data storage element"},
                    {"name": "Quantum Wire", "description": "1D conductor"},
                    {"name": "Molecular Diode", "description": "Rectifying junction"}
                ]
            },
            {
                "category": "nanoelectronics",
                "subcategory": "printed_electronics",
                "applications": [
                    {"name": "Conductive Ink", "description": "Printable circuit material"},
                    {"name": "Flexible Antenna", "description": "RF communication"},
                    {"name": "Printed Sensor", "description": "Thin film detector"},
                    {"name": "RFID Tag Material", "description": "Wireless identification"},
                    {"name": "Printed Battery", "description": "Energy storage film"}
                ]
            },
            # Nanomedicine
            {
                "category": "nanomedicine",
                "subcategory": "drug_delivery",
                "applications": [
                    {"name": "Nanocarrier System", "description": "Targeted drug vehicle"},
                    {"name": "Liposome Alternative", "description": "Biocompatible vesicle"},
                    {"name": "Nanogel Particle", "description": "Responsive drug matrix"},
                    {"name": "Dendrimer Analog", "description": "Branched delivery system"},
                    {"name": "Nanorod Carrier", "description": "Elongated drug vehicle"}
                ]
            },
            {
                "category": "nanomedicine",
                "subcategory": "diagnostics",
                "applications": [
                    {"name": "Biosensor Nanomaterial", "description": "Disease detection"},
                    {"name": "Contrast Agent", "description": "Medical imaging"},
                    {"name": "Lab-on-Chip Material", "description": "Miniaturized testing"},
                    {"name": "Nanoprobe", "description": "Cellular imaging"},
                    {"name": "Theranostic Particle", "description": "Therapy + diagnostics"}
                ]
            },
            # Energy Applications
            {
                "category": "nano_energy",
                "subcategory": "solar_cells",
                "applications": [
                    {"name": "Quantum Dot Solar Cell", "description": "Enhanced light absorption"},
                    {"name": "Dye-Sensitized Material", "description": "DSSC component"},
                    {"name": "Perovskite Template", "description": "Crystal structure base"},
                    {"name": "Transparent Conductor", "description": "ITO alternative"},
                    {"name": "Hole Transport Layer", "description": "Charge carrier material"}
                ]
            },
            {
                "category": "nano_energy",
                "subcategory": "energy_storage",
                "applications": [
                    {"name": "Supercapacitor Electrode", "description": "High surface area"},
                    {"name": "Battery Anode Material", "description": "Li-ion storage"},
                    {"name": "Solid Electrolyte", "description": "Ion conductor"},
                    {"name": "Separator Membrane", "description": "Battery component"},
                    {"name": "Conductive Additive", "description": "Conductivity enhancer"}
                ]
            },
            # Environmental Applications
            {
                "category": "nano_environmental",
                "subcategory": "water_treatment",
                "applications": [
                    {"name": "Photocatalyst Material", "description": "Pollutant degradation"},
                    {"name": "Heavy Metal Adsorbent", "description": "Ion removal"},
                    {"name": "Oil Spill Absorbent", "description": "Hydrophobic material"},
                    {"name": "Antimicrobial Filter", "description": "Pathogen removal"},
                    {"name": "Desalination Membrane", "description": "Salt separation"}
                ]
            },
            {
                "category": "nano_environmental",
                "subcategory": "air_purification",
                "applications": [
                    {"name": "VOC Adsorbent", "description": "Organic vapor capture"},
                    {"name": "Particulate Filter", "description": "PM2.5 removal"},
                    {"name": "Catalytic Converter", "description": "Emission control"},
                    {"name": "CO2 Capture Material", "description": "Carbon sequestration"},
                    {"name": "Bioaerosol Filter", "description": "Microbe removal"}
                ]
            }
        ]
        
        # Product templates by category
        self.product_templates = {
            "nanofibers": [
                "Hemp Nanofiber {} Material",
                "Nano-Hemp {} Fiber",
                "Advanced Hemp {} Nanofilament"
            ],
            "nanoparticles": [
                "Hemp {} Nanoparticle",
                "Nano-Hemp {} Quantum Material",
                "Hemp-Derived {} Nanodot"
            ],
            "nanocomposites": [
                "Hemp {} Nanocomposite",
                "Nano-Reinforced Hemp {} Material",
                "Hemp-Based {} Nanostructure"
            ],
            "nanomembranes": [
                "Hemp {} Nanomembrane",
                "Nano-Hemp {} Film",
                "Hemp-Based {} Nanobarrier"
            ],
            "nanoelectronics": [
                "Hemp {} Nanoelectronic Material",
                "Nano-Hemp {} Electronic Component",
                "Hemp-Derived {} Nanodevice"
            ],
            "nanomedicine": [
                "Medical Hemp {} Nanomaterial",
                "Nano-Hemp {} Biomedical System",
                "Hemp-Based {} Nanocarrier"
            ],
            "nano_energy": [
                "Energy Hemp {} Nanomaterial",
                "Nano-Hemp {} Power Component",
                "Hemp-Based {} Energy Nanostructure"
            ],
            "nano_environmental": [
                "Environmental Hemp {} Nanomaterial",
                "Nano-Hemp {} Remediation System",
                "Hemp-Based {} Nanofilter"
            ]
        }
        
        # Industry mapping
        self.industry_id = 18  # Advanced Materials

    def load_existing_products(self):
        """Load existing products into cache"""
        print("Loading existing products into cache...")
        offset = 0
        limit = 1000
        
        while True:
            response = requests.get(
                f"{self.supabase_url}/rest/v1/uses_products",
                headers=self.headers,
                params={
                    "select": "name",
                    "order": "id",
                    "offset": offset,
                    "limit": limit
                }
            )
            
            if response.status_code != 200 or not response.json():
                break
                
            batch = response.json()
            for product in batch:
                name = product['name']
                self.existing_products_cache.add(name.lower())
                normalized = self.normalize_name(name)
                self.normalized_cache[normalized.lower()] = name
            
            if len(batch) < limit:
                break
            offset += limit
        
        print(f"Loaded {len(self.existing_products_cache)} existing products")

    def normalize_name(self, name: str) -> str:
        """Normalize product name"""
        name = ' '.join(name.split())
        name = re.sub(r'\b(\w+)\s+\1\b', r'\1', name)
        name = name.replace('Hemp-Based', 'Hemp')
        name = name.replace('Hemp-Derived', 'Hemp')
        name = re.sub(r'(\w+)\s+\1$', r'\1', name)
        return name.strip()

    def check_duplicate_advanced(self, name: str, threshold: float = 0.85) -> bool:
        """Advanced duplicate detection"""
        name_lower = name.lower()
        normalized = self.normalize_name(name)
        normalized_lower = normalized.lower()
        
        if name_lower in self.existing_products_cache:
            return True
        
        if normalized_lower in self.normalized_cache:
            print(f"  ⚠️  Similar to existing: {self.normalized_cache[normalized_lower]}")
            return True
        
        for existing in self.existing_products_cache:
            similarity = SequenceMatcher(None, normalized_lower, existing).ratio()
            if similarity > threshold:
                print(f"  ⚠️  Too similar ({similarity:.2f}) to existing product")
                return True
        
        return False

    def get_plant_part_for_category(self, category: str) -> int:
        """Get appropriate plant part ID for nanotech category"""
        mapping = {
            "nanofibers": 1,          # Fiber for fiber-based nano
            "nanoparticles": 6,       # Flower/Extract for chemical extraction
            "nanocomposites": 7,      # Whole plant for complex materials
            "nanomembranes": 1,       # Fiber for membrane structures
            "nanoelectronics": 6,     # Flower/Extract for conductive materials
            "nanomedicine": 6,        # Flower/Extract for bioactive compounds
            "nano_energy": 7,         # Whole plant for energy applications
            "nano_environmental": 7   # Whole plant for environmental uses
        }
        return mapping.get(category, 7)

    def generate_nanotech_description(self, app_name: str, app_desc: str, 
                                    category: str, subcategory: str) -> str:
        """Generate detailed nanotech product description"""
        descriptions = {
            "nanofibers": f"Advanced {app_name} engineered at nanoscale for {subcategory.replace('_', ' ')} applications. "
                         f"{app_desc}. Utilizing hemp's unique molecular structure to create ultra-fine fibers "
                         f"with exceptional mechanical and functional properties at the nanometer scale.",
            
            "nanoparticles": f"Precision-engineered {app_name} for {subcategory.replace('_', ' ')} technologies. "
                            f"{app_desc}. Hemp-derived nanoparticles offer controllable size, surface chemistry, "
                            f"and optical properties for cutting-edge nanotechnology applications.",
            
            "nanocomposites": f"High-performance {app_name} developed for {subcategory.replace('_', ' ')}. "
                             f"{app_desc}. Incorporates hemp nanomaterials to enhance mechanical, thermal, "
                             f"and functional properties beyond conventional composite materials.",
            
            "nanomembranes": f"Selective {app_name} designed for {subcategory.replace('_', ' ')} processes. "
                            f"{app_desc}. Exploits hemp's nanoscale architecture to create membranes with "
                            f"precise pore control and exceptional separation capabilities.",
            
            "nanoelectronics": f"Next-generation {app_name} enabling {subcategory.replace('_', ' ')} devices. "
                              f"{app_desc}. Hemp-based molecular electronics push boundaries of miniaturization "
                              f"with sustainable, organic semiconductor materials.",
            
            "nanomedicine": f"Biocompatible {app_name} for {subcategory.replace('_', ' ')} applications. "
                           f"{app_desc}. Leverages hemp's natural biocompatibility at the nanoscale for "
                           f"targeted therapeutic delivery and advanced medical diagnostics.",
            
            "nano_energy": f"Efficient {app_name} optimized for {subcategory.replace('_', ' ')} systems. "
                          f"{app_desc}. Hemp nanomaterials enable breakthrough energy conversion and "
                          f"storage with sustainable, high-performance characteristics.",
            
            "nano_environmental": f"Sustainable {app_name} for {subcategory.replace('_', ' ')} solutions. "
                                 f"{app_desc}. Hemp-based nanotechnology provides eco-friendly remediation "
                                 f"and purification at molecular scales for environmental protection."
        }
        
        return descriptions.get(category, f"Nanotech {app_name}. {app_desc}.")

    def generate_nanotech_benefits(self, category: str, subcategory: str) -> str:
        """Generate benefits based on nanotech application"""
        base_benefits = ["Nanoscale precision", "Enhanced surface area", "Quantum effects potential"]
        
        category_benefits = {
            "nanofibers": ["Ultra-high strength", "Extreme flexibility", "Tunable properties"],
            "nanoparticles": ["Size-dependent properties", "High reactivity", "Quantum confinement"],
            "nanocomposites": ["Superior mechanical properties", "Multifunctionality", "Lightweight design"],
            "nanomembranes": ["Selective permeability", "High flux rates", "Molecular sieving"],
            "nanoelectronics": ["Quantum computing potential", "Flexible circuits", "Single molecule sensing"],
            "nanomedicine": ["Targeted delivery", "Biocompatibility", "Controlled release"],
            "nano_energy": ["High efficiency", "Quantum yield", "Enhanced conductivity"],
            "nano_environmental": ["High adsorption capacity", "Photocatalytic activity", "Selective removal"]
        }
        
        benefits = base_benefits + category_benefits.get(category, ["Advanced nanotechnology"])
        return "{" + ",".join([f'"{b}"' for b in benefits]) + "}"

    def create_nanotech_products(self, nano_group: Dict) -> List[Dict]:
        """Create products from nanotech application group"""
        products = []
        category = nano_group["category"]
        subcategory = nano_group["subcategory"]
        
        for app in nano_group["applications"]:
            # Generate 1-2 variations per application
            num_variations = random.randint(1, 2)
            
            for i in range(num_variations):
                templates = self.product_templates.get(category, ["Hemp {} Nanomaterial"])
                template = random.choice(templates)
                
                # Create product name
                if i == 0:
                    product_name = template.format(app["name"])
                else:
                    modifiers = ["Advanced", "Quantum", "Ultra", "Next-Gen", "High-Performance"]
                    modifier = random.choice(modifiers)
                    product_name = template.format(f"{modifier} {app['name']}")
                
                product_name = self.normalize_name(product_name)
                
                # Check for duplicates
                if self.check_duplicate_advanced(product_name):
                    continue
                
                # Determine plant part
                plant_part_id = self.get_plant_part_for_category(category)
                
                # Calculate confidence score (0.7-0.95 for nanotech)
                confidence_score = round(0.7 + (random.random() * 0.25), 2)
                
                product = {
                    'name': product_name,
                    'description': self.generate_nanotech_description(
                        app["name"], app["description"], category, subcategory
                    ),
                    'industry_sub_category_id': self.industry_id,
                    'plant_part_id': plant_part_id,
                    'confidence_score': confidence_score,
                    'image_url': None,
                    'benefits_advantages': self.generate_nanotech_benefits(category, subcategory),
                    'technical_specifications': json.dumps({
                        "category": category,
                        "subcategory": subcategory,
                        "application": "nanotechnology",
                        "scale": "1-100 nanometers",
                        "synthesis_method": "Various",
                        "characterization": ["TEM", "SEM", "AFM", "XRD"]
                    }),
                    'source_type': 'ai_agent',
                    'source_agent': self.name,
                    'source_url': f"Nanotech: {category}/{subcategory}"
                }
                
                products.append(product)
                
                # Add to cache immediately
                self.existing_products_cache.add(product_name.lower())
                self.normalized_cache[product_name.lower()] = product_name
        
        return products

    def save_product_api(self, product_data: Dict) -> bool:
        """Save product using REST API"""
        try:
            response = requests.post(
                f"{self.supabase_url}/rest/v1/uses_products",
                headers=self.headers,
                json=product_data
            )
            
            if response.status_code in [200, 201, 204]:
                self.discovered_count += 1
                return True
            else:
                print(f"Failed to save product: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"Error saving product: {e}")
            return False

    def run_discovery(self) -> Dict:
        """Run nanotechnology applications discovery"""
        start_time = time.time()
        total_saved = 0
        total_duplicates = 0
        total_errors = 0
        
        print(f"\n🔬 {self.name} - Starting Discovery Cycle")
        print(f"Nanotech categories: {len(self.nanotech_applications)}")
        print("=" * 60)
        
        for idx, nano_group in enumerate(self.nanotech_applications, 1):
            category = nano_group["category"]
            subcategory = nano_group["subcategory"]
            num_apps = len(nano_group["applications"])
            
            print(f"\n[{idx}/{len(self.nanotech_applications)}] {category.title().replace('_', ' ')} - {subcategory.title().replace('_', ' ')}")
            print(f"  Applications in category: {num_apps}")
            
            try:
                # Create products from nano group
                products = self.create_nanotech_products(nano_group)
                print(f"  Generated {len(products)} product variations")
                
                # Save each product
                saved = 0
                for product in products:
                    if self.save_product_api(product):
                        saved += 1
                        total_saved += 1
                        print(f"  ✅ {product['name']}")
                    else:
                        total_errors += 1
                
                # Track duplicates prevented
                expected = num_apps * 1.5  # Average expected variations
                prevented = max(0, expected - len(products))
                total_duplicates += int(prevented)
                
                print(f"  Saved {saved} new products")
                
                # Rate limiting
                time.sleep(0.5)
                
            except Exception as e:
                print(f"  ❌ Error processing category: {e}")
                total_errors += 1
        
        # Summary
        duration = time.time() - start_time
        print(f"\n{'=' * 60}")
        print(f"Discovery Complete!")
        print(f"  Duration: {duration:.1f} seconds")
        print(f"  Categories processed: {len(self.nanotech_applications)}")
        print(f"  New products saved: {total_saved}")
        print(f"  Duplicates prevented: ~{total_duplicates}")
        print(f"  Errors: {total_errors}")
        
        return {
            'agent': self.name,
            'duration': duration,
            'categories_processed': len(self.nanotech_applications),
            'products_saved': total_saved,
            'duplicates_prevented': total_duplicates,
            'errors': total_errors
        }


if __name__ == "__main__":
    agent = APINanotechApplicationsAgent()
    results = agent.run_discovery()
    print(f"\nResults: {json.dumps(results, indent=2)}")