#!/usr/bin/env python3
"""
Regional/Cultural Agent - Discovers traditional and cultural hemp uses
Part of the Hemp Database Expansion Initiative
"""

import json
import time
import random
from datetime import datetime
from typing import Dict, List, Optional
import psycopg2
import os

class RegionalCulturalAgent:
    """Discovers traditional and regional hemp product uses"""
    
    def __init__(self):
        self.name = "Regional/Cultural Agent"
        self.version = "1.0.0"
        self.discovered_count = 0
        self.database_url = os.environ.get('DATABASE_URL')
        
        # Regional/Cultural hemp traditions database
        self.cultural_uses = [
            # Asian Traditions
            {
                "region": "Japan",
                "culture": "Japanese",
                "traditional_name": "Asa",
                "uses": [
                    {
                        "name": "Shimenawa Sacred Rope",
                        "description": "Traditional Shinto ceremonial rope made from hemp fibers",
                        "category": "religious"
                    },
                    {
                        "name": "Noren Door Curtains",
                        "description": "Traditional hemp fabric curtains for shops and homes",
                        "category": "textile"
                    },
                    {
                        "name": "Kimono Hemp Fabric",
                        "description": "High-quality hemp fabric for traditional garments",
                        "category": "textile"
                    }
                ]
            },
            {
                "region": "China",
                "culture": "Chinese",
                "traditional_name": "Ma",
                "uses": [
                    {
                        "name": "Ma Ren Wan Medicine",
                        "description": "Traditional Chinese medicine using hemp seeds",
                        "category": "medicine"
                    },
                    {
                        "name": "Hemp Paper (Cai Lun)",
                        "description": "Ancient paper-making technique using hemp fibers",
                        "category": "paper"
                    },
                    {
                        "name": "Hemp Rope Bridges",
                        "description": "Traditional suspension bridges using hemp rope",
                        "category": "construction"
                    }
                ]
            },
            {
                "region": "India",
                "culture": "Indian",
                "traditional_name": "Bhang/Ganja",
                "uses": [
                    {
                        "name": "Bhang Lassi",
                        "description": "Traditional beverage made with hemp leaves",
                        "category": "food"
                    },
                    {
                        "name": "Ayurvedic Hemp Medicine",
                        "description": "Traditional medicine preparations using hemp",
                        "category": "medicine"
                    },
                    {
                        "name": "Hemp Rope (Sutli)",
                        "description": "Traditional rope and twine for various uses",
                        "category": "material"
                    }
                ]
            },
            
            # European Traditions
            {
                "region": "Eastern Europe",
                "culture": "Slavic",
                "traditional_name": "Konopie",
                "uses": [
                    {
                        "name": "Hemp Seed Oil (Olej Konopny)",
                        "description": "Traditional cooking oil and medicine",
                        "category": "food"
                    },
                    {
                        "name": "Hemp Cloth (Płótno)",
                        "description": "Traditional fabric for clothing and household items",
                        "category": "textile"
                    },
                    {
                        "name": "Hemp Rope Crafts",
                        "description": "Traditional rope-making and crafts",
                        "category": "craft"
                    }
                ]
            },
            {
                "region": "Mediterranean",
                "culture": "Italian",
                "traditional_name": "Canapa",
                "uses": [
                    {
                        "name": "Hemp Pasta",
                        "description": "Traditional pasta made with hemp flour",
                        "category": "food"
                    },
                    {
                        "name": "Maritime Hemp Rope",
                        "description": "Ship rigging and naval applications",
                        "category": "marine"
                    },
                    {
                        "name": "Hemp Building Plaster",
                        "description": "Traditional building material mixed with lime",
                        "category": "construction"
                    }
                ]
            },
            {
                "region": "Baltic",
                "culture": "Lithuanian",
                "traditional_name": "Kanapės",
                "uses": [
                    {
                        "name": "Hemp Butter (Kanapių Sviestas)",
                        "description": "Traditional spread made from hemp seeds",
                        "category": "food"
                    },
                    {
                        "name": "Wedding Hemp Cloth",
                        "description": "Ceremonial textiles for traditional weddings",
                        "category": "textile"
                    },
                    {
                        "name": "Hemp Fishing Nets",
                        "description": "Traditional fishing equipment",
                        "category": "fishing"
                    }
                ]
            },
            
            # American Traditions
            {
                "region": "Native American",
                "culture": "Cherokee",
                "traditional_name": "Gatunlati",
                "uses": [
                    {
                        "name": "Ceremonial Hemp Bags",
                        "description": "Sacred medicine bags made from hemp fiber",
                        "category": "religious"
                    },
                    {
                        "name": "Hemp Bow Strings",
                        "description": "Traditional hunting equipment",
                        "category": "hunting"
                    },
                    {
                        "name": "Hemp Medicine Poultices",
                        "description": "Traditional healing applications",
                        "category": "medicine"
                    }
                ]
            },
            {
                "region": "Colonial America",
                "culture": "Early American",
                "traditional_name": "Hemp",
                "uses": [
                    {
                        "name": "Colonial Hemp Canvas",
                        "description": "Sail cloth and covered wagon material",
                        "category": "textile"
                    },
                    {
                        "name": "Hemp Lamp Oil",
                        "description": "Traditional lighting fuel",
                        "category": "energy"
                    },
                    {
                        "name": "Hemp Paper Currency",
                        "description": "Early American paper money material",
                        "category": "paper"
                    }
                ]
            },
            
            # African Traditions
            {
                "region": "North Africa",
                "culture": "Moroccan",
                "traditional_name": "Kif",
                "uses": [
                    {
                        "name": "Hemp Soap (Saboun Beldi)",
                        "description": "Traditional black soap with hemp oil",
                        "category": "cosmetics"
                    },
                    {
                        "name": "Hemp Textile Bags",
                        "description": "Traditional market bags and carriers",
                        "category": "textile"
                    },
                    {
                        "name": "Hemp Building Mortar",
                        "description": "Traditional construction binding material",
                        "category": "construction"
                    }
                ]
            },
            {
                "region": "South Africa",
                "culture": "Zulu",
                "traditional_name": "Insangu",
                "uses": [
                    {
                        "name": "Traditional Hemp Medicine",
                        "description": "Medicinal preparations for various ailments",
                        "category": "medicine"
                    },
                    {
                        "name": "Hemp Fiber Baskets",
                        "description": "Traditional woven containers",
                        "category": "craft"
                    },
                    {
                        "name": "Hemp Rope Sandals",
                        "description": "Traditional footwear",
                        "category": "textile"
                    }
                ]
            },
            
            # Middle Eastern Traditions
            {
                "region": "Turkey",
                "culture": "Turkish",
                "traditional_name": "Kenevir",
                "uses": [
                    {
                        "name": "Hemp Halva",
                        "description": "Traditional sweet made with hemp seeds",
                        "category": "food"
                    },
                    {
                        "name": "Hemp Prayer Rugs",
                        "description": "Traditional prayer mats",
                        "category": "religious"
                    },
                    {
                        "name": "Hemp Coffee (Kenevir Kahvesi)",
                        "description": "Traditional hemp seed coffee substitute",
                        "category": "food"
                    }
                ]
            },
            {
                "region": "Iran",
                "culture": "Persian",
                "traditional_name": "Shahdaneh",
                "uses": [
                    {
                        "name": "Hemp Seed Halvah",
                        "description": "Traditional confection with hemp seeds",
                        "category": "food"
                    },
                    {
                        "name": "Persian Hemp Carpets",
                        "description": "Traditional rug backing material",
                        "category": "textile"
                    },
                    {
                        "name": "Hemp Water Pipes",
                        "description": "Traditional smoking implements",
                        "category": "craft"
                    }
                ]
            },
            
            # Pacific Traditions
            {
                "region": "Pacific Islands",
                "culture": "Polynesian",
                "traditional_name": "Aute",
                "uses": [
                    {
                        "name": "Hemp Tapa Cloth",
                        "description": "Traditional bark cloth alternative",
                        "category": "textile"
                    },
                    {
                        "name": "Hemp Fishing Lines",
                        "description": "Traditional deep-sea fishing equipment",
                        "category": "fishing"
                    },
                    {
                        "name": "Hemp Lei Cord",
                        "description": "Traditional flower garland string",
                        "category": "craft"
                    }
                ]
            },
            
            # South American Traditions
            {
                "region": "Andes",
                "culture": "Andean",
                "traditional_name": "Cáñamo",
                "uses": [
                    {
                        "name": "Hemp Carrying Cloths",
                        "description": "Traditional aguayo carrying cloths",
                        "category": "textile"
                    },
                    {
                        "name": "Hemp Altitude Medicine",
                        "description": "Traditional remedy for altitude sickness",
                        "category": "medicine"
                    },
                    {
                        "name": "Hemp Rope Bridges (Q'eswachaka)",
                        "description": "Traditional Incan bridge construction",
                        "category": "construction"
                    }
                ]
            },
            
            # Australian Aboriginal
            {
                "region": "Australia",
                "culture": "Aboriginal",
                "traditional_name": "Native Hemp",
                "uses": [
                    {
                        "name": "Hemp Fiber Nets",
                        "description": "Traditional fishing and hunting nets",
                        "category": "hunting"
                    },
                    {
                        "name": "Hemp String Bags (Dilly Bags)",
                        "description": "Traditional carrying bags",
                        "category": "craft"
                    },
                    {
                        "name": "Hemp Fiber Rope Art",
                        "description": "Traditional ceremonial rope art",
                        "category": "art"
                    }
                ]
            }
        ]
        
        # Product templates
        self.templates = {
            "traditional": "Traditional {} Hemp {}",
            "cultural": "{} Cultural Hemp {}",
            "regional": "{} Regional Hemp {}",
            "ancestral": "Ancestral {} Hemp {}",
            "heritage": "{} Heritage Hemp {}",
            "artisanal": "Artisanal {} Hemp {}"
        }
    
    def generate_products_from_tradition(self, tradition: Dict) -> List[Dict]:
        """Generate products from cultural tradition"""
        products = []
        region = tradition['region']
        culture = tradition['culture']
        
        for use in tradition['uses']:
            # Generate variations
            variations = self.generate_cultural_variations(use, culture)
            
            for variation in variations[:2]:  # 2 variations per use
                # Select template
                template = random.choice(list(self.templates.values()))
                product_name = template.format(culture, variation)
                
                # Generate description
                description = self.create_cultural_description(
                    product_name, use, tradition, variation
                )
                
                # Generate specifications
                tech_specs = self.generate_cultural_specs(use, tradition)
                
                # Generate benefits
                benefits = self.generate_cultural_benefits(use, culture)
                
                # Map to categories
                plant_part_id = self.map_use_to_plant_part(use)
                industry_id = self.map_use_to_industry(use)
                
                product = {
                    'name': product_name,
                    'description': description,
                    'plant_part_id': plant_part_id,
                    'industry_id': industry_id,
                    'benefits': benefits,
                    'technical_specifications': tech_specs,
                    'source_agent': self.name,
                    'confidence_score': 0.70 + random.random() * 0.20
                }
                
                products.append(product)
        
        return products
    
    def generate_cultural_variations(self, use: Dict, culture: str) -> List[str]:
        """Generate product variations based on cultural use"""
        base_name = use['name'].split()[0]  # First word of traditional name
        category = use['category']
        
        # Category-based variations
        variations = {
            'textile': ['Fabric', 'Cloth', 'Textile', 'Material'],
            'food': ['Product', 'Delicacy', 'Ingredient', 'Preparation'],
            'medicine': ['Remedy', 'Treatment', 'Preparation', 'Medicine'],
            'construction': ['Material', 'Component', 'System', 'Product'],
            'religious': ['Item', 'Object', 'Material', 'Product'],
            'craft': ['Craft', 'Artwork', 'Creation', 'Product'],
            'paper': ['Paper', 'Material', 'Product', 'Medium'],
            'marine': ['Equipment', 'Material', 'Product', 'Component'],
            'fishing': ['Equipment', 'Gear', 'Tool', 'Product'],
            'hunting': ['Equipment', 'Tool', 'Gear', 'Product'],
            'cosmetics': ['Product', 'Preparation', 'Formula', 'Treatment'],
            'energy': ['Fuel', 'Source', 'Product', 'Material'],
            'art': ['Artwork', 'Creation', 'Medium', 'Material']
        }
        
        return variations.get(category, ['Product', 'Material', 'Item', 'Creation'])
    
    def create_cultural_description(self, name: str, use: Dict, 
                                  tradition: Dict, variation: str) -> str:
        """Create culturally-informed product description"""
        culture = tradition['culture']
        region = tradition['region']
        trad_name = tradition['traditional_name']
        
        desc = f"{name} inspired by {culture} traditions from {region}. "
        desc += f"Known locally as '{trad_name}', this represents centuries of cultural heritage. "
        desc += f"{use['description']} "
        desc += f"This traditional {variation.lower()} showcases the deep cultural significance "
        desc += f"of hemp in {culture} society. Modern production methods preserve "
        desc += f"the authentic qualities while meeting contemporary standards."
        
        return desc
    
    def generate_cultural_specs(self, use: Dict, tradition: Dict) -> Dict:
        """Generate specifications for cultural product"""
        specs = {
            "cultural_origin": tradition['culture'],
            "region": tradition['region'],
            "traditional_name": tradition['traditional_name'],
            "category": use['category'],
            "authenticity": "Culturally verified",
            "production_method": "Traditional techniques adapted"
        }
        
        # Category-specific specs
        category = use['category']
        if category == 'textile':
            specs.update({
                "weave_pattern": "Traditional",
                "fiber_processing": "Cultural method",
                "durability": "Generational"
            })
        elif category == 'food':
            specs.update({
                "preparation_method": "Traditional",
                "ingredients": "Authentic recipe",
                "shelf_life": "Natural preservation"
            })
        elif category == 'medicine':
            specs.update({
                "preparation": "Traditional method",
                "dosage": "Cultural practice",
                "application": "Time-tested"
            })
        elif category == 'construction':
            specs.update({
                "mixing_ratio": "Traditional",
                "curing_method": "Cultural technique",
                "longevity": "Centuries proven"
            })
        
        return specs
    
    def generate_cultural_benefits(self, use: Dict, culture: str) -> List[str]:
        """Generate benefits highlighting cultural value"""
        base_benefits = [
            f"Preserves {culture} cultural heritage",
            "Time-tested through generations",
            "Sustainable traditional methods"
        ]
        
        # Category-specific benefits
        category_benefits = {
            'textile': [
                "Natural fiber with cultural significance",
                "Traditional weaving techniques preserved"
            ],
            'food': [
                "Nutritional wisdom from ancestral diets",
                "Authentic cultural flavor profiles"
            ],
            'medicine': [
                "Traditional healing properties",
                "Holistic health approach"
            ],
            'construction': [
                "Proven durability over centuries",
                "Climate-adapted building method"
            ],
            'religious': [
                "Sacred and ceremonial significance",
                "Spiritual connection to heritage"
            ],
            'craft': [
                "Artisanal quality and uniqueness",
                "Supports cultural craft traditions"
            ]
        }
        
        category = use['category']
        if category in category_benefits:
            base_benefits.extend(category_benefits[category])
        
        return base_benefits[:5]
    
    def map_use_to_plant_part(self, use: Dict) -> int:
        """Map cultural use to plant part"""
        category = use['category']
        
        mapping = {
            'textile': 1,      # Hemp Bast (Fiber)
            'food': 2,         # Hemp Seed
            'medicine': 4,     # Cannabinoids (often)
            'construction': 7, # Hemp Hurd
            'religious': 1,    # Hemp Bast (Fiber)
            'craft': 1,        # Hemp Bast (Fiber)
            'paper': 1,        # Hemp Bast (Fiber)
            'marine': 1,       # Hemp Bast (Fiber)
            'fishing': 1,      # Hemp Bast (Fiber)
            'hunting': 1,      # Hemp Bast (Fiber)
            'cosmetics': 3,    # Hemp Seed Oil
            'energy': 3,       # Hemp Seed Oil
            'art': 1           # Hemp Bast (Fiber)
        }
        
        return mapping.get(category, 1)
    
    def map_use_to_industry(self, use: Dict) -> int:
        """Map cultural use to industry"""
        category = use['category']
        
        mapping = {
            'textile': 2,       # Textiles
            'food': 8,          # Food/Nutrition
            'medicine': 4,      # Pharmaceutical
            'construction': 1,  # Construction
            'religious': 19,    # Cultural/Religious
            'craft': 20,        # Arts/Crafts
            'paper': 11,        # Paper/Packaging
            'marine': 15,       # Marine
            'fishing': 15,      # Marine
            'hunting': 14,      # Sports/Recreation
            'cosmetics': 10,    # Personal Care
            'energy': 5,        # Energy
            'art': 20           # Arts/Crafts
        }
        
        return mapping.get(category, 9)  # Default to Advanced Tech
    
    def is_duplicate(self, product: Dict) -> bool:
        """Check if product already exists"""
        try:
            conn = psycopg2.connect(self.database_url)
            cur = conn.cursor()
            
            # Check by name similarity
            cur.execute("""
                SELECT id FROM uses_products
                WHERE similarity(name, %s) > 0.85
                LIMIT 1
            """, (product['name'],))
            
            result = cur.fetchone()
            conn.close()
            
            return result is not None
            
        except Exception as e:
            print(f"Error checking duplicate: {e}")
            return False
    
    def save_product(self, product: Dict) -> bool:
        """Save product to database"""
        if self.is_duplicate(product):
            print(f"  ⚠️  Skipping duplicate: {product['name']}")
            return False
        
        try:
            conn = psycopg2.connect(self.database_url)
            cur = conn.cursor()
            
            cur.execute("""
                INSERT INTO uses_products (
                    name, description, plant_part_id,
                    industry_sub_category_id, benefits_advantages,
                    technical_specifications, source_agent,
                    created_at
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                RETURNING id
            """, (
                product['name'],
                product['description'],
                product['plant_part_id'],
                product['industry_id'],
                product['benefits'],
                json.dumps(product['technical_specifications']),
                product['source_agent'],
                datetime.now()
            ))
            
            product_id = cur.fetchone()[0]
            conn.commit()
            conn.close()
            
            self.discovered_count += 1
            print(f"  ✓ Added: {product['name']} (ID: {product_id})")
            return True
            
        except Exception as e:
            print(f"  ❌ Error saving {product['name']}: {e}")
            return False
    
    def run_discovery_cycle(self):
        """Run cultural discovery cycle"""
        print(f"\n🌍 {self.name} - Starting Discovery Cycle")
        print("=" * 60)
        
        total_products = 0
        
        for i, tradition in enumerate(self.cultural_uses):
            region = tradition['region']
            culture = tradition['culture']
            print(f"\n[{i+1}/{len(self.cultural_uses)}] Exploring {culture} traditions from {region}")
            print(f"  Traditional name: {tradition['traditional_name']}")
            print(f"  Number of uses: {len(tradition['uses'])}")
            
            try:
                # Generate products from this tradition
                products = self.generate_products_from_tradition(tradition)
                print(f"  Generated {len(products)} product variations")
                
                # Save each product
                for product in products:
                    if self.save_product(product):
                        total_products += 1
                
                # Brief pause
                time.sleep(0.5)
                
            except Exception as e:
                print(f"  ❌ Error processing tradition: {e}")
                continue
        
        print(f"\n✅ Cultural discovery cycle complete!")
        print(f"Total products added: {total_products}")
        print(f"Agent total: {self.discovered_count}")
        
        return total_products

if __name__ == "__main__":
    agent = RegionalCulturalAgent()
    agent.run_discovery_cycle()