#!/usr/bin/env python3
"""
API-Based Waste Management Solutions Agent - Discovers hemp applications in waste processing
Focuses on recycling, waste treatment, composting systems, and circular economy solutions
"""

import json
import time
import requests
from datetime import datetime
from typing import Dict, List, Optional, Set
import os
import re
import random
from difflib import SequenceMatcher

class APIWasteManagementAgent:
    """Waste management hemp applications discovery with duplicate prevention"""
    
    def __init__(self):
        self.name = "API Waste Management Solutions Agent"
        self.version = "1.0.0"
        self.discovered_count = 0
        
        # Supabase configuration
        self.supabase_url = "https://ktoqznqmlnxrtvubewyz.supabase.co"
        self.service_role_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt0b3F6bnFtbG54cnR2dWJld3l6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODQ5MTc3NiwiZXhwIjoyMDY0MDY3Nzc2fQ.hxEiOy6xebv7I4CYJEU49oCEXRvmU6iz2l8Kg-NnvTs"
        
        self.headers = {
            "apikey": self.service_role_key,
            "Authorization": f"Bearer {self.service_role_key}",
            "Content-Type": "application/json",
            "Prefer": "return=minimal"
        }
        
        # Cache for existing products
        self.existing_products_cache = set()
        self.normalized_cache = {}
        self.load_existing_products()
        
        # Waste management applications database
        self.waste_applications = [
            # Recycling Systems
            {
                "category": "recycling_systems",
                "subcategory": "sorting_equipment",
                "applications": [
                    {"name": "Sorting Conveyor Belt", "description": "Material transport"},
                    {"name": "Separation Screen", "description": "Size classification"},
                    {"name": "Magnetic Separator Belt", "description": "Metal removal"},
                    {"name": "Optical Sorter Component", "description": "Material identification"},
                    {"name": "Baler Wire Alternative", "description": "Bundle securing"}
                ]
            },
            {
                "category": "recycling_systems",
                "subcategory": "processing_equipment",
                "applications": [
                    {"name": "Shredder Component", "description": "Size reduction"},
                    {"name": "Granulator Part", "description": "Material grinding"},
                    {"name": "Pelletizer Component", "description": "Material forming"},
                    {"name": "Compactor Plate", "description": "Volume reduction"},
                    {"name": "Extruder Part", "description": "Material processing"}
                ]
            },
            # Composting Systems
            {
                "category": "composting_systems",
                "subcategory": "composting_equipment",
                "applications": [
                    {"name": "Compost Bin", "description": "Organic containment"},
                    {"name": "Aeration Tube", "description": "Oxygen supply"},
                    {"name": "Compost Cover", "description": "Weather protection"},
                    {"name": "Turning Equipment Part", "description": "Material mixing"},
                    {"name": "Biofilter Media", "description": "Odor control"}
                ]
            },
            {
                "category": "composting_systems",
                "subcategory": "vermiculture",
                "applications": [
                    {"name": "Worm Bedding", "description": "Habitat material"},
                    {"name": "Vermicompost Bin", "description": "Worm housing"},
                    {"name": "Drainage Layer", "description": "Moisture control"},
                    {"name": "Harvest Screen", "description": "Separation tool"},
                    {"name": "Migration Barrier", "description": "Containment system"}
                ]
            },
            # Waste Collection
            {
                "category": "waste_collection",
                "subcategory": "collection_containers",
                "applications": [
                    {"name": "Waste Bin Liner", "description": "Container protection"},
                    {"name": "Collection Bag", "description": "Waste containment"},
                    {"name": "Recycling Bin", "description": "Material sorting"},
                    {"name": "Dumpster Component", "description": "Large container"},
                    {"name": "Roll-Off Container Part", "description": "Transport container"}
                ]
            },
            {
                "category": "waste_collection",
                "subcategory": "transport_equipment",
                "applications": [
                    {"name": "Truck Bed Liner", "description": "Vehicle protection"},
                    {"name": "Hydraulic Hose Cover", "description": "Equipment protection"},
                    {"name": "Collection Arm Part", "description": "Automated system"},
                    {"name": "Compactor Component", "description": "Volume reduction"},
                    {"name": "Leachate Tank Lining", "description": "Liquid containment"}
                ]
            },
            # Treatment Facilities
            {
                "category": "treatment_facilities",
                "subcategory": "waste_processing",
                "applications": [
                    {"name": "Tipping Floor Surface", "description": "Unloading area"},
                    {"name": "Bunker Wall Lining", "description": "Storage protection"},
                    {"name": "Conveyor Component", "description": "Material movement"},
                    {"name": "Screening Equipment", "description": "Size separation"},
                    {"name": "Baling Press Part", "description": "Compaction equipment"}
                ]
            },
            {
                "category": "treatment_facilities",
                "subcategory": "odor_control",
                "applications": [
                    {"name": "Biofilter Medium", "description": "Odor absorption"},
                    {"name": "Scrubber Packing", "description": "Gas treatment"},
                    {"name": "Activated Carbon Alternative", "description": "Air purification"},
                    {"name": "Misting System Component", "description": "Odor suppression"},
                    {"name": "Cover Material", "description": "Emission control"}
                ]
            },
            # Landfill Operations
            {
                "category": "landfill_operations",
                "subcategory": "landfill_infrastructure",
                "applications": [
                    {"name": "Daily Cover", "description": "Waste coverage"},
                    {"name": "Erosion Control Mat", "description": "Slope protection"},
                    {"name": "Gas Collection Pipe", "description": "Methane capture"},
                    {"name": "Leachate Pipe", "description": "Liquid collection"},
                    {"name": "Cap System Component", "description": "Final closure"}
                ]
            },
            {
                "category": "landfill_operations",
                "subcategory": "environmental_protection",
                "applications": [
                    {"name": "Liner System Component", "description": "Groundwater protection"},
                    {"name": "Drainage Layer", "description": "Water management"},
                    {"name": "Geotextile Alternative", "description": "Separation layer"},
                    {"name": "Monitoring Well Cap", "description": "Testing access"},
                    {"name": "Stormwater Channel", "description": "Runoff control"}
                ]
            },
            # Hazardous Waste
            {
                "category": "hazardous_waste",
                "subcategory": "containment_systems",
                "applications": [
                    {"name": "Secondary Containment", "description": "Spill protection"},
                    {"name": "Chemical Storage Liner", "description": "Tank protection"},
                    {"name": "Spill Kit Component", "description": "Emergency response"},
                    {"name": "Absorbent Material", "description": "Liquid cleanup"},
                    {"name": "Containment Berm", "description": "Area protection"}
                ]
            },
            {
                "category": "hazardous_waste",
                "subcategory": "treatment_equipment",
                "applications": [
                    {"name": "Neutralization Tank Part", "description": "Chemical treatment"},
                    {"name": "Filter Press Component", "description": "Solid separation"},
                    {"name": "Incinerator Part", "description": "Thermal treatment"},
                    {"name": "Scrubber Component", "description": "Emission control"},
                    {"name": "Storage Drum Liner", "description": "Container protection"}
                ]
            },
            # Organic Waste
            {
                "category": "organic_waste",
                "subcategory": "anaerobic_digestion",
                "applications": [
                    {"name": "Digester Insulation", "description": "Heat retention"},
                    {"name": "Gas Holder Component", "description": "Biogas storage"},
                    {"name": "Mixing System Part", "description": "Material agitation"},
                    {"name": "Feed System Component", "description": "Input control"},
                    {"name": "Digestate Storage", "description": "Output containment"}
                ]
            },
            {
                "category": "organic_waste",
                "subcategory": "food_waste",
                "applications": [
                    {"name": "Collection Container", "description": "Food waste bin"},
                    {"name": "Dehydrator Component", "description": "Moisture removal"},
                    {"name": "Grinder Part", "description": "Size reduction"},
                    {"name": "Biodigester Component", "description": "Small-scale processing"},
                    {"name": "Compostable Liner", "description": "Container lining"}
                ]
            },
            # E-Waste Management
            {
                "category": "ewaste_management",
                "subcategory": "dismantling_equipment",
                "applications": [
                    {"name": "Dismantling Table", "description": "Work surface"},
                    {"name": "Component Sorter", "description": "Part separation"},
                    {"name": "Cable Stripper Part", "description": "Wire processing"},
                    {"name": "Circuit Board Holder", "description": "Processing fixture"},
                    {"name": "Battery Storage Container", "description": "Safe containment"}
                ]
            },
            {
                "category": "ewaste_management",
                "subcategory": "material_recovery",
                "applications": [
                    {"name": "Precious Metal Separator", "description": "Value recovery"},
                    {"name": "Plastic Sorter Component", "description": "Polymer separation"},
                    {"name": "Glass Crusher Part", "description": "Screen processing"},
                    {"name": "Metal Shredder Component", "description": "Size reduction"},
                    {"name": "Dust Collection Filter", "description": "Air quality"}
                ]
            },
            # Circular Economy
            {
                "category": "circular_economy",
                "subcategory": "upcycling_systems",
                "applications": [
                    {"name": "Material Classifier", "description": "Quality sorting"},
                    {"name": "Reprocessing Equipment", "description": "Material upgrade"},
                    {"name": "Quality Control Station", "description": "Standard checking"},
                    {"name": "Packaging Alternative", "description": "Sustainable wrapping"},
                    {"name": "Storage System", "description": "Material holding"}
                ]
            },
            {
                "category": "circular_economy",
                "subcategory": "waste_to_energy",
                "applications": [
                    {"name": "Gasification Component", "description": "Energy conversion"},
                    {"name": "Pyrolysis Chamber Part", "description": "Thermal processing"},
                    {"name": "Energy Recovery System", "description": "Heat capture"},
                    {"name": "Ash Handling Component", "description": "Residue management"},
                    {"name": "Emission Control Part", "description": "Air quality"}
                ]
            }
        ]
        
        # Product templates by category
        self.product_templates = {
            "recycling_systems": [
                "Recycling Hemp {} Equipment",
                "Material Recovery Hemp {} Component",
                "Recycling Facility Hemp {} Product"
            ],
            "composting_systems": [
                "Composting Hemp {} Solution",
                "Organic Processing Hemp {} Component",
                "Biodegradable Hemp {} Product"
            ],
            "waste_collection": [
                "Waste Collection Hemp {} Equipment",
                "Municipal Hemp {} Component",
                "Collection System Hemp {} Product"
            ],
            "treatment_facilities": [
                "Waste Treatment Hemp {} Component",
                "Processing Facility Hemp {} Equipment",
                "Treatment Plant Hemp {} Product"
            ],
            "landfill_operations": [
                "Landfill Hemp {} Material",
                "Disposal Site Hemp {} Component",
                "Landfill Management Hemp {} Product"
            ],
            "hazardous_waste": [
                "Hazmat Hemp {} Equipment",
                "Chemical Waste Hemp {} Component",
                "Hazardous Material Hemp {} Product"
            ],
            "organic_waste": [
                "Organic Waste Hemp {} Solution",
                "Bio-waste Hemp {} Component",
                "Organic Processing Hemp {} Product"
            ],
            "ewaste_management": [
                "E-Waste Hemp {} Equipment",
                "Electronics Recycling Hemp {} Component",
                "Digital Waste Hemp {} Product"
            ],
            "circular_economy": [
                "Circular Economy Hemp {} Solution",
                "Zero Waste Hemp {} Component",
                "Sustainable Hemp {} Product"
            ]
        }
        
        # Industry mapping
        self.industry_id = 24  # Environmental Services

    def load_existing_products(self):
        """Load existing products into cache"""
        print("Loading existing products into cache...")
        offset = 0
        limit = 1000
        
        while True:
            response = requests.get(
                f"{self.supabase_url}/rest/v1/uses_products",
                headers=self.headers,
                params={
                    "select": "name",
                    "order": "id",
                    "offset": offset,
                    "limit": limit
                }
            )
            
            if response.status_code != 200 or not response.json():
                break
                
            batch = response.json()
            for product in batch:
                name = product['name']
                self.existing_products_cache.add(name.lower())
                normalized = self.normalize_name(name)
                self.normalized_cache[normalized.lower()] = name
            
            if len(batch) < limit:
                break
            offset += limit
        
        print(f"Loaded {len(self.existing_products_cache)} existing products")

    def normalize_name(self, name: str) -> str:
        """Normalize product name"""
        name = ' '.join(name.split())
        name = re.sub(r'\b(\w+)\s+\1\b', r'\1', name)
        name = name.replace('Hemp-Based', 'Hemp')
        name = name.replace('Hemp-Derived', 'Hemp')
        name = re.sub(r'(\w+)\s+\1$', r'\1', name)
        return name.strip()

    def check_duplicate_advanced(self, name: str, threshold: float = 0.85) -> bool:
        """Advanced duplicate detection"""
        name_lower = name.lower()
        normalized = self.normalize_name(name)
        normalized_lower = normalized.lower()
        
        if name_lower in self.existing_products_cache:
            return True
        
        if normalized_lower in self.normalized_cache:
            print(f"  ⚠️  Similar to existing: {self.normalized_cache[normalized_lower]}")
            return True
        
        for existing in self.existing_products_cache:
            similarity = SequenceMatcher(None, normalized_lower, existing).ratio()
            if similarity > threshold:
                print(f"  ⚠️  Too similar ({similarity:.2f}) to existing product")
                return True
        
        return False

    def get_plant_part_for_category(self, category: str) -> int:
        """Get appropriate plant part ID for waste management category"""
        mapping = {
            "recycling_systems": 1,      # Fiber for sorting/processing
            "composting_systems": 7,     # Whole plant for composting
            "waste_collection": 1,       # Fiber for bags/liners
            "treatment_facilities": 3,   # Stem for infrastructure
            "landfill_operations": 3,    # Stem for covers/mats
            "hazardous_waste": 1,        # Fiber for absorption
            "organic_waste": 7,          # Whole plant for bio-processing
            "ewaste_management": 1,      # Fiber for components
            "circular_economy": 7        # Whole plant for full utilization
        }
        return mapping.get(category, 1)

    def generate_waste_description(self, app_name: str, app_desc: str, 
                                  category: str, subcategory: str) -> str:
        """Generate detailed waste management product description"""
        descriptions = {
            "recycling_systems": f"Recycling-grade {app_name} engineered for {subcategory.replace('_', ' ')} operations. "
                                f"{app_desc}. Hemp materials enhance recycling efficiency with durable, "
                                f"sustainable components that reduce equipment wear and maintenance costs.",
            
            "composting_systems": f"Compost-compatible {app_name} designed for {subcategory.replace('_', ' ')} applications. "
                                 f"{app_desc}. Hemp products accelerate composting processes while adding "
                                 f"valuable organic matter and improving final compost quality.",
            
            "waste_collection": f"Collection-ready {app_name} developed for {subcategory.replace('_', ' ')} needs. "
                               f"{app_desc}. Hemp-based collection equipment provides superior strength "
                               f"and biodegradability for sustainable waste management operations.",
            
            "treatment_facilities": f"Facility-grade {app_name} optimized for {subcategory.replace('_', ' ')} processes. "
                                   f"{app_desc}. Hemp components withstand continuous operation in waste "
                                   f"treatment facilities while contributing to environmental sustainability.",
            
            "landfill_operations": f"Landfill-certified {app_name} built for {subcategory.replace('_', ' ')} requirements. "
                                  f"{app_desc}. Hemp materials provide effective containment and environmental "
                                  f"protection in landfill operations with natural biodegradation properties.",
            
            "hazardous_waste": f"Hazmat-rated {app_name} engineered for {subcategory.replace('_', ' ')} handling. "
                              f"{app_desc}. Hemp-based hazardous waste solutions offer chemical resistance "
                              f"and absorption capabilities while maintaining safety compliance.",
            
            "organic_waste": f"Bio-processing {app_name} designed for {subcategory.replace('_', ' ')} systems. "
                            f"{app_desc}. Hemp organic waste products enhance biological processing "
                            f"efficiency and contribute to nutrient-rich end products.",
            
            "ewaste_management": f"E-waste specialized {app_name} created for {subcategory.replace('_', ' ')} operations. "
                                f"{app_desc}. Hemp components facilitate safe electronic waste processing "
                                f"while providing sustainable alternatives to traditional materials.",
            
            "circular_economy": f"Zero-waste {app_name} developed for {subcategory.replace('_', ' ')} initiatives. "
                               f"{app_desc}. Hemp circular economy solutions maximize resource recovery "
                               f"and minimize environmental impact through innovative reuse applications."
        }
        
        return descriptions.get(category, f"Waste management {app_name}. {app_desc}.")

    def generate_waste_benefits(self, category: str, subcategory: str) -> str:
        """Generate benefits based on waste management application"""
        base_benefits = ["Biodegradable", "Sustainable material", "Reduces landfill"]
        
        category_benefits = {
            "recycling_systems": ["Improves sorting efficiency", "Wear resistant", "Easy maintenance"],
            "composting_systems": ["Accelerates decomposition", "Adds nutrients", "Controls odor"],
            "waste_collection": ["Puncture resistant", "Leak proof", "UV stable"],
            "treatment_facilities": ["Chemical resistant", "High throughput", "Long service life"],
            "landfill_operations": ["Erosion control", "Methane reduction", "Natural breakdown"],
            "hazardous_waste": ["Chemical absorption", "Spill containment", "Safety compliant"],
            "organic_waste": ["Enhances biogas", "Nutrient retention", "Process efficiency"],
            "ewaste_management": ["Static dissipative", "Component protection", "Recyclable"],
            "circular_economy": ["Zero waste design", "Value retention", "Closed loop system"]
        }
        
        benefits = base_benefits + category_benefits.get(category, ["Environmental protection"])
        return "{" + ",".join([f'"{b}"' for b in benefits]) + "}"

    def create_waste_products(self, waste_group: Dict) -> List[Dict]:
        """Create products from waste management application group"""
        products = []
        category = waste_group["category"]
        subcategory = waste_group["subcategory"]
        
        for app in waste_group["applications"]:
            # Generate 1-2 variations per application
            num_variations = random.randint(1, 2)
            
            for i in range(num_variations):
                templates = self.product_templates.get(category, ["Hemp {} Waste Management Product"])
                template = random.choice(templates)
                
                # Create product name
                if i == 0:
                    product_name = template.format(app["name"])
                else:
                    modifiers = ["Industrial", "Commercial", "Heavy-Duty", "Eco", "Professional"]
                    modifier = random.choice(modifiers)
                    product_name = template.format(f"{modifier} {app['name']}")
                
                product_name = self.normalize_name(product_name)
                
                # Check for duplicates
                if self.check_duplicate_advanced(product_name):
                    continue
                
                # Determine plant part
                plant_part_id = self.get_plant_part_for_category(category)
                
                # Calculate confidence score (0.7-0.9 for waste management)
                confidence_score = round(0.7 + (random.random() * 0.2), 2)
                
                product_data = {
                    'name': product_name,
                    'description': self.generate_waste_description(
                        app["name"], app["description"], category, subcategory
                    ),
                    'industry_sub_category_id': self.industry_id,
                    'plant_part_id': plant_part_id,
                    'confidence_score': confidence_score,
                    'image_url': None,
                    'benefits_advantages': self.generate_waste_benefits(category, subcategory),
                    'technical_specifications': json.dumps({
                        "category": category,
                        "subcategory": subcategory,
                        "waste_application": subcategory.replace('_', ' '),
                        "equipment_type": app["name"],
                        "environmental_standards": ["EPA compliant", "ISO 14001", "Zero waste certified"],
                        "waste_stream": category.replace('_', ' ').title()
                    }),
                    'source_type': 'ai_agent',
                    'source_agent': self.name,
                    'source_url': f"Waste: {category}/{subcategory}"
                }
                
                products.append(product_data)
                
                # Add to cache immediately
                self.existing_products_cache.add(product_name.lower())
                self.normalized_cache[product_name.lower()] = product_name
        
        return products

    def save_product_api(self, product_data: Dict) -> bool:
        """Save product using REST API"""
        try:
            response = requests.post(
                f"{self.supabase_url}/rest/v1/uses_products",
                headers=self.headers,
                json=product_data
            )
            
            if response.status_code in [200, 201, 204]:
                self.discovered_count += 1
                return True
            else:
                print(f"Failed to save product: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"Error saving product: {e}")
            return False

    def run_discovery(self) -> Dict:
        """Run waste management discovery"""
        start_time = time.time()
        total_saved = 0
        total_duplicates = 0
        total_errors = 0
        
        print(f"\n♻️ {self.name} - Starting Discovery Cycle")
        print(f"Waste management categories: {len(self.waste_applications)}")
        print("=" * 60)
        
        for idx, waste_group in enumerate(self.waste_applications, 1):
            category = waste_group["category"]
            subcategory = waste_group["subcategory"]
            num_apps = len(waste_group["applications"])
            
            print(f"\n[{idx}/{len(self.waste_applications)}] {category.title().replace('_', ' ')} - {subcategory.title().replace('_', ' ')}")
            print(f"  Applications: {num_apps}")
            
            try:
                # Create products from waste group
                products = self.create_waste_products(waste_group)
                print(f"  Generated {len(products)} product variations")
                
                # Save each product
                saved = 0
                for product in products:
                    if self.save_product_api(product):
                        saved += 1
                        total_saved += 1
                        print(f"  ✅ {product['name']}")
                    else:
                        total_errors += 1
                
                # Track duplicates prevented
                expected = num_apps * 1.5  # Average expected variations
                prevented = max(0, expected - len(products))
                total_duplicates += int(prevented)
                
                print(f"  Saved {saved} new products")
                
                # Rate limiting
                time.sleep(0.5)
                
            except Exception as e:
                print(f"  ❌ Error processing category: {e}")
                total_errors += 1
        
        # Summary
        duration = time.time() - start_time
        print(f"\n{'=' * 60}")
        print(f"Discovery Complete!")
        print(f"  Duration: {duration:.1f} seconds")
        print(f"  Categories processed: {len(self.waste_applications)}")
        print(f"  New products saved: {total_saved}")
        print(f"  Duplicates prevented: ~{total_duplicates}")
        print(f"  Errors: {total_errors}")
        
        return {
            'agent': self.name,
            'duration': duration,
            'categories_processed': len(self.waste_applications),
            'products_saved': total_saved,
            'duplicates_prevented': total_duplicates,
            'errors': total_errors
        }


if __name__ == "__main__":
    agent = APIWasteManagementAgent()
    results = agent.run_discovery()
    print(f"\nResults: {json.dumps(results, indent=2)}")