#!/usr/bin/env python3
"""
API-Based Packaging Innovation Agent - Discovers hemp packaging solutions
Focuses on sustainable packaging, food containers, shipping materials, and smart packaging
"""

import json
import time
import requests
from datetime import datetime
from typing import Dict, List, Optional, Set
import os
import re
import random
from difflib import SequenceMatcher

class APIPackagingInnovationAgent:
    """Packaging industry hemp product discovery with duplicate prevention"""
    
    def __init__(self):
        self.name = "API Packaging Innovation Agent"
        self.version = "1.0.0"
        self.discovered_count = 0
        
        # Supabase configuration
        self.supabase_url = "https://ktoqznqmlnxrtvubewyz.supabase.co"
        self.service_role_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt0b3F6bnFtbG54cnR2dWJld3l6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODQ5MTc3NiwiZXhwIjoyMDY0MDY3Nzc2fQ.hxEiOy6xebv7I4CYJEU49oCEXRvmU6iz2l8Kg-NnvTs"
        
        self.headers = {
            "apikey": self.service_role_key,
            "Authorization": f"Bearer {self.service_role_key}",
            "Content-Type": "application/json",
            "Prefer": "return=minimal"
        }
        
        # Cache for existing products
        self.existing_products_cache = set()
        self.normalized_cache = {}
        self.load_existing_products()
        
        # Packaging applications database
        self.packaging_applications = [
            # Food & Beverage Packaging
            {
                "category": "food_packaging",
                "subcategory": "primary_containers",
                "applications": [
                    {"name": "Food Tray", "description": "Biodegradable serving container"},
                    {"name": "Beverage Cup", "description": "Compostable drink container"},
                    {"name": "Take-Out Box", "description": "Restaurant to-go packaging"},
                    {"name": "Deli Container", "description": "Fresh food storage"},
                    {"name": "Produce Clamshell", "description": "Fruit/vegetable packaging"}
                ]
            },
            {
                "category": "food_packaging",
                "subcategory": "flexible_packaging",
                "applications": [
                    {"name": "Snack Wrapper", "description": "Chip and cookie packaging"},
                    {"name": "Bread Bag", "description": "Bakery product protection"},
                    {"name": "Frozen Food Pouch", "description": "Freezer-safe packaging"},
                    {"name": "Coffee Bag", "description": "Aroma-preserving package"},
                    {"name": "Candy Wrapper", "description": "Confectionery packaging"}
                ]
            },
            # E-commerce & Shipping
            {
                "category": "shipping",
                "subcategory": "protective_packaging",
                "applications": [
                    {"name": "Bubble Wrap Alternative", "description": "Cushioning material"},
                    {"name": "Packing Peanuts", "description": "Void fill material"},
                    {"name": "Foam Insert", "description": "Custom protection"},
                    {"name": "Air Pillow", "description": "Inflatable cushioning"},
                    {"name": "Corner Protector", "description": "Edge protection"}
                ]
            },
            {
                "category": "shipping",
                "subcategory": "boxes_mailers",
                "applications": [
                    {"name": "Corrugated Box", "description": "Shipping container"},
                    {"name": "Padded Mailer", "description": "Envelope with cushioning"},
                    {"name": "Tube Mailer", "description": "Cylindrical shipping"},
                    {"name": "Flat Mailer", "description": "Document shipping"},
                    {"name": "Multi-Depth Box", "description": "Adjustable container"}
                ]
            },
            # Retail Packaging
            {
                "category": "retail",
                "subcategory": "display_packaging",
                "applications": [
                    {"name": "Blister Pack", "description": "Product display card"},
                    {"name": "Hang Tab", "description": "Retail display hanger"},
                    {"name": "Window Box", "description": "See-through packaging"},
                    {"name": "Gift Box", "description": "Premium presentation"},
                    {"name": "Sample Packet", "description": "Trial size container"}
                ]
            },
            {
                "category": "retail",
                "subcategory": "shopping_bags",
                "applications": [
                    {"name": "Grocery Bag", "description": "Reusable shopping bag"},
                    {"name": "Boutique Bag", "description": "Luxury retail bag"},
                    {"name": "Produce Bag", "description": "Mesh vegetable bag"},
                    {"name": "Wine Bag", "description": "Bottle carrier"},
                    {"name": "Bulk Bag", "description": "Large capacity tote"}
                ]
            },
            # Industrial Packaging
            {
                "category": "industrial",
                "subcategory": "bulk_containers",
                "applications": [
                    {"name": "Drum Liner", "description": "Chemical container lining"},
                    {"name": "IBC Tote", "description": "Intermediate bulk container"},
                    {"name": "Pallet Wrap", "description": "Stretch film alternative"},
                    {"name": "Dunnage Bag", "description": "Load stabilization"},
                    {"name": "Bulk Sack", "description": "Heavy-duty container"}
                ]
            },
            {
                "category": "industrial",
                "subcategory": "protective_films",
                "applications": [
                    {"name": "Shrink Wrap", "description": "Heat-activated film"},
                    {"name": "Stretch Film", "description": "Pallet wrapping"},
                    {"name": "VCI Film", "description": "Corrosion inhibitor"},
                    {"name": "Barrier Film", "description": "Moisture protection"},
                    {"name": "Anti-Static Wrap", "description": "Electronic protection"}
                ]
            },
            # Smart Packaging
            {
                "category": "smart_packaging",
                "subcategory": "active_packaging",
                "applications": [
                    {"name": "Oxygen Absorber Pack", "description": "Freshness preservation"},
                    {"name": "Moisture Control Packet", "description": "Humidity regulation"},
                    {"name": "Antimicrobial Film", "description": "Pathogen prevention"},
                    {"name": "Ethylene Absorber", "description": "Ripening control"},
                    {"name": "Time-Temperature Indicator", "description": "Cold chain monitor"}
                ]
            },
            {
                "category": "smart_packaging",
                "subcategory": "intelligent_labels",
                "applications": [
                    {"name": "RFID Tag Substrate", "description": "Smart tracking label"},
                    {"name": "QR Code Label", "description": "Digital interaction"},
                    {"name": "Freshness Indicator", "description": "Quality monitor"},
                    {"name": "Tamper Evidence Seal", "description": "Security feature"},
                    {"name": "Temperature Logger", "description": "Thermal history"}
                ]
            },
            # Luxury Packaging
            {
                "category": "luxury",
                "subcategory": "cosmetic_packaging",
                "applications": [
                    {"name": "Compact Case", "description": "Makeup container"},
                    {"name": "Perfume Box", "description": "Fragrance packaging"},
                    {"name": "Serum Bottle", "description": "Skincare container"},
                    {"name": "Lipstick Tube", "description": "Cosmetic applicator"},
                    {"name": "Gift Set Box", "description": "Premium collection"}
                ]
            },
            {
                "category": "luxury",
                "subcategory": "jewelry_packaging",
                "applications": [
                    {"name": "Ring Box", "description": "Jewelry presentation"},
                    {"name": "Watch Case", "description": "Timepiece storage"},
                    {"name": "Necklace Pouch", "description": "Soft jewelry bag"},
                    {"name": "Bracelet Display", "description": "Retail presentation"},
                    {"name": "Earring Card", "description": "Display backing"}
                ]
            },
            # Medical & Pharmaceutical
            {
                "category": "medical",
                "subcategory": "sterile_packaging",
                "applications": [
                    {"name": "Blister Pack Base", "description": "Pill packaging"},
                    {"name": "Surgical Tray", "description": "Instrument container"},
                    {"name": "Syringe Package", "description": "Medical device wrap"},
                    {"name": "Bandage Wrapper", "description": "Wound care packaging"},
                    {"name": "Test Kit Container", "description": "Diagnostic packaging"}
                ]
            },
            {
                "category": "medical",
                "subcategory": "pharmaceutical_bottles",
                "applications": [
                    {"name": "Pill Bottle", "description": "Medicine container"},
                    {"name": "Liquid Medicine Bottle", "description": "Syrup container"},
                    {"name": "Dropper Bottle", "description": "Precise dispensing"},
                    {"name": "Inhaler Housing", "description": "Respiratory device"},
                    {"name": "Vial Container", "description": "Injectable storage"}
                ]
            },
            # Agricultural Packaging
            {
                "category": "agricultural",
                "subcategory": "seed_fertilizer",
                "applications": [
                    {"name": "Seed Packet", "description": "Planting material"},
                    {"name": "Fertilizer Bag", "description": "Nutrient packaging"},
                    {"name": "Mulch Film", "description": "Ground covering"},
                    {"name": "Transplant Pot", "description": "Seedling container"},
                    {"name": "Harvest Crate", "description": "Crop collection"}
                ]
            },
            {
                "category": "agricultural",
                "subcategory": "crop_protection",
                "applications": [
                    {"name": "Pesticide Container", "description": "Chemical storage"},
                    {"name": "Greenhouse Film", "description": "Climate control"},
                    {"name": "Shade Cloth", "description": "Sun protection"},
                    {"name": "Frost Cover", "description": "Temperature shield"},
                    {"name": "Bird Netting", "description": "Crop protection"}
                ]
            }
        ]
        
        # Product templates by category
        self.product_templates = {
            "food_packaging": [
                "Food-Safe Hemp {} Container",
                "Compostable Hemp {} Package",
                "Hemp-Based {} Food Packaging"
            ],
            "shipping": [
                "Shipping Hemp {} Material",
                "E-commerce Hemp {} Solution",
                "Protective Hemp {} Packaging"
            ],
            "retail": [
                "Retail Hemp {} Package",
                "Consumer Hemp {} Container",
                "Display Hemp {} Solution"
            ],
            "industrial": [
                "Industrial Hemp {} Material",
                "Heavy-Duty Hemp {} Container",
                "Commercial Hemp {} Packaging"
            ],
            "smart_packaging": [
                "Smart Hemp {} Package",
                "Interactive Hemp {} Material",
                "Intelligent Hemp {} System"
            ],
            "luxury": [
                "Premium Hemp {} Container",
                "Luxury Hemp {} Package",
                "Designer Hemp {} Box"
            ],
            "medical": [
                "Medical-Grade Hemp {} Package",
                "Sterile Hemp {} Container",
                "Pharmaceutical Hemp {} Material"
            ],
            "agricultural": [
                "Agricultural Hemp {} Container",
                "Farm Hemp {} Package",
                "Crop Hemp {} Material"
            ]
        }
        
        # Industry mapping
        self.industry_id = 11  # Packaging Industry

    def load_existing_products(self):
        """Load existing products into cache"""
        print("Loading existing products into cache...")
        offset = 0
        limit = 1000
        
        while True:
            response = requests.get(
                f"{self.supabase_url}/rest/v1/uses_products",
                headers=self.headers,
                params={
                    "select": "name",
                    "order": "id",
                    "offset": offset,
                    "limit": limit
                }
            )
            
            if response.status_code != 200 or not response.json():
                break
                
            batch = response.json()
            for product in batch:
                name = product['name']
                self.existing_products_cache.add(name.lower())
                normalized = self.normalize_name(name)
                self.normalized_cache[normalized.lower()] = name
            
            if len(batch) < limit:
                break
            offset += limit
        
        print(f"Loaded {len(self.existing_products_cache)} existing products")

    def normalize_name(self, name: str) -> str:
        """Normalize product name"""
        name = ' '.join(name.split())
        name = re.sub(r'\b(\w+)\s+\1\b', r'\1', name)
        name = name.replace('Hemp-Based', 'Hemp')
        name = name.replace('Hemp-Derived', 'Hemp')
        name = re.sub(r'(\w+)\s+\1$', r'\1', name)
        return name.strip()

    def check_duplicate_advanced(self, name: str, threshold: float = 0.85) -> bool:
        """Advanced duplicate detection"""
        name_lower = name.lower()
        normalized = self.normalize_name(name)
        normalized_lower = normalized.lower()
        
        if name_lower in self.existing_products_cache:
            return True
        
        if normalized_lower in self.normalized_cache:
            print(f"  ⚠️  Similar to existing: {self.normalized_cache[normalized_lower]}")
            return True
        
        for existing in self.existing_products_cache:
            similarity = SequenceMatcher(None, normalized_lower, existing).ratio()
            if similarity > threshold:
                print(f"  ⚠️  Too similar ({similarity:.2f}) to existing product")
                return True
        
        return False

    def get_plant_part_for_category(self, category: str) -> int:
        """Get appropriate plant part ID for packaging category"""
        mapping = {
            "food_packaging": 1,    # Fiber for food safety
            "shipping": 3,          # Stem for strength
            "retail": 1,            # Fiber for appearance
            "industrial": 3,        # Stem for durability
            "smart_packaging": 7,   # Whole plant for tech
            "luxury": 1,            # Fiber for premium feel
            "medical": 1,           # Fiber for purity
            "agricultural": 3       # Stem for outdoor use
        }
        return mapping.get(category, 1)

    def generate_packaging_description(self, app_name: str, app_desc: str, 
                                     category: str, subcategory: str) -> str:
        """Generate detailed packaging product description"""
        descriptions = {
            "food_packaging": f"Food-safe {app_name} designed for {subcategory.replace('_', ' ')} applications. "
                            f"{app_desc}. Meets FDA requirements for food contact while providing "
                            f"complete compostability and barrier properties for freshness.",
            
            "shipping": f"Protective {app_name} engineered for {subcategory.replace('_', ' ')} needs. "
                       f"{app_desc}. Reduces shipping damage while eliminating plastic waste "
                       f"with renewable, cushioning hemp materials.",
            
            "retail": f"Consumer-friendly {app_name} created for {subcategory.replace('_', ' ')} purposes. "
                     f"{app_desc}. Enhances brand sustainability while providing attractive "
                     f"presentation and functional retail solutions.",
            
            "industrial": f"Heavy-duty {app_name} built for {subcategory.replace('_', ' ')} requirements. "
                         f"{app_desc}. Withstands industrial handling and storage conditions "
                         f"while reducing environmental impact of bulk packaging.",
            
            "smart_packaging": f"Innovative {app_name} featuring {subcategory.replace('_', ' ')} technology. "
                              f"{app_desc}. Integrates digital features with sustainable materials "
                              f"for enhanced product protection and consumer engagement.",
            
            "luxury": f"Premium {app_name} crafted for {subcategory.replace('_', ' ')} market. "
                     f"{app_desc}. Delivers luxury unboxing experience with sustainable "
                     f"materials that reflect brand values and environmental consciousness.",
            
            "medical": f"Certified {app_name} developed for {subcategory.replace('_', ' ')} use. "
                      f"{app_desc}. Maintains sterility and product integrity while meeting "
                      f"strict medical packaging regulations with bio-based materials.",
            
            "agricultural": f"Durable {app_name} designed for {subcategory.replace('_', ' ')} applications. "
                           f"{app_desc}. Provides weather-resistant protection for agricultural "
                           f"products while biodegrading naturally after use."
        }
        
        return descriptions.get(category, f"Packaging {app_name}. {app_desc}.")

    def generate_packaging_benefits(self, category: str, subcategory: str) -> str:
        """Generate benefits based on packaging application"""
        base_benefits = ["100% compostable", "Plastic-free alternative", "Renewable resource"]
        
        category_benefits = {
            "food_packaging": ["Food contact safe", "Moisture barrier", "Extends shelf life"],
            "shipping": ["Shock absorption", "Lightweight design", "Space efficient"],
            "retail": ["Brand differentiation", "Consumer appeal", "Easy disposal"],
            "industrial": ["High strength", "Weather resistant", "Cost effective"],
            "smart_packaging": ["Track and trace", "Interactive features", "Data collection"],
            "luxury": ["Premium aesthetics", "Unboxing experience", "Brand elevation"],
            "medical": ["Sterile barrier", "Tamper evident", "Regulatory compliant"],
            "agricultural": ["UV resistant", "Biodegradable in soil", "Pest deterrent"]
        }
        
        benefits = base_benefits + category_benefits.get(category, ["Sustainable packaging"])
        return "{" + ",".join([f'"{b}"' for b in benefits]) + "}"

    def create_packaging_products(self, pack_group: Dict) -> List[Dict]:
        """Create products from packaging application group"""
        products = []
        category = pack_group["category"]
        subcategory = pack_group["subcategory"]
        
        for app in pack_group["applications"]:
            # Generate 1-2 variations per application
            num_variations = random.randint(1, 2)
            
            for i in range(num_variations):
                templates = self.product_templates.get(category, ["Hemp {} Packaging"])
                template = random.choice(templates)
                
                # Create product name
                if i == 0:
                    product_name = template.format(app["name"])
                else:
                    modifiers = ["Eco", "Bio", "Green", "Sustainable", "Natural"]
                    modifier = random.choice(modifiers)
                    product_name = template.format(f"{modifier} {app['name']}")
                
                product_name = self.normalize_name(product_name)
                
                # Check for duplicates
                if self.check_duplicate_advanced(product_name):
                    continue
                
                # Determine plant part
                plant_part_id = self.get_plant_part_for_category(category)
                
                # Calculate confidence score (0.7-0.9 for packaging)
                confidence_score = round(0.7 + (random.random() * 0.2), 2)
                
                product = {
                    'name': product_name,
                    'description': self.generate_packaging_description(
                        app["name"], app["description"], category, subcategory
                    ),
                    'industry_sub_category_id': self.industry_id,
                    'plant_part_id': plant_part_id,
                    'confidence_score': confidence_score,
                    'image_url': None,
                    'benefits_advantages': self.generate_packaging_benefits(category, subcategory),
                    'technical_specifications': json.dumps({
                        "category": category,
                        "subcategory": subcategory,
                        "application_type": subcategory.replace('_', ' '),
                        "packaging_class": app["name"],
                        "biodegradability": "120 days",
                        "certifications": ["BPI", "OK Compost", "FSC"]
                    }),
                    'source_type': 'ai_agent',
                    'source_agent': self.name,
                    'source_url': f"Packaging: {category}/{subcategory}"
                }
                
                products.append(product)
                
                # Add to cache immediately
                self.existing_products_cache.add(product_name.lower())
                self.normalized_cache[product_name.lower()] = product_name
        
        return products

    def save_product_api(self, product_data: Dict) -> bool:
        """Save product using REST API"""
        try:
            response = requests.post(
                f"{self.supabase_url}/rest/v1/uses_products",
                headers=self.headers,
                json=product_data
            )
            
            if response.status_code in [200, 201, 204]:
                self.discovered_count += 1
                return True
            else:
                print(f"Failed to save product: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"Error saving product: {e}")
            return False

    def run_discovery(self) -> Dict:
        """Run packaging innovation discovery"""
        start_time = time.time()
        total_saved = 0
        total_duplicates = 0
        total_errors = 0
        
        print(f"\n📦 {self.name} - Starting Discovery Cycle")
        print(f"Packaging categories: {len(self.packaging_applications)}")
        print("=" * 60)
        
        for idx, pack_group in enumerate(self.packaging_applications, 1):
            category = pack_group["category"]
            subcategory = pack_group["subcategory"]
            num_apps = len(pack_group["applications"])
            
            print(f"\n[{idx}/{len(self.packaging_applications)}] {category.title().replace('_', ' ')} - {subcategory.title().replace('_', ' ')}")
            print(f"  Applications: {num_apps}")
            
            try:
                # Create products from packaging group
                products = self.create_packaging_products(pack_group)
                print(f"  Generated {len(products)} product variations")
                
                # Save each product
                saved = 0
                for product in products:
                    if self.save_product_api(product):
                        saved += 1
                        total_saved += 1
                        print(f"  ✅ {product['name']}")
                    else:
                        total_errors += 1
                
                # Track duplicates prevented
                expected = num_apps * 1.5  # Average expected variations
                prevented = max(0, expected - len(products))
                total_duplicates += int(prevented)
                
                print(f"  Saved {saved} new products")
                
                # Rate limiting
                time.sleep(0.5)
                
            except Exception as e:
                print(f"  ❌ Error processing category: {e}")
                total_errors += 1
        
        # Summary
        duration = time.time() - start_time
        print(f"\n{'=' * 60}")
        print(f"Discovery Complete!")
        print(f"  Duration: {duration:.1f} seconds")
        print(f"  Categories processed: {len(self.packaging_applications)}")
        print(f"  New products saved: {total_saved}")
        print(f"  Duplicates prevented: ~{total_duplicates}")
        print(f"  Errors: {total_errors}")
        
        return {
            'agent': self.name,
            'duration': duration,
            'categories_processed': len(self.packaging_applications),
            'products_saved': total_saved,
            'duplicates_prevented': total_duplicates,
            'errors': total_errors
        }


if __name__ == "__main__":
    agent = APIPackagingInnovationAgent()
    results = agent.run_discovery()
    print(f"\nResults: {json.dumps(results, indent=2)}")