#!/usr/bin/env python3
"""
API-Based Agricultural Applications Agent - Discovers hemp farming and crop production uses
Focuses on soil improvement, crop protection, agricultural inputs, and farming equipment
"""

import json
import time
import requests
from datetime import datetime
from typing import Dict, List, Optional, Set
import os
import re
import random
from difflib import SequenceMatcher

class APIAgriculturalApplicationsAgent:
    """Agricultural hemp applications discovery with duplicate prevention"""
    
    def __init__(self):
        self.name = "API Agricultural Applications Agent"
        self.version = "1.0.0"
        self.discovered_count = 0
        
        # Supabase configuration
        self.supabase_url = "https://ktoqznqmlnxrtvubewyz.supabase.co"
        self.service_role_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt0b3F6bnFtbG54cnR2dWJld3l6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODQ5MTc3NiwiZXhwIjoyMDY0MDY3Nzc2fQ.hxEiOy6xebv7I4CYJEU49oCEXRvmU6iz2l8Kg-NnvTs"
        
        self.headers = {
            "apikey": self.service_role_key,
            "Authorization": f"Bearer {self.service_role_key}",
            "Content-Type": "application/json",
            "Prefer": "return=minimal"
        }
        
        # Cache for existing products
        self.existing_products_cache = set()
        self.normalized_cache = {}
        self.load_existing_products()
        
        # Agricultural applications database
        self.agricultural_applications = [
            # Soil Management
            {
                "category": "soil_management",
                "subcategory": "amendments",
                "applications": [
                    {"name": "Soil Conditioner", "description": "Structure improvement"},
                    {"name": "Compost Additive", "description": "Organic matter boost"},
                    {"name": "pH Buffer", "description": "Acidity control"},
                    {"name": "Water Retention Aid", "description": "Moisture management"},
                    {"name": "Erosion Control Mat", "description": "Soil stabilization"}
                ]
            },
            {
                "category": "soil_management",
                "subcategory": "bioremediation",
                "applications": [
                    {"name": "Heavy Metal Absorber", "description": "Contaminant removal"},
                    {"name": "Oil Spill Remediation", "description": "Petroleum cleanup"},
                    {"name": "Pesticide Breakdown", "description": "Chemical degradation"},
                    {"name": "Salt Extraction", "description": "Salinity reduction"},
                    {"name": "Phytoremediation Plant", "description": "Living filter"}
                ]
            },
            # Crop Protection
            {
                "category": "crop_protection",
                "subcategory": "pest_management",
                "applications": [
                    {"name": "Insect Repellent", "description": "Natural deterrent"},
                    {"name": "Companion Plant", "description": "Pest confusion"},
                    {"name": "Trap Crop", "description": "Pest diversion"},
                    {"name": "Beneficial Insect Habitat", "description": "Predator support"},
                    {"name": "Nematode Suppressant", "description": "Root protection"}
                ]
            },
            {
                "category": "crop_protection",
                "subcategory": "disease_control",
                "applications": [
                    {"name": "Fungal Inhibitor", "description": "Disease prevention"},
                    {"name": "Bacterial Control", "description": "Pathogen suppression"},
                    {"name": "Viral Resistance", "description": "Disease barrier"},
                    {"name": "Root Rot Prevention", "description": "Soil health"},
                    {"name": "Foliar Disease Shield", "description": "Leaf protection"}
                ]
            },
            # Fertilizers & Nutrients
            {
                "category": "fertilizers",
                "subcategory": "organic_inputs",
                "applications": [
                    {"name": "Green Manure", "description": "Nitrogen fixation"},
                    {"name": "Biomass Fertilizer", "description": "Nutrient cycling"},
                    {"name": "Seed Meal", "description": "Slow-release nutrients"},
                    {"name": "Liquid Plant Food", "description": "Foliar feeding"},
                    {"name": "Micronutrient Supplement", "description": "Trace elements"}
                ]
            },
            {
                "category": "fertilizers",
                "subcategory": "growth_enhancers",
                "applications": [
                    {"name": "Root Stimulator", "description": "Root development"},
                    {"name": "Bloom Booster", "description": "Flower production"},
                    {"name": "Fruit Set Enhancer", "description": "Yield improvement"},
                    {"name": "Stress Reliever", "description": "Plant resilience"},
                    {"name": "Growth Regulator", "description": "Development control"}
                ]
            },
            # Mulch & Ground Cover
            {
                "category": "mulch_cover",
                "subcategory": "weed_control",
                "applications": [
                    {"name": "Weed Barrier Mat", "description": "Growth prevention"},
                    {"name": "Biodegradable Mulch", "description": "Temporary cover"},
                    {"name": "Living Mulch", "description": "Ground cover crop"},
                    {"name": "Landscape Fabric", "description": "Permanent barrier"},
                    {"name": "Row Cover", "description": "Crop protection"}
                ]
            },
            {
                "category": "mulch_cover",
                "subcategory": "moisture_retention",
                "applications": [
                    {"name": "Water Conservation Mulch", "description": "Evaporation reduction"},
                    {"name": "Hydrogel Additive", "description": "Water storage"},
                    {"name": "Shade Cloth", "description": "Sun protection"},
                    {"name": "Frost Blanket", "description": "Temperature control"},
                    {"name": "Wind Break", "description": "Exposure reduction"}
                ]
            },
            # Greenhouse & Nursery
            {
                "category": "greenhouse",
                "subcategory": "growing_media",
                "applications": [
                    {"name": "Potting Mix", "description": "Container medium"},
                    {"name": "Seed Starting Mix", "description": "Germination substrate"},
                    {"name": "Hydroponic Medium", "description": "Soilless growing"},
                    {"name": "Propagation Plugs", "description": "Cutting support"},
                    {"name": "Transplant Medium", "description": "Shock reduction"}
                ]
            },
            {
                "category": "greenhouse",
                "subcategory": "climate_control",
                "applications": [
                    {"name": "Shade Screen", "description": "Light diffusion"},
                    {"name": "Thermal Screen", "description": "Temperature regulation"},
                    {"name": "Humidity Mat", "description": "Moisture control"},
                    {"name": "CO2 Generator", "description": "Growth enhancement"},
                    {"name": "Air Circulation Fan", "description": "Climate uniformity"}
                ]
            },
            # Livestock Integration
            {
                "category": "livestock",
                "subcategory": "forage_feed",
                "applications": [
                    {"name": "Silage Additive", "description": "Feed preservation"},
                    {"name": "Pasture Plant", "description": "Grazing crop"},
                    {"name": "Hay Alternative", "description": "Dried fodder"},
                    {"name": "Feed Supplement", "description": "Nutrition boost"},
                    {"name": "Bedding Straw", "description": "Animal comfort"}
                ]
            },
            {
                "category": "livestock",
                "subcategory": "pasture_management",
                "applications": [
                    {"name": "Rotation Crop", "description": "Pasture renewal"},
                    {"name": "Shade Provider", "description": "Animal shelter"},
                    {"name": "Windbreak Plant", "description": "Weather protection"},
                    {"name": "Erosion Preventer", "description": "Pasture stability"},
                    {"name": "Parasite Breaker", "description": "Cycle interruption"}
                ]
            },
            # Precision Agriculture
            {
                "category": "precision_ag",
                "subcategory": "monitoring_tools",
                "applications": [
                    {"name": "Sensor Housing", "description": "Equipment protection"},
                    {"name": "Drone Component", "description": "UAV part"},
                    {"name": "Weather Station Part", "description": "Climate monitoring"},
                    {"name": "Soil Probe Cover", "description": "Sensor protection"},
                    {"name": "GPS Marker", "description": "Field mapping"}
                ]
            },
            {
                "category": "precision_ag",
                "subcategory": "data_collection",
                "applications": [
                    {"name": "Sample Container", "description": "Test storage"},
                    {"name": "Field Notebook", "description": "Data recording"},
                    {"name": "Plot Marker", "description": "Trial identification"},
                    {"name": "Yield Monitor Part", "description": "Harvest tracking"},
                    {"name": "Quality Tester", "description": "Crop assessment"}
                ]
            },
            # Water Management
            {
                "category": "water_management",
                "subcategory": "irrigation",
                "applications": [
                    {"name": "Drip Line", "description": "Water delivery"},
                    {"name": "Mulch Ring", "description": "Tree watering"},
                    {"name": "Wick System", "description": "Passive irrigation"},
                    {"name": "Rain Collector", "description": "Water harvesting"},
                    {"name": "Moisture Sensor", "description": "Irrigation control"}
                ]
            },
            {
                "category": "water_management",
                "subcategory": "drainage",
                "applications": [
                    {"name": "Drain Tile Wrap", "description": "Pipe protection"},
                    {"name": "French Drain Fill", "description": "Water movement"},
                    {"name": "Swale Liner", "description": "Erosion control"},
                    {"name": "Retention Pond Plant", "description": "Water filtration"},
                    {"name": "Bioswale Component", "description": "Runoff treatment"}
                ]
            }
        ]
        
        # Product templates by category
        self.product_templates = {
            "soil_management": [
                "Agricultural Hemp {} Product",
                "Hemp-Based {} Amendment",
                "Sustainable Hemp {} Solution"
            ],
            "crop_protection": [
                "Hemp {} Crop Protection",
                "Natural Hemp {} Control",
                "Organic Hemp {} Treatment"
            ],
            "fertilizers": [
                "Hemp {} Fertilizer",
                "Organic Hemp {} Nutrient",
                "Hemp-Derived {} Supplement"
            ],
            "mulch_cover": [
                "Hemp {} Mulch Product",
                "Agricultural Hemp {} Cover",
                "Hemp Ground {} Material"
            ],
            "greenhouse": [
                "Greenhouse Hemp {} Product",
                "Hemp {} Growing Solution",
                "Nursery Hemp {} Material"
            ],
            "livestock": [
                "Livestock Hemp {} Product",
                "Hemp {} Feed Component",
                "Agricultural Hemp {} Integration"
            ],
            "precision_ag": [
                "Precision Hemp {} Component",
                "Smart Farm Hemp {} Product",
                "AgTech Hemp {} Solution"
            ],
            "water_management": [
                "Hemp {} Water Solution",
                "Irrigation Hemp {} Product",
                "Water-Smart Hemp {} Material"
            ]
        }
        
        # Industry mapping
        self.industry_id = 1  # Agriculture

    def load_existing_products(self):
        """Load existing products into cache"""
        print("Loading existing products into cache...")
        offset = 0
        limit = 1000
        
        while True:
            response = requests.get(
                f"{self.supabase_url}/rest/v1/uses_products",
                headers=self.headers,
                params={
                    "select": "name",
                    "order": "id",
                    "offset": offset,
                    "limit": limit
                }
            )
            
            if response.status_code != 200 or not response.json():
                break
                
            batch = response.json()
            for product in batch:
                name = product['name']
                self.existing_products_cache.add(name.lower())
                normalized = self.normalize_name(name)
                self.normalized_cache[normalized.lower()] = name
            
            if len(batch) < limit:
                break
            offset += limit
        
        print(f"Loaded {len(self.existing_products_cache)} existing products")

    def normalize_name(self, name: str) -> str:
        """Normalize product name"""
        name = ' '.join(name.split())
        name = re.sub(r'\b(\w+)\s+\1\b', r'\1', name)
        name = name.replace('Hemp-Based', 'Hemp')
        name = name.replace('Hemp-Derived', 'Hemp')
        name = re.sub(r'(\w+)\s+\1$', r'\1', name)
        return name.strip()

    def check_duplicate_advanced(self, name: str, threshold: float = 0.85) -> bool:
        """Advanced duplicate detection"""
        name_lower = name.lower()
        normalized = self.normalize_name(name)
        normalized_lower = normalized.lower()
        
        if name_lower in self.existing_products_cache:
            return True
        
        if normalized_lower in self.normalized_cache:
            print(f"  ⚠️  Similar to existing: {self.normalized_cache[normalized_lower]}")
            return True
        
        for existing in self.existing_products_cache:
            similarity = SequenceMatcher(None, normalized_lower, existing).ratio()
            if similarity > threshold:
                print(f"  ⚠️  Too similar ({similarity:.2f}) to existing product")
                return True
        
        return False

    def get_plant_part_for_category(self, category: str) -> int:
        """Get appropriate plant part ID for agricultural category"""
        mapping = {
            "soil_management": 7,     # Whole plant for soil improvement
            "crop_protection": 4,     # Leaves for pest control
            "fertilizers": 7,         # Whole plant for nutrients
            "mulch_cover": 3,         # Stem for ground cover
            "greenhouse": 7,          # Whole plant for various uses
            "livestock": 7,           # Whole plant for feed
            "precision_ag": 1,        # Fiber for equipment
            "water_management": 3     # Stem for water applications
        }
        return mapping.get(category, 7)

    def generate_agricultural_description(self, app_name: str, app_desc: str, 
                                        category: str, subcategory: str) -> str:
        """Generate detailed agricultural product description"""
        descriptions = {
            "soil_management": f"Revolutionary {app_name} designed for {subcategory.replace('_', ' ')} applications. "
                              f"{app_desc}. Hemp's deep root system and natural properties enhance "
                              f"soil health while supporting sustainable farming practices.",
            
            "crop_protection": f"Natural {app_name} formulated for {subcategory.replace('_', ' ')} strategies. "
                              f"{app_desc}. Hemp-based solutions provide eco-friendly crop protection "
                              f"without harmful chemical residues or environmental damage.",
            
            "fertilizers": f"Organic {app_name} developed for {subcategory.replace('_', ' ')} needs. "
                          f"{app_desc}. Hemp-derived nutrients support healthy plant growth "
                          f"while building long-term soil fertility and biological activity.",
            
            "mulch_cover": f"Sustainable {app_name} engineered for {subcategory.replace('_', ' ')} purposes. "
                          f"{app_desc}. Hemp mulch products conserve resources while providing "
                          f"effective ground cover that enriches soil as it decomposes.",
            
            "greenhouse": f"Professional {app_name} optimized for {subcategory.replace('_', ' ')} operations. "
                         f"{app_desc}. Hemp-based greenhouse solutions enhance controlled "
                         f"environment agriculture with sustainable, effective materials.",
            
            "livestock": f"Integrated {app_name} designed for {subcategory.replace('_', ' ')} systems. "
                        f"{app_desc}. Hemp products support regenerative agriculture by "
                        f"combining crop and livestock production sustainably.",
            
            "precision_ag": f"Advanced {app_name} developed for {subcategory.replace('_', ' ')} technology. "
                           f"{app_desc}. Hemp materials enable smart farming practices "
                           f"with biodegradable components for modern agriculture.",
            
            "water_management": f"Efficient {app_name} created for {subcategory.replace('_', ' ')} solutions. "
                               f"{app_desc}. Hemp-based water management products conserve "
                               f"precious resources while improving agricultural productivity."
        }
        
        return descriptions.get(category, f"Agricultural {app_name}. {app_desc}.")

    def generate_agricultural_benefits(self, category: str, subcategory: str) -> str:
        """Generate benefits based on agricultural application"""
        base_benefits = ["Organic certified", "Soil building", "Sustainable farming"]
        
        category_benefits = {
            "soil_management": ["Improves soil structure", "Increases organic matter", "Enhances microbial life"],
            "crop_protection": ["Chemical-free protection", "Beneficial insect friendly", "Disease suppression"],
            "fertilizers": ["Slow-release nutrients", "Balanced NPK ratio", "Micronutrient rich"],
            "mulch_cover": ["Weed suppression", "Moisture retention", "Temperature moderation"],
            "greenhouse": ["Optimal growing conditions", "Disease prevention", "Resource efficient"],
            "livestock": ["High protein content", "Palatability", "Digestibility"],
            "precision_ag": ["Data-driven decisions", "Resource optimization", "Yield improvement"],
            "water_management": ["Water conservation", "Erosion control", "Nutrient retention"]
        }
        
        benefits = base_benefits + category_benefits.get(category, ["Agricultural innovation"])
        return "{" + ",".join([f'"{b}"' for b in benefits]) + "}"

    def create_agricultural_products(self, ag_group: Dict) -> List[Dict]:
        """Create products from agricultural application group"""
        products = []
        category = ag_group["category"]
        subcategory = ag_group["subcategory"]
        
        for app in ag_group["applications"]:
            # Generate 1-2 variations per application
            num_variations = random.randint(1, 2)
            
            for i in range(num_variations):
                templates = self.product_templates.get(category, ["Hemp {} Agricultural Product"])
                template = random.choice(templates)
                
                # Create product name
                if i == 0:
                    product_name = template.format(app["name"])
                else:
                    modifiers = ["Certified", "Premium", "Professional", "Organic", "Regenerative"]
                    modifier = random.choice(modifiers)
                    product_name = template.format(f"{modifier} {app['name']}")
                
                product_name = self.normalize_name(product_name)
                
                # Check for duplicates
                if self.check_duplicate_advanced(product_name):
                    continue
                
                # Determine plant part
                plant_part_id = self.get_plant_part_for_category(category)
                
                # Calculate confidence score (0.7-0.95 for agriculture)
                confidence_score = round(0.7 + (random.random() * 0.25), 2)
                
                product_data = {
                    'name': product_name,
                    'description': self.generate_agricultural_description(
                        app["name"], app["description"], category, subcategory
                    ),
                    'industry_sub_category_id': self.industry_id,
                    'plant_part_id': plant_part_id,
                    'confidence_score': confidence_score,
                    'image_url': None,
                    'benefits_advantages': self.generate_agricultural_benefits(category, subcategory),
                    'technical_specifications': json.dumps({
                        "category": category,
                        "subcategory": subcategory,
                        "application_type": subcategory.replace('_', ' '),
                        "farming_method": app["name"],
                        "sustainability_metrics": ["Carbon negative", "Water efficient", "Biodegradable"],
                        "certifications": ["USDA Organic", "OMRI Listed", "Regenerative Certified"]
                    }),
                    'source_type': 'ai_agent',
                    'source_agent': self.name,
                    'source_url': f"Agriculture: {category}/{subcategory}"
                }
                
                products.append(product_data)
                
                # Add to cache immediately
                self.existing_products_cache.add(product_name.lower())
                self.normalized_cache[product_name.lower()] = product_name
        
        return products

    def save_product_api(self, product_data: Dict) -> bool:
        """Save product using REST API"""
        try:
            response = requests.post(
                f"{self.supabase_url}/rest/v1/uses_products",
                headers=self.headers,
                json=product_data
            )
            
            if response.status_code in [200, 201, 204]:
                self.discovered_count += 1
                return True
            else:
                print(f"Failed to save product: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"Error saving product: {e}")
            return False

    def run_discovery(self) -> Dict:
        """Run agricultural applications discovery"""
        start_time = time.time()
        total_saved = 0
        total_duplicates = 0
        total_errors = 0
        
        print(f"\n🌾 {self.name} - Starting Discovery Cycle")
        print(f"Agricultural categories: {len(self.agricultural_applications)}")
        print("=" * 60)
        
        for idx, ag_group in enumerate(self.agricultural_applications, 1):
            category = ag_group["category"]
            subcategory = ag_group["subcategory"]
            num_apps = len(ag_group["applications"])
            
            print(f"\n[{idx}/{len(self.agricultural_applications)}] {category.title().replace('_', ' ')} - {subcategory.title().replace('_', ' ')}")
            print(f"  Applications: {num_apps}")
            
            try:
                # Create products from agricultural group
                products = self.create_agricultural_products(ag_group)
                print(f"  Generated {len(products)} product variations")
                
                # Save each product
                saved = 0
                for product in products:
                    if self.save_product_api(product):
                        saved += 1
                        total_saved += 1
                        print(f"  ✅ {product['name']}")
                    else:
                        total_errors += 1
                
                # Track duplicates prevented
                expected = num_apps * 1.5  # Average expected variations
                prevented = max(0, expected - len(products))
                total_duplicates += int(prevented)
                
                print(f"  Saved {saved} new products")
                
                # Rate limiting
                time.sleep(0.5)
                
            except Exception as e:
                print(f"  ❌ Error processing category: {e}")
                total_errors += 1
        
        # Summary
        duration = time.time() - start_time
        print(f"\n{'=' * 60}")
        print(f"Discovery Complete!")
        print(f"  Duration: {duration:.1f} seconds")
        print(f"  Categories processed: {len(self.agricultural_applications)}")
        print(f"  New products saved: {total_saved}")
        print(f"  Duplicates prevented: ~{total_duplicates}")
        print(f"  Errors: {total_errors}")
        
        return {
            'agent': self.name,
            'duration': duration,
            'categories_processed': len(self.agricultural_applications),
            'products_saved': total_saved,
            'duplicates_prevented': total_duplicates,
            'errors': total_errors
        }


if __name__ == "__main__":
    agent = APIAgriculturalApplicationsAgent()
    results = agent.run_discovery()
    print(f"\nResults: {json.dumps(results, indent=2)}")