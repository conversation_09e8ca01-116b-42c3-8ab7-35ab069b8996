#!/usr/bin/env python3
"""
API-Based Furniture Manufacturing Agent - Discovers hemp furniture applications
Focuses on residential, commercial, outdoor furniture and components
"""

import json
import time
import requests
from datetime import datetime
from typing import Dict, List, Optional, Set
import os
import re
import random
from difflib import SequenceMatcher

class APIFurnitureManufacturingAgent:
    """Furniture industry hemp product discovery with duplicate prevention"""
    
    def __init__(self):
        self.name = "API Furniture Manufacturing Agent"
        self.version = "1.0.0"
        self.discovered_count = 0
        
        # Supabase configuration
        self.supabase_url = "https://ktoqznqmlnxrtvubewyz.supabase.co"
        self.service_role_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt0b3F6bnFtbG54cnR2dWJld3l6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODQ5MTc3NiwiZXhwIjoyMDY0MDY3Nzc2fQ.hxEiOy6xebv7I4CYJEU49oCEXRvmU6iz2l8Kg-NnvTs"
        
        self.headers = {
            "apikey": self.service_role_key,
            "Authorization": f"Bearer {self.service_role_key}",
            "Content-Type": "application/json",
            "Prefer": "return=minimal"
        }
        
        # Cache for existing products
        self.existing_products_cache = set()
        self.normalized_cache = {}
        self.load_existing_products()
        
        # Furniture applications database
        self.furniture_applications = [
            # Living Room Furniture
            {
                "category": "living_room",
                "subcategory": "seating",
                "products": [
                    {"name": "Sofa Frame", "description": "Structural couch skeleton"},
                    {"name": "Chair Seat Base", "description": "Sitting surface support"},
                    {"name": "Ottoman Structure", "description": "Footrest framework"},
                    {"name": "Recliner Mechanism Housing", "description": "Moving parts enclosure"},
                    {"name": "Loveseat Frame", "description": "Two-person seating structure"}
                ]
            },
            {
                "category": "living_room",
                "subcategory": "tables",
                "products": [
                    {"name": "Coffee Table Top", "description": "Central table surface"},
                    {"name": "End Table Frame", "description": "Side table structure"},
                    {"name": "Console Table Base", "description": "Wall table support"},
                    {"name": "Nesting Table Set", "description": "Stackable table components"},
                    {"name": "TV Stand Structure", "description": "Entertainment center frame"}
                ]
            },
            # Bedroom Furniture
            {
                "category": "bedroom",
                "subcategory": "beds",
                "products": [
                    {"name": "Bed Frame", "description": "Mattress support structure"},
                    {"name": "Headboard Panel", "description": "Decorative bed backing"},
                    {"name": "Platform Bed Base", "description": "Low-profile bed foundation"},
                    {"name": "Bunk Bed Frame", "description": "Stacked sleeping structure"},
                    {"name": "Daybed Structure", "description": "Dual-purpose bed frame"}
                ]
            },
            {
                "category": "bedroom",
                "subcategory": "storage",
                "products": [
                    {"name": "Dresser Frame", "description": "Drawer cabinet structure"},
                    {"name": "Wardrobe Shell", "description": "Clothing storage cabinet"},
                    {"name": "Nightstand Body", "description": "Bedside table frame"},
                    {"name": "Chest of Drawers", "description": "Vertical storage unit"},
                    {"name": "Vanity Table Base", "description": "Makeup table structure"}
                ]
            },
            # Dining Room Furniture
            {
                "category": "dining_room",
                "subcategory": "tables_chairs",
                "products": [
                    {"name": "Dining Table Top", "description": "Meal surface panel"},
                    {"name": "Dining Chair Frame", "description": "Seating structure"},
                    {"name": "Buffet Cabinet", "description": "Serving storage unit"},
                    {"name": "China Cabinet Frame", "description": "Display storage structure"},
                    {"name": "Bar Stool Structure", "description": "High seating frame"}
                ]
            },
            {
                "category": "dining_room",
                "subcategory": "extensions",
                "products": [
                    {"name": "Table Leaf", "description": "Extension panel"},
                    {"name": "Expandable Table Mechanism", "description": "Extension hardware"},
                    {"name": "Bench Seat", "description": "Long seating surface"},
                    {"name": "Server Cart Frame", "description": "Mobile serving unit"},
                    {"name": "Wine Rack Structure", "description": "Bottle storage frame"}
                ]
            },
            # Office Furniture
            {
                "category": "office",
                "subcategory": "desks",
                "products": [
                    {"name": "Desk Top Surface", "description": "Work surface panel"},
                    {"name": "Standing Desk Frame", "description": "Adjustable desk structure"},
                    {"name": "L-Shaped Desk Component", "description": "Corner desk section"},
                    {"name": "Reception Desk Shell", "description": "Front desk structure"},
                    {"name": "Computer Desk Frame", "description": "Tech workstation"}
                ]
            },
            {
                "category": "office",
                "subcategory": "storage_seating",
                "products": [
                    {"name": "Filing Cabinet Shell", "description": "Document storage unit"},
                    {"name": "Bookshelf Frame", "description": "Book storage structure"},
                    {"name": "Office Chair Base", "description": "Ergonomic seat foundation"},
                    {"name": "Credenza Structure", "description": "Storage sideboard"},
                    {"name": "Mobile Pedestal", "description": "Rolling storage unit"}
                ]
            },
            # Outdoor Furniture
            {
                "category": "outdoor",
                "subcategory": "patio_seating",
                "products": [
                    {"name": "Patio Chair Frame", "description": "Weather-resistant seating"},
                    {"name": "Garden Bench Structure", "description": "Outdoor bench frame"},
                    {"name": "Adirondack Chair", "description": "Classic outdoor seating"},
                    {"name": "Swing Seat", "description": "Hanging seat structure"},
                    {"name": "Lounge Chair Frame", "description": "Reclining outdoor seat"}
                ]
            },
            {
                "category": "outdoor",
                "subcategory": "tables_accessories",
                "products": [
                    {"name": "Picnic Table Top", "description": "Outdoor dining surface"},
                    {"name": "Fire Pit Table", "description": "Heat-resistant table"},
                    {"name": "Planter Box", "description": "Garden container"},
                    {"name": "Pergola Component", "description": "Shade structure part"},
                    {"name": "Outdoor Storage Box", "description": "Weather-proof storage"}
                ]
            },
            # Commercial Furniture
            {
                "category": "commercial",
                "subcategory": "hospitality",
                "products": [
                    {"name": "Hotel Bed Frame", "description": "Commercial bed structure"},
                    {"name": "Restaurant Table Base", "description": "Heavy-duty table support"},
                    {"name": "Banquette Seating", "description": "Built-in bench seating"},
                    {"name": "Reception Furniture", "description": "Lobby seating set"},
                    {"name": "Conference Table", "description": "Meeting room table"}
                ]
            },
            {
                "category": "commercial",
                "subcategory": "institutional",
                "products": [
                    {"name": "School Desk", "description": "Educational furniture"},
                    {"name": "Library Shelving", "description": "Book storage system"},
                    {"name": "Hospital Furniture", "description": "Medical facility items"},
                    {"name": "Cafeteria Table", "description": "Mass seating surface"},
                    {"name": "Auditorium Seating", "description": "Theater chair structure"}
                ]
            },
            # Furniture Components
            {
                "category": "components",
                "subcategory": "structural_parts",
                "products": [
                    {"name": "Table Leg", "description": "Support column"},
                    {"name": "Chair Back", "description": "Seating support panel"},
                    {"name": "Drawer Front", "description": "Storage face panel"},
                    {"name": "Shelf Board", "description": "Horizontal storage surface"},
                    {"name": "Cabinet Door", "description": "Access panel"}
                ]
            },
            {
                "category": "components",
                "subcategory": "hardware_fittings",
                "products": [
                    {"name": "Drawer Slide", "description": "Movement mechanism"},
                    {"name": "Hinge Plate", "description": "Pivot hardware"},
                    {"name": "Handle Backing", "description": "Pull support"},
                    {"name": "Corner Bracket", "description": "Joint reinforcement"},
                    {"name": "Furniture Glide", "description": "Floor protection"}
                ]
            },
            # Specialty Furniture
            {
                "category": "specialty",
                "subcategory": "children",
                "products": [
                    {"name": "Crib Frame", "description": "Baby bed structure"},
                    {"name": "Toy Chest", "description": "Play storage box"},
                    {"name": "Kids Table Set", "description": "Child-sized furniture"},
                    {"name": "Changing Table", "description": "Baby care surface"},
                    {"name": "Bookcase Headboard", "description": "Storage bed combo"}
                ]
            },
            {
                "category": "specialty",
                "subcategory": "space_saving",
                "products": [
                    {"name": "Murphy Bed Frame", "description": "Wall-mounted bed"},
                    {"name": "Folding Table", "description": "Collapsible surface"},
                    {"name": "Modular Shelving", "description": "Adjustable storage"},
                    {"name": "Convertible Sofa", "description": "Multi-function seating"},
                    {"name": "Stackable Stool", "description": "Space-efficient seating"}
                ]
            }
        ]
        
        # Product templates by category
        self.product_templates = {
            "living_room": [
                "Hemp {} Furniture Component",
                "Sustainable Hemp {} Frame",
                "Eco-Friendly Hemp {} Structure"
            ],
            "bedroom": [
                "Hemp {} Bedroom Furniture",
                "Natural Hemp {} Component",
                "Sustainable Hemp {} Frame"
            ],
            "dining_room": [
                "Hemp {} Dining Furniture",
                "Eco Hemp {} Component",
                "Natural Hemp {} Structure"
            ],
            "office": [
                "Hemp {} Office Furniture",
                "Professional Hemp {} Component",
                "Ergonomic Hemp {} Structure"
            ],
            "outdoor": [
                "Outdoor Hemp {} Furniture",
                "Weather-Resistant Hemp {} Component",
                "Durable Hemp {} Structure"
            ],
            "commercial": [
                "Commercial Hemp {} Furniture",
                "Heavy-Duty Hemp {} Component",
                "Contract Hemp {} Structure"
            ],
            "components": [
                "Hemp {} Furniture Part",
                "Modular Hemp {} Component",
                "Universal Hemp {} Element"
            ],
            "specialty": [
                "Specialty Hemp {} Furniture",
                "Custom Hemp {} Component",
                "Innovative Hemp {} Design"
            ]
        }
        
        # Industry mapping
        self.industry_id = 16  # Furniture Industry

    def load_existing_products(self):
        """Load existing products into cache"""
        print("Loading existing products into cache...")
        offset = 0
        limit = 1000
        
        while True:
            response = requests.get(
                f"{self.supabase_url}/rest/v1/uses_products",
                headers=self.headers,
                params={
                    "select": "name",
                    "order": "id",
                    "offset": offset,
                    "limit": limit
                }
            )
            
            if response.status_code != 200 or not response.json():
                break
                
            batch = response.json()
            for product in batch:
                name = product['name']
                self.existing_products_cache.add(name.lower())
                normalized = self.normalize_name(name)
                self.normalized_cache[normalized.lower()] = name
            
            if len(batch) < limit:
                break
            offset += limit
        
        print(f"Loaded {len(self.existing_products_cache)} existing products")

    def normalize_name(self, name: str) -> str:
        """Normalize product name"""
        name = ' '.join(name.split())
        name = re.sub(r'\b(\w+)\s+\1\b', r'\1', name)
        name = name.replace('Hemp-Based', 'Hemp')
        name = name.replace('Hemp-Derived', 'Hemp')
        name = re.sub(r'(\w+)\s+\1$', r'\1', name)
        return name.strip()

    def check_duplicate_advanced(self, name: str, threshold: float = 0.85) -> bool:
        """Advanced duplicate detection"""
        name_lower = name.lower()
        normalized = self.normalize_name(name)
        normalized_lower = normalized.lower()
        
        if name_lower in self.existing_products_cache:
            return True
        
        if normalized_lower in self.normalized_cache:
            print(f"  ⚠️  Similar to existing: {self.normalized_cache[normalized_lower]}")
            return True
        
        for existing in self.existing_products_cache:
            similarity = SequenceMatcher(None, normalized_lower, existing).ratio()
            if similarity > threshold:
                print(f"  ⚠️  Too similar ({similarity:.2f}) to existing product")
                return True
        
        return False

    def get_plant_part_for_category(self, category: str) -> int:
        """Get appropriate plant part ID for furniture category"""
        mapping = {
            "living_room": 3,     # Stem for structural strength
            "bedroom": 3,         # Stem for durability
            "dining_room": 3,     # Stem for stability
            "office": 3,          # Stem for commercial use
            "outdoor": 3,         # Stem for weather resistance
            "commercial": 3,      # Stem for heavy duty
            "components": 7,      # Whole plant for various parts
            "specialty": 3        # Stem for specialty applications
        }
        return mapping.get(category, 3)

    def generate_furniture_description(self, product_name: str, product_desc: str, 
                                     category: str, subcategory: str) -> str:
        """Generate detailed furniture product description"""
        descriptions = {
            "living_room": f"Stylish {product_name} designed for {subcategory.replace('_', ' ')} applications. "
                          f"{product_desc}. Combines aesthetic appeal with sustainable hemp materials "
                          f"for modern living spaces that prioritize environmental responsibility.",
            
            "bedroom": f"Comfortable {product_name} crafted for {subcategory.replace('_', ' ')} use. "
                      f"{product_desc}. Hemp-based construction provides durability and natural "
                      f"beauty while supporting healthy sleep environments.",
            
            "dining_room": f"Elegant {product_name} manufactured for {subcategory.replace('_', ' ')} purposes. "
                          f"{product_desc}. Sustainable hemp materials create dining furniture "
                          f"that combines functionality with environmental consciousness.",
            
            "office": f"Professional {product_name} engineered for {subcategory.replace('_', ' ')} needs. "
                     f"{product_desc}. Ergonomic design meets sustainability with hemp-based "
                     f"office furniture that enhances productivity and well-being.",
            
            "outdoor": f"Weather-resistant {product_name} built for {subcategory.replace('_', ' ')} environments. "
                      f"{product_desc}. Hemp materials provide natural durability against elements "
                      f"while maintaining attractive appearance in outdoor settings.",
            
            "commercial": f"Heavy-duty {product_name} designed for {subcategory.replace('_', ' ')} facilities. "
                         f"{product_desc}. Contract-grade hemp furniture meets commercial standards "
                         f"for durability, safety, and sustainable procurement.",
            
            "components": f"Versatile {product_name} created for {subcategory.replace('_', ' ')} assembly. "
                         f"{product_desc}. Modular hemp components enable flexible furniture "
                         f"design while reducing environmental impact of manufacturing.",
            
            "specialty": f"Innovative {product_name} developed for {subcategory.replace('_', ' ')} applications. "
                        f"{product_desc}. Specialty hemp furniture solutions address unique needs "
                        f"with sustainable materials and thoughtful design."
        }
        
        return descriptions.get(category, f"Furniture {product_name}. {product_desc}.")

    def generate_furniture_benefits(self, category: str, subcategory: str) -> str:
        """Generate benefits based on furniture application"""
        base_benefits = ["Sustainable materials", "Durable construction", "Formaldehyde-free"]
        
        category_benefits = {
            "living_room": ["Stylish design", "Comfortable support", "Easy maintenance"],
            "bedroom": ["Healthy materials", "Long-lasting quality", "Natural aesthetics"],
            "dining_room": ["Stable construction", "Beautiful finish", "Family-safe"],
            "office": ["Ergonomic design", "Professional appearance", "BIFMA compliant"],
            "outdoor": ["Weather resistant", "UV stable", "Mold resistant"],
            "commercial": ["Contract grade", "Fire retardant option", "Warranty backed"],
            "components": ["Modular design", "Easy assembly", "Universal fit"],
            "specialty": ["Space efficient", "Multi-functional", "Innovative features"]
        }
        
        benefits = base_benefits + category_benefits.get(category, ["Quality furniture"])
        return "{" + ",".join([f'"{b}"' for b in benefits]) + "}"

    def create_furniture_products(self, furniture_group: Dict) -> List[Dict]:
        """Create products from furniture application group"""
        products = []
        category = furniture_group["category"]
        subcategory = furniture_group["subcategory"]
        
        for product in furniture_group["products"]:
            # Generate 1-2 variations per product
            num_variations = random.randint(1, 2)
            
            for i in range(num_variations):
                templates = self.product_templates.get(category, ["Hemp {} Furniture"])
                template = random.choice(templates)
                
                # Create product name
                if i == 0:
                    product_name = template.format(product["name"])
                else:
                    modifiers = ["Premium", "Designer", "Modern", "Classic", "Luxury"]
                    modifier = random.choice(modifiers)
                    product_name = template.format(f"{modifier} {product['name']}")
                
                product_name = self.normalize_name(product_name)
                
                # Check for duplicates
                if self.check_duplicate_advanced(product_name):
                    continue
                
                # Determine plant part
                plant_part_id = self.get_plant_part_for_category(category)
                
                # Calculate confidence score (0.7-0.9 for furniture)
                confidence_score = round(0.7 + (random.random() * 0.2), 2)
                
                product_data = {
                    'name': product_name,
                    'description': self.generate_furniture_description(
                        product["name"], product["description"], category, subcategory
                    ),
                    'industry_sub_category_id': self.industry_id,
                    'plant_part_id': plant_part_id,
                    'confidence_score': confidence_score,
                    'image_url': None,
                    'benefits_advantages': self.generate_furniture_benefits(category, subcategory),
                    'technical_specifications': json.dumps({
                        "category": category,
                        "subcategory": subcategory,
                        "furniture_type": subcategory.replace('_', ' '),
                        "product_class": product["name"],
                        "finish_options": ["Natural", "Stained", "Painted"],
                        "certifications": ["GREENGUARD", "FSC Alternative", "CARB2"]
                    }),
                    'source_type': 'ai_agent',
                    'source_agent': self.name,
                    'source_url': f"Furniture: {category}/{subcategory}"
                }
                
                products.append(product_data)
                
                # Add to cache immediately
                self.existing_products_cache.add(product_name.lower())
                self.normalized_cache[product_name.lower()] = product_name
        
        return products

    def save_product_api(self, product_data: Dict) -> bool:
        """Save product using REST API"""
        try:
            response = requests.post(
                f"{self.supabase_url}/rest/v1/uses_products",
                headers=self.headers,
                json=product_data
            )
            
            if response.status_code in [200, 201, 204]:
                self.discovered_count += 1
                return True
            else:
                print(f"Failed to save product: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"Error saving product: {e}")
            return False

    def run_discovery(self) -> Dict:
        """Run furniture manufacturing discovery"""
        start_time = time.time()
        total_saved = 0
        total_duplicates = 0
        total_errors = 0
        
        print(f"\n🪑 {self.name} - Starting Discovery Cycle")
        print(f"Furniture categories: {len(self.furniture_applications)}")
        print("=" * 60)
        
        for idx, furniture_group in enumerate(self.furniture_applications, 1):
            category = furniture_group["category"]
            subcategory = furniture_group["subcategory"]
            num_products = len(furniture_group["products"])
            
            print(f"\n[{idx}/{len(self.furniture_applications)}] {category.title().replace('_', ' ')} - {subcategory.title().replace('_', ' ')}")
            print(f"  Products in category: {num_products}")
            
            try:
                # Create products from furniture group
                products = self.create_furniture_products(furniture_group)
                print(f"  Generated {len(products)} product variations")
                
                # Save each product
                saved = 0
                for product in products:
                    if self.save_product_api(product):
                        saved += 1
                        total_saved += 1
                        print(f"  ✅ {product['name']}")
                    else:
                        total_errors += 1
                
                # Track duplicates prevented
                expected = num_products * 1.5  # Average expected variations
                prevented = max(0, expected - len(products))
                total_duplicates += int(prevented)
                
                print(f"  Saved {saved} new products")
                
                # Rate limiting
                time.sleep(0.5)
                
            except Exception as e:
                print(f"  ❌ Error processing category: {e}")
                total_errors += 1
        
        # Summary
        duration = time.time() - start_time
        print(f"\n{'=' * 60}")
        print(f"Discovery Complete!")
        print(f"  Duration: {duration:.1f} seconds")
        print(f"  Categories processed: {len(self.furniture_applications)}")
        print(f"  New products saved: {total_saved}")
        print(f"  Duplicates prevented: ~{total_duplicates}")
        print(f"  Errors: {total_errors}")
        
        return {
            'agent': self.name,
            'duration': duration,
            'categories_processed': len(self.furniture_applications),
            'products_saved': total_saved,
            'duplicates_prevented': total_duplicates,
            'errors': total_errors
        }


if __name__ == "__main__":
    agent = APIFurnitureManufacturingAgent()
    results = agent.run_discovery()
    print(f"\nResults: {json.dumps(results, indent=2)}")