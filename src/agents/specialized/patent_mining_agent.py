#!/usr/bin/env python3
"""
Patent Mining Agent - Discovers hemp products from patent databases
Part of the Hemp Database Expansion Initiative
Target: 3,000+ products from patent sources
"""

import requests
import json
import time
import hashlib
from datetime import datetime
from typing import Dict, List, Optional
import psycopg2
from psycopg2.extras import RealDictCursor
import os
import re

class PatentMiningAgent:
    """Specialized agent for mining hemp-related patents and extracting products"""
    
    def __init__(self):
        self.name = "USPTO Patent Mining Agent"
        self.version = "1.0.0"
        self.discovered_count = 0
        self.database_url = os.environ.get('DATABASE_URL')
        
        # Patent search configurations
        self.search_terms = [
            # Material-based searches
            "hemp fiber composite",
            "hemp cellulose", 
            "hemp lignin",
            "hemp hurd",
            "hemp bast fiber",
            "cannabinoid extraction",
            "hemp seed oil",
            "hemp protein isolate",
            
            # Application-based searches
            "hemp construction material",
            "hemp automotive",
            "hemp aerospace",
            "hemp medical device",
            "hemp pharmaceutical",
            "hemp cosmetic formulation",
            "hemp textile innovation",
            "hemp bioplastic",
            "hemp battery",
            "hemp supercapacitor",
            "hemp nanotechnology",
            "hemp 3D printing",
            
            # Process-based searches
            "hemp processing method",
            "hemp extraction technique",
            "hemp purification",
            "hemp modification",
            "hemp composite manufacturing"
        ]
        
        # IPC (International Patent Classification) codes relevant to hemp
        self.ipc_codes = [
            "A01G31",  # Soilless cultivation (hemp)
            "A23L7",    # Cereal-derived products (hemp seed)
            "A61K36",   # Medicinal preparations (plant-based)
            "B27N3",    # Manufacture of articles from particles (hemp composites)
            "C08L97",   # Compositions of lignin-containing materials
            "C12N15",   # Mutation or genetic engineering (hemp varieties)
            "D01B1",    # Mechanical treatment of plant fibers
            "D21C5",    # Pulp from non-wood plants
            "E04B1",    # Construction materials
            "H01M4",    # Electrodes (hemp-based)
        ]
        
    def search_patents(self, query: str, max_results: int = 100) -> List[Dict]:
        """Search USPTO database for patents"""
        patents = []
        
        # Using USPTO PatentsView API
        base_url = "https://api.patentsview.org/patents/query"
        
        payload = {
            "q": {
                "_or": [
                    {"_text_any": {"patent_title": query}},
                    {"_text_any": {"patent_abstract": query}},
                    {"_text_phrase": {"patent_title": query}},
                    {"_text_phrase": {"patent_abstract": query}}
                ]
            },
            "f": [
                "patent_number",
                "patent_title", 
                "patent_abstract",
                "patent_date",
                "inventor_first_name",
                "inventor_last_name",
                "assignee_organization",
                "cpc_section_id",
                "cpc_subsection_title"
            ],
            "o": {
                "page": 1,
                "per_page": max_results
            }
        }
        
        try:
            response = requests.post(base_url, json=payload)
            if response.status_code == 200:
                data = response.json()
                if 'patents' in data:
                    patents = data['patents']
        except Exception as e:
            print(f"Error searching patents: {e}")
            
        return patents
    
    def extract_products_from_patent(self, patent: Dict) -> List[Dict]:
        """Extract potential products from a patent"""
        products = []
        
        title = patent.get('patent_title', '')
        abstract = patent.get('patent_abstract', '')
        patent_number = patent.get('patent_number', '')
        patent_date = patent.get('patent_date', '')
        
        # Combine text for analysis
        full_text = f"{title} {abstract}".lower()
        
        # Product extraction patterns
        product_patterns = [
            # Compositional patterns
            r"hemp[- ]based\s+(\w+\s*\w*)",
            r"hemp\s+(\w+\s*\w*)\s+composite",
            r"(\w+\s*\w*)\s+containing hemp",
            r"hemp[- ]derived\s+(\w+\s*\w*)",
            
            # Application patterns  
            r"hemp\s+for\s+(\w+\s*\w*)",
            r"hemp\s+(\w+)\s+material",
            r"hemp\s+(\w+)\s+device",
            r"hemp\s+(\w+)\s+system",
            
            # Method patterns
            r"method\s+for\s+producing\s+hemp\s+(\w+\s*\w*)",
            r"hemp\s+(\w+)\s+manufacturing",
            r"process\s+for\s+hemp\s+(\w+)"
        ]
        
        # Extract potential products
        found_products = set()
        for pattern in product_patterns:
            matches = re.findall(pattern, full_text)
            for match in matches:
                if match and len(match) > 3:
                    found_products.add(match.strip())
        
        # Specific product identification
        specific_products = {
            "composite": "Advanced Hemp Fiber Composite Material",
            "concrete": "Hemp-Based Concrete (Hempcrete) Formulation", 
            "insulation": "Hemp Fiber Insulation System",
            "plastic": "Hemp Bioplastic Compound",
            "battery": "Hemp-Based Battery Electrode",
            "capacitor": "Hemp Supercapacitor Device",
            "textile": "Advanced Hemp Textile Material",
            "pharmaceutical": "Hemp-Derived Pharmaceutical Formulation",
            "cosmetic": "Hemp-Based Cosmetic Formulation",
            "food": "Hemp Food Product Innovation",
            "packaging": "Hemp-Based Packaging Material",
            "filter": "Hemp Filtration System",
            "catalyst": "Hemp-Derived Catalyst Material"
        }
        
        # Create product entries
        for keyword, base_name in specific_products.items():
            if keyword in full_text:
                # Generate unique product name based on patent
                product_name = f"{base_name} - Patent {patent_number}"
                
                # Extract technical details
                technical_specs = self.extract_technical_specs(full_text, keyword)
                
                product = {
                    'name': product_name,
                    'description': f"{abstract[:500]}... (Patent: {patent_number})",
                    'patent_number': patent_number,
                    'patent_date': patent_date,
                    'technical_specifications': technical_specs,
                    'source_agent': self.name,
                    'discovery_method': 'patent_mining',
                    'confidence_score': 0.85
                }
                
                products.append(product)
        
        return products
    
    def extract_technical_specs(self, text: str, product_type: str) -> Dict:
        """Extract technical specifications from patent text"""
        specs = {}
        
        # Common specification patterns
        spec_patterns = {
            'tensile_strength': r'tensile strength[:\s]+(\d+\.?\d*)\s*(MPa|psi|N/mm)',
            'density': r'density[:\s]+(\d+\.?\d*)\s*(g/cm3|kg/m3)',
            'temperature': r'temperature[:\s]+(\d+\.?\d*)\s*(°C|°F|K)',
            'concentration': r'concentration[:\s]+(\d+\.?\d*)\s*(%|mg/ml|ppm)',
            'particle_size': r'particle size[:\s]+(\d+\.?\d*)\s*(μm|nm|mm)',
            'molecular_weight': r'molecular weight[:\s]+(\d+\.?\d*)\s*(kDa|Da|g/mol)',
            'purity': r'purity[:\s]+(\d+\.?\d*)\s*%',
            'yield': r'yield[:\s]+(\d+\.?\d*)\s*%'
        }
        
        for spec_name, pattern in spec_patterns.items():
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                specs[spec_name] = {
                    'value': match.group(1),
                    'unit': match.group(2) if len(match.groups()) > 1 else ''
                }
        
        return specs
    
    def validate_product(self, product: Dict) -> bool:
        """Validate if product should be added to database"""
        # Check for required fields
        if not product.get('name') or not product.get('description'):
            return False
            
        # Check name quality
        name = product['name']
        if len(name) < 10 or len(name) > 200:
            return False
            
        # Avoid generic names
        generic_terms = ['hemp product', 'hemp material', 'hemp device']
        if any(term == name.lower() for term in generic_terms):
            return False
            
        # Check for existing similar products
        if self.check_duplicate(product):
            return False
            
        return True
    
    def check_duplicate(self, product: Dict) -> bool:
        """Check if similar product already exists"""
        try:
            conn = psycopg2.connect(self.database_url)
            cur = conn.cursor()
            
            # Check for exact patent number
            if 'patent_number' in product:
                cur.execute("""
                    SELECT id FROM uses_products 
                    WHERE description LIKE %s
                """, (f"%Patent: {product['patent_number']}%",))
                
                if cur.fetchone():
                    conn.close()
                    return True
            
            # Check for similar names
            cur.execute("""
                SELECT id FROM uses_products
                WHERE similarity(name, %s) > 0.8
            """, (product['name'],))
            
            if cur.fetchone():
                conn.close()
                return True
                
            conn.close()
            return False
            
        except Exception as e:
            print(f"Error checking duplicate: {e}")
            return True  # Assume duplicate on error
    
    def save_product(self, product: Dict) -> bool:
        """Save product to database"""
        try:
            conn = psycopg2.connect(self.database_url)
            cur = conn.cursor()
            
            # Map to appropriate plant part and industry
            plant_part_id = self.map_plant_part(product)
            industry_id = self.map_industry(product)
            
            # Generate benefits from patent claims
            benefits = self.generate_benefits(product)
            
            cur.execute("""
                INSERT INTO uses_products (
                    name, description, plant_part_id, 
                    industry_sub_category_id, benefits_advantages,
                    technical_specifications, source_agent,
                    created_at
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                RETURNING id
            """, (
                product['name'],
                product['description'],
                plant_part_id,
                industry_id,
                json.dumps(benefits),
                json.dumps(product.get('technical_specifications', {})),
                product['source_agent'],
                datetime.now()
            ))
            
            product_id = cur.fetchone()[0]
            conn.commit()
            conn.close()
            
            self.discovered_count += 1
            print(f"✓ Added: {product['name']} (ID: {product_id})")
            return True
            
        except Exception as e:
            print(f"Error saving product: {e}")
            return False
    
    def map_plant_part(self, product: Dict) -> int:
        """Map product to appropriate plant part"""
        name_lower = product['name'].lower()
        desc_lower = product['description'].lower()
        combined = name_lower + ' ' + desc_lower
        
        # Plant part mapping logic
        if any(term in combined for term in ['fiber', 'bast', 'textile', 'rope']):
            return 1  # Hemp Bast (Fiber)
        elif any(term in combined for term in ['seed', 'oil', 'protein']):
            return 2  # Hemp Seed
        elif any(term in combined for term in ['hurd', 'shiv', 'core']):
            return 7  # Hemp Hurd
        elif any(term in combined for term in ['flower', 'bud']):
            return 8  # Hemp Flowers
        elif any(term in combined for term in ['leaf', 'leaves']):
            return 3  # Hemp Leaves
        elif any(term in combined for term in ['cannabinoid', 'cbd', 'cbg']):
            return 4  # Cannabinoids
        else:
            return 1  # Default to fiber
    
    def map_industry(self, product: Dict) -> int:
        """Map product to appropriate industry"""
        name_lower = product['name'].lower()
        desc_lower = product['description'].lower()
        combined = name_lower + ' ' + desc_lower
        
        # Industry mapping (would need actual IDs from database)
        industry_keywords = {
            1: ['construction', 'building', 'concrete', 'insulation'],
            2: ['automotive', 'vehicle', 'car', 'transportation'],
            3: ['textile', 'fabric', 'clothing', 'fashion'],
            4: ['pharmaceutical', 'medical', 'drug', 'medicine'],
            5: ['cosmetic', 'skincare', 'beauty'],
            6: ['food', 'nutrition', 'dietary', 'supplement'],
            7: ['packaging', 'container', 'wrapper'],
            8: ['electronic', 'battery', 'capacitor', 'circuit'],
            9: ['aerospace', 'aircraft', 'satellite'],
            10: ['marine', 'boat', 'ship', 'underwater']
        }
        
        for industry_id, keywords in industry_keywords.items():
            if any(keyword in combined for keyword in keywords):
                return industry_id
                
        return 1  # Default to construction
    
    def generate_benefits(self, product: Dict) -> List[str]:
        """Generate benefits based on patent information"""
        benefits = []
        
        # Extract benefits from technical specs
        specs = product.get('technical_specifications', {})
        
        if 'tensile_strength' in specs:
            benefits.append(f"High tensile strength: {specs['tensile_strength']['value']} {specs['tensile_strength']['unit']}")
        
        if 'density' in specs:
            benefits.append(f"Optimal density: {specs['density']['value']} {specs['density']['unit']}")
            
        # Pattern-based benefit extraction
        desc = product['description'].lower()
        
        benefit_patterns = {
            'sustainable': 'Environmentally sustainable alternative',
            'biodegradable': 'Fully biodegradable material',
            'renewable': 'Made from renewable hemp resources',
            'lightweight': 'Lightweight yet durable construction',
            'cost-effective': 'Cost-effective manufacturing process',
            'high-performance': 'High-performance material properties',
            'antimicrobial': 'Natural antimicrobial properties',
            'fire-resistant': 'Enhanced fire resistance',
            'water-resistant': 'Water-resistant properties',
            'thermal': 'Excellent thermal properties'
        }
        
        for keyword, benefit in benefit_patterns.items():
            if keyword in desc:
                benefits.append(benefit)
        
        # Ensure at least 3 benefits
        if len(benefits) < 3:
            benefits.extend([
                "Patent-protected innovation",
                "Optimized for industrial applications",
                "Meets industry standards"
            ])
        
        return benefits[:5]  # Limit to 5 benefits
    
    def run_discovery_cycle(self):
        """Run a complete discovery cycle"""
        print(f"\n🔍 {self.name} - Starting Discovery Cycle")
        print("=" * 60)
        
        total_products_found = 0
        
        for search_term in self.search_terms:
            print(f"\nSearching for: {search_term}")
            
            try:
                # Search patents
                patents = self.search_patents(search_term)
                print(f"Found {len(patents)} patents")
                
                # Extract products from each patent
                for patent in patents[:10]:  # Limit per search term
                    products = self.extract_products_from_patent(patent)
                    
                    for product in products:
                        if self.validate_product(product):
                            if self.save_product(product):
                                total_products_found += 1
                
                # Rate limiting
                time.sleep(2)
                
            except Exception as e:
                print(f"Error in discovery cycle: {e}")
                continue
        
        print(f"\n✅ Discovery cycle complete!")
        print(f"Total products discovered: {total_products_found}")
        print(f"Total products by this agent: {self.discovered_count}")
        
        return total_products_found

if __name__ == "__main__":
    agent = PatentMiningAgent()
    agent.run_discovery_cycle()