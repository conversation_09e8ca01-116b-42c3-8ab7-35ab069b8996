#!/usr/bin/env python3
"""
Company Enrichment Agent
Enriches existing company data with missing information
"""

import os
import sys
import json
import logging
import re
from datetime import datetime
from typing import Dict, List, Optional, Any
import time

import psycopg2
from psycopg2.extras import RealDictCursor
from dotenv import load_dotenv
import requests
from bs4 import BeautifulSoup
from urllib.parse import urlparse, urljoin

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Database connection - Use pooler to avoid IPv6 issues
DATABASE_URL = os.getenv('DATABASE_URL', '')
if 'db.ktoqznqmlnxrtvubewyz.supabase.co' in DATABASE_URL:
    DATABASE_URL = DATABASE_URL.replace('db.ktoqznqmlnxrtvubewyz.supabase.co:5432', 'aws-0-us-west-1.pooler.supabase.com:6543')

class CompanyEnrichmentAgent:
    """Agent for enriching company data"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
    def get_connection(self):
        """Get database connection"""
        return psycopg2.connect(DATABASE_URL, cursor_factory=RealDictCursor)
    
    def extract_website_metadata(self, url: str) -> Dict[str, Any]:
        """Extract metadata from company website"""
        try:
            # Ensure URL has protocol
            if not url.startswith(('http://', 'https://')):
                url = f'https://{url}'
            
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            metadata = {
                'title': None,
                'description': None,
                'keywords': [],
                'social_media': {},
                'contact_info': {},
                'location_hints': []
            }
            
            # Extract title
            title_tag = soup.find('title')
            if title_tag:
                metadata['title'] = title_tag.text.strip()
            
            # Extract meta description
            meta_desc = soup.find('meta', attrs={'name': 'description'}) or \
                       soup.find('meta', attrs={'property': 'og:description'})
            if meta_desc:
                metadata['description'] = meta_desc.get('content', '').strip()
            
            # Extract keywords
            meta_keywords = soup.find('meta', attrs={'name': 'keywords'})
            if meta_keywords:
                metadata['keywords'] = [k.strip() for k in meta_keywords.get('content', '').split(',')]
            
            # Find social media links
            social_patterns = {
                'facebook': r'facebook\.com/[\w\-\.]+',
                'twitter': r'twitter\.com/[\w\-\.]+',
                'instagram': r'instagram\.com/[\w\-\.]+',
                'linkedin': r'linkedin\.com/company/[\w\-\.]+',
                'youtube': r'youtube\.com/(c|channel|user)/[\w\-\.]+'
            }
            
            for link in soup.find_all('a', href=True):
                href = link['href']
                for platform, pattern in social_patterns.items():
                    if re.search(pattern, href, re.I):
                        metadata['social_media'][platform] = href
                        break
            
            # Extract contact information
            # Email
            email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
            emails = re.findall(email_pattern, response.text)
            if emails:
                metadata['contact_info']['email'] = emails[0]
            
            # Phone
            phone_pattern = r'[\+]?[(]?[0-9]{1,3}[)]?[-\s\.]?[(]?[0-9]{1,4}[)]?[-\s\.]?[0-9]{1,4}[-\s\.]?[0-9]{1,9}'
            phones = re.findall(phone_pattern, response.text)
            if phones:
                metadata['contact_info']['phone'] = phones[0]
            
            # Location hints from text
            location_keywords = ['headquartered in', 'located in', 'based in', 'offices in']
            text_content = soup.get_text().lower()
            for keyword in location_keywords:
                if keyword in text_content:
                    # Extract the next few words after the keyword
                    idx = text_content.find(keyword)
                    snippet = text_content[idx:idx+100].split('.')[0]
                    metadata['location_hints'].append(snippet)
            
            return metadata
            
        except Exception as e:
            logger.error(f"Error extracting metadata from {url}: {str(e)}")
            return None
    
    def infer_company_type(self, name: str, description: str) -> str:
        """Infer company type from name and description"""
        name_lower = name.lower()
        desc_lower = (description or '').lower()
        combined = f"{name_lower} {desc_lower}"
        
        # Type inference rules
        if any(word in combined for word in ['manufacturer', 'manufacturing', 'produces', 'production']):
            return 'manufacturer'
        elif any(word in combined for word in ['distributor', 'distribution', 'wholesale']):
            return 'distributor'
        elif any(word in combined for word in ['retailer', 'retail', 'store', 'shop']):
            return 'retailer'
        elif any(word in combined for word in ['brand', 'products', 'line']):
            return 'brand'
        elif any(word in combined for word in ['farm', 'grower', 'cultivation']):
            return 'grower'
        elif any(word in combined for word in ['research', 'laboratory', 'testing']):
            return 'research'
        else:
            return 'brand'  # Default
    
    def estimate_founded_year(self, name: str, website: str = None) -> Optional[int]:
        """Estimate founded year based on various signals"""
        current_year = datetime.now().year
        
        # Check if year is in the name
        year_match = re.search(r'(19|20)\d{2}', name)
        if year_match:
            year = int(year_match.group())
            if 1990 <= year <= current_year:
                return year
        
        # Hemp industry really took off after 2014 Farm Bill
        # Most companies are relatively new
        # Use a conservative estimate based on company type
        if 'CBD' in name or 'Hemp' in name:
            return 2018  # Post-2018 Farm Bill boom
        else:
            return 2015  # General hemp industry growth
    
    def enrich_company(self, company: Dict) -> Dict[str, Any]:
        """Enrich a single company with additional data"""
        enrichments = {
            'id': company['id'],
            'updates': {}
        }
        
        # Fix company type if it's mixed case
        if company.get('company_type'):
            enrichments['updates']['company_type'] = company['company_type'].lower()
        
        # Extract metadata from website if available
        if company.get('website') and not company.get('country'):
            logger.info(f"Extracting metadata for {company['name']} from {company['website']}")
            metadata = self.extract_website_metadata(company['website'])
            
            if metadata:
                # Update description if current one is generic
                if metadata.get('description') and (
                    not company.get('description') or 
                    'Further details pending' in company.get('description', '')
                ):
                    enrichments['updates']['description'] = metadata['description'][:500]
                
                # Add social media if found
                if metadata.get('social_media'):
                    enrichments['updates']['social_media'] = json.dumps(metadata['social_media'])
                
                # Try to infer location from hints
                if metadata.get('location_hints') and not company.get('country'):
                    # Simple country detection
                    location_text = ' '.join(metadata['location_hints']).lower()
                    if 'united states' in location_text or 'usa' in location_text or 'u.s.' in location_text:
                        enrichments['updates']['country'] = 'United States'
                    elif 'canada' in location_text:
                        enrichments['updates']['country'] = 'Canada'
                    elif 'uk' in location_text or 'united kingdom' in location_text:
                        enrichments['updates']['country'] = 'United Kingdom'
                    elif 'australia' in location_text:
                        enrichments['updates']['country'] = 'Australia'
            
            # Rate limiting
            time.sleep(1)
        
        # Infer company type if missing
        if not company.get('company_type') or company.get('company_type') == 'Brand':
            inferred_type = self.infer_company_type(
                company['name'], 
                company.get('description', '')
            )
            enrichments['updates']['company_type'] = inferred_type
        
        # Estimate founded year if missing
        if not company.get('founded_year'):
            estimated_year = self.estimate_founded_year(
                company['name'],
                company.get('website')
            )
            if estimated_year:
                enrichments['updates']['founded_year'] = estimated_year
        
        # Generate website from name if missing
        if not company.get('website'):
            # Simple website generation
            clean_name = re.sub(r'[^\w\s-]', '', company['name']).strip()
            clean_name = re.sub(r'[-\s]+', '-', clean_name).lower()
            potential_domains = [
                f"https://{clean_name}.com",
                f"https://www.{clean_name}.com",
                f"https://{clean_name.replace('-', '')}.com"
            ]
            # Note: In production, you'd want to verify these domains exist
            enrichments['updates']['website'] = potential_domains[0]
        
        return enrichments
    
    def run_enrichment(self, limit: int = 50):
        """Run enrichment on companies missing data"""
        logger.info("Starting company enrichment process...")
        
        with self.get_connection() as conn:
            with conn.cursor() as cur:
                # Find companies needing enrichment
                cur.execute("""
                    SELECT id, name, description, website, country, 
                           company_type, founded_year
                    FROM hemp_companies
                    WHERE (
                        country IS NULL 
                        OR founded_year IS NULL
                        OR company_type = 'Brand'
                        OR description LIKE '%Further details pending%'
                    )
                    AND name NOT LIKE 'Generic%'
                    ORDER BY 
                        CASE WHEN website IS NOT NULL THEN 0 ELSE 1 END,
                        id
                    LIMIT %s
                """, (limit,))
                
                companies = cur.fetchall()
                logger.info(f"Found {len(companies)} companies to enrich")
                
                enriched_count = 0
                for company in companies:
                    try:
                        enrichments = self.enrich_company(company)
                        
                        if enrichments['updates']:
                            # Build update query
                            update_parts = []
                            values = []
                            for field, value in enrichments['updates'].items():
                                update_parts.append(f"{field} = %s")
                                values.append(value)
                            
                            values.append(enrichments['id'])
                            
                            cur.execute(f"""
                                UPDATE hemp_companies
                                SET {', '.join(update_parts)},
                                    updated_at = NOW()
                                WHERE id = %s
                            """, values)
                            
                            logger.info(f"Enriched {company['name']} with {len(enrichments['updates'])} updates")
                            enriched_count += 1
                            
                    except Exception as e:
                        logger.error(f"Error enriching company {company['name']}: {str(e)}")
                        continue
                
                conn.commit()
                logger.info(f"Successfully enriched {enriched_count} companies")
    
    def link_products_by_name_matching(self):
        """Link products to companies based on name matching"""
        logger.info("Linking products to companies by name matching...")
        
        with self.get_connection() as conn:
            with conn.cursor() as cur:
                # Find products that mention company names
                cur.execute("""
                    WITH company_patterns AS (
                        SELECT id, name, 
                               LOWER(REGEXP_REPLACE(name, '[^a-zA-Z0-9]', '', 'g')) as clean_name
                        FROM hemp_companies
                        WHERE name NOT LIKE 'Generic%'
                    ),
                    unlinked_products AS (
                        SELECT p.id, p.name, p.description
                        FROM uses_products p
                        WHERE NOT EXISTS (
                            SELECT 1 FROM hemp_company_products hcp 
                            WHERE hcp.product_id = p.id
                        )
                    )
                    SELECT 
                        up.id as product_id,
                        up.name as product_name,
                        cp.id as company_id,
                        cp.name as company_name
                    FROM unlinked_products up
                    CROSS JOIN company_patterns cp
                    WHERE 
                        LOWER(up.name) LIKE '%' || cp.clean_name || '%'
                        OR LOWER(up.description) LIKE '%' || cp.clean_name || '%'
                    LIMIT 100
                """)
                
                matches = cur.fetchall()
                logger.info(f"Found {len(matches)} potential product-company matches")
                
                linked_count = 0
                for match in matches:
                    cur.execute("""
                        INSERT INTO hemp_company_products 
                        (company_id, product_id, relationship_type, is_primary, created_at)
                        VALUES (%s, %s, 'manufacturer', true, NOW())
                        ON CONFLICT (company_id, product_id) DO NOTHING
                        RETURNING company_id
                    """, (match['company_id'], match['product_id']))
                    
                    if cur.fetchone():
                        logger.info(f"Linked '{match['product_name']}' to '{match['company_name']}'")
                        linked_count += 1
                
                conn.commit()
                logger.info(f"Successfully linked {linked_count} products to companies")

def main():
    """Run the enrichment agent"""
    agent = CompanyEnrichmentAgent()
    
    # Run enrichment
    agent.run_enrichment(limit=20)
    
    # Link products
    agent.link_products_by_name_matching()
    
    logger.info("Company enrichment completed!")

if __name__ == "__main__":
    main()