#!/usr/bin/env python3
"""
Patent Mining Agent V2 - Uses Google Patents & Patent Lens
Part of the Hemp Database Expansion Initiative
Target: 3,000+ products from patent sources
"""

import requests
import json
import time
import hashlib
from datetime import datetime
from typing import Dict, List, Optional
import psycopg2
from psycopg2.extras import RealDictCursor
import os
import re
from bs4 import BeautifulSoup
import urllib.parse

class PatentMiningAgentV2:
    """Enhanced patent mining agent using multiple sources"""
    
    def __init__(self):
        self.name = "Patent Mining Agent V2"
        self.version = "2.0.0"
        self.discovered_count = 0
        self.database_url = os.environ.get('DATABASE_URL')
        
        # Enhanced search terms for hemp patents
        self.search_terms = [
            # Material innovations
            "hemp fiber composite",
            "hemp cellulose extraction",
            "hemp lignin processing",
            "hemp hurd construction",
            "hemp bast fiber textile",
            
            # Technical applications
            "hemp battery electrode",
            "hemp supercapacitor",
            "hemp bioplastic formulation",
            "hemp carbon nanosheets",
            "hemp graphene production",
            
            # Medical/pharma
            "cannabinoid delivery system",
            "hemp pharmaceutical formulation",
            "hemp medical device coating",
            "hemp biocompatible material",
            
            # Industrial processes
            "hemp decortication method",
            "hemp retting process",
            "hemp fiber separation",
            "hemp oil extraction method",
            "hemp protein isolation"
        ]
        
        # Classification codes
        self.cpc_codes = [
            "A01G31",   # Soilless cultivation (hemp growing)
            "A23L33",   # Modifying nutritive qualities of foods
            "A61K36",   # Medicinal preparations from plants
            "B27N3",    # Manufacture from particles/fibers
            "C08L97",   # Lignin-containing materials
            "D01B1",    # Mechanical treatment of plant fibers
            "D21C5",    # Pulp from non-wood plants
            "E04B1",    # Construction materials
            "H01M4",    # Battery electrodes
            "C01B32",   # Carbon materials
        ]
        
    def search_google_patents(self, query: str, max_results: int = 20) -> List[Dict]:
        """Search Google Patents using web scraping"""
        patents = []
        
        # Construct Google Patents search URL
        encoded_query = urllib.parse.quote(query)
        url = f"https://patents.google.com/xhr/query?url=q%3D{encoded_query}%26num%3D{max_results}"
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json',
            'Referer': 'https://patents.google.com/'
        }
        
        try:
            response = requests.get(url, headers=headers, timeout=10)
            if response.status_code == 200:
                data = response.json()
                
                if 'results' in data:
                    results = data.get('results')
                    if isinstance(results, dict) and 'cluster' in results:
                        clusters = results['cluster']
                        if isinstance(clusters, list):
                            for cluster in clusters:
                                if isinstance(cluster, dict) and 'result' in cluster:
                                    for result in cluster['result']:
                                        if isinstance(result, dict) and 'patent' in result:
                                            patent = result['patent']
                                            
                                            patent_data = {
                                                'patent_number': patent.get('publication_number', ''),
                                                'patent_title': patent.get('title', ''),
                                                'patent_abstract': patent.get('abstract', ''),
                                                'patent_date': patent.get('publication_date', ''),
                                                'assignee': patent.get('assignee', ''),
                                                'inventor': patent.get('inventor', []),
                                                'source': 'Google Patents'
                                            }
                                            
                                            # Only include if it has substantive content
                                            if patent_data['patent_title'] and patent_data['patent_abstract']:
                                                patents.append(patent_data)
                            
        except Exception as e:
            print(f"Error searching Google Patents: {e}")
            
        return patents
    
    def search_patent_lens(self, query: str, max_results: int = 20) -> List[Dict]:
        """Search Patent Lens API (free tier)"""
        patents = []
        
        # Patent Lens API endpoint
        url = "https://api.lens.org/patent/search"
        
        headers = {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        }
        
        # Build query
        payload = {
            'query': {
                'bool': {
                    'must': [
                        {'match': {'abstract': query}},
                        {'match': {'title': query}}
                    ]
                }
            },
            'size': max_results,
            'from': 0
        }
        
        try:
            response = requests.post(url, headers=headers, json=payload, timeout=10)
            if response.status_code == 200:
                data = response.json()
                
                if 'data' in data:
                    for patent in data['data']:
                        patent_data = {
                            'patent_number': patent.get('publication_number', ''),
                            'patent_title': patent.get('title', ''),
                            'patent_abstract': patent.get('abstract', ''),
                            'patent_date': patent.get('published_date', ''),
                            'assignee': ', '.join(patent.get('applicant', [])),
                            'inventor': patent.get('inventor', []),
                            'source': 'Patent Lens'
                        }
                        
                        if patent_data['patent_title']:
                            patents.append(patent_data)
                            
        except Exception as e:
            print(f"Error searching Patent Lens: {e}")
            
        return patents
    
    def extract_products_from_patent(self, patent: Dict) -> List[Dict]:
        """Extract potential products from patent data"""
        products = []
        
        title = patent.get('patent_title', '')
        abstract = patent.get('patent_abstract', '')
        patent_num = patent.get('patent_number', '')
        
        # Combine text for analysis
        full_text = f"{title} {abstract}".lower()
        
        # Define product extraction patterns
        product_patterns = [
            # Composites and materials
            (r'hemp (?:fiber|fibre) (?:reinforced |composite |)(\w+)', 'Hemp Fiber {} Composite'),
            (r'hemp (?:based |derived |)(\w+) (?:material|composite)', 'Hemp-Based {} Material'),
            
            # Construction materials
            (r'hemp(?:crete|) (\w+) (?:block|panel|board)', 'Hemp {} Construction Material'),
            (r'hemp (?:insulation|insulating) (\w+)', 'Hemp {} Insulation'),
            
            # Technical products
            (r'hemp (?:based |)(\w+) (?:electrode|battery|capacitor)', 'Hemp {} Energy Storage'),
            (r'hemp (?:derived |)(\w+) (?:coating|film|membrane)', 'Hemp {} Coating'),
            
            # Medical/pharma
            (r'cannabinoid (\w+) (?:delivery|formulation|system)', 'Cannabinoid {} System'),
            (r'hemp (?:extract |oil |)(\w+) (?:pharmaceutical|medicine)', 'Hemp {} Pharmaceutical'),
            
            # Processing methods that imply products
            (r'method (?:for |of |)(?:producing|manufacturing|making) hemp (\w+)', 'Hemp {} Product'),
            (r'hemp (\w+) (?:production|manufacturing) (?:method|process)', 'Hemp {} Product'),
        ]
        
        # Extract products based on patterns
        for pattern, name_template in product_patterns:
            matches = re.finditer(pattern, full_text)
            for match in matches:
                component = match.group(1).title()
                product_name = name_template.format(component)
                
                # Generate description
                description = self.generate_product_description(
                    product_name, title, abstract, patent_num
                )
                
                # Create product entry
                product = {
                    'name': product_name,
                    'description': description,
                    'patent_number': patent_num,
                    'patent_title': title,
                    'technical_specifications': {
                        'patent_date': patent.get('patent_date', ''),
                        'assignee': patent.get('assignee', ''),
                        'source': patent.get('source', '')
                    },
                    'source_agent': self.name,
                    'confidence_score': 0.85
                }
                
                products.append(product)
        
        # If no pattern matches, try to extract a general product
        if not products and 'hemp' in full_text:
            # Try to identify the main innovation
            if 'method' in title.lower() or 'process' in title.lower():
                # It's a process patent - extract what it produces
                product_name = self.extract_process_product(title, abstract)
            else:
                # Direct product patent
                product_name = self.extract_direct_product(title, abstract)
            
            if product_name:
                description = self.generate_product_description(
                    product_name, title, abstract, patent_num
                )
                
                product = {
                    'name': product_name,
                    'description': description,
                    'patent_number': patent_num,
                    'patent_title': title,
                    'technical_specifications': {
                        'patent_date': patent.get('patent_date', ''),
                        'assignee': patent.get('assignee', ''),
                        'source': patent.get('source', '')
                    },
                    'source_agent': self.name,
                    'confidence_score': 0.75
                }
                
                products.append(product)
        
        return products[:3]  # Limit products per patent
    
    def extract_process_product(self, title: str, abstract: str) -> Optional[str]:
        """Extract product from process patent"""
        # Look for "for producing X" or "for making X"
        match = re.search(r'for (?:producing|making|manufacturing|creating) (.+?)(?:\.|,|;|$)', 
                         title.lower())
        if match:
            product = match.group(1).strip()
            # Clean up the product name
            product = re.sub(r'\b(?:a|an|the)\b', '', product).strip()
            return f"Hemp {product.title()}"
        
        # Look in abstract
        if 'produce' in abstract.lower() or 'result' in abstract.lower():
            sentences = abstract.split('.')
            for sentence in sentences:
                if 'produce' in sentence.lower() or 'result' in sentence.lower():
                    # Extract the object of production
                    words = sentence.split()
                    for i, word in enumerate(words):
                        if word.lower() in ['produces', 'producing', 'results', 'yields']:
                            if i + 1 < len(words):
                                product = ' '.join(words[i+1:i+4])
                                return f"Hemp {product.title()}"
        
        return None
    
    def extract_direct_product(self, title: str, abstract: str) -> Optional[str]:
        """Extract product from direct product patent"""
        # Remove common patent words
        cleaned_title = title.lower()
        remove_words = ['improved', 'novel', 'new', 'enhanced', 'system', 'apparatus', 'device']
        for word in remove_words:
            cleaned_title = cleaned_title.replace(word, '')
        
        # Extract hemp-related product
        if 'hemp' in cleaned_title:
            # Get words after 'hemp'
            words = cleaned_title.split('hemp')[1].split()[:3]
            if words:
                return f"Hemp {' '.join(words).title()}"
        
        return f"Hemp-Based {title.split()[0].title()} Product"
    
    def generate_product_description(self, name: str, title: str, abstract: str, patent_num: str) -> str:
        """Generate comprehensive product description"""
        # Extract key technical details from abstract
        technical_points = []
        
        # Look for performance metrics
        metrics = re.findall(r'(\d+(?:\.\d+)?)\s*(?:%|percent|times|x)', abstract.lower())
        if metrics:
            technical_points.append(f"Performance improvements up to {metrics[0]}")
        
        # Look for applications
        applications = re.findall(r'(?:used? (?:for|in)|applied? (?:to|in)|suitable (?:for|in)) ([^,\.]+)', 
                                abstract.lower())
        if applications:
            technical_points.append(f"Applications in {applications[0]}")
        
        # Build description
        description = f"{name} based on patented technology from {patent_num}. "
        description += f"This innovation involves {title.lower()}. "
        
        if technical_points:
            description += f"Key features: {'; '.join(technical_points)}. "
        
        description += f"(Patent: {patent_num})"
        
        return description
    
    def validate_product(self, product: Dict) -> bool:
        """Validate product quality and uniqueness"""
        # Check required fields
        required = ['name', 'description', 'source_agent']
        for field in required:
            if not product.get(field):
                return False
        
        # Check minimum quality
        if len(product['name']) < 5 or len(product['description']) < 20:
            return False
        
        # Check for hemp relevance
        text = f"{product['name']} {product['description']}".lower()
        if 'hemp' not in text and 'cannabis' not in text and 'cannabinoid' not in text:
            return False
        
        # Check not duplicate
        return not self.is_duplicate(product)
    
    def is_duplicate(self, product: Dict) -> bool:
        """Check if similar product already exists"""
        try:
            conn = psycopg2.connect(self.database_url)
            cur = conn.cursor()
            
            # Check for exact patent number
            if 'patent_number' in product:
                cur.execute("""
                    SELECT id FROM uses_products 
                    WHERE description LIKE %s
                """, (f"%Patent: {product['patent_number']}%",))
                
                if cur.fetchone():
                    conn.close()
                    return True
            
            # Check for similar names using trigram similarity
            cur.execute("""
                SELECT id FROM uses_products
                WHERE similarity(name, %s) > 0.8
                OR (
                    LENGTH(name) > 20 AND 
                    LENGTH(%s) > 20 AND
                    similarity(LEFT(name, 20), LEFT(%s, 20)) > 0.9
                )
            """, (product['name'], product['name'], product['name']))
            
            if cur.fetchone():
                conn.close()
                return True
                
            conn.close()
            return False
            
        except Exception as e:
            print(f"Error checking duplicate: {e}")
            return True  # Assume duplicate on error
    
    def save_product(self, product: Dict) -> bool:
        """Save product to database"""
        try:
            conn = psycopg2.connect(self.database_url)
            cur = conn.cursor()
            
            # Map to appropriate plant part and industry
            plant_part_id = self.map_plant_part(product)
            industry_id = self.map_industry(product)
            
            # Generate benefits from patent claims
            benefits = self.generate_benefits(product)
            
            cur.execute("""
                INSERT INTO uses_products (
                    name, description, plant_part_id, 
                    industry_sub_category_id, benefits_advantages,
                    technical_specifications, source_agent,
                    created_at
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                RETURNING id
            """, (
                product['name'],
                product['description'],
                plant_part_id,
                industry_id,
                json.dumps(benefits),
                json.dumps(product.get('technical_specifications', {})),
                product['source_agent'],
                datetime.now()
            ))
            
            product_id = cur.fetchone()[0]
            conn.commit()
            conn.close()
            
            self.discovered_count += 1
            print(f"✓ Added: {product['name']} (ID: {product_id})")
            return True
            
        except Exception as e:
            print(f"Error saving product: {e}")
            return False
    
    def map_plant_part(self, product: Dict) -> int:
        """Map product to appropriate plant part"""
        name_lower = product['name'].lower()
        desc_lower = product['description'].lower()
        combined = name_lower + ' ' + desc_lower
        
        # Plant part mapping based on database schema
        if any(term in combined for term in ['fiber', 'fibre', 'bast', 'textile', 'rope', 'composite']):
            return 1  # Hemp Bast (Fiber)
        elif any(term in combined for term in ['seed', 'oil', 'protein', 'omega']):
            return 2  # Hemp Seed
        elif any(term in combined for term in ['hurd', 'shiv', 'core', 'woody']):
            return 7  # Hemp Hurd (Shivs)
        elif any(term in combined for term in ['flower', 'bud', 'inflorescence']):
            return 8  # Hemp Flowers
        elif any(term in combined for term in ['leaf', 'leaves', 'foliage']):
            return 3  # Hemp Leaves
        elif any(term in combined for term in ['cannabinoid', 'cbd', 'cbg', 'thc']):
            return 4  # Cannabinoids
        elif any(term in combined for term in ['root', 'rhizome']):
            return 6  # Hemp Roots
        elif any(term in combined for term in ['terpene', 'essential oil', 'aromatic']):
            return 5  # Terpenes
        else:
            return 1  # Default to fiber
    
    def map_industry(self, product: Dict) -> int:
        """Map product to appropriate industry subcategory"""
        name_lower = product['name'].lower()
        desc_lower = product['description'].lower()
        combined = name_lower + ' ' + desc_lower
        
        # Industry mapping (you'll need to check your actual IDs)
        if any(term in combined for term in ['construction', 'building', 'insulation', 'concrete']):
            return 1  # Construction/Building Materials
        elif any(term in combined for term in ['textile', 'fabric', 'clothing', 'fashion']):
            return 2  # Textiles/Fashion
        elif any(term in combined for term in ['automotive', 'vehicle', 'car', 'transport']):
            return 3  # Automotive
        elif any(term in combined for term in ['medical', 'pharmaceutical', 'drug', 'medicine']):
            return 4  # Medical/Pharmaceutical
        elif any(term in combined for term in ['battery', 'capacitor', 'energy', 'electrode']):
            return 5  # Energy Storage
        elif any(term in combined for term in ['plastic', 'polymer', 'composite', 'material']):
            return 6  # Plastics/Composites
        elif any(term in combined for term in ['cosmetic', 'beauty', 'skincare', 'personal care']):
            return 7  # Cosmetics/Personal Care
        elif any(term in combined for term in ['food', 'nutrition', 'supplement', 'edible']):
            return 8  # Food/Nutrition
        else:
            return 9  # Other/General Industrial
    
    def generate_benefits(self, product: Dict) -> List[str]:
        """Generate benefits from patent information"""
        benefits = []
        
        desc = product['description'].lower()
        name = product['name'].lower()
        
        # Extract benefits from description
        if 'improve' in desc:
            benefits.append("Improved performance over conventional materials")
        if 'sustainable' in desc or 'eco' in desc:
            benefits.append("Environmentally sustainable alternative")
        if 'cost' in desc or 'economic' in desc:
            benefits.append("Cost-effective solution")
        if 'strength' in desc or 'durable' in desc:
            benefits.append("Enhanced strength and durability")
        if 'light' in desc or 'weight' in desc:
            benefits.append("Lightweight design")
        
        # Add patent-specific benefits
        benefits.extend([
            "Patent-protected innovation",
            "Validated through research and development",
            "Industrial-scale production ready"
        ])
        
        return benefits[:5]  # Limit to 5 benefits
    
    def run_discovery_cycle(self):
        """Run a complete discovery cycle"""
        print(f"\n🔍 {self.name} - Starting Discovery Cycle")
        print("=" * 60)
        
        total_products_found = 0
        
        for i, search_term in enumerate(self.search_terms):
            print(f"\n[{i+1}/{len(self.search_terms)}] Searching for: {search_term}")
            
            try:
                # Search both sources
                google_patents = self.search_google_patents(search_term)
                print(f"  Google Patents: {len(google_patents)} results")
                
                # Process Google Patents results
                for patent in google_patents[:5]:  # Limit per search
                    products = self.extract_products_from_patent(patent)
                    
                    for product in products:
                        if self.validate_product(product):
                            if self.save_product(product):
                                total_products_found += 1
                
                # Try Patent Lens as backup
                if len(google_patents) < 3:
                    lens_patents = self.search_patent_lens(search_term)
                    print(f"  Patent Lens: {len(lens_patents)} results")
                    
                    for patent in lens_patents[:3]:
                        products = self.extract_products_from_patent(patent)
                        
                        for product in products:
                            if self.validate_product(product):
                                if self.save_product(product):
                                    total_products_found += 1
                
                # Rate limiting
                time.sleep(2)
                
            except Exception as e:
                print(f"Error in discovery cycle: {e}")
                continue
        
        print(f"\n✅ Discovery cycle complete!")
        print(f"Total products discovered: {total_products_found}")
        print(f"Total products by this agent: {self.discovered_count}")
        
        return total_products_found

if __name__ == "__main__":
    agent = PatentMiningAgentV2()
    agent.run_discovery_cycle()