#!/usr/bin/env python3
"""
API-Based Material Substitution Agent - Discovers where hemp can replace other materials
Focuses on sustainable alternatives across industries
"""

import json
import time
import requests
from datetime import datetime
from typing import Dict, List, Optional, Set
import os
import re
import random
from difflib import SequenceMatcher

class APIMaterialSubstitutionAgent:
    """Material substitution discovery - finding where hemp replaces traditional materials"""
    
    def __init__(self):
        self.name = "API Material Substitution Agent"
        self.version = "1.0.0"
        self.discovered_count = 0
        
        # Supabase configuration
        self.supabase_url = "https://ktoqznqmlnxrtvubewyz.supabase.co"
        self.service_role_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt0b3F6bnFtbG54cnR2dWJld3l6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODQ5MTc3NiwiZXhwIjoyMDY0MDY3Nzc2fQ.hxEiOy6xebv7I4CYJEU49oCEXRvmU6iz2l8Kg-NnvTs"
        
        self.headers = {
            "apikey": self.service_role_key,
            "Authorization": f"Bearer {self.service_role_key}",
            "Content-Type": "application/json",
            "Prefer": "return=minimal"
        }
        
        # Cache for existing products
        self.existing_products_cache = set()
        self.normalized_cache = {}
        self.load_existing_products()
        
        # Material substitution database
        self.substitution_opportunities = [
            # Plastics Replacement
            {
                "category": "plastic_alternatives",
                "traditional_material": "Polypropylene (PP)",
                "applications": [
                    {"name": "Food Container Alternative", "use": "Food storage containers"},
                    {"name": "Automotive Interior Substitute", "use": "Dashboard components"},
                    {"name": "Packaging Film Replacement", "use": "Flexible packaging"},
                    {"name": "Bottle Cap Alternative", "use": "Closure systems"},
                    {"name": "Medical Device Substitute", "use": "Non-implant medical parts"}
                ]
            },
            {
                "category": "plastic_alternatives",
                "traditional_material": "Polyethylene (PE)",
                "applications": [
                    {"name": "Shopping Bag Alternative", "use": "Retail carry bags"},
                    {"name": "Plastic Bottle Substitute", "use": "Beverage containers"},
                    {"name": "Playground Equipment Replacement", "use": "Outdoor recreation"},
                    {"name": "Pipe Insulation Alternative", "use": "Plumbing insulation"},
                    {"name": "Toy Component Substitute", "use": "Children's products"}
                ]
            },
            {
                "category": "plastic_alternatives",
                "traditional_material": "Polystyrene (PS)",
                "applications": [
                    {"name": "Foam Packaging Alternative", "use": "Protective packaging"},
                    {"name": "Disposable Cutlery Substitute", "use": "Single-use utensils"},
                    {"name": "Insulation Board Replacement", "use": "Building insulation"},
                    {"name": "Coffee Cup Alternative", "use": "Hot beverage containers"},
                    {"name": "CD Case Substitute", "use": "Media storage"}
                ]
            },
            # Metal Replacements
            {
                "category": "metal_alternatives",
                "traditional_material": "Aluminum",
                "applications": [
                    {"name": "Lightweight Panel Alternative", "use": "Non-structural panels"},
                    {"name": "Heat Sink Substitute", "use": "Thermal management"},
                    {"name": "Decorative Trim Replacement", "use": "Aesthetic components"},
                    {"name": "Laptop Case Alternative", "use": "Electronic housings"},
                    {"name": "Bicycle Frame Substitute", "use": "Light vehicle structures"}
                ]
            },
            {
                "category": "metal_alternatives",
                "traditional_material": "Steel",
                "applications": [
                    {"name": "Rebar Alternative", "use": "Concrete reinforcement"},
                    {"name": "Non-Critical Fastener Substitute", "use": "Low-stress connections"},
                    {"name": "Furniture Frame Replacement", "use": "Indoor furniture"},
                    {"name": "Storage Rack Alternative", "use": "Light-duty shelving"},
                    {"name": "Tool Handle Substitute", "use": "Hand tool grips"}
                ]
            },
            # Glass Alternatives
            {
                "category": "glass_alternatives",
                "traditional_material": "Glass Fiber",
                "applications": [
                    {"name": "Insulation Batting Alternative", "use": "Thermal insulation"},
                    {"name": "Composite Reinforcement Substitute", "use": "Structural composites"},
                    {"name": "Filter Media Replacement", "use": "Air/liquid filtration"},
                    {"name": "Acoustic Panel Alternative", "use": "Sound absorption"},
                    {"name": "Boat Hull Substitute", "use": "Marine composites"}
                ]
            },
            # Synthetic Fiber Replacements
            {
                "category": "synthetic_fiber_alternatives",
                "traditional_material": "Polyester",
                "applications": [
                    {"name": "Clothing Fabric Alternative", "use": "Apparel textiles"},
                    {"name": "Carpet Backing Substitute", "use": "Floor coverings"},
                    {"name": "Sail Material Replacement", "use": "Marine textiles"},
                    {"name": "Upholstery Alternative", "use": "Furniture covering"},
                    {"name": "Rope Substitute", "use": "Cordage applications"}
                ]
            },
            {
                "category": "synthetic_fiber_alternatives",
                "traditional_material": "Nylon",
                "applications": [
                    {"name": "Parachute Fabric Alternative", "use": "High-strength textiles"},
                    {"name": "Fishing Net Substitute", "use": "Marine equipment"},
                    {"name": "Gear Material Replacement", "use": "Mechanical components"},
                    {"name": "Backpack Fabric Alternative", "use": "Outdoor equipment"},
                    {"name": "Bristle Substitute", "use": "Brush manufacturing"}
                ]
            },
            # Wood & Paper Alternatives
            {
                "category": "wood_alternatives",
                "traditional_material": "Hardwood",
                "applications": [
                    {"name": "Flooring Alternative", "use": "Interior flooring"},
                    {"name": "Furniture Board Substitute", "use": "Cabinet making"},
                    {"name": "Decking Replacement", "use": "Outdoor platforms"},
                    {"name": "Musical Instrument Alternative", "use": "Acoustic components"},
                    {"name": "Handle Material Substitute", "use": "Tool handles"}
                ]
            },
            {
                "category": "wood_alternatives",
                "traditional_material": "Plywood",
                "applications": [
                    {"name": "Sheathing Alternative", "use": "Building envelope"},
                    {"name": "Subflooring Substitute", "use": "Floor underlayment"},
                    {"name": "Packaging Crate Replacement", "use": "Shipping containers"},
                    {"name": "Cabinet Box Alternative", "use": "Storage furniture"},
                    {"name": "Sign Board Substitute", "use": "Display materials"}
                ]
            },
            # Concrete & Ceramic Alternatives
            {
                "category": "concrete_alternatives",
                "traditional_material": "Portland Cement",
                "applications": [
                    {"name": "Hempcrete Alternative", "use": "Non-structural concrete"},
                    {"name": "Mortar Substitute", "use": "Masonry binding"},
                    {"name": "Stucco Replacement", "use": "Exterior coating"},
                    {"name": "Tile Adhesive Alternative", "use": "Flooring installation"},
                    {"name": "Grout Substitute", "use": "Joint filling"}
                ]
            },
            # Leather Alternatives
            {
                "category": "leather_alternatives",
                "traditional_material": "Animal Leather",
                "applications": [
                    {"name": "Shoe Upper Alternative", "use": "Footwear manufacturing"},
                    {"name": "Handbag Material Substitute", "use": "Fashion accessories"},
                    {"name": "Car Seat Replacement", "use": "Automotive upholstery"},
                    {"name": "Watch Strap Alternative", "use": "Wearable accessories"},
                    {"name": "Wallet Material Substitute", "use": "Personal accessories"}
                ]
            },
            # Foam Alternatives
            {
                "category": "foam_alternatives",
                "traditional_material": "Polyurethane Foam",
                "applications": [
                    {"name": "Mattress Foam Alternative", "use": "Bedding materials"},
                    {"name": "Seat Cushion Substitute", "use": "Furniture padding"},
                    {"name": "Packaging Foam Replacement", "use": "Protective shipping"},
                    {"name": "Soundproofing Alternative", "use": "Acoustic treatment"},
                    {"name": "Shoe Sole Substitute", "use": "Footwear cushioning"}
                ]
            },
            # Rubber Alternatives
            {
                "category": "rubber_alternatives",
                "traditional_material": "Synthetic Rubber",
                "applications": [
                    {"name": "Gasket Material Alternative", "use": "Sealing applications"},
                    {"name": "Vibration Damper Substitute", "use": "Shock absorption"},
                    {"name": "Conveyor Belt Replacement", "use": "Material handling"},
                    {"name": "Floor Mat Alternative", "use": "Anti-fatigue surfaces"},
                    {"name": "Weatherstrip Substitute", "use": "Door/window sealing"}
                ]
            }
        ]
        
        # Product templates by category
        self.product_templates = {
            "plastic_alternatives": [
                "Hemp-Based {} to Replace Plastic",
                "Sustainable Hemp {} Alternative",
                "Bio-Based Hemp {} Substitute"
            ],
            "metal_alternatives": [
                "Hemp Composite {} Metal Replacement",
                "Lightweight Hemp {} Alternative",
                "Hemp-Based {} Metal Substitute"
            ],
            "glass_alternatives": [
                "Hemp Fiber {} Glass Alternative",
                "Natural Hemp {} Replacement",
                "Hemp-Based {} Glass Substitute"
            ],
            "synthetic_fiber_alternatives": [
                "Natural Hemp {} Fiber Alternative",
                "Hemp Textile {} Replacement",
                "Sustainable Hemp {} Substitute"
            ],
            "wood_alternatives": [
                "Hemp-Based {} Wood Alternative",
                "Engineered Hemp {} Replacement",
                "Hemp Composite {} Wood Substitute"
            ],
            "concrete_alternatives": [
                "Hemp-Based {} Concrete Alternative",
                "Natural Hemp {} Cement Substitute",
                "Hemp Aggregate {} Replacement"
            ],
            "leather_alternatives": [
                "Hemp Leather {} Alternative",
                "Vegan Hemp {} Replacement",
                "Hemp-Based {} Leather Substitute"
            ],
            "foam_alternatives": [
                "Hemp Foam {} Alternative",
                "Natural Hemp {} Cushioning",
                "Bio-Based Hemp {} Foam Substitute"
            ],
            "rubber_alternatives": [
                "Hemp-Based {} Rubber Alternative",
                "Flexible Hemp {} Replacement",
                "Hemp Elastomer {} Substitute"
            ]
        }
        
        # Industry mapping based on application
        self.industry_mapping = {
            "plastic_alternatives": 12,      # Packaging
            "metal_alternatives": 6,         # Construction Materials
            "glass_alternatives": 5,         # Industrial Textiles
            "synthetic_fiber_alternatives": 4, # Apparel and Fashion
            "wood_alternatives": 6,          # Construction Materials
            "concrete_alternatives": 6,      # Construction Materials
            "leather_alternatives": 4,       # Apparel and Fashion
            "foam_alternatives": 10,         # Furniture and Home Goods
            "rubber_alternatives": 5         # Industrial Textiles
        }

    def load_existing_products(self):
        """Load existing products into cache"""
        print("Loading existing products into cache...")
        offset = 0
        limit = 1000
        
        while True:
            response = requests.get(
                f"{self.supabase_url}/rest/v1/uses_products",
                headers=self.headers,
                params={
                    "select": "name",
                    "order": "id",
                    "offset": offset,
                    "limit": limit
                }
            )
            
            if response.status_code != 200 or not response.json():
                break
                
            batch = response.json()
            for product in batch:
                name = product['name']
                self.existing_products_cache.add(name.lower())
                normalized = self.normalize_name(name)
                self.normalized_cache[normalized.lower()] = name
            
            if len(batch) < limit:
                break
            offset += limit
        
        print(f"Loaded {len(self.existing_products_cache)} existing products")

    def normalize_name(self, name: str) -> str:
        """Normalize product name"""
        name = ' '.join(name.split())
        name = re.sub(r'\b(\w+)\s+\1\b', r'\1', name)
        name = name.replace('Hemp-Based', 'Hemp')
        name = name.replace('Hemp-Derived', 'Hemp')
        name = re.sub(r'(\w+)\s+\1$', r'\1', name)
        return name.strip()

    def check_duplicate_advanced(self, name: str, threshold: float = 0.85) -> bool:
        """Advanced duplicate detection"""
        name_lower = name.lower()
        normalized = self.normalize_name(name)
        normalized_lower = normalized.lower()
        
        if name_lower in self.existing_products_cache:
            return True
        
        if normalized_lower in self.normalized_cache:
            print(f"  ⚠️  Similar to existing: {self.normalized_cache[normalized_lower]}")
            return True
        
        for existing in self.existing_products_cache:
            similarity = SequenceMatcher(None, normalized_lower, existing).ratio()
            if similarity > threshold:
                print(f"  ⚠️  Too similar ({similarity:.2f}) to existing product")
                return True
        
        return False

    def get_plant_part_for_category(self, category: str) -> int:
        """Get appropriate plant part ID for substitution category"""
        mapping = {
            "plastic_alternatives": 7,       # Whole plant for versatility
            "metal_alternatives": 3,         # Stem for structural strength
            "glass_alternatives": 1,         # Fiber for flexibility
            "synthetic_fiber_alternatives": 1, # Fiber as direct replacement
            "wood_alternatives": 3,          # Stem/hurds for wood-like properties
            "concrete_alternatives": 3,      # Stem/hurds for aggregate
            "leather_alternatives": 1,       # Fiber for textile properties
            "foam_alternatives": 3,          # Stem for cellular structure
            "rubber_alternatives": 7         # Whole plant for complex polymers
        }
        return mapping.get(category, 7)

    def generate_substitution_description(self, app_name: str, app_use: str, 
                                        category: str, traditional: str) -> str:
        """Generate detailed substitution product description"""
        descriptions = {
            "plastic_alternatives": f"Innovative {app_name} designed to replace {traditional} in {app_use}. "
                                  f"This bio-based alternative offers comparable performance with significantly "
                                  f"reduced environmental impact, biodegradability, and renewable sourcing.",
            
            "metal_alternatives": f"Advanced {app_name} engineered as a sustainable alternative to {traditional} "
                                f"for {app_use}. Provides excellent strength-to-weight ratio and corrosion "
                                f"resistance while eliminating mining and processing impacts.",
            
            "glass_alternatives": f"Natural {app_name} developed to substitute {traditional} in {app_use}. "
                                f"Offers similar performance characteristics with improved sustainability, "
                                f"reduced energy consumption, and safer end-of-life disposal.",
            
            "synthetic_fiber_alternatives": f"Sustainable {app_name} created to replace {traditional} in {app_use}. "
                                          f"Delivers comparable durability and performance with natural, "
                                          f"biodegradable fibers free from microplastic pollution.",
            
            "wood_alternatives": f"Engineered {app_name} designed as an eco-friendly substitute for {traditional} "
                               f"in {app_use}. Reduces deforestation pressure while providing similar "
                               f"structural and aesthetic properties with faster renewability.",
            
            "concrete_alternatives": f"Revolutionary {app_name} formulated to replace {traditional} in {app_use}. "
                                   f"Offers carbon-negative potential with improved insulation properties "
                                   f"and reduced embodied energy compared to traditional concrete.",
            
            "leather_alternatives": f"Cruelty-free {app_name} developed as a vegan alternative to {traditional} "
                                  f"for {app_use}. Provides leather-like aesthetics and durability without "
                                  f"animal products, toxic tanning processes, or ethical concerns.",
            
            "foam_alternatives": f"Bio-based {app_name} engineered to replace {traditional} in {app_use}. "
                               f"Delivers excellent cushioning and insulation properties with natural "
                               f"materials free from harmful chemicals and petroleum derivatives.",
            
            "rubber_alternatives": f"Flexible {app_name} created as a sustainable substitute for {traditional} "
                                 f"in {app_use}. Provides elasticity and durability using plant-based "
                                 f"materials instead of petroleum-derived synthetic rubber."
        }
        
        return descriptions.get(category, f"Hemp-based {app_name} to replace {traditional} in {app_use}.")

    def generate_substitution_benefits(self, category: str, traditional: str) -> str:
        """Generate benefits based on material being replaced"""
        base_benefits = [f"Replaces {traditional}", "Sustainable alternative", "Renewable resource"]
        
        category_benefits = {
            "plastic_alternatives": ["Biodegradable", "No microplastics", "Carbon negative potential"],
            "metal_alternatives": ["Lightweight", "Corrosion resistant", "No mining required"],
            "glass_alternatives": ["Shatter resistant", "Lower energy production", "Safer handling"],
            "synthetic_fiber_alternatives": ["Natural fiber", "Breathable", "No synthetic chemicals"],
            "wood_alternatives": ["Fast growing", "No deforestation", "Consistent quality"],
            "concrete_alternatives": ["Carbon sequestration", "Better insulation", "Mold resistant"],
            "leather_alternatives": ["Cruelty free", "No toxic tanning", "Water resistant"],
            "foam_alternatives": ["Chemical free", "Hypoallergenic", "Compostable"],
            "rubber_alternatives": ["Plant based", "UV resistant", "Recyclable"]
        }
        
        benefits = base_benefits + category_benefits.get(category, ["Eco-friendly substitute"])
        return "{" + ",".join([f'"{b}"' for b in benefits]) + "}"

    def create_substitution_products(self, substitution_group: Dict) -> List[Dict]:
        """Create products from substitution opportunity"""
        products = []
        category = substitution_group["category"]
        traditional = substitution_group["traditional_material"]
        
        for app in substitution_group["applications"]:
            # Generate 1-2 variations per application
            num_variations = random.randint(1, 2)
            
            for i in range(num_variations):
                templates = self.product_templates.get(category, ["Hemp {} Alternative"])
                template = random.choice(templates)
                
                # Create product name
                if i == 0:
                    product_name = template.format(app["name"])
                else:
                    modifiers = ["Advanced", "Premium", "Commercial", "Industrial", "Eco"]
                    modifier = random.choice(modifiers)
                    product_name = template.format(f"{modifier} {app['name']}")
                
                product_name = self.normalize_name(product_name)
                
                # Check for duplicates
                if self.check_duplicate_advanced(product_name):
                    continue
                
                # Determine plant part
                plant_part_id = self.get_plant_part_for_category(category)
                
                # Calculate confidence score (0.65-0.9 for substitutions)
                confidence_score = round(0.65 + (random.random() * 0.25), 2)
                
                product = {
                    'name': product_name,
                    'description': self.generate_substitution_description(
                        app["name"], app["use"], category, traditional
                    ),
                    'industry_sub_category_id': self.industry_mapping.get(category, 18),
                    'plant_part_id': plant_part_id,
                    'confidence_score': confidence_score,
                    'image_url': None,
                    'benefits_advantages': self.generate_substitution_benefits(category, traditional),
                    'technical_specifications': json.dumps({
                        "category": category,
                        "replaces": traditional,
                        "application": app["use"],
                        "substitution_type": app["name"],
                        "environmental_benefit": "High"
                    }),
                    'source_type': 'ai_agent',
                    'source_agent': self.name,
                    'source_url': f"Substitution: {traditional}"
                }
                
                products.append(product)
                
                # Add to cache immediately
                self.existing_products_cache.add(product_name.lower())
                self.normalized_cache[product_name.lower()] = product_name
        
        return products

    def save_product_api(self, product_data: Dict) -> bool:
        """Save product using REST API"""
        try:
            response = requests.post(
                f"{self.supabase_url}/rest/v1/uses_products",
                headers=self.headers,
                json=product_data
            )
            
            if response.status_code in [200, 201, 204]:
                self.discovered_count += 1
                return True
            else:
                print(f"Failed to save product: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"Error saving product: {e}")
            return False

    def run_discovery(self) -> Dict:
        """Run material substitution discovery"""
        start_time = time.time()
        total_saved = 0
        total_duplicates = 0
        total_errors = 0
        
        print(f"\n♻️ {self.name} - Starting Discovery Cycle")
        print(f"Substitution categories: {len(self.substitution_opportunities)}")
        print("=" * 60)
        
        for idx, substitution_group in enumerate(self.substitution_opportunities, 1):
            category = substitution_group["category"]
            traditional = substitution_group["traditional_material"]
            num_apps = len(substitution_group["applications"])
            
            print(f"\n[{idx}/{len(self.substitution_opportunities)}] Replacing {traditional}")
            print(f"  Category: {category.replace('_', ' ').title()}")
            print(f"  Applications: {num_apps}")
            
            try:
                # Create products from substitution group
                products = self.create_substitution_products(substitution_group)
                print(f"  Generated {len(products)} product variations")
                
                # Save each product
                saved = 0
                for product in products:
                    if self.save_product_api(product):
                        saved += 1
                        total_saved += 1
                        print(f"  ✅ {product['name']}")
                    else:
                        total_errors += 1
                
                # Track duplicates prevented
                expected = num_apps * 1.5  # Average expected variations
                prevented = max(0, expected - len(products))
                total_duplicates += int(prevented)
                
                print(f"  Saved {saved} new products")
                
                # Rate limiting
                time.sleep(0.5)
                
            except Exception as e:
                print(f"  ❌ Error processing category: {e}")
                total_errors += 1
        
        # Summary
        duration = time.time() - start_time
        print(f"\n{'=' * 60}")
        print(f"Discovery Complete!")
        print(f"  Duration: {duration:.1f} seconds")
        print(f"  Materials replaced: {len(self.substitution_opportunities)}")
        print(f"  New products saved: {total_saved}")
        print(f"  Duplicates prevented: ~{total_duplicates}")
        print(f"  Errors: {total_errors}")
        
        return {
            'agent': self.name,
            'duration': duration,
            'materials_replaced': len(self.substitution_opportunities),
            'products_saved': total_saved,
            'duplicates_prevented': total_duplicates,
            'errors': total_errors
        }


if __name__ == "__main__":
    agent = APIMaterialSubstitutionAgent()
    results = agent.run_discovery()
    print(f"\nResults: {json.dumps(results, indent=2)}")