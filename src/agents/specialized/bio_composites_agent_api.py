#!/usr/bin/env python3
"""
API-Based Bio-composites Agent - Discovers hemp composite material applications
Focuses on advanced materials, reinforced composites, and hybrid materials
"""

import json
import time
import requests
from datetime import datetime
from typing import Dict, List, Optional, Set
import os
import re
import random
from difflib import SequenceMatcher

class APIBioCompositesAgent:
    """Bio-composite materials hemp discovery with duplicate prevention"""
    
    def __init__(self):
        self.name = "API Bio-composites Agent"
        self.version = "1.0.0"
        self.discovered_count = 0
        
        # Supabase configuration
        self.supabase_url = "https://ktoqznqmlnxrtvubewyz.supabase.co"
        self.service_role_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt0b3F6bnFtbG54cnR2dWJld3l6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODQ5MTc3NiwiZXhwIjoyMDY0MDY3Nzc2fQ.hxEiOy6xebv7I4CYJEU49oCEXRvmU6iz2l8Kg-NnvTs"
        
        self.headers = {
            "apikey": self.service_role_key,
            "Authorization": f"Bearer {self.service_role_key}",
            "Content-Type": "application/json",
            "Prefer": "return=minimal"
        }
        
        # Cache for existing products
        self.existing_products_cache = set()
        self.normalized_cache = {}
        self.load_existing_products()
        
        # Bio-composites database
        self.bio_composites = [
            # Fiber-Reinforced Composites
            {
                "category": "fiber_reinforced",
                "subcategory": "continuous_fiber",
                "materials": [
                    {"name": "Unidirectional Tape", "description": "Aligned fiber composite"},
                    {"name": "Woven Fabric Composite", "description": "Interlaced fiber matrix"},
                    {"name": "Braided Composite", "description": "3D fiber architecture"},
                    {"name": "Filament Wound", "description": "Continuous wound structure"},
                    {"name": "Pultrusion Profile", "description": "Continuous pulled shape"}
                ]
            },
            {
                "category": "fiber_reinforced",
                "subcategory": "short_fiber",
                "materials": [
                    {"name": "Chopped Fiber Composite", "description": "Random short fibers"},
                    {"name": "Mat Composite", "description": "Non-woven fiber mat"},
                    {"name": "Spray-Up Composite", "description": "Sprayed fiber matrix"},
                    {"name": "Sheet Molding Compound", "description": "SMC material"},
                    {"name": "Bulk Molding Compound", "description": "BMC material"}
                ]
            },
            # Natural Fiber Hybrids
            {
                "category": "hybrid_composites",
                "subcategory": "natural_blends",
                "materials": [
                    {"name": "Hemp-Flax Composite", "description": "Dual natural fiber"},
                    {"name": "Hemp-Jute Hybrid", "description": "Mixed plant fibers"},
                    {"name": "Hemp-Kenaf Blend", "description": "Complementary fibers"},
                    {"name": "Hemp-Sisal Composite", "description": "Strong fiber mix"},
                    {"name": "Hemp-Bamboo Hybrid", "description": "Sustainable blend"}
                ]
            },
            {
                "category": "hybrid_composites",
                "subcategory": "synthetic_blends",
                "materials": [
                    {"name": "Hemp-Carbon Fiber", "description": "High-performance hybrid"},
                    {"name": "Hemp-Glass Fiber", "description": "Cost-effective blend"},
                    {"name": "Hemp-Aramid Composite", "description": "Impact resistant mix"},
                    {"name": "Hemp-Basalt Hybrid", "description": "Mineral fiber blend"},
                    {"name": "Hemp-UHMWPE Composite", "description": "Ultra-strong hybrid"}
                ]
            },
            # Bio-resin Composites
            {
                "category": "bio_resin",
                "subcategory": "plant_based_resins",
                "materials": [
                    {"name": "Soy-Based Composite", "description": "Soybean resin matrix"},
                    {"name": "Cashew Shell Composite", "description": "CNSL resin system"},
                    {"name": "Pine Resin Composite", "description": "Tree resin matrix"},
                    {"name": "Linseed Oil Composite", "description": "Drying oil system"},
                    {"name": "Castor Oil Composite", "description": "Renewable polyol base"}
                ]
            },
            {
                "category": "bio_resin",
                "subcategory": "modified_resins",
                "materials": [
                    {"name": "Epoxidized Hemp Oil", "description": "Modified oil resin"},
                    {"name": "Bio-Polyurethane", "description": "Plant-based PU"},
                    {"name": "Bio-Epoxy Composite", "description": "Renewable epoxy"},
                    {"name": "Bio-Polyester", "description": "Natural polyester"},
                    {"name": "Bio-Vinyl Ester", "description": "Plant vinyl ester"}
                ]
            },
            # Sandwich Composites
            {
                "category": "sandwich_structures",
                "subcategory": "core_materials",
                "materials": [
                    {"name": "Honeycomb Core", "description": "Hexagonal cell structure"},
                    {"name": "Foam Core Composite", "description": "Lightweight foam center"},
                    {"name": "Balsa Core Panel", "description": "Wood core sandwich"},
                    {"name": "Corrugated Core", "description": "Wave pattern core"},
                    {"name": "3D Printed Core", "description": "Custom lattice core"}
                ]
            },
            {
                "category": "sandwich_structures",
                "subcategory": "face_sheets",
                "materials": [
                    {"name": "Prepreg Face Sheet", "description": "Pre-impregnated skin"},
                    {"name": "Wet Lay-Up Face", "description": "Hand laid skin"},
                    {"name": "RTM Face Sheet", "description": "Resin transfer skin"},
                    {"name": "Vacuum Infused Face", "description": "VARTM skin"},
                    {"name": "Compression Molded Face", "description": "Pressed skin layer"}
                ]
            },
            # Particle Composites
            {
                "category": "particle_composites",
                "subcategory": "micro_particles",
                "materials": [
                    {"name": "Microfiber Composite", "description": "Ultra-fine particles"},
                    {"name": "Nano-Hemp Composite", "description": "Nanoparticle blend"},
                    {"name": "Whisker Reinforced", "description": "Crystal whiskers"},
                    {"name": "Platelet Composite", "description": "Flat particle mix"},
                    {"name": "Spherical Filler", "description": "Ball-shaped particles"}
                ]
            },
            {
                "category": "particle_composites",
                "subcategory": "macro_particles",
                "materials": [
                    {"name": "Wood-Plastic Composite", "description": "WPC material"},
                    {"name": "Aggregate Composite", "description": "Stone particle mix"},
                    {"name": "Recycled Composite", "description": "Waste material blend"},
                    {"name": "Cork Composite", "description": "Natural cork particles"},
                    {"name": "Shell Composite", "description": "Nut shell filler"}
                ]
            },
            # Functional Composites
            {
                "category": "functional_composites",
                "subcategory": "conductive",
                "materials": [
                    {"name": "Conductive Fiber Composite", "description": "Electrical conductor"},
                    {"name": "EMI Shielding", "description": "Electromagnetic shield"},
                    {"name": "Thermal Conductor", "description": "Heat transfer material"},
                    {"name": "Static Dissipative", "description": "ESD protection"},
                    {"name": "Lightning Strike", "description": "Electrical protection"}
                ]
            },
            {
                "category": "functional_composites",
                "subcategory": "smart_materials",
                "materials": [
                    {"name": "Self-Healing Composite", "description": "Damage recovery"},
                    {"name": "Shape Memory", "description": "Temperature activated"},
                    {"name": "Piezoelectric Composite", "description": "Energy harvesting"},
                    {"name": "Magnetostrictive", "description": "Magnetic response"},
                    {"name": "Chromogenic Composite", "description": "Color changing"}
                ]
            },
            # Processing-Specific Composites
            {
                "category": "process_specific",
                "subcategory": "compression_molding",
                "materials": [
                    {"name": "GMT Composite", "description": "Glass mat thermoplastic"},
                    {"name": "LFT Composite", "description": "Long fiber thermoplastic"},
                    {"name": "Direct LFT", "description": "In-line compounding"},
                    {"name": "Hybrid Molding", "description": "Multi-material process"},
                    {"name": "Co-Molded Composite", "description": "Over-molded structure"}
                ]
            },
            {
                "category": "process_specific",
                "subcategory": "liquid_molding",
                "materials": [
                    {"name": "RTM Composite", "description": "Resin transfer molded"},
                    {"name": "VARTM Material", "description": "Vacuum assisted RTM"},
                    {"name": "Infusion Composite", "description": "Vacuum infused"},
                    {"name": "Wet Compression", "description": "Liquid compression"},
                    {"name": "Reaction Injection", "description": "RIM composite"}
                ]
            },
            # Industry-Specific Composites
            {
                "category": "industry_specific",
                "subcategory": "automotive_grade",
                "materials": [
                    {"name": "Class A Surface", "description": "Painted body panel"},
                    {"name": "Structural Member", "description": "Load bearing part"},
                    {"name": "NVH Composite", "description": "Noise reduction"},
                    {"name": "Crash Structure", "description": "Energy absorption"},
                    {"name": "Under-Hood Part", "description": "High temperature"}
                ]
            },
            {
                "category": "industry_specific",
                "subcategory": "marine_grade",
                "materials": [
                    {"name": "Hull Composite", "description": "Boat body material"},
                    {"name": "Deck Composite", "description": "Marine flooring"},
                    {"name": "Mast Material", "description": "Sail support"},
                    {"name": "Underwater Composite", "description": "Submersible rated"},
                    {"name": "Osmosis Resistant", "description": "Water barrier"}
                ]
            }
        ]
        
        # Product templates by category
        self.product_templates = {
            "fiber_reinforced": [
                "Hemp {} Fiber Composite",
                "Reinforced Hemp {} Material",
                "High-Strength Hemp {} Composite"
            ],
            "hybrid_composites": [
                "Hemp {} Hybrid Material",
                "Blended Hemp {} Composite",
                "Multi-Fiber Hemp {} System"
            ],
            "bio_resin": [
                "Bio-Resin Hemp {} Composite",
                "Natural Matrix Hemp {} Material",
                "Sustainable Hemp {} System"
            ],
            "sandwich_structures": [
                "Hemp {} Sandwich Panel",
                "Lightweight Hemp {} Structure",
                "Core-Shell Hemp {} Composite"
            ],
            "particle_composites": [
                "Hemp {} Particle Composite",
                "Filled Hemp {} Material",
                "Reinforced Hemp {} Blend"
            ],
            "functional_composites": [
                "Functional Hemp {} Composite",
                "Smart Hemp {} Material",
                "Active Hemp {} System"
            ],
            "process_specific": [
                "Process-Optimized Hemp {} Composite",
                "Molded Hemp {} Material",
                "Engineered Hemp {} Product"
            ],
            "industry_specific": [
                "Industry-Grade Hemp {} Composite",
                "Specialized Hemp {} Material",
                "Application-Specific Hemp {} Product"
            ]
        }
        
        # Industry mapping
        self.industry_id = 18  # Advanced Materials

    def load_existing_products(self):
        """Load existing products into cache"""
        print("Loading existing products into cache...")
        offset = 0
        limit = 1000
        
        while True:
            response = requests.get(
                f"{self.supabase_url}/rest/v1/uses_products",
                headers=self.headers,
                params={
                    "select": "name",
                    "order": "id",
                    "offset": offset,
                    "limit": limit
                }
            )
            
            if response.status_code != 200 or not response.json():
                break
                
            batch = response.json()
            for product in batch:
                name = product['name']
                self.existing_products_cache.add(name.lower())
                normalized = self.normalize_name(name)
                self.normalized_cache[normalized.lower()] = name
            
            if len(batch) < limit:
                break
            offset += limit
        
        print(f"Loaded {len(self.existing_products_cache)} existing products")

    def normalize_name(self, name: str) -> str:
        """Normalize product name"""
        name = ' '.join(name.split())
        name = re.sub(r'\b(\w+)\s+\1\b', r'\1', name)
        name = name.replace('Hemp-Based', 'Hemp')
        name = name.replace('Hemp-Derived', 'Hemp')
        name = re.sub(r'(\w+)\s+\1$', r'\1', name)
        return name.strip()

    def check_duplicate_advanced(self, name: str, threshold: float = 0.85) -> bool:
        """Advanced duplicate detection"""
        name_lower = name.lower()
        normalized = self.normalize_name(name)
        normalized_lower = normalized.lower()
        
        if name_lower in self.existing_products_cache:
            return True
        
        if normalized_lower in self.normalized_cache:
            print(f"  ⚠️  Similar to existing: {self.normalized_cache[normalized_lower]}")
            return True
        
        for existing in self.existing_products_cache:
            similarity = SequenceMatcher(None, normalized_lower, existing).ratio()
            if similarity > threshold:
                print(f"  ⚠️  Too similar ({similarity:.2f}) to existing product")
                return True
        
        return False

    def get_plant_part_for_category(self, category: str) -> int:
        """Get appropriate plant part ID for bio-composite category"""
        mapping = {
            "fiber_reinforced": 1,    # Fiber for reinforcement
            "hybrid_composites": 7,   # Whole plant for complex blends
            "bio_resin": 6,          # Flower/Extract for resin
            "sandwich_structures": 3, # Stem for core materials
            "particle_composites": 7, # Whole plant for particles
            "functional_composites": 7, # Whole plant for functionality
            "process_specific": 1,    # Fiber for processing
            "industry_specific": 7    # Whole plant for applications
        }
        return mapping.get(category, 7)

    def generate_composite_description(self, material_name: str, material_desc: str, 
                                     category: str, subcategory: str) -> str:
        """Generate detailed bio-composite description"""
        descriptions = {
            "fiber_reinforced": f"Advanced {material_name} engineered for {subcategory.replace('_', ' ')} applications. "
                               f"{material_desc}. Hemp fibers provide exceptional mechanical properties "
                               f"with sustainable reinforcement for high-performance composites.",
            
            "hybrid_composites": f"Multi-material {material_name} designed for {subcategory.replace('_', ' ')} systems. "
                                f"{material_desc}. Synergistic combination of hemp with other fibers "
                                f"achieves optimized performance beyond single-fiber limitations.",
            
            "bio_resin": f"Sustainable {material_name} formulated with {subcategory.replace('_', ' ')} technology. "
                        f"{material_desc}. Bio-based resin systems maximize renewable content "
                        f"while maintaining composite performance standards.",
            
            "sandwich_structures": f"Lightweight {material_name} constructed for {subcategory.replace('_', ' ')} design. "
                                  f"{material_desc}. Sandwich architecture leverages hemp's specific "
                                  f"strength for maximum stiffness at minimum weight.",
            
            "particle_composites": f"Reinforced {material_name} utilizing {subcategory.replace('_', ' ')} technology. "
                                  f"{material_desc}. Hemp particles enhance matrix properties through "
                                  f"optimized size distribution and surface treatment.",
            
            "functional_composites": f"Smart {material_name} featuring {subcategory.replace('_', ' ')} capabilities. "
                                    f"{material_desc}. Functional hemp composites integrate active "
                                    f"properties for next-generation material systems.",
            
            "process_specific": f"Optimized {material_name} developed for {subcategory.replace('_', ' ')} manufacturing. "
                               f"{material_desc}. Process-tailored hemp composites maximize production "
                               f"efficiency while maintaining material quality.",
            
            "industry_specific": f"Specialized {material_name} qualified for {subcategory.replace('_', ' ')} requirements. "
                                f"{material_desc}. Industry-specific hemp composites meet rigorous "
                                f"standards for demanding applications."
        }
        
        return descriptions.get(category, f"Bio-composite {material_name}. {material_desc}.")

    def generate_composite_benefits(self, category: str, subcategory: str) -> str:
        """Generate benefits based on composite application"""
        base_benefits = ["High specific strength", "Bio-based content", "Tailorable properties"]
        
        category_benefits = {
            "fiber_reinforced": ["Excellent stiffness", "Fatigue resistance", "Damage tolerance"],
            "hybrid_composites": ["Synergistic properties", "Cost optimization", "Performance balance"],
            "bio_resin": ["Renewable matrix", "Low VOC emissions", "Recyclable system"],
            "sandwich_structures": ["Ultra-lightweight", "High bending stiffness", "Impact absorption"],
            "particle_composites": ["Isotropic properties", "Easy processing", "Cost effective"],
            "functional_composites": ["Multi-functionality", "Smart behavior", "Sensing capability"],
            "process_specific": ["Optimized cycle time", "Consistent quality", "Scalable production"],
            "industry_specific": ["Certified performance", "Application tested", "Standards compliant"]
        }
        
        benefits = base_benefits + category_benefits.get(category, ["Advanced composite"])
        return "{" + ",".join([f'"{b}"' for b in benefits]) + "}"

    def create_composite_products(self, composite_group: Dict) -> List[Dict]:
        """Create products from bio-composite group"""
        products = []
        category = composite_group["category"]
        subcategory = composite_group["subcategory"]
        
        for material in composite_group["materials"]:
            # Generate 1-2 variations per material
            num_variations = random.randint(1, 2)
            
            for i in range(num_variations):
                templates = self.product_templates.get(category, ["Hemp {} Composite"])
                template = random.choice(templates)
                
                # Create product name
                if i == 0:
                    product_name = template.format(material["name"])
                else:
                    modifiers = ["Advanced", "Premium", "Next-Gen", "High-Performance", "Engineered"]
                    modifier = random.choice(modifiers)
                    product_name = template.format(f"{modifier} {material['name']}")
                
                product_name = self.normalize_name(product_name)
                
                # Check for duplicates
                if self.check_duplicate_advanced(product_name):
                    continue
                
                # Determine plant part
                plant_part_id = self.get_plant_part_for_category(category)
                
                # Calculate confidence score (0.75-0.95 for composites)
                confidence_score = round(0.75 + (random.random() * 0.2), 2)
                
                product_data = {
                    'name': product_name,
                    'description': self.generate_composite_description(
                        material["name"], material["description"], category, subcategory
                    ),
                    'industry_sub_category_id': self.industry_id,
                    'plant_part_id': plant_part_id,
                    'confidence_score': confidence_score,
                    'image_url': None,
                    'benefits_advantages': self.generate_composite_benefits(category, subcategory),
                    'technical_specifications': json.dumps({
                        "category": category,
                        "subcategory": subcategory,
                        "composite_type": subcategory.replace('_', ' '),
                        "material_class": material["name"],
                        "processing_methods": ["RTM", "Compression", "Filament Winding"],
                        "standards": ["ASTM D3039", "ISO 527", "SACMA"]
                    }),
                    'source_type': 'ai_agent',
                    'source_agent': self.name,
                    'source_url': f"Composites: {category}/{subcategory}"
                }
                
                products.append(product_data)
                
                # Add to cache immediately
                self.existing_products_cache.add(product_name.lower())
                self.normalized_cache[product_name.lower()] = product_name
        
        return products

    def save_product_api(self, product_data: Dict) -> bool:
        """Save product using REST API"""
        try:
            response = requests.post(
                f"{self.supabase_url}/rest/v1/uses_products",
                headers=self.headers,
                json=product_data
            )
            
            if response.status_code in [200, 201, 204]:
                self.discovered_count += 1
                return True
            else:
                print(f"Failed to save product: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"Error saving product: {e}")
            return False

    def run_discovery(self) -> Dict:
        """Run bio-composites discovery"""
        start_time = time.time()
        total_saved = 0
        total_duplicates = 0
        total_errors = 0
        
        print(f"\n🧪 {self.name} - Starting Discovery Cycle")
        print(f"Composite categories: {len(self.bio_composites)}")
        print("=" * 60)
        
        for idx, composite_group in enumerate(self.bio_composites, 1):
            category = composite_group["category"]
            subcategory = composite_group["subcategory"]
            num_materials = len(composite_group["materials"])
            
            print(f"\n[{idx}/{len(self.bio_composites)}] {category.title().replace('_', ' ')} - {subcategory.title().replace('_', ' ')}")
            print(f"  Materials in category: {num_materials}")
            
            try:
                # Create products from composite group
                products = self.create_composite_products(composite_group)
                print(f"  Generated {len(products)} product variations")
                
                # Save each product
                saved = 0
                for product in products:
                    if self.save_product_api(product):
                        saved += 1
                        total_saved += 1
                        print(f"  ✅ {product['name']}")
                    else:
                        total_errors += 1
                
                # Track duplicates prevented
                expected = num_materials * 1.5  # Average expected variations
                prevented = max(0, expected - len(products))
                total_duplicates += int(prevented)
                
                print(f"  Saved {saved} new products")
                
                # Rate limiting
                time.sleep(0.5)
                
            except Exception as e:
                print(f"  ❌ Error processing category: {e}")
                total_errors += 1
        
        # Summary
        duration = time.time() - start_time
        print(f"\n{'=' * 60}")
        print(f"Discovery Complete!")
        print(f"  Duration: {duration:.1f} seconds")
        print(f"  Categories processed: {len(self.bio_composites)}")
        print(f"  New products saved: {total_saved}")
        print(f"  Duplicates prevented: ~{total_duplicates}")
        print(f"  Errors: {total_errors}")
        
        return {
            'agent': self.name,
            'duration': duration,
            'categories_processed': len(self.bio_composites),
            'products_saved': total_saved,
            'duplicates_prevented': total_duplicates,
            'errors': total_errors
        }


if __name__ == "__main__":
    agent = APIBioCompositesAgent()
    results = agent.run_discovery()
    print(f"\nResults: {json.dumps(results, indent=2)}")