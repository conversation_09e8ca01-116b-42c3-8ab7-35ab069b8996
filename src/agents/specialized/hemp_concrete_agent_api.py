#!/usr/bin/env python3
"""
API-Based Hemp Concrete Specialist Agent - Discovers hempcrete and concrete applications
Focuses on building materials, infrastructure, and specialized concrete products
"""

import json
import time
import requests
from datetime import datetime
from typing import Dict, List, Optional, Set
import os
import re
import random
from difflib import SequenceMatcher

class APIHempConcreteAgent:
    """Hemp concrete and hempcrete product discovery with duplicate prevention"""
    
    def __init__(self):
        self.name = "API Hemp Concrete Specialist Agent"
        self.version = "1.0.0"
        self.discovered_count = 0
        
        # Supabase configuration
        self.supabase_url = "https://ktoqznqmlnxrtvubewyz.supabase.co"
        self.service_role_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt0b3F6bnFtbG54cnR2dWJld3l6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODQ5MTc3NiwiZXhwIjoyMDY0MDY3Nzc2fQ.hxEiOy6xebv7I4CYJEU49oCEXRvmU6iz2l8Kg-NnvTs"
        
        self.headers = {
            "apikey": self.service_role_key,
            "Authorization": f"Bearer {self.service_role_key}",
            "Content-Type": "application/json",
            "Prefer": "return=minimal"
        }
        
        # Cache for existing products
        self.existing_products_cache = set()
        self.normalized_cache = {}
        self.load_existing_products()
        
        # Hemp concrete applications database
        self.hemp_concrete_applications = [
            # Traditional Hempcrete
            {
                "category": "hempcrete",
                "subcategory": "wall_systems",
                "applications": [
                    {"name": "Cast-in-Place Wall", "description": "Monolithic wall construction"},
                    {"name": "Hempcrete Block", "description": "Pre-formed building block"},
                    {"name": "Spray-Applied Hempcrete", "description": "Projected wall coating"},
                    {"name": "Hempcrete Panel", "description": "Prefabricated wall panel"},
                    {"name": "Insulating Plaster", "description": "Interior/exterior coating"}
                ]
            },
            {
                "category": "hempcrete",
                "subcategory": "floor_systems",
                "applications": [
                    {"name": "Floor Screed", "description": "Leveling compound"},
                    {"name": "Underfloor Insulation", "description": "Thermal floor layer"},
                    {"name": "Acoustic Floor", "description": "Sound dampening layer"},
                    {"name": "Radiant Floor Base", "description": "Heated floor substrate"},
                    {"name": "Lightweight Topping", "description": "Floor finishing layer"}
                ]
            },
            # Structural Concrete
            {
                "category": "structural_concrete",
                "subcategory": "reinforced_elements",
                "applications": [
                    {"name": "Reinforced Beam", "description": "Load-bearing horizontal member"},
                    {"name": "Column Mix", "description": "Vertical support element"},
                    {"name": "Foundation Concrete", "description": "Building base material"},
                    {"name": "Slab Concrete", "description": "Horizontal platform"},
                    {"name": "Shear Wall Mix", "description": "Lateral force resistance"}
                ]
            },
            {
                "category": "structural_concrete",
                "subcategory": "precast_elements",
                "applications": [
                    {"name": "Precast Panel", "description": "Factory-made wall section"},
                    {"name": "Hollow Core Slab", "description": "Lightweight floor element"},
                    {"name": "Precast Beam", "description": "Ready-made structural member"},
                    {"name": "Tilt-Up Panel", "description": "Site-cast wall system"},
                    {"name": "Precast Column", "description": "Vertical support unit"}
                ]
            },
            # Specialty Concretes
            {
                "category": "specialty_concrete",
                "subcategory": "high_performance",
                "applications": [
                    {"name": "Ultra-High Performance", "description": "UHPC alternative"},
                    {"name": "Self-Compacting Concrete", "description": "SCC formulation"},
                    {"name": "Fiber-Reinforced Concrete", "description": "FRC mixture"},
                    {"name": "High-Early Strength", "description": "Fast-curing mix"},
                    {"name": "Shrinkage-Compensating", "description": "Dimensional stability"}
                ]
            },
            {
                "category": "specialty_concrete",
                "subcategory": "functional_concrete",
                "applications": [
                    {"name": "Pervious Concrete", "description": "Water-permeable paving"},
                    {"name": "Heavyweight Concrete", "description": "Radiation shielding"},
                    {"name": "Refractory Concrete", "description": "Heat-resistant material"},
                    {"name": "Polymer Concrete", "description": "Chemical-resistant mix"},
                    {"name": "Cellular Concrete", "description": "Lightweight foam concrete"}
                ]
            },
            # Infrastructure Applications
            {
                "category": "infrastructure",
                "subcategory": "transportation",
                "applications": [
                    {"name": "Road Base Material", "description": "Highway substrate"},
                    {"name": "Bridge Deck Overlay", "description": "Surface restoration"},
                    {"name": "Airport Pavement", "description": "Runway surface"},
                    {"name": "Railway Ballast", "description": "Track bed material"},
                    {"name": "Tunnel Lining", "description": "Underground support"}
                ]
            },
            {
                "category": "infrastructure",
                "subcategory": "water_structures",
                "applications": [
                    {"name": "Dam Concrete", "description": "Water retention structure"},
                    {"name": "Canal Lining", "description": "Water channel coating"},
                    {"name": "Pipe Bedding", "description": "Underground pipe support"},
                    {"name": "Marine Concrete", "description": "Seawater resistant mix"},
                    {"name": "Water Tank Coating", "description": "Storage vessel lining"}
                ]
            },
            # Repair & Restoration
            {
                "category": "repair_materials",
                "subcategory": "structural_repair",
                "applications": [
                    {"name": "Repair Mortar", "description": "Crack filling compound"},
                    {"name": "Patching Compound", "description": "Surface repair material"},
                    {"name": "Injection Grout", "description": "Void filling material"},
                    {"name": "Bonding Agent", "description": "Adhesion promoter"},
                    {"name": "Resurfacing Mix", "description": "Surface renewal compound"}
                ]
            },
            {
                "category": "repair_materials",
                "subcategory": "protective_coatings",
                "applications": [
                    {"name": "Waterproof Coating", "description": "Moisture barrier"},
                    {"name": "Anti-Carbonation", "description": "CO2 protection"},
                    {"name": "Chemical Resistant", "description": "Acid/alkali protection"},
                    {"name": "Fire Protection", "description": "Heat-resistant coating"},
                    {"name": "Anti-Graffiti", "description": "Surface protection"}
                ]
            },
            # Decorative Concrete
            {
                "category": "decorative_concrete",
                "subcategory": "architectural_finishes",
                "applications": [
                    {"name": "Stamped Concrete", "description": "Patterned surface"},
                    {"name": "Exposed Aggregate", "description": "Decorative texture"},
                    {"name": "Polished Concrete", "description": "Smooth finish"},
                    {"name": "Stained Concrete", "description": "Colored surface"},
                    {"name": "Textured Overlay", "description": "Surface treatment"}
                ]
            },
            {
                "category": "decorative_concrete",
                "subcategory": "landscape_products",
                "applications": [
                    {"name": "Paver Stone", "description": "Interlocking unit"},
                    {"name": "Garden Edging", "description": "Border element"},
                    {"name": "Retaining Wall Block", "description": "Landscape wall unit"},
                    {"name": "Stepping Stone", "description": "Pathway element"},
                    {"name": "Planter Mix", "description": "Container material"}
                ]
            },
            # Green Concrete
            {
                "category": "green_concrete",
                "subcategory": "carbon_negative",
                "applications": [
                    {"name": "Carbon-Sequestering Mix", "description": "CO2 absorbing concrete"},
                    {"name": "Bio-Mineralized Concrete", "description": "Living concrete"},
                    {"name": "Recycled Aggregate Mix", "description": "Waste-based concrete"},
                    {"name": "Low-Carbon Cement", "description": "Reduced CO2 binder"},
                    {"name": "Geopolymer Concrete", "description": "Alternative chemistry"}
                ]
            },
            {
                "category": "green_concrete",
                "subcategory": "energy_efficient",
                "applications": [
                    {"name": "Phase Change Concrete", "description": "Thermal storage"},
                    {"name": "Photocatalytic Concrete", "description": "Air purifying surface"},
                    {"name": "Cool Concrete", "description": "Heat island reduction"},
                    {"name": "Insulated Concrete Form", "description": "ICF system"},
                    {"name": "Thermal Mass Concrete", "description": "Temperature regulation"}
                ]
            },
            # Industrial Applications
            {
                "category": "industrial",
                "subcategory": "manufacturing",
                "applications": [
                    {"name": "Machine Foundation", "description": "Vibration dampening base"},
                    {"name": "Industrial Floor", "description": "Heavy-duty surface"},
                    {"name": "Chemical Containment", "description": "Spill protection"},
                    {"name": "Blast Resistant", "description": "Explosion protection"},
                    {"name": "Radiation Shielding", "description": "Nuclear protection"}
                ]
            },
            {
                "category": "industrial",
                "subcategory": "agricultural",
                "applications": [
                    {"name": "Silo Foundation", "description": "Storage structure base"},
                    {"name": "Barn Floor", "description": "Agricultural surface"},
                    {"name": "Manure Pit Lining", "description": "Waste containment"},
                    {"name": "Feed Bunk", "description": "Animal feeding trough"},
                    {"name": "Greenhouse Floor", "description": "Horticultural surface"}
                ]
            }
        ]
        
        # Product templates by category
        self.product_templates = {
            "hempcrete": [
                "Hempcrete {} Material",
                "Natural Hemp {} Concrete",
                "Sustainable Hempcrete {} Mix"
            ],
            "structural_concrete": [
                "Structural Hemp {} Concrete",
                "Reinforced Hemp {} Mix",
                "High-Strength Hemp {} Material"
            ],
            "specialty_concrete": [
                "Specialty Hemp {} Concrete",
                "Advanced Hemp {} Mix",
                "Performance Hemp {} Material"
            ],
            "infrastructure": [
                "Infrastructure Hemp {} Concrete",
                "Heavy-Duty Hemp {} Mix",
                "Civil Hemp {} Material"
            ],
            "repair_materials": [
                "Hemp {} Repair Material",
                "Restoration Hemp {} Mix",
                "Maintenance Hemp {} Compound"
            ],
            "decorative_concrete": [
                "Decorative Hemp {} Concrete",
                "Architectural Hemp {} Mix",
                "Designer Hemp {} Material"
            ],
            "green_concrete": [
                "Green Hemp {} Concrete",
                "Eco-Friendly Hemp {} Mix",
                "Sustainable Hemp {} Material"
            ],
            "industrial": [
                "Industrial Hemp {} Concrete",
                "Heavy-Duty Hemp {} Mix",
                "Specialized Hemp {} Material"
            ]
        }
        
        # Industry mapping
        self.industry_id = 15  # Construction and Building

    def load_existing_products(self):
        """Load existing products into cache"""
        print("Loading existing products into cache...")
        offset = 0
        limit = 1000
        
        while True:
            response = requests.get(
                f"{self.supabase_url}/rest/v1/uses_products",
                headers=self.headers,
                params={
                    "select": "name",
                    "order": "id",
                    "offset": offset,
                    "limit": limit
                }
            )
            
            if response.status_code != 200 or not response.json():
                break
                
            batch = response.json()
            for product in batch:
                name = product['name']
                self.existing_products_cache.add(name.lower())
                normalized = self.normalize_name(name)
                self.normalized_cache[normalized.lower()] = name
            
            if len(batch) < limit:
                break
            offset += limit
        
        print(f"Loaded {len(self.existing_products_cache)} existing products")

    def normalize_name(self, name: str) -> str:
        """Normalize product name"""
        name = ' '.join(name.split())
        name = re.sub(r'\b(\w+)\s+\1\b', r'\1', name)
        name = name.replace('Hemp-Based', 'Hemp')
        name = name.replace('Hemp-Derived', 'Hemp')
        name = re.sub(r'(\w+)\s+\1$', r'\1', name)
        return name.strip()

    def check_duplicate_advanced(self, name: str, threshold: float = 0.85) -> bool:
        """Advanced duplicate detection"""
        name_lower = name.lower()
        normalized = self.normalize_name(name)
        normalized_lower = normalized.lower()
        
        if name_lower in self.existing_products_cache:
            return True
        
        if normalized_lower in self.normalized_cache:
            print(f"  ⚠️  Similar to existing: {self.normalized_cache[normalized_lower]}")
            return True
        
        for existing in self.existing_products_cache:
            similarity = SequenceMatcher(None, normalized_lower, existing).ratio()
            if similarity > threshold:
                print(f"  ⚠️  Too similar ({similarity:.2f}) to existing product")
                return True
        
        return False

    def get_plant_part_for_category(self, category: str) -> int:
        """Get appropriate plant part ID for concrete category"""
        mapping = {
            "hempcrete": 3,           # Stem/Hurds for hempcrete
            "structural_concrete": 3,  # Stem for strength
            "specialty_concrete": 7,   # Whole plant for special properties
            "infrastructure": 3,       # Stem for durability
            "repair_materials": 1,     # Fiber for bonding
            "decorative_concrete": 3,  # Stem for aesthetics
            "green_concrete": 7,       # Whole plant for sustainability
            "industrial": 3            # Stem for heavy duty
        }
        return mapping.get(category, 3)

    def generate_concrete_description(self, app_name: str, app_desc: str, 
                                    category: str, subcategory: str) -> str:
        """Generate detailed concrete product description"""
        descriptions = {
            "hempcrete": f"Sustainable {app_name} designed for {subcategory.replace('_', ' ')} applications. "
                        f"{app_desc}. Hempcrete provides excellent thermal insulation, breathability, "
                        f"and carbon sequestration while being lightweight and easy to work with.",
            
            "structural_concrete": f"High-strength {app_name} engineered for {subcategory.replace('_', ' ')} construction. "
                                  f"{app_desc}. Hemp-reinforced structural concrete delivers superior "
                                  f"performance with reduced environmental impact and enhanced durability.",
            
            "specialty_concrete": f"Advanced {app_name} formulated for {subcategory.replace('_', ' ')} requirements. "
                                 f"{app_desc}. Specialty hemp concrete meets demanding performance "
                                 f"specifications while incorporating sustainable materials.",
            
            "infrastructure": f"Heavy-duty {app_name} developed for {subcategory.replace('_', ' ')} projects. "
                             f"{app_desc}. Infrastructure-grade hemp concrete provides long-term "
                             f"durability and performance for critical applications.",
            
            "repair_materials": f"Specialized {app_name} created for {subcategory.replace('_', ' ')} needs. "
                               f"{app_desc}. Hemp-based repair materials offer excellent adhesion "
                               f"and compatibility with existing structures while being eco-friendly.",
            
            "decorative_concrete": f"Aesthetic {app_name} designed for {subcategory.replace('_', ' ')} installations. "
                                  f"{app_desc}. Decorative hemp concrete combines visual appeal "
                                  f"with sustainable construction practices for beautiful spaces.",
            
            "green_concrete": f"Eco-innovative {app_name} optimized for {subcategory.replace('_', ' ')} goals. "
                             f"{app_desc}. Green hemp concrete actively contributes to environmental "
                             f"sustainability through carbon sequestration and energy efficiency.",
            
            "industrial": f"Robust {app_name} engineered for {subcategory.replace('_', ' ')} environments. "
                         f"{app_desc}. Industrial hemp concrete withstands extreme conditions "
                         f"while maintaining structural integrity and performance."
        }
        
        return descriptions.get(category, f"Hemp concrete {app_name}. {app_desc}.")

    def generate_concrete_benefits(self, category: str, subcategory: str) -> str:
        """Generate benefits based on concrete application"""
        base_benefits = ["Carbon negative", "Sustainable material", "Durable construction"]
        
        category_benefits = {
            "hempcrete": ["Excellent insulation", "Breathable walls", "Lightweight material"],
            "structural_concrete": ["High compressive strength", "Crack resistance", "Long lifespan"],
            "specialty_concrete": ["Specialized properties", "Performance optimized", "Application specific"],
            "infrastructure": ["Heavy load capacity", "Weather resistant", "Low maintenance"],
            "repair_materials": ["Strong adhesion", "Compatible chemistry", "Quick application"],
            "decorative_concrete": ["Aesthetic appeal", "Design flexibility", "Color retention"],
            "green_concrete": ["CO2 sequestration", "Energy efficient", "LEED compatible"],
            "industrial": ["Chemical resistant", "Impact resistant", "Temperature stable"]
        }
        
        benefits = base_benefits + category_benefits.get(category, ["Innovative concrete"])
        return "{" + ",".join([f'"{b}"' for b in benefits]) + "}"

    def create_concrete_products(self, concrete_group: Dict) -> List[Dict]:
        """Create products from concrete application group"""
        products = []
        category = concrete_group["category"]
        subcategory = concrete_group["subcategory"]
        
        for app in concrete_group["applications"]:
            # Generate 1-2 variations per application
            num_variations = random.randint(1, 2)
            
            for i in range(num_variations):
                templates = self.product_templates.get(category, ["Hemp {} Concrete"])
                template = random.choice(templates)
                
                # Create product name
                if i == 0:
                    product_name = template.format(app["name"])
                else:
                    modifiers = ["Premium", "Professional", "Advanced", "Certified", "Eco"]
                    modifier = random.choice(modifiers)
                    product_name = template.format(f"{modifier} {app['name']}")
                
                product_name = self.normalize_name(product_name)
                
                # Check for duplicates
                if self.check_duplicate_advanced(product_name):
                    continue
                
                # Determine plant part
                plant_part_id = self.get_plant_part_for_category(category)
                
                # Calculate confidence score (0.75-0.95 for concrete)
                confidence_score = round(0.75 + (random.random() * 0.2), 2)
                
                product_data = {
                    'name': product_name,
                    'description': self.generate_concrete_description(
                        app["name"], app["description"], category, subcategory
                    ),
                    'industry_sub_category_id': self.industry_id,
                    'plant_part_id': plant_part_id,
                    'confidence_score': confidence_score,
                    'image_url': None,
                    'benefits_advantages': self.generate_concrete_benefits(category, subcategory),
                    'technical_specifications': json.dumps({
                        "category": category,
                        "subcategory": subcategory,
                        "concrete_type": subcategory.replace('_', ' '),
                        "application_class": app["name"],
                        "mix_design": "Proprietary",
                        "standards": ["ASTM C150", "EN 197-1", "ISO 14040"]
                    }),
                    'source_type': 'ai_agent',
                    'source_agent': self.name,
                    'source_url': f"Concrete: {category}/{subcategory}"
                }
                
                products.append(product_data)
                
                # Add to cache immediately
                self.existing_products_cache.add(product_name.lower())
                self.normalized_cache[product_name.lower()] = product_name
        
        return products

    def save_product_api(self, product_data: Dict) -> bool:
        """Save product using REST API"""
        try:
            response = requests.post(
                f"{self.supabase_url}/rest/v1/uses_products",
                headers=self.headers,
                json=product_data
            )
            
            if response.status_code in [200, 201, 204]:
                self.discovered_count += 1
                return True
            else:
                print(f"Failed to save product: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"Error saving product: {e}")
            return False

    def run_discovery(self) -> Dict:
        """Run hemp concrete discovery"""
        start_time = time.time()
        total_saved = 0
        total_duplicates = 0
        total_errors = 0
        
        print(f"\n🏗️ {self.name} - Starting Discovery Cycle")
        print(f"Concrete categories: {len(self.hemp_concrete_applications)}")
        print("=" * 60)
        
        for idx, concrete_group in enumerate(self.hemp_concrete_applications, 1):
            category = concrete_group["category"]
            subcategory = concrete_group["subcategory"]
            num_apps = len(concrete_group["applications"])
            
            print(f"\n[{idx}/{len(self.hemp_concrete_applications)}] {category.title().replace('_', ' ')} - {subcategory.title().replace('_', ' ')}")
            print(f"  Applications: {num_apps}")
            
            try:
                # Create products from concrete group
                products = self.create_concrete_products(concrete_group)
                print(f"  Generated {len(products)} product variations")
                
                # Save each product
                saved = 0
                for product in products:
                    if self.save_product_api(product):
                        saved += 1
                        total_saved += 1
                        print(f"  ✅ {product['name']}")
                    else:
                        total_errors += 1
                
                # Track duplicates prevented
                expected = num_apps * 1.5  # Average expected variations
                prevented = max(0, expected - len(products))
                total_duplicates += int(prevented)
                
                print(f"  Saved {saved} new products")
                
                # Rate limiting
                time.sleep(0.5)
                
            except Exception as e:
                print(f"  ❌ Error processing category: {e}")
                total_errors += 1
        
        # Summary
        duration = time.time() - start_time
        print(f"\n{'=' * 60}")
        print(f"Discovery Complete!")
        print(f"  Duration: {duration:.1f} seconds")
        print(f"  Categories processed: {len(self.hemp_concrete_applications)}")
        print(f"  New products saved: {total_saved}")
        print(f"  Duplicates prevented: ~{total_duplicates}")
        print(f"  Errors: {total_errors}")
        
        return {
            'agent': self.name,
            'duration': duration,
            'categories_processed': len(self.hemp_concrete_applications),
            'products_saved': total_saved,
            'duplicates_prevented': total_duplicates,
            'errors': total_errors
        }


if __name__ == "__main__":
    agent = APIHempConcreteAgent()
    results = agent.run_discovery()
    print(f"\nResults: {json.dumps(results, indent=2)}")