#!/usr/bin/env python3
"""
API-Based Electronics Components Agent - Discovers hemp applications in electronics
Focuses on circuits, batteries, displays, sensors, and electronic materials
"""

import json
import time
import requests
from datetime import datetime
from typing import Dict, List, Optional, Set
import os
import re
import random
from difflib import SequenceMatcher

class APIElectronicsComponentsAgent:
    """Electronics industry hemp component discovery with duplicate prevention"""
    
    def __init__(self):
        self.name = "API Electronics Components Agent"
        self.version = "1.0.0"
        self.discovered_count = 0
        
        # Supabase configuration
        self.supabase_url = "https://ktoqznqmlnxrtvubewyz.supabase.co"
        self.service_role_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt0b3F6bnFtbG54cnR2dWJld3l6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODQ5MTc3NiwiZXhwIjoyMDY0MDY3Nzc2fQ.hxEiOy6xebv7I4CYJEU49oCEXRvmU6iz2l8Kg-NnvTs"
        
        self.headers = {
            "apikey": self.service_role_key,
            "Authorization": f"Bearer {self.service_role_key}",
            "Content-Type": "application/json",
            "Prefer": "return=minimal"
        }
        
        # Cache for existing products
        self.existing_products_cache = set()
        self.normalized_cache = {}
        self.load_existing_products()
        
        # Electronics components database
        self.electronics_components = [
            # Circuit Board Materials
            {
                "category": "circuit_boards",
                "subcategory": "pcb_substrates",
                "components": [
                    {"name": "PCB Base Material", "description": "Rigid circuit board substrate"},
                    {"name": "Flexible PCB Film", "description": "Bendable circuit substrate"},
                    {"name": "High-Frequency PCB", "description": "RF/microwave circuit base"},
                    {"name": "Multilayer Board Core", "description": "Stackable PCB layer material"},
                    {"name": "HDI Board Substrate", "description": "High-density interconnect base"}
                ]
            },
            {
                "category": "circuit_boards",
                "subcategory": "pcb_components",
                "components": [
                    {"name": "Solder Mask Material", "description": "Protective PCB coating"},
                    {"name": "Via Fill Compound", "description": "Conductive hole filling"},
                    {"name": "Conformal Coating", "description": "Environmental protection layer"},
                    {"name": "EMI Shield Layer", "description": "Electromagnetic interference barrier"},
                    {"name": "Thermal Interface Material", "description": "Heat dissipation layer"}
                ]
            },
            # Energy Storage
            {
                "category": "energy_storage",
                "subcategory": "battery_components",
                "components": [
                    {"name": "Battery Separator Film", "description": "Ion-permeable membrane"},
                    {"name": "Anode Material", "description": "Negative electrode substrate"},
                    {"name": "Cathode Binder", "description": "Positive electrode adhesive"},
                    {"name": "Battery Case Material", "description": "Protective cell housing"},
                    {"name": "Current Collector Substrate", "description": "Conductive backing material"}
                ]
            },
            {
                "category": "energy_storage",
                "subcategory": "supercapacitors",
                "components": [
                    {"name": "Supercapacitor Electrode", "description": "High-surface-area carbon material"},
                    {"name": "Capacitor Dielectric Film", "description": "Insulating separator layer"},
                    {"name": "Electrolyte Carrier", "description": "Ion transport medium"},
                    {"name": "Pseudocapacitor Material", "description": "Hybrid energy storage element"},
                    {"name": "EDLC Carbon Material", "description": "Double-layer capacitor electrode"}
                ]
            },
            # Display Technologies
            {
                "category": "displays",
                "subcategory": "display_substrates",
                "components": [
                    {"name": "LCD Backlight Diffuser", "description": "Light distribution film"},
                    {"name": "Touch Screen Substrate", "description": "Transparent conductive base"},
                    {"name": "OLED Encapsulation", "description": "Moisture barrier layer"},
                    {"name": "E-ink Display Film", "description": "Electronic paper substrate"},
                    {"name": "Quantum Dot Film", "description": "Color enhancement layer"}
                ]
            },
            {
                "category": "displays",
                "subcategory": "optical_components",
                "components": [
                    {"name": "Light Guide Plate", "description": "Edge-lit display component"},
                    {"name": "Polarizer Film Base", "description": "Light polarization substrate"},
                    {"name": "Anti-Glare Coating", "description": "Surface reflection reducer"},
                    {"name": "Privacy Filter Material", "description": "Viewing angle limiter"},
                    {"name": "Blue Light Filter", "description": "Eye protection layer"}
                ]
            },
            # Sensors & Transducers
            {
                "category": "sensors",
                "subcategory": "sensor_substrates",
                "components": [
                    {"name": "Pressure Sensor Membrane", "description": "Force-sensitive material"},
                    {"name": "Temperature Sensor Base", "description": "Thermally responsive substrate"},
                    {"name": "Humidity Sensor Element", "description": "Moisture-absorbing material"},
                    {"name": "Gas Sensor Substrate", "description": "Chemical detection base"},
                    {"name": "Biosensor Platform", "description": "Biological sensing substrate"}
                ]
            },
            {
                "category": "sensors",
                "subcategory": "mems_components",
                "components": [
                    {"name": "MEMS Diaphragm", "description": "Micro-mechanical membrane"},
                    {"name": "Accelerometer Mass", "description": "Inertial sensing element"},
                    {"name": "Gyroscope Substrate", "description": "Rotation sensing base"},
                    {"name": "Microphone Membrane", "description": "Acoustic sensing film"},
                    {"name": "Actuator Material", "description": "Micro-movement element"}
                ]
            },
            # Semiconductor Packaging
            {
                "category": "packaging",
                "subcategory": "chip_packaging",
                "components": [
                    {"name": "IC Package Substrate", "description": "Chip mounting base"},
                    {"name": "Underfill Material", "description": "Chip-to-board adhesive"},
                    {"name": "Molding Compound", "description": "Protective chip encapsulation"},
                    {"name": "Lead Frame Material", "description": "Chip connection structure"},
                    {"name": "Die Attach Adhesive", "description": "Chip bonding material"}
                ]
            },
            {
                "category": "packaging",
                "subcategory": "thermal_management",
                "components": [
                    {"name": "Heat Spreader", "description": "Thermal distribution plate"},
                    {"name": "Thermal Pad", "description": "Heat transfer interface"},
                    {"name": "Heat Pipe Wick", "description": "Capillary action material"},
                    {"name": "Phase Change Material", "description": "Temperature regulation compound"},
                    {"name": "Thermal Insulator", "description": "Heat isolation barrier"}
                ]
            },
            # Connectors & Cables
            {
                "category": "connectors",
                "subcategory": "cable_materials",
                "components": [
                    {"name": "Cable Insulation", "description": "Wire protective covering"},
                    {"name": "Cable Sheath", "description": "Outer protective layer"},
                    {"name": "Fiber Optic Buffer", "description": "Optical fiber protection"},
                    {"name": "Coaxial Dielectric", "description": "RF cable insulator"},
                    {"name": "Ribbon Cable Base", "description": "Multi-conductor substrate"}
                ]
            },
            {
                "category": "connectors",
                "subcategory": "connector_components",
                "components": [
                    {"name": "Connector Housing", "description": "Protective connector shell"},
                    {"name": "Contact Insulator", "description": "Pin separation material"},
                    {"name": "Gasket Material", "description": "Environmental seal"},
                    {"name": "Strain Relief Boot", "description": "Cable stress protection"},
                    {"name": "EMI Gasket", "description": "Shielding seal material"}
                ]
            },
            # Emerging Technologies
            {
                "category": "emerging_tech",
                "subcategory": "flexible_electronics",
                "components": [
                    {"name": "Stretchable Conductor", "description": "Elastic circuit material"},
                    {"name": "Wearable Sensor Base", "description": "Body-compatible substrate"},
                    {"name": "E-textile Substrate", "description": "Electronic fabric base"},
                    {"name": "Biocompatible Circuit", "description": "Medical implant material"},
                    {"name": "Self-Healing Circuit", "description": "Damage-repairing conductor"}
                ]
            },
            {
                "category": "emerging_tech",
                "subcategory": "quantum_computing",
                "components": [
                    {"name": "Qubit Substrate", "description": "Quantum bit platform"},
                    {"name": "Cryogenic Insulator", "description": "Ultra-low temp material"},
                    {"name": "Quantum Dot Matrix", "description": "Quantum confinement structure"},
                    {"name": "Josephson Junction Base", "description": "Superconducting substrate"},
                    {"name": "Photonic Crystal", "description": "Light manipulation structure"}
                ]
            }
        ]
        
        # Product templates by category
        self.product_templates = {
            "circuit_boards": [
                "Electronic Hemp {} Component",
                "PCB Hemp {} Material",
                "Circuit-Grade Hemp {} Element"
            ],
            "energy_storage": [
                "Energy Storage Hemp {} Component",
                "Battery-Grade Hemp {} Material",
                "Power Hemp {} Element"
            ],
            "displays": [
                "Display Hemp {} Component",
                "Optical Hemp {} Material",
                "Screen Hemp {} Element"
            ],
            "sensors": [
                "Sensor Hemp {} Component",
                "Detection Hemp {} Material",
                "Sensing Hemp {} Element"
            ],
            "packaging": [
                "Electronic Packaging Hemp {} Material",
                "Chip Hemp {} Component",
                "Thermal Hemp {} Element"
            ],
            "connectors": [
                "Connector Hemp {} Material",
                "Cable Hemp {} Component",
                "Interface Hemp {} Element"
            ],
            "emerging_tech": [
                "Next-Gen Hemp {} Component",
                "Advanced Electronic Hemp {} Material",
                "Future Tech Hemp {} Element"
            ]
        }
        
        # Industry mapping - could be Electronics or Advanced Materials
        self.industry_id = 18  # Advanced Materials

    def load_existing_products(self):
        """Load existing products into cache"""
        print("Loading existing products into cache...")
        offset = 0
        limit = 1000
        
        while True:
            response = requests.get(
                f"{self.supabase_url}/rest/v1/uses_products",
                headers=self.headers,
                params={
                    "select": "name",
                    "order": "id",
                    "offset": offset,
                    "limit": limit
                }
            )
            
            if response.status_code != 200 or not response.json():
                break
                
            batch = response.json()
            for product in batch:
                name = product['name']
                self.existing_products_cache.add(name.lower())
                normalized = self.normalize_name(name)
                self.normalized_cache[normalized.lower()] = name
            
            if len(batch) < limit:
                break
            offset += limit
        
        print(f"Loaded {len(self.existing_products_cache)} existing products")

    def normalize_name(self, name: str) -> str:
        """Normalize product name"""
        name = ' '.join(name.split())
        name = re.sub(r'\b(\w+)\s+\1\b', r'\1', name)
        name = name.replace('Hemp-Based', 'Hemp')
        name = name.replace('Hemp-Derived', 'Hemp')
        name = re.sub(r'(\w+)\s+\1$', r'\1', name)
        return name.strip()

    def check_duplicate_advanced(self, name: str, threshold: float = 0.85) -> bool:
        """Advanced duplicate detection"""
        name_lower = name.lower()
        normalized = self.normalize_name(name)
        normalized_lower = normalized.lower()
        
        if name_lower in self.existing_products_cache:
            return True
        
        if normalized_lower in self.normalized_cache:
            print(f"  ⚠️  Similar to existing: {self.normalized_cache[normalized_lower]}")
            return True
        
        for existing in self.existing_products_cache:
            similarity = SequenceMatcher(None, normalized_lower, existing).ratio()
            if similarity > threshold:
                print(f"  ⚠️  Too similar ({similarity:.2f}) to existing product")
                return True
        
        return False

    def get_plant_part_for_category(self, category: str) -> int:
        """Get appropriate plant part ID for electronics category"""
        mapping = {
            "circuit_boards": 3,     # Stem for rigid substrates
            "energy_storage": 7,     # Whole plant for advanced materials
            "displays": 1,          # Fiber for optical properties
            "sensors": 1,           # Fiber for sensing properties
            "packaging": 3,         # Stem for structural packaging
            "connectors": 1,        # Fiber for insulation
            "emerging_tech": 7      # Whole plant for novel applications
        }
        return mapping.get(category, 7)

    def generate_electronics_description(self, component_name: str, component_desc: str, 
                                       category: str, subcategory: str) -> str:
        """Generate detailed electronics product description"""
        descriptions = {
            "circuit_boards": f"Advanced {component_name} engineered for {subcategory.replace('_', ' ')} applications. "
                            f"{component_desc}. Provides sustainable alternative to traditional PCB materials with "
                            f"excellent electrical properties and thermal stability.",
            
            "energy_storage": f"High-performance {component_name} designed for {subcategory.replace('_', ' ')}. "
                            f"{component_desc}. Enhances energy density and cycling stability while reducing "
                            f"environmental impact of battery production.",
            
            "displays": f"Optical-grade {component_name} optimized for {subcategory.replace('_', ' ')}. "
                       f"{component_desc}. Delivers superior optical clarity and durability for modern "
                       f"display technologies with sustainable materials.",
            
            "sensors": f"Precision {component_name} developed for {subcategory.replace('_', ' ')} applications. "
                      f"{component_desc}. Enables accurate sensing with natural materials offering "
                      f"biocompatibility and environmental sustainability.",
            
            "packaging": f"Electronic-grade {component_name} for {subcategory.replace('_', ' ')}. "
                        f"{component_desc}. Ensures reliable protection and thermal management for "
                        f"sensitive electronic components using eco-friendly materials.",
            
            "connectors": f"Durable {component_name} engineered for {subcategory.replace('_', ' ')}. "
                         f"{component_desc}. Provides excellent insulation and mechanical properties "
                         f"for reliable electronic connections.",
            
            "emerging_tech": f"Revolutionary {component_name} enabling {subcategory.replace('_', ' ')} technologies. "
                            f"{component_desc}. Pushes boundaries of electronic materials with sustainable "
                            f"hemp-based innovations for next-generation devices."
        }
        
        return descriptions.get(category, f"Electronic {component_name}. {component_desc}.")

    def generate_electronics_benefits(self, category: str, subcategory: str) -> str:
        """Generate benefits based on electronics application"""
        base_benefits = ["RoHS compliant potential", "Sustainable electronics material", "Cost-effective production"]
        
        category_benefits = {
            "circuit_boards": ["High dielectric strength", "Low moisture absorption", "Excellent dimensional stability"],
            "energy_storage": ["Enhanced energy density", "Improved cycle life", "Thermal stability"],
            "displays": ["Superior optical properties", "Flexibility potential", "UV resistance"],
            "sensors": ["High sensitivity", "Biocompatibility", "Chemical resistance"],
            "packaging": ["Thermal conductivity", "Mechanical protection", "EMI shielding capability"],
            "connectors": ["Electrical insulation", "Wear resistance", "Environmental sealing"],
            "emerging_tech": ["Novel properties", "Quantum compatibility", "Self-assembly potential"]
        }
        
        benefits = base_benefits + category_benefits.get(category, ["Innovative electronic design"])
        return "{" + ",".join([f'"{b}"' for b in benefits]) + "}"

    def create_electronics_products(self, component_group: Dict) -> List[Dict]:
        """Create products from electronics component group"""
        products = []
        category = component_group["category"]
        subcategory = component_group["subcategory"]
        
        for component in component_group["components"]:
            # Generate 1-2 variations per component
            num_variations = random.randint(1, 2)
            
            for i in range(num_variations):
                templates = self.product_templates.get(category, ["Electronic Hemp {} Component"])
                template = random.choice(templates)
                
                # Create product name
                if i == 0:
                    product_name = template.format(component["name"])
                else:
                    modifiers = ["High-Performance", "Industrial", "Premium", "Advanced", "Professional"]
                    modifier = random.choice(modifiers)
                    product_name = template.format(f"{modifier} {component['name']}")
                
                product_name = self.normalize_name(product_name)
                
                # Check for duplicates
                if self.check_duplicate_advanced(product_name):
                    continue
                
                # Determine plant part
                plant_part_id = self.get_plant_part_for_category(category)
                
                # Calculate confidence score (0.7-0.9 for electronics)
                confidence_score = round(0.7 + (random.random() * 0.2), 2)
                
                product = {
                    'name': product_name,
                    'description': self.generate_electronics_description(
                        component["name"], component["description"], category, subcategory
                    ),
                    'industry_sub_category_id': self.industry_id,
                    'plant_part_id': plant_part_id,
                    'confidence_score': confidence_score,
                    'image_url': None,
                    'benefits_advantages': self.generate_electronics_benefits(category, subcategory),
                    'technical_specifications': json.dumps({
                        "category": category,
                        "subcategory": subcategory,
                        "application": "electronics",
                        "component_type": component["name"],
                        "standards": ["IPC", "JEDEC", "IEEE"],
                        "lead_free": True
                    }),
                    'source_type': 'ai_agent',
                    'source_agent': self.name,
                    'source_url': f"Electronics: {category}/{subcategory}"
                }
                
                products.append(product)
                
                # Add to cache immediately
                self.existing_products_cache.add(product_name.lower())
                self.normalized_cache[product_name.lower()] = product_name
        
        return products

    def save_product_api(self, product_data: Dict) -> bool:
        """Save product using REST API"""
        try:
            response = requests.post(
                f"{self.supabase_url}/rest/v1/uses_products",
                headers=self.headers,
                json=product_data
            )
            
            if response.status_code in [200, 201, 204]:
                self.discovered_count += 1
                return True
            else:
                print(f"Failed to save product: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"Error saving product: {e}")
            return False

    def run_discovery(self) -> Dict:
        """Run electronics components discovery"""
        start_time = time.time()
        total_saved = 0
        total_duplicates = 0
        total_errors = 0
        
        print(f"\n💻 {self.name} - Starting Discovery Cycle")
        print(f"Component categories: {len(self.electronics_components)}")
        print("=" * 60)
        
        for idx, component_group in enumerate(self.electronics_components, 1):
            category = component_group["category"]
            subcategory = component_group["subcategory"]
            num_components = len(component_group["components"])
            
            print(f"\n[{idx}/{len(self.electronics_components)}] {category.title().replace('_', ' ')} - {subcategory.title().replace('_', ' ')}")
            print(f"  Components in category: {num_components}")
            
            try:
                # Create products from component group
                products = self.create_electronics_products(component_group)
                print(f"  Generated {len(products)} product variations")
                
                # Save each product
                saved = 0
                for product in products:
                    if self.save_product_api(product):
                        saved += 1
                        total_saved += 1
                        print(f"  ✅ {product['name']}")
                    else:
                        total_errors += 1
                
                # Track duplicates prevented
                expected = num_components * 1.5  # Average expected variations
                prevented = max(0, expected - len(products))
                total_duplicates += int(prevented)
                
                print(f"  Saved {saved} new products")
                
                # Rate limiting
                time.sleep(0.5)
                
            except Exception as e:
                print(f"  ❌ Error processing category: {e}")
                total_errors += 1
        
        # Summary
        duration = time.time() - start_time
        print(f"\n{'=' * 60}")
        print(f"Discovery Complete!")
        print(f"  Duration: {duration:.1f} seconds")
        print(f"  Categories processed: {len(self.electronics_components)}")
        print(f"  New products saved: {total_saved}")
        print(f"  Duplicates prevented: ~{total_duplicates}")
        print(f"  Errors: {total_errors}")
        
        return {
            'agent': self.name,
            'duration': duration,
            'categories_processed': len(self.electronics_components),
            'products_saved': total_saved,
            'duplicates_prevented': total_duplicates,
            'errors': total_errors
        }


if __name__ == "__main__":
    agent = APIElectronicsComponentsAgent()
    results = agent.run_discovery()
    print(f"\nResults: {json.dumps(results, indent=2)}")