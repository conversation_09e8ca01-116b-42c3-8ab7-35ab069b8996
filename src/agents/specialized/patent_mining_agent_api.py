#!/usr/bin/env python3
"""
API-Based Patent Mining Agent V2 - With Advanced Duplicate Prevention
Includes fuzzy matching and name normalization
"""

import json
import time
import requests
from datetime import datetime
from typing import Dict, List, Optional, Set
import os
import re
import random
import hashlib
from difflib import SequenceMatcher

try:
    from .expanded_patent_database import get_all_patents, get_patent_count
except ImportError:
    from expanded_patent_database import get_all_patents, get_patent_count

class APIPatentMiningAgentV2:
    """Patent mining with advanced duplicate prevention"""
    
    def __init__(self):
        self.name = "API Patent Mining Agent V2"
        self.version = "4.0.0"
        self.discovered_count = 0
        
        # Supabase configuration
        self.supabase_url = "https://ktoqznqmlnxrtvubewyz.supabase.co"
        self.service_role_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt0b3F6bnFtbG54cnR2dWJld3l6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODQ5MTc3NiwiZXhwIjoyMDY0MDY3Nzc2fQ.hxEiOy6xebv7I4CYJEU49oCEXRvmU6iz2l8Kg-NnvTs"
        
        # Set up headers with service role key
        self.headers = {
            "apikey": self.service_role_key,
            "Authorization": f"Bearer {self.service_role_key}",
            "Content-Type": "application/json",
            "Prefer": "return=minimal"
        }
        
        # Cache for existing products (to reduce API calls)
        self.existing_products_cache = set()
        self.normalized_cache = {}  # Map normalized names to original names
        self.load_existing_products()
        
        # Load expanded patent database
        self.patent_examples = get_all_patents()
        print(f"Loaded {len(self.patent_examples)} patents from expanded database")
        
        # Improved product templates (avoid redundancy)
        self.product_templates = {
            "composites": [
                "Hemp Fiber {} Composite",
                "High-Strength Hemp {} Material",
                "Hemp {} Panel"
            ],
            "energy": [
                "Hemp {} Energy Storage",
                "Hemp Carbon {} Electrode",
                "Hemp {} Battery Component"
            ],
            "construction": [
                "Hemp {} Building Material",
                "Structural Hemp {}",
                "Hemp {} Insulation"
            ],
            "plastics": [
                "Biodegradable Hemp {}",
                "Hemp Biopolymer {}",
                "Sustainable Hemp {} Packaging"
            ],
            "textiles": [
                "Hemp {} Textile",
                "Hemp Fiber {} Fabric",
                "Hemp {} Material"
            ],
            "nanotechnology": [
                "Hemp {} Nanomaterial",
                "Hemp Nano-{}",
                "Advanced Hemp {} Nanostructure"
            ],
            "pharmaceutical": [
                "Hemp {} Pharmaceutical",
                "CBD {} Formulation",
                "Medical Hemp {}"
            ],
            "food": [
                "Hemp {} Food",
                "Hemp {} Supplement",
                "Hemp {} Ingredient"
            ],
            "automotive": [
                "Hemp {} Automotive Part",
                "Vehicle Hemp {}",
                "Hemp {} Auto Component"
            ],
            "filtration": [
                "Hemp {} Filter",
                "Hemp {} Filtration System",
                "Hemp Fiber {} Membrane"
            ]
        }
        
        # Variation modifiers (reduced to avoid redundancy)
        self.modifiers = {
            'basic': ["Advanced", "Premium", "Enhanced", "Professional", "Industrial"],
            'quality': ["High-Performance", "Superior", "Optimized", "Refined"],
            'market': ["Commercial", "Consumer", "Professional-Grade"],
        }
        
        # Industry mapping
        self.industry_mapping = {
            "composites": 6,      # Construction Materials
            "energy": 15,         # Energy
            "construction": 6,    # Construction Materials
            "plastics": 12,       # Packaging
            "textiles": 4,        # Apparel and Fashion
            "nanotechnology": 18, # Advanced Materials
            "pharmaceutical": 9,  # Health and Wellness
            "food": 8,           # Food and Beverage
            "automotive": 14,     # Automotive
            "filtration": 5,      # Industrial Textiles
        }

    def load_existing_products(self):
        """Load existing product names into cache for faster duplicate checking"""
        print("Loading existing products into cache...")
        offset = 0
        limit = 1000
        
        while True:
            response = requests.get(
                f"{self.supabase_url}/rest/v1/uses_products",
                headers=self.headers,
                params={
                    "select": "name",
                    "order": "id",
                    "offset": offset,
                    "limit": limit
                }
            )
            
            if response.status_code != 200 or not response.json():
                break
                
            batch = response.json()
            for product in batch:
                name = product['name']
                self.existing_products_cache.add(name.lower())
                normalized = self.normalize_name(name)
                self.normalized_cache[normalized.lower()] = name
            
            if len(batch) < limit:
                break
            offset += limit
        
        print(f"Loaded {len(self.existing_products_cache)} existing products")

    def normalize_name(self, name: str) -> str:
        """Normalize product name to prevent duplicates"""
        # Remove extra spaces
        name = ' '.join(name.split())
        
        # Remove redundant words
        name = re.sub(r'\b(\w+)\s+\1\b', r'\1', name)  # Remove doubled words
        
        # Standardize Hemp variations
        name = name.replace('Hemp-Based', 'Hemp')
        name = name.replace('Hemp-Derived', 'Hemp')
        
        # Remove trailing generic terms if preceded by the same term
        name = re.sub(r'(\w+)\s+\1$', r'\1', name)  # "Textile Textile" -> "Textile"
        
        return name.strip()

    def check_duplicate_advanced(self, name: str, threshold: float = 0.85) -> bool:
        """Advanced duplicate detection using multiple strategies"""
        name_lower = name.lower()
        normalized = self.normalize_name(name)
        normalized_lower = normalized.lower()
        
        # 1. Exact match check
        if name_lower in self.existing_products_cache:
            return True
        
        # 2. Normalized match check
        if normalized_lower in self.normalized_cache:
            print(f"  ⚠️  Similar to existing: {self.normalized_cache[normalized_lower]}")
            return True
        
        # 3. Fuzzy matching for very similar names
        for existing in self.existing_products_cache:
            similarity = SequenceMatcher(None, normalized_lower, existing).ratio()
            if similarity > threshold:
                print(f"  ⚠️  Too similar ({similarity:.2f}) to existing product")
                return True
        
        return False

    def generate_unique_modifier(self, category: str, used_modifiers: Set[str]) -> str:
        """Generate a unique modifier that hasn't been used yet"""
        all_modifiers = []
        for mod_list in self.modifiers.values():
            all_modifiers.extend(mod_list)
        
        # Try to find an unused modifier
        available = [m for m in all_modifiers if m not in used_modifiers]
        if available:
            modifier = random.choice(available)
            used_modifiers.add(modifier)
            return modifier
        
        # If all basic modifiers used, create compound modifiers
        prefix = random.choice(['Ultra', 'Super', 'Mega', 'Next-Gen'])
        base = random.choice(all_modifiers)
        modifier = f"{prefix}-{base}"
        used_modifiers.add(modifier)
        return modifier

    def extract_products_from_patent(self, patent: Dict) -> List[Dict]:
        """Extract products with better duplicate prevention"""
        products = []
        patent_id = patent.get('number', 'Unknown')
        title = patent.get('title', 'Hemp innovation')
        category = patent.get('category', 'general')
        
        # Generate fewer, higher-quality products (3-6 instead of 3-12)
        num_products = random.randint(3, 6)
        templates = self.product_templates.get(category, ["Hemp {} Product"])
        used_modifiers = set()
        used_names = set()
        
        # Extract meaningful terms from patent title
        title_words = re.findall(r'\b[A-Za-z]+\b', title)
        key_terms = [w for w in title_words if len(w) > 4 and 
                    w.lower() not in ['hemp', 'method', 'system', 'apparatus', 'device', 'process']]
        
        attempts = 0
        while len(products) < num_products and attempts < num_products * 3:
            attempts += 1
            
            template = random.choice(templates)
            
            # Use key term from patent if available, otherwise generate modifier
            if key_terms and random.random() > 0.5:
                modifier = random.choice(key_terms).title()
            else:
                modifier = self.generate_unique_modifier(category, used_modifiers)
            
            product_name = template.format(modifier)
            product_name = self.normalize_name(product_name)  # Normalize immediately
            
            # Skip if already generated in this batch
            if product_name in used_names:
                continue
            
            # Check for duplicates
            if self.check_duplicate_advanced(product_name):
                continue
            
            used_names.add(product_name)
            
            # Determine plant part based on category
            plant_part_id = self.get_plant_part_for_category(category)
            
            # Calculate confidence score (0.7-0.95)
            confidence_score = round(0.7 + (random.random() * 0.25), 2)
            
            product = {
                'name': product_name,
                'description': self.generate_product_description(patent, product_name, category),
                'industry_sub_category_id': self.industry_mapping.get(category, 5),
                'plant_part_id': plant_part_id,
                'confidence_score': confidence_score,
                'image_url': None,
                'benefits_advantages': self.generate_benefits(patent_id, category),
                'technical_specifications': json.dumps({
                    "patent_reference": patent_id,
                    "category": category,
                    "innovation_type": patent.get('type', 'product')
                }),
                'source_type': 'ai_agent',
                'source_agent': self.name,
                'source_url': f"Patent: {patent_id}"
            }
            
            products.append(product)
            
            # Add to cache immediately
            self.existing_products_cache.add(product_name.lower())
            self.normalized_cache[product_name.lower()] = product_name
        
        return products

    def get_plant_part_for_category(self, category: str) -> int:
        """Get appropriate plant part ID for category"""
        mapping = {
            "fiber": 1, "textiles": 1, "composites": 1, "paper": 1,
            "food": 2, "oil": 2, "cosmetics": 2,
            "pharmaceutical": 6, "medical": 6,
            "construction": 3, "agriculture": 3,
            "energy": 7, "filtration": 7, "automotive": 7, "nanotechnology": 7
        }
        return mapping.get(category, 7)  # Default to whole plant

    def generate_benefits(self, patent_id: str, category: str) -> str:
        """Generate category-specific benefits"""
        base_benefits = [
            "Sustainable and eco-friendly",
            f"Based on patented technology ({patent_id})",
            "Reduced environmental impact"
        ]
        
        category_benefits = {
            "composites": ["High strength-to-weight ratio", "Durability"],
            "energy": ["High energy density", "Fast charging capability"],
            "construction": ["Fire resistant", "Excellent insulation"],
            "textiles": ["Breathable", "Antimicrobial properties"],
            "pharmaceutical": ["Natural healing properties", "Non-toxic"],
            "food": ["High nutritional value", "Rich in omega fatty acids"]
        }
        
        benefits = base_benefits + category_benefits.get(category, ["Innovative design"])
        return "{" + ",".join([f'"{b}"' for b in benefits]) + "}"

    def generate_product_description(self, patent: Dict, product_name: str, category: str) -> str:
        """Generate meaningful product description"""
        descriptions = {
            "composites": f"Advanced {product_name} incorporating hemp fibers for enhanced strength. Based on {patent.get('title', 'innovative technology')}.",
            "energy": f"Efficient {product_name} utilizing hemp-derived materials. Leveraging {patent.get('title', 'breakthrough technology')}.",
            "construction": f"Sustainable {product_name} for eco-friendly building. Developed using {patent.get('title', 'patented methods')}.",
            "plastics": f"Biodegradable {product_name} offering environmental benefits. Based on {patent.get('title', 'innovative processing')}.",
            "textiles": f"High-performance {product_name} with superior comfort. Incorporating {patent.get('title', 'advanced techniques')}.",
            "pharmaceutical": f"Therapeutic {product_name} for health applications. Developed through {patent.get('title', 'proprietary methods')}.",
            "food": f"Nutritious {product_name} from sustainable sources. Based on {patent.get('title', 'innovative processing')}.",
        }
        
        return descriptions.get(category, 
            f"Innovative {product_name} developed using {patent.get('title', 'patent technology')}.")

    def save_product_api(self, product_data: Dict) -> bool:
        """Save product using REST API"""
        try:
            response = requests.post(
                f"{self.supabase_url}/rest/v1/uses_products",
                headers=self.headers,
                json=product_data
            )
            
            if response.status_code in [200, 201, 204]:
                self.discovered_count += 1
                return True
            else:
                print(f"Failed to save product: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"Error saving product: {e}")
            return False

    def run_discovery(self, max_patents: Optional[int] = None) -> Dict:
        """Run patent discovery with improved duplicate prevention"""
        start_time = time.time()
        total_saved = 0
        total_duplicates = 0
        total_errors = 0
        
        print(f"\n🔍 {self.name} - Starting Discovery Cycle")
        print(f"Duplicate prevention: ENHANCED")
        print("=" * 60)
        
        # Process patents
        patents_to_process = self.patent_examples[:max_patents] if max_patents else self.patent_examples
        
        for idx, patent in enumerate(patents_to_process, 1):
            print(f"\n[{idx}/{len(patents_to_process)}] Processing patent: {patent.get('number', 'Unknown')}")
            print(f"  Title: {patent.get('title', 'No title')}")
            
            try:
                # Extract products from patent
                products = self.extract_products_from_patent(patent)
                print(f"  Generated {len(products)} unique product variations")
                
                # Save each product
                saved = 0
                for product in products:
                    if self.save_product_api(product):
                        saved += 1
                        total_saved += 1
                        print(f"  ✅ Saved: {product['name']}")
                    else:
                        total_errors += 1
                
                # Track prevented duplicates
                prevented = (idx * 6) - len(products)  # Estimate based on average
                total_duplicates += max(0, prevented)
                
                print(f"  Saved {saved} new products")
                
                # Rate limiting
                time.sleep(0.5)
                
            except Exception as e:
                print(f"  ❌ Error processing patent: {e}")
                total_errors += 1
        
        # Summary
        duration = time.time() - start_time
        print(f"\n{'=' * 60}")
        print(f"Discovery Complete!")
        print(f"  Duration: {duration:.1f} seconds")
        print(f"  Patents processed: {len(patents_to_process)}")
        print(f"  New products saved: {total_saved}")
        print(f"  Duplicates prevented: ~{total_duplicates}")
        print(f"  Errors: {total_errors}")
        print(f"  Cache size: {len(self.existing_products_cache)} products")
        
        return {
            'agent': self.name,
            'duration': duration,
            'patents_processed': len(patents_to_process),
            'products_saved': total_saved,
            'duplicates_prevented': total_duplicates,
            'errors': total_errors
        }


if __name__ == "__main__":
    agent = APIPatentMiningAgentV2()
    results = agent.run_discovery(max_patents=10)  # Test with 10 patents
    print(f"\nResults: {json.dumps(results, indent=2)}")