#!/usr/bin/env python3
"""
API-Based Cosmetics & Beauty Agent - Discovers hemp beauty and personal care applications
Focuses on skincare, makeup, hair care, body care, and spa products
"""

import json
import time
import requests
from datetime import datetime
from typing import Dict, List, Optional, Set
import os
import re
import random
from difflib import SequenceMatcher

class APICosmeticsBeautyAgent:
    """Cosmetics and beauty hemp product discovery with duplicate prevention"""
    
    def __init__(self):
        self.name = "API Cosmetics & Beauty Agent"
        self.version = "1.0.0"
        self.discovered_count = 0
        
        # Supabase configuration
        self.supabase_url = "https://ktoqznqmlnxrtvubewyz.supabase.co"
        self.service_role_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt0b3F6bnFtbG54cnR2dWJld3l6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODQ5MTc3NiwiZXhwIjoyMDY0MDY3Nzc2fQ.hxEiOy6xebv7I4CYJEU49oCEXRvmU6iz2l8Kg-NnvTs"
        
        self.headers = {
            "apikey": self.service_role_key,
            "Authorization": f"Bearer {self.service_role_key}",
            "Content-Type": "application/json",
            "Prefer": "return=minimal"
        }
        
        # Cache for existing products
        self.existing_products_cache = set()
        self.normalized_cache = {}
        self.load_existing_products()
        
        # Cosmetics and beauty applications database
        self.beauty_applications = [
            # Facial Skincare
            {
                "category": "facial_skincare",
                "subcategory": "cleansers",
                "products": [
                    {"name": "Gentle Face Wash", "description": "Daily cleansing foam"},
                    {"name": "Oil Cleanser", "description": "Makeup remover"},
                    {"name": "Exfoliating Scrub", "description": "Dead skin removal"},
                    {"name": "Micellar Water", "description": "No-rinse cleanser"},
                    {"name": "Clay Mask Cleanser", "description": "Deep pore cleansing"}
                ]
            },
            {
                "category": "facial_skincare",
                "subcategory": "moisturizers",
                "products": [
                    {"name": "Day Cream", "description": "Daytime hydration"},
                    {"name": "Night Cream", "description": "Overnight repair"},
                    {"name": "Anti-Aging Serum", "description": "Wrinkle treatment"},
                    {"name": "Eye Cream", "description": "Delicate area care"},
                    {"name": "Face Oil", "description": "Nourishing treatment"}
                ]
            },
            # Makeup Products
            {
                "category": "makeup",
                "subcategory": "complexion",
                "products": [
                    {"name": "Foundation", "description": "Base coverage"},
                    {"name": "BB Cream", "description": "Beauty balm"},
                    {"name": "Concealer", "description": "Spot coverage"},
                    {"name": "Primer", "description": "Makeup base"},
                    {"name": "Setting Powder", "description": "Finish product"}
                ]
            },
            {
                "category": "makeup",
                "subcategory": "color_cosmetics",
                "products": [
                    {"name": "Lipstick", "description": "Lip color"},
                    {"name": "Lip Gloss", "description": "Shine treatment"},
                    {"name": "Mascara", "description": "Lash enhancement"},
                    {"name": "Eyeshadow", "description": "Eye color"},
                    {"name": "Blush", "description": "Cheek color"}
                ]
            },
            # Hair Care
            {
                "category": "hair_care",
                "subcategory": "cleansing_conditioning",
                "products": [
                    {"name": "Shampoo", "description": "Hair cleanser"},
                    {"name": "Conditioner", "description": "Hair softener"},
                    {"name": "Hair Mask", "description": "Deep treatment"},
                    {"name": "Leave-In Conditioner", "description": "No-rinse treatment"},
                    {"name": "Scalp Treatment", "description": "Root care"}
                ]
            },
            {
                "category": "hair_care",
                "subcategory": "styling",
                "products": [
                    {"name": "Hair Oil", "description": "Shine and protection"},
                    {"name": "Styling Cream", "description": "Hold and definition"},
                    {"name": "Hair Gel", "description": "Strong hold"},
                    {"name": "Hair Spray", "description": "Finishing hold"},
                    {"name": "Heat Protectant", "description": "Thermal shield"}
                ]
            },
            # Body Care
            {
                "category": "body_care",
                "subcategory": "cleansing",
                "products": [
                    {"name": "Body Wash", "description": "Shower cleanser"},
                    {"name": "Bar Soap", "description": "Traditional cleanser"},
                    {"name": "Body Scrub", "description": "Exfoliating treatment"},
                    {"name": "Bath Oil", "description": "Luxurious soak"},
                    {"name": "Bubble Bath", "description": "Foaming bath product"}
                ]
            },
            {
                "category": "body_care",
                "subcategory": "moisturizing",
                "products": [
                    {"name": "Body Lotion", "description": "Daily moisturizer"},
                    {"name": "Body Butter", "description": "Rich moisturizer"},
                    {"name": "Body Oil", "description": "Nourishing treatment"},
                    {"name": "Hand Cream", "description": "Hand moisturizer"},
                    {"name": "Foot Cream", "description": "Foot treatment"}
                ]
            },
            # Sun Care
            {
                "category": "sun_care",
                "subcategory": "protection",
                "products": [
                    {"name": "Sunscreen Lotion", "description": "UV protection"},
                    {"name": "Face Sunscreen", "description": "Facial UV shield"},
                    {"name": "Lip Balm SPF", "description": "Lip sun protection"},
                    {"name": "After Sun Lotion", "description": "Soothing treatment"},
                    {"name": "Tanning Oil", "description": "Bronzing product"}
                ]
            },
            {
                "category": "sun_care",
                "subcategory": "self_tanning",
                "products": [
                    {"name": "Self Tanner", "description": "Sunless bronzer"},
                    {"name": "Gradual Tanner", "description": "Building color"},
                    {"name": "Tanning Drops", "description": "Customizable color"},
                    {"name": "Bronzing Lotion", "description": "Instant glow"},
                    {"name": "Tan Extender", "description": "Color maintenance"}
                ]
            },
            # Men's Grooming
            {
                "category": "mens_grooming",
                "subcategory": "shaving",
                "products": [
                    {"name": "Shaving Cream", "description": "Lather product"},
                    {"name": "Pre-Shave Oil", "description": "Prep treatment"},
                    {"name": "After Shave Balm", "description": "Soothing treatment"},
                    {"name": "Beard Oil", "description": "Facial hair care"},
                    {"name": "Beard Balm", "description": "Styling treatment"}
                ]
            },
            {
                "category": "mens_grooming",
                "subcategory": "skincare",
                "products": [
                    {"name": "Face Wash Men", "description": "Male skin cleanser"},
                    {"name": "Moisturizer Men", "description": "Male hydration"},
                    {"name": "Eye Gel Men", "description": "Under-eye treatment"},
                    {"name": "Anti-Aging Men", "description": "Age defense"},
                    {"name": "Body Wash Men", "description": "Male body cleanser"}
                ]
            },
            # Nail Care
            {
                "category": "nail_care",
                "subcategory": "treatments",
                "products": [
                    {"name": "Cuticle Oil", "description": "Nail bed treatment"},
                    {"name": "Nail Strengthener", "description": "Fortifying treatment"},
                    {"name": "Hand Mask", "description": "Intensive care"},
                    {"name": "Nail Polish Base", "description": "Protective layer"},
                    {"name": "Nail Repair Serum", "description": "Damage treatment"}
                ]
            },
            {
                "category": "nail_care",
                "subcategory": "polish_removers",
                "products": [
                    {"name": "Polish Remover", "description": "Nail cleanser"},
                    {"name": "Remover Pads", "description": "Pre-soaked wipes"},
                    {"name": "Gel Remover", "description": "Gel polish solution"},
                    {"name": "Cuticle Remover", "description": "Softening treatment"},
                    {"name": "Nail Prep Solution", "description": "Pre-polish treatment"}
                ]
            },
            # Spa & Wellness
            {
                "category": "spa_wellness",
                "subcategory": "massage",
                "products": [
                    {"name": "Massage Oil", "description": "Body work medium"},
                    {"name": "Massage Cream", "description": "Non-greasy medium"},
                    {"name": "Aromatherapy Oil", "description": "Scented treatment"},
                    {"name": "Hot Stone Oil", "description": "Thermal treatment"},
                    {"name": "Sports Massage Gel", "description": "Athletic treatment"}
                ]
            },
            {
                "category": "spa_wellness",
                "subcategory": "treatments",
                "products": [
                    {"name": "Body Wrap", "description": "Detox treatment"},
                    {"name": "Salt Scrub", "description": "Mineral exfoliant"},
                    {"name": "Mud Mask", "description": "Purifying treatment"},
                    {"name": "Steam Cream", "description": "Sauna product"},
                    {"name": "Foot Soak", "description": "Relaxing treatment"}
                ]
            }
        ]
        
        # Product templates by category
        self.product_templates = {
            "facial_skincare": [
                "Hemp {} Skincare",
                "CBD-Infused {} Treatment",
                "Hemp Seed {} Formula"
            ],
            "makeup": [
                "Hemp {} Cosmetic",
                "Natural Hemp {} Makeup",
                "Hemp-Enhanced {} Product"
            ],
            "hair_care": [
                "Hemp {} Hair Treatment",
                "Hemp Oil {} Formula",
                "Cannabis Sativa {} Product"
            ],
            "body_care": [
                "Hemp {} Body Care",
                "Hemp-Enriched {} Treatment",
                "Natural Hemp {} Product"
            ],
            "sun_care": [
                "Hemp {} Sun Protection",
                "Hemp-Based {} Formula",
                "Natural Hemp {} Suncare"
            ],
            "mens_grooming": [
                "Hemp {} Men's Product",
                "Men's Hemp {} Treatment",
                "Hemp-Powered {} Grooming"
            ],
            "nail_care": [
                "Hemp {} Nail Treatment",
                "Hemp Oil {} Formula",
                "Hemp-Infused {} Care"
            ],
            "spa_wellness": [
                "Hemp {} Spa Treatment",
                "Therapeutic Hemp {} Product",
                "Hemp Wellness {} Formula"
            ]
        }
        
        # Industry mapping
        self.industry_id = 3  # Health and Beauty

    def load_existing_products(self):
        """Load existing products into cache"""
        print("Loading existing products into cache...")
        offset = 0
        limit = 1000
        
        while True:
            response = requests.get(
                f"{self.supabase_url}/rest/v1/uses_products",
                headers=self.headers,
                params={
                    "select": "name",
                    "order": "id",
                    "offset": offset,
                    "limit": limit
                }
            )
            
            if response.status_code != 200 or not response.json():
                break
                
            batch = response.json()
            for product in batch:
                name = product['name']
                self.existing_products_cache.add(name.lower())
                normalized = self.normalize_name(name)
                self.normalized_cache[normalized.lower()] = name
            
            if len(batch) < limit:
                break
            offset += limit
        
        print(f"Loaded {len(self.existing_products_cache)} existing products")

    def normalize_name(self, name: str) -> str:
        """Normalize product name"""
        name = ' '.join(name.split())
        name = re.sub(r'\b(\w+)\s+\1\b', r'\1', name)
        name = name.replace('Hemp-Based', 'Hemp')
        name = name.replace('Hemp-Derived', 'Hemp')
        name = re.sub(r'(\w+)\s+\1$', r'\1', name)
        return name.strip()

    def check_duplicate_advanced(self, name: str, threshold: float = 0.85) -> bool:
        """Advanced duplicate detection"""
        name_lower = name.lower()
        normalized = self.normalize_name(name)
        normalized_lower = normalized.lower()
        
        if name_lower in self.existing_products_cache:
            return True
        
        if normalized_lower in self.normalized_cache:
            print(f"  ⚠️  Similar to existing: {self.normalized_cache[normalized_lower]}")
            return True
        
        for existing in self.existing_products_cache:
            similarity = SequenceMatcher(None, normalized_lower, existing).ratio()
            if similarity > threshold:
                print(f"  ⚠️  Too similar ({similarity:.2f}) to existing product")
                return True
        
        return False

    def get_plant_part_for_category(self, category: str) -> int:
        """Get appropriate plant part ID for beauty category"""
        mapping = {
            "facial_skincare": 6,    # Flower/Extract for CBD content
            "makeup": 2,             # Seeds for oil
            "hair_care": 2,          # Seeds for nourishing oil
            "body_care": 6,          # Flower/Extract for therapeutic
            "sun_care": 2,           # Seeds for protective oil
            "mens_grooming": 6,      # Flower/Extract for soothing
            "nail_care": 2,          # Seeds for strengthening oil
            "spa_wellness": 6        # Flower/Extract for relaxation
        }
        return mapping.get(category, 6)

    def generate_beauty_description(self, product_name: str, product_desc: str, 
                                   category: str, subcategory: str) -> str:
        """Generate detailed beauty product description"""
        descriptions = {
            "facial_skincare": f"Luxurious {product_name} formulated for {subcategory.replace('_', ' ')} needs. "
                              f"{product_desc}. Hemp seed oil and CBD provide essential fatty acids "
                              f"and antioxidants for radiant, healthy-looking skin.",
            
            "makeup": f"Natural {product_name} designed for {subcategory.replace('_', ' ')} enhancement. "
                     f"{product_desc}. Hemp-infused formulas offer long-wearing coverage "
                     f"while nourishing skin with beneficial cannabinoids and nutrients.",
            
            "hair_care": f"Nourishing {product_name} created for {subcategory.replace('_', ' ')} benefits. "
                        f"{product_desc}. Hemp oil strengthens hair follicles and promotes "
                        f"healthy growth while adding natural shine and manageability.",
            
            "body_care": f"Indulgent {product_name} crafted for {subcategory.replace('_', ' ')} rituals. "
                        f"{product_desc}. Hemp-enriched formulations deeply moisturize "
                        f"and restore skin's natural balance with omega fatty acids.",
            
            "sun_care": f"Protective {product_name} developed for {subcategory.replace('_', ' ')} needs. "
                       f"{product_desc}. Hemp seed oil provides natural SPF boosting "
                       f"while soothing and hydrating sun-exposed skin.",
            
            "mens_grooming": f"Performance {product_name} engineered for {subcategory.replace('_', ' ')} routines. "
                            f"{product_desc}. Hemp-powered formulas address men's specific "
                            f"skincare needs with anti-inflammatory and balancing properties.",
            
            "nail_care": f"Strengthening {product_name} formulated for {subcategory.replace('_', ' ')} care. "
                        f"{product_desc}. Hemp oil penetrates deeply to fortify nails "
                        f"and cuticles with essential proteins and vitamins.",
            
            "spa_wellness": f"Therapeutic {product_name} designed for {subcategory.replace('_', ' ')} experiences. "
                           f"{product_desc}. Hemp and CBD create a luxurious spa treatment "
                           f"that promotes relaxation and whole-body wellness."
        }
        
        return descriptions.get(category, f"Hemp beauty {product_name}. {product_desc}.")

    def generate_beauty_benefits(self, category: str, subcategory: str) -> str:
        """Generate benefits based on beauty application"""
        base_benefits = ["Natural ingredients", "Cruelty-free", "Hemp-enriched formula"]
        
        category_benefits = {
            "facial_skincare": ["Anti-aging properties", "Balances oil production", "Rich in antioxidants"],
            "makeup": ["Long-wearing formula", "Skin-nourishing", "Non-comedogenic"],
            "hair_care": ["Strengthens hair", "Promotes growth", "Adds natural shine"],
            "body_care": ["Deep moisturization", "Soothes irritation", "Improves elasticity"],
            "sun_care": ["Natural SPF boost", "After-sun healing", "Antioxidant protection"],
            "mens_grooming": ["Reduces razor burn", "Controls oil", "Anti-inflammatory"],
            "nail_care": ["Strengthens nails", "Hydrates cuticles", "Promotes growth"],
            "spa_wellness": ["Stress relief", "Muscle relaxation", "Aromatherapy benefits"]
        }
        
        benefits = base_benefits + category_benefits.get(category, ["Premium beauty care"])
        return "{" + ",".join([f'"{b}"' for b in benefits]) + "}"

    def create_beauty_products(self, beauty_group: Dict) -> List[Dict]:
        """Create products from beauty application group"""
        products = []
        category = beauty_group["category"]
        subcategory = beauty_group["subcategory"]
        
        for product in beauty_group["products"]:
            # Generate 1-2 variations per product
            num_variations = random.randint(1, 2)
            
            for i in range(num_variations):
                templates = self.product_templates.get(category, ["Hemp {} Beauty Product"])
                template = random.choice(templates)
                
                # Create product name
                if i == 0:
                    product_name = template.format(product["name"])
                else:
                    modifiers = ["Premium", "Organic", "Luxury", "Professional", "Clinical"]
                    modifier = random.choice(modifiers)
                    product_name = template.format(f"{modifier} {product['name']}")
                
                product_name = self.normalize_name(product_name)
                
                # Check for duplicates
                if self.check_duplicate_advanced(product_name):
                    continue
                
                # Determine plant part
                plant_part_id = self.get_plant_part_for_category(category)
                
                # Calculate confidence score (0.75-0.95 for beauty)
                confidence_score = round(0.75 + (random.random() * 0.2), 2)
                
                product_data = {
                    'name': product_name,
                    'description': self.generate_beauty_description(
                        product["name"], product["description"], category, subcategory
                    ),
                    'industry_sub_category_id': self.industry_id,
                    'plant_part_id': plant_part_id,
                    'confidence_score': confidence_score,
                    'image_url': None,
                    'benefits_advantages': self.generate_beauty_benefits(category, subcategory),
                    'technical_specifications': json.dumps({
                        "category": category,
                        "subcategory": subcategory,
                        "product_type": subcategory.replace('_', ' '),
                        "formulation": product["name"],
                        "key_ingredients": ["Hemp seed oil", "CBD extract", "Natural botanicals"],
                        "certifications": ["Organic", "Vegan", "Cruelty-free"]
                    }),
                    'source_type': 'ai_agent',
                    'source_agent': self.name,
                    'source_url': f"Beauty: {category}/{subcategory}"
                }
                
                products.append(product_data)
                
                # Add to cache immediately
                self.existing_products_cache.add(product_name.lower())
                self.normalized_cache[product_name.lower()] = product_name
        
        return products

    def save_product_api(self, product_data: Dict) -> bool:
        """Save product using REST API"""
        try:
            response = requests.post(
                f"{self.supabase_url}/rest/v1/uses_products",
                headers=self.headers,
                json=product_data
            )
            
            if response.status_code in [200, 201, 204]:
                self.discovered_count += 1
                return True
            else:
                print(f"Failed to save product: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"Error saving product: {e}")
            return False

    def run_discovery(self) -> Dict:
        """Run cosmetics and beauty discovery"""
        start_time = time.time()
        total_saved = 0
        total_duplicates = 0
        total_errors = 0
        
        print(f"\n💄 {self.name} - Starting Discovery Cycle")
        print(f"Beauty categories: {len(self.beauty_applications)}")
        print("=" * 60)
        
        for idx, beauty_group in enumerate(self.beauty_applications, 1):
            category = beauty_group["category"]
            subcategory = beauty_group["subcategory"]
            num_products = len(beauty_group["products"])
            
            print(f"\n[{idx}/{len(self.beauty_applications)}] {category.title().replace('_', ' ')} - {subcategory.title().replace('_', ' ')}")
            print(f"  Products in category: {num_products}")
            
            try:
                # Create products from beauty group
                products = self.create_beauty_products(beauty_group)
                print(f"  Generated {len(products)} product variations")
                
                # Save each product
                saved = 0
                for product in products:
                    if self.save_product_api(product):
                        saved += 1
                        total_saved += 1
                        print(f"  ✅ {product['name']}")
                    else:
                        total_errors += 1
                
                # Track duplicates prevented
                expected = num_products * 1.5  # Average expected variations
                prevented = max(0, expected - len(products))
                total_duplicates += int(prevented)
                
                print(f"  Saved {saved} new products")
                
                # Rate limiting
                time.sleep(0.5)
                
            except Exception as e:
                print(f"  ❌ Error processing category: {e}")
                total_errors += 1
        
        # Summary
        duration = time.time() - start_time
        print(f"\n{'=' * 60}")
        print(f"Discovery Complete!")
        print(f"  Duration: {duration:.1f} seconds")
        print(f"  Categories processed: {len(self.beauty_applications)}")
        print(f"  New products saved: {total_saved}")
        print(f"  Duplicates prevented: ~{total_duplicates}")
        print(f"  Errors: {total_errors}")
        
        return {
            'agent': self.name,
            'duration': duration,
            'categories_processed': len(self.beauty_applications),
            'products_saved': total_saved,
            'duplicates_prevented': total_duplicates,
            'errors': total_errors
        }


if __name__ == "__main__":
    agent = APICosmeticsBeautyAgent()
    results = agent.run_discovery()
    print(f"\nResults: {json.dumps(results, indent=2)}")