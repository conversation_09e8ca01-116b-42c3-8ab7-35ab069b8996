#!/usr/bin/env python3
"""
API-Based Renewable Energy Components Agent - Discovers hemp applications in clean energy
Focuses on solar, wind, battery systems, hydroelectric, and energy storage
"""

import json
import time
import requests
from datetime import datetime
from typing import Dict, List, Optional, Set
import os
import re
import random
from difflib import SequenceMatcher

class APIRenewableEnergyAgent:
    """Renewable energy hemp applications discovery with duplicate prevention"""
    
    def __init__(self):
        self.name = "API Renewable Energy Components Agent"
        self.version = "1.0.0"
        self.discovered_count = 0
        
        # Supabase configuration
        self.supabase_url = "https://ktoqznqmlnxrtvubewyz.supabase.co"
        self.service_role_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt0b3F6bnFtbG54cnR2dWJld3l6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODQ5MTc3NiwiZXhwIjoyMDY0MDY3Nzc2fQ.hxEiOy6xebv7I4CYJEU49oCEXRvmU6iz2l8Kg-NnvTs"
        
        self.headers = {
            "apikey": self.service_role_key,
            "Authorization": f"Bearer {self.service_role_key}",
            "Content-Type": "application/json",
            "Prefer": "return=minimal"
        }
        
        # Cache for existing products
        self.existing_products_cache = set()
        self.normalized_cache = {}
        self.load_existing_products()
        
        # Renewable energy applications database
        self.renewable_applications = [
            # Solar Energy
            {
                "category": "solar_energy",
                "subcategory": "photovoltaic_systems",
                "applications": [
                    {"name": "Solar Panel Backing", "description": "Structural support"},
                    {"name": "PV Module Frame", "description": "Panel enclosure"},
                    {"name": "Junction Box", "description": "Electrical housing"},
                    {"name": "Solar Tracker Component", "description": "Movement system"},
                    {"name": "Mounting Rail", "description": "Installation support"}
                ]
            },
            {
                "category": "solar_energy",
                "subcategory": "solar_thermal",
                "applications": [
                    {"name": "Collector Housing", "description": "Heat collection"},
                    {"name": "Insulation Panel", "description": "Thermal barrier"},
                    {"name": "Storage Tank Lining", "description": "Heat retention"},
                    {"name": "Pipe Insulation", "description": "Heat loss prevention"},
                    {"name": "Reflector Backing", "description": "Light concentration"}
                ]
            },
            # Wind Energy
            {
                "category": "wind_energy",
                "subcategory": "turbine_components",
                "applications": [
                    {"name": "Blade Core Material", "description": "Structural element"},
                    {"name": "Nacelle Cover", "description": "Equipment housing"},
                    {"name": "Tower Section", "description": "Support structure"},
                    {"name": "Spinner Cone", "description": "Aerodynamic cover"},
                    {"name": "Hub Component", "description": "Blade attachment"}
                ]
            },
            {
                "category": "wind_energy",
                "subcategory": "wind_farm_infrastructure",
                "applications": [
                    {"name": "Cable Protection", "description": "Underground conduit"},
                    {"name": "Transformer Housing", "description": "Electrical enclosure"},
                    {"name": "Control Building Panel", "description": "Facility material"},
                    {"name": "Access Platform", "description": "Maintenance deck"},
                    {"name": "Foundation Component", "description": "Base element"}
                ]
            },
            # Battery Storage
            {
                "category": "battery_storage",
                "subcategory": "battery_components",
                "applications": [
                    {"name": "Battery Case", "description": "Cell enclosure"},
                    {"name": "Separator Material", "description": "Cell divider"},
                    {"name": "Terminal Cover", "description": "Connection protection"},
                    {"name": "Thermal Management", "description": "Temperature control"},
                    {"name": "Module Housing", "description": "Battery container"}
                ]
            },
            {
                "category": "battery_storage",
                "subcategory": "energy_storage_systems",
                "applications": [
                    {"name": "ESS Cabinet", "description": "System enclosure"},
                    {"name": "Cooling System Part", "description": "Heat management"},
                    {"name": "Fire Suppression Component", "description": "Safety system"},
                    {"name": "Rack System", "description": "Storage structure"},
                    {"name": "BMS Housing", "description": "Management system"}
                ]
            },
            # Hydroelectric
            {
                "category": "hydroelectric",
                "subcategory": "turbine_systems",
                "applications": [
                    {"name": "Turbine Blade", "description": "Water capture"},
                    {"name": "Penstock Lining", "description": "Water conduit"},
                    {"name": "Generator Housing", "description": "Equipment cover"},
                    {"name": "Draft Tube Component", "description": "Water discharge"},
                    {"name": "Wicket Gate", "description": "Flow control"}
                ]
            },
            {
                "category": "hydroelectric",
                "subcategory": "dam_components",
                "applications": [
                    {"name": "Gate Seal", "description": "Water barrier"},
                    {"name": "Spillway Lining", "description": "Overflow protection"},
                    {"name": "Intake Screen", "description": "Debris filter"},
                    {"name": "Control Room Panel", "description": "Operations center"},
                    {"name": "Access Hatch", "description": "Maintenance entry"}
                ]
            },
            # Geothermal
            {
                "category": "geothermal",
                "subcategory": "plant_components",
                "applications": [
                    {"name": "Well Head Component", "description": "Steam control"},
                    {"name": "Pipe Insulation", "description": "Heat retention"},
                    {"name": "Separator Vessel Part", "description": "Steam/water division"},
                    {"name": "Cooling Tower Fill", "description": "Heat dissipation"},
                    {"name": "Turbine Housing", "description": "Equipment enclosure"}
                ]
            },
            {
                "category": "geothermal",
                "subcategory": "heat_exchange",
                "applications": [
                    {"name": "Heat Exchanger Plate", "description": "Thermal transfer"},
                    {"name": "Expansion Joint", "description": "Thermal movement"},
                    {"name": "Condenser Component", "description": "Steam conversion"},
                    {"name": "Pump Housing", "description": "Fluid movement"},
                    {"name": "Valve Component", "description": "Flow control"}
                ]
            },
            # Grid Integration
            {
                "category": "grid_integration",
                "subcategory": "power_electronics",
                "applications": [
                    {"name": "Inverter Housing", "description": "DC/AC conversion"},
                    {"name": "Transformer Enclosure", "description": "Voltage change"},
                    {"name": "Switchgear Cabinet", "description": "Circuit control"},
                    {"name": "Capacitor Bank Housing", "description": "Power factor"},
                    {"name": "Control Panel", "description": "System interface"}
                ]
            },
            {
                "category": "grid_integration",
                "subcategory": "transmission",
                "applications": [
                    {"name": "Cable Tray", "description": "Wire support"},
                    {"name": "Busbar Cover", "description": "Conductor protection"},
                    {"name": "Substation Component", "description": "Grid connection"},
                    {"name": "Lightning Arrester Base", "description": "Surge protection"},
                    {"name": "Relay Housing", "description": "Protection device"}
                ]
            },
            # Biomass Energy
            {
                "category": "biomass_energy",
                "subcategory": "conversion_equipment",
                "applications": [
                    {"name": "Gasifier Component", "description": "Fuel conversion"},
                    {"name": "Pellet Mill Part", "description": "Fuel production"},
                    {"name": "Digester Lining", "description": "Biogas production"},
                    {"name": "Combustion Chamber", "description": "Energy release"},
                    {"name": "Ash Handling System", "description": "Waste management"}
                ]
            },
            {
                "category": "biomass_energy",
                "subcategory": "fuel_handling",
                "applications": [
                    {"name": "Storage Silo Liner", "description": "Fuel containment"},
                    {"name": "Conveyor Component", "description": "Material transport"},
                    {"name": "Feeder System Part", "description": "Fuel metering"},
                    {"name": "Dryer Component", "description": "Moisture removal"},
                    {"name": "Chipper Housing", "description": "Size reduction"}
                ]
            },
            # Energy Efficiency
            {
                "category": "energy_efficiency",
                "subcategory": "building_systems",
                "applications": [
                    {"name": "Smart Meter Housing", "description": "Usage monitoring"},
                    {"name": "LED Driver Case", "description": "Lighting control"},
                    {"name": "HVAC Component", "description": "Climate control"},
                    {"name": "Building Automation Panel", "description": "System control"},
                    {"name": "Sensor Housing", "description": "Data collection"}
                ]
            },
            {
                "category": "energy_efficiency",
                "subcategory": "industrial_efficiency",
                "applications": [
                    {"name": "VFD Enclosure", "description": "Motor control"},
                    {"name": "Heat Recovery Component", "description": "Waste heat use"},
                    {"name": "Power Monitor Housing", "description": "Energy tracking"},
                    {"name": "Compressed Air Part", "description": "System efficiency"},
                    {"name": "Process Control Panel", "description": "Optimization"}
                ]
            }
        ]
        
        # Product templates by category
        self.product_templates = {
            "solar_energy": [
                "Solar Hemp {} Component",
                "Photovoltaic Hemp {} Material",
                "Solar System Hemp {} Product"
            ],
            "wind_energy": [
                "Wind Power Hemp {} Component",
                "Turbine Hemp {} Material",
                "Wind Energy Hemp {} Product"
            ],
            "battery_storage": [
                "Energy Storage Hemp {} Component",
                "Battery System Hemp {} Material",
                "ESS Hemp {} Product"
            ],
            "hydroelectric": [
                "Hydro Power Hemp {} Component",
                "Water Energy Hemp {} Material",
                "Hydroelectric Hemp {} Product"
            ],
            "geothermal": [
                "Geothermal Hemp {} Component",
                "Earth Energy Hemp {} Material",
                "Thermal Power Hemp {} Product"
            ],
            "grid_integration": [
                "Grid Hemp {} Component",
                "Power System Hemp {} Material",
                "Electrical Hemp {} Product"
            ],
            "biomass_energy": [
                "Biomass Hemp {} Component",
                "Bioenergy Hemp {} Material",
                "Renewable Fuel Hemp {} Product"
            ],
            "energy_efficiency": [
                "Efficiency Hemp {} Component",
                "Energy Saving Hemp {} Material",
                "Conservation Hemp {} Product"
            ]
        }
        
        # Industry mapping
        self.industry_id = 15  # Energy

    def load_existing_products(self):
        """Load existing products into cache"""
        print("Loading existing products into cache...")
        offset = 0
        limit = 1000
        
        while True:
            response = requests.get(
                f"{self.supabase_url}/rest/v1/uses_products",
                headers=self.headers,
                params={
                    "select": "name",
                    "order": "id",
                    "offset": offset,
                    "limit": limit
                }
            )
            
            if response.status_code != 200 or not response.json():
                break
                
            batch = response.json()
            for product in batch:
                name = product['name']
                self.existing_products_cache.add(name.lower())
                normalized = self.normalize_name(name)
                self.normalized_cache[normalized.lower()] = name
            
            if len(batch) < limit:
                break
            offset += limit
        
        print(f"Loaded {len(self.existing_products_cache)} existing products")

    def normalize_name(self, name: str) -> str:
        """Normalize product name"""
        name = ' '.join(name.split())
        name = re.sub(r'\b(\w+)\s+\1\b', r'\1', name)
        name = name.replace('Hemp-Based', 'Hemp')
        name = name.replace('Hemp-Derived', 'Hemp')
        name = re.sub(r'(\w+)\s+\1$', r'\1', name)
        return name.strip()

    def check_duplicate_advanced(self, name: str, threshold: float = 0.85) -> bool:
        """Advanced duplicate detection"""
        name_lower = name.lower()
        normalized = self.normalize_name(name)
        normalized_lower = normalized.lower()
        
        if name_lower in self.existing_products_cache:
            return True
        
        if normalized_lower in self.normalized_cache:
            print(f"  ⚠️  Similar to existing: {self.normalized_cache[normalized_lower]}")
            return True
        
        for existing in self.existing_products_cache:
            similarity = SequenceMatcher(None, normalized_lower, existing).ratio()
            if similarity > threshold:
                print(f"  ⚠️  Too similar ({similarity:.2f}) to existing product")
                return True
        
        return False

    def get_plant_part_for_category(self, category: str) -> int:
        """Get appropriate plant part ID for renewable energy category"""
        mapping = {
            "solar_energy": 1,         # Fiber for lightweight panels
            "wind_energy": 1,          # Fiber for composite blades
            "battery_storage": 6,      # Flower/Extract for potential electrolytes
            "hydroelectric": 3,        # Stem for structural components
            "geothermal": 1,          # Fiber for insulation
            "grid_integration": 1,     # Fiber for electrical insulation
            "biomass_energy": 7,       # Whole plant for fuel
            "energy_efficiency": 1     # Fiber for insulation/components
        }
        return mapping.get(category, 1)

    def generate_renewable_description(self, app_name: str, app_desc: str, 
                                      category: str, subcategory: str) -> str:
        """Generate detailed renewable energy product description"""
        descriptions = {
            "solar_energy": f"Solar-grade {app_name} engineered for {subcategory.replace('_', ' ')} applications. "
                           f"{app_desc}. Hemp materials provide lightweight, durable solutions for "
                           f"photovoltaic systems with excellent UV resistance and thermal stability.",
            
            "wind_energy": f"Wind-certified {app_name} designed for {subcategory.replace('_', ' ')} requirements. "
                          f"{app_desc}. Hemp composites deliver high strength-to-weight ratios essential "
                          f"for wind turbine efficiency while reducing carbon footprint.",
            
            "battery_storage": f"Energy storage {app_name} optimized for {subcategory.replace('_', ' ')} systems. "
                              f"{app_desc}. Hemp-based components enhance battery performance with "
                              f"natural fire resistance and sustainable material properties.",
            
            "hydroelectric": f"Hydro-rated {app_name} built for {subcategory.replace('_', ' ')} operations. "
                            f"{app_desc}. Hemp materials withstand constant water exposure and pressure "
                            f"while maintaining structural integrity in hydroelectric installations.",
            
            "geothermal": f"High-temperature {app_name} developed for {subcategory.replace('_', ' ')} applications. "
                         f"{app_desc}. Hemp components resist extreme heat and corrosive environments "
                         f"found in geothermal energy extraction with minimal degradation.",
            
            "grid_integration": f"Grid-compatible {app_name} engineered for {subcategory.replace('_', ' ')} needs. "
                               f"{app_desc}. Hemp electrical components provide excellent insulation "
                               f"and EMI shielding for reliable power distribution systems.",
            
            "biomass_energy": f"Bioenergy {app_name} designed for {subcategory.replace('_', ' ')} processes. "
                             f"{app_desc}. Hemp materials support efficient biomass conversion "
                             f"while contributing to the circular economy of renewable energy.",
            
            "energy_efficiency": f"Efficiency-focused {app_name} created for {subcategory.replace('_', ' ')} improvements. "
                                f"{app_desc}. Hemp-based efficiency products reduce energy consumption "
                                f"through superior insulation and sustainable material properties."
        }
        
        return descriptions.get(category, f"Renewable energy {app_name}. {app_desc}.")

    def generate_renewable_benefits(self, category: str, subcategory: str) -> str:
        """Generate benefits based on renewable energy application"""
        base_benefits = ["Carbon negative material", "Renewable resource", "Energy efficient"]
        
        category_benefits = {
            "solar_energy": ["UV resistant", "Lightweight design", "25+ year lifespan"],
            "wind_energy": ["High tensile strength", "Fatigue resistant", "Vibration dampening"],
            "battery_storage": ["Fire retardant", "Thermal management", "Non-toxic materials"],
            "hydroelectric": ["Water resistant", "Corrosion proof", "Low maintenance"],
            "geothermal": ["Heat resistant", "Chemical inert", "Pressure rated"],
            "grid_integration": ["Electrical insulation", "EMI shielding", "Weather resistant"],
            "biomass_energy": ["High energy content", "Low emissions", "Sustainable feedstock"],
            "energy_efficiency": ["Superior R-value", "Moisture regulation", "Long-term savings"]
        }
        
        benefits = base_benefits + category_benefits.get(category, ["Clean energy compatible"])
        return "{" + ",".join([f'"{b}"' for b in benefits]) + "}"

    def create_renewable_products(self, renewable_group: Dict) -> List[Dict]:
        """Create products from renewable energy application group"""
        products = []
        category = renewable_group["category"]
        subcategory = renewable_group["subcategory"]
        
        for app in renewable_group["applications"]:
            # Generate 1-2 variations per application
            num_variations = random.randint(1, 2)
            
            for i in range(num_variations):
                templates = self.product_templates.get(category, ["Hemp {} Renewable Energy Product"])
                template = random.choice(templates)
                
                # Create product name
                if i == 0:
                    product_name = template.format(app["name"])
                else:
                    modifiers = ["High-Performance", "Grid-Ready", "Commercial", "Industrial", "Advanced"]
                    modifier = random.choice(modifiers)
                    product_name = template.format(f"{modifier} {app['name']}")
                
                product_name = self.normalize_name(product_name)
                
                # Check for duplicates
                if self.check_duplicate_advanced(product_name):
                    continue
                
                # Determine plant part
                plant_part_id = self.get_plant_part_for_category(category)
                
                # Calculate confidence score (0.7-0.95 for renewable energy)
                confidence_score = round(0.7 + (random.random() * 0.25), 2)
                
                product_data = {
                    'name': product_name,
                    'description': self.generate_renewable_description(
                        app["name"], app["description"], category, subcategory
                    ),
                    'industry_sub_category_id': self.industry_id,
                    'plant_part_id': plant_part_id,
                    'confidence_score': confidence_score,
                    'image_url': None,
                    'benefits_advantages': self.generate_renewable_benefits(category, subcategory),
                    'technical_specifications': json.dumps({
                        "category": category,
                        "subcategory": subcategory,
                        "energy_application": subcategory.replace('_', ' '),
                        "component_type": app["name"],
                        "certifications": ["IEC 61215", "UL Listed", "CE Marked"],
                        "renewable_sector": category.replace('_', ' ').title()
                    }),
                    'source_type': 'ai_agent',
                    'source_agent': self.name,
                    'source_url': f"Renewable: {category}/{subcategory}"
                }
                
                products.append(product_data)
                
                # Add to cache immediately
                self.existing_products_cache.add(product_name.lower())
                self.normalized_cache[product_name.lower()] = product_name
        
        return products

    def save_product_api(self, product_data: Dict) -> bool:
        """Save product using REST API"""
        try:
            response = requests.post(
                f"{self.supabase_url}/rest/v1/uses_products",
                headers=self.headers,
                json=product_data
            )
            
            if response.status_code in [200, 201, 204]:
                self.discovered_count += 1
                return True
            else:
                print(f"Failed to save product: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"Error saving product: {e}")
            return False

    def run_discovery(self) -> Dict:
        """Run renewable energy discovery"""
        start_time = time.time()
        total_saved = 0
        total_duplicates = 0
        total_errors = 0
        
        print(f"\n⚡ {self.name} - Starting Discovery Cycle")
        print(f"Renewable energy categories: {len(self.renewable_applications)}")
        print("=" * 60)
        
        for idx, renewable_group in enumerate(self.renewable_applications, 1):
            category = renewable_group["category"]
            subcategory = renewable_group["subcategory"]
            num_apps = len(renewable_group["applications"])
            
            print(f"\n[{idx}/{len(self.renewable_applications)}] {category.title().replace('_', ' ')} - {subcategory.title().replace('_', ' ')}")
            print(f"  Applications: {num_apps}")
            
            try:
                # Create products from renewable group
                products = self.create_renewable_products(renewable_group)
                print(f"  Generated {len(products)} product variations")
                
                # Save each product
                saved = 0
                for product in products:
                    if self.save_product_api(product):
                        saved += 1
                        total_saved += 1
                        print(f"  ✅ {product['name']}")
                    else:
                        total_errors += 1
                
                # Track duplicates prevented
                expected = num_apps * 1.5  # Average expected variations
                prevented = max(0, expected - len(products))
                total_duplicates += int(prevented)
                
                print(f"  Saved {saved} new products")
                
                # Rate limiting
                time.sleep(0.5)
                
            except Exception as e:
                print(f"  ❌ Error processing category: {e}")
                total_errors += 1
        
        # Summary
        duration = time.time() - start_time
        print(f"\n{'=' * 60}")
        print(f"Discovery Complete!")
        print(f"  Duration: {duration:.1f} seconds")
        print(f"  Categories processed: {len(self.renewable_applications)}")
        print(f"  New products saved: {total_saved}")
        print(f"  Duplicates prevented: ~{total_duplicates}")
        print(f"  Errors: {total_errors}")
        
        return {
            'agent': self.name,
            'duration': duration,
            'categories_processed': len(self.renewable_applications),
            'products_saved': total_saved,
            'duplicates_prevented': total_duplicates,
            'errors': total_errors
        }


if __name__ == "__main__":
    agent = APIRenewableEnergyAgent()
    results = agent.run_discovery()
    print(f"\nResults: {json.dumps(results, indent=2)}")