#!/usr/bin/env python3
"""
Space Technology Agent - Phase 3 Emerging Technology
Discovers hemp applications in space exploration and extreme environments
"""

import random
import requests
import json
from typing import List, Dict, Tuple
from datetime import datetime
import sys
import os

# Configuration
SUPABASE_URL = "https://ktoqznqmlnxrtvubewyz.supabase.co"
SUPABASE_SERVICE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt0b3F6bnFtbG54cnR2dWJld3l6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODQ5MTc3NiwiZXhwIjoyMDY0MDY3Nzc2fQ.hxEiOy6xebv7I4CYJEU49oCEXRvmU6iz2l8Kg-NnvTs"

class SpaceTechnologyAgent:
    def __init__(self):
        self.name = "Space Technology Agent"
        self.version = "1.0.0"
        self.headers = {
            "apikey": SUPABASE_SERVICE_KEY,
            "Authorization": f"Bearer {SUPABASE_SERVICE_KEY}",
            "Content-Type": "application/json"
        }
        
        # Space technology categories
        self.categories = {
            "spacecraft_materials": {
                "name": "Spacecraft Structural Materials",
                "applications": [
                    "Heat shield components", "Radiation shielding",
                    "Micrometeorite protection", "Thermal insulation",
                    "Structural composites", "Ablative materials"
                ]
            },
            "life_support": {
                "name": "Life Support Systems",
                "applications": [
                    "Air filtration systems", "Water recycling filters",
                    "CO2 scrubbers", "Oxygen generation substrates",
                    "Waste processing materials", "Bioregenerative systems"
                ]
            },
            "space_habitats": {
                "name": "Space Habitat Construction",
                "applications": [
                    "Inflatable habitat materials", "3D printed structures",
                    "Regolith composites", "Modular construction panels",
                    "Airlock seals", "Pressure vessel linings"
                ]
            },
            "eva_equipment": {
                "name": "EVA and Spacesuits",
                "applications": [
                    "Spacesuit fabrics", "EVA tool materials",
                    "Flexible joint systems", "Thermal control layers",
                    "Dust-resistant coatings", "Mobility enhancers"
                ]
            },
            "propulsion": {
                "name": "Propulsion Technologies",
                "applications": [
                    "Solid rocket binders", "Hybrid fuel components",
                    "Ion thruster materials", "Solar sail fabrics",
                    "Propellant tanks", "Nozzle linings"
                ]
            },
            "power_systems": {
                "name": "Space Power Systems",
                "applications": [
                    "Solar panel substrates", "Battery components",
                    "Fuel cell membranes", "RTG insulation",
                    "Power cable shielding", "Energy storage materials"
                ]
            },
            "communication": {
                "name": "Communication Systems",
                "applications": [
                    "Antenna materials", "Signal cables",
                    "RF shielding", "Optical fiber alternatives",
                    "Satellite bus components", "Ground station materials"
                ]
            },
            "planetary_exploration": {
                "name": "Planetary Exploration",
                "applications": [
                    "Rover components", "Drilling equipment",
                    "Sample containers", "Terrain anchors",
                    "Dust mitigation systems", "Exploration tools"
                ]
            },
            "microgravity_research": {
                "name": "Microgravity Research",
                "applications": [
                    "Experiment containers", "Fluid handling systems",
                    "Crystal growth chambers", "Protein crystallization",
                    "Cell culture platforms", "Material science rigs"
                ]
            },
            "space_manufacturing": {
                "name": "In-Space Manufacturing",
                "applications": [
                    "3D printing feedstock", "Welding materials",
                    "Assembly tools", "Quality control devices",
                    "Robotic components", "Manufacturing platforms"
                ]
            },
            "satellite_technology": {
                "name": "Satellite Components",
                "applications": [
                    "CubeSat structures", "Deployable systems",
                    "Thermal blankets", "Attitude control materials",
                    "Solar array deployment", "Payload housings"
                ]
            },
            "space_agriculture": {
                "name": "Space Agriculture",
                "applications": [
                    "Growth substrates", "Hydroponic systems",
                    "Plant support structures", "Nutrient delivery",
                    "Light diffusion materials", "Root zone containers"
                ]
            },
            "radiation_protection": {
                "name": "Radiation Protection",
                "applications": [
                    "Personal dosimeters", "Shielding composites",
                    "Storm shelter materials", "Electronics protection",
                    "Medical countermeasures", "Detection systems"
                ]
            },
            "extreme_environment": {
                "name": "Extreme Environment Tech",
                "applications": [
                    "Cryogenic insulation", "High-temp materials",
                    "Vacuum-compatible seals", "Pressure suits",
                    "Corrosion resistance", "Thermal cycling materials"
                ]
            },
            "space_medicine": {
                "name": "Space Medicine",
                "applications": [
                    "Medical device housings", "Surgical tools",
                    "Diagnostic equipment", "Drug delivery systems",
                    "Wound care materials", "Exercise equipment"
                ]
            }
        }
        
    def generate_products(self) -> List[Dict]:
        """Generate space technology hemp products"""
        products = []
        product_id = 80000  # Starting ID for space tech products
        
        for category_key, category_data in self.categories.items():
            for i, application in enumerate(category_data['applications']):
                # Generate multiple variants for each application
                variants = self._generate_variants(application, category_data['name'])
                
                for variant in variants:
                    product = {
                        "id": product_id,
                        "name": variant['name'],
                        "description": variant['description'],
                        "category": "Space Technology",
                        "sub_category": category_data['name'],
                        "application": application,
                        "plant_part": self._select_plant_part(application),
                        "confidence_score": round(random.uniform(0.65, 0.90), 2),
                        "market_readiness": random.choice(["research", "prototype", "pilot"]),
                        "innovation_level": random.choice(["breakthrough", "advanced", "experimental"]),
                        "space_features": variant['features'],
                        "created_at": datetime.now().isoformat(),
                        "agent": self.name,
                        "agent_version": self.version
                    }
                    products.append(product)
                    product_id += 1
                    
        return products
    
    def _generate_variants(self, application: str, category: str) -> List[Dict]:
        """Generate variants for each application"""
        variants = []
        
        # Base variant
        base_name = f"Space-Grade Hemp {application}"
        variants.append({
            "name": base_name,
            "description": self._generate_description(application, category, "standard"),
            "features": self._generate_features(application, "standard")
        })
        
        # Advanced variant
        if random.random() > 0.4:
            advanced_name = f"Advanced Space {application} System"
            variants.append({
                "name": advanced_name,
                "description": self._generate_description(application, category, "advanced"),
                "features": self._generate_features(application, "advanced")
            })
        
        # Zero-G variant
        if random.random() > 0.5:
            zerog_name = f"Zero-G Optimized {application}"
            variants.append({
                "name": zerog_name,
                "description": self._generate_description(application, category, "zerog"),
                "features": self._generate_features(application, "zerog")
            })
        
        return variants
    
    def _generate_description(self, application: str, category: str, variant_type: str) -> str:
        """Generate detailed description"""
        base_descriptions = {
            "standard": f"Space-qualified hemp material engineered for {application.lower()} in {category.lower()}. Meets rigorous space environment requirements.",
            "advanced": f"Next-generation space technology for {application.lower()} utilizing advanced {category.lower()} principles. Designed for extreme space conditions.",
            "zerog": f"Zero-gravity optimized hemp solution for {application.lower()}. Specifically engineered for microgravity {category.lower()} applications."
        }
        
        technical_details = [
            "Withstands extreme temperature variations",
            "Resistant to cosmic radiation",
            "Outgassing compliant for space use",
            "Micrometeorite impact resistant",
            "Functions in hard vacuum conditions",
            "Maintains integrity in zero gravity",
            "Exceeds NASA material standards"
        ]
        
        return f"{base_descriptions[variant_type]} {random.choice(technical_details)}."
    
    def _generate_features(self, application: str, variant_type: str) -> List[str]:
        """Generate space technology specific features"""
        base_features = [
            "Space-qualified",
            "Radiation resistant",
            "Vacuum compatible",
            "Low outgassing",
            "Thermal stable"
        ]
        
        advanced_features = [
            "ISS certified materials",
            "Deep space ready",
            "Multi-planetary use",
            "Self-repairing in space",
            "AI-monitored performance"
        ]
        
        features = base_features.copy()
        if variant_type in ["advanced", "zerog"]:
            features.extend(random.sample(advanced_features, 2))
            
        return features
    
    def _select_plant_part(self, application: str) -> str:
        """Select appropriate plant part based on application"""
        if any(word in application.lower() for word in ["fabric", "textile", "fiber", "composite", "insulation"]):
            return "Hemp Bast (Fiber)"
        elif any(word in application.lower() for word in ["structural", "panel", "shield"]):
            return "Hemp Hurd (Shivs)"
        elif any(word in application.lower() for word in ["filter", "substrate", "growth"]):
            return "Hemp Leaves"
        elif any(word in application.lower() for word in ["fuel", "energy", "nutrition"]):
            return "Hemp Seed"
        else:
            return random.choice(["Hemp Bast (Fiber)", "Hemp Hurd (Shivs)", "Hemp Leaves"])
    
    def _check_duplicate(self, product_name: str) -> bool:
        """Check if product already exists"""
        response = requests.get(
            f"{SUPABASE_URL}/rest/v1/uses_products",
            headers=self.headers,
            params={
                "name": f"eq.{product_name}",
                "select": "id"
            }
        )
        return len(response.json()) > 0 if response.status_code == 200 else False
    
    def save_to_database(self, products: List[Dict]) -> Tuple[int, int]:
        """Save products to database"""
        saved = 0
        failed = 0
        
        for product in products:
            if not self._check_duplicate(product['name']):
                # Prepare database record
                db_record = {
                    "name": product['name'],
                    "description": product['description'],
                    "benefits_advantages": product['space_features'],
                    "technical_specifications": {
                        "category": product['category'],
                        "sub_category": product['sub_category'],
                        "application": product['application'],
                        "market_readiness": product['market_readiness'],
                        "innovation_level": product['innovation_level']
                    },
                    "sustainability_aspects": ["space debris reduction", "sustainable space exploration", "eco-friendly aerospace"],
                    "commercialization_stage": product['market_readiness'],
                    "manufacturing_processes_summary": "Advanced aerospace manufacturing with space-qualification testing and certification",
                    "keywords": ["space", "aerospace", "hemp", product['sub_category'].lower(), product['application'].lower()],
                    "industry_sub_category_id": 11,  # Aerospace industry
                    "created_at": product['created_at'],
                    "source_agent": self.name,
                    "confidence_score": product['confidence_score'],
                    "source_type": "ai_agent"
                }
                
                # Get plant part ID
                plant_part_response = requests.get(
                    f"{SUPABASE_URL}/rest/v1/plant_parts",
                    headers=self.headers,
                    params={"name": f"eq.{product['plant_part']}", "select": "id"}
                )
                
                if plant_part_response.status_code == 200 and plant_part_response.json():
                    db_record["plant_part_id"] = plant_part_response.json()[0]['id']
                
                # Insert product
                response = requests.post(
                    f"{SUPABASE_URL}/rest/v1/uses_products",
                    headers=self.headers,
                    json=db_record
                )
                
                if response.status_code == 201:
                    saved += 1
                    print(f"✅ Saved: {product['name']}")
                else:
                    failed += 1
                    print(f"❌ Failed: {product['name']} - {response.text}")
            else:
                print(f"⏭️ Skipped (duplicate): {product['name']}")
                
        return saved, failed

def main():
    print("🚀 Space Technology Agent - Phase 3")
    print("=" * 60)
    
    agent = SpaceTechnologyAgent()
    
    # Generate products
    print("\n📊 Generating space technology hemp products...")
    products = agent.generate_products()
    print(f"Generated {len(products)} potential products")
    
    # Save to database
    print("\n💾 Saving to database...")
    saved, failed = agent.save_to_database(products)
    
    print("\n📈 Results:")
    print(f"✅ Successfully saved: {saved}")
    print(f"❌ Failed to save: {failed}")
    print(f"📊 Total processed: {len(products)}")
    
if __name__ == "__main__":
    main()