#!/usr/bin/env python3
"""
Phase 3 Coordinator - Manages all emerging technology agents
"""

import subprocess
import time
import os
import sys
from datetime import datetime
import requests

# Configuration
SUPABASE_URL = "https://ktoqznqmlnxrtvubewyz.supabase.co"
SUPABASE_SERVICE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt0b3F6bnFtbG54cnR2dWJld3l6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODQ5MTc3NiwiZXhwIjoyMDY0MDY3Nzc2fQ.hxEiOy6xebv7I4CYJEU49oCEXRvmU6iz2l8Kg-NnvTs"

class Phase3Coordinator:
    def __init__(self):
        self.agents = [
            {
                "name": "AI/ML Applications Agent",
                "script": "ai_ml_applications_agent_api.py",
                "status": "ready"
            },
            {
                "name": "Bioengineering Agent", 
                "script": "bioengineering_agent_api.py",
                "status": "ready"
            },
            {
                "name": "Quantum Materials Agent",
                "script": "quantum_materials_agent_api.py",
                "status": "pending"
            },
            {
                "name": "Space Technology Agent",
                "script": "space_technology_agent_api.py", 
                "status": "pending"
            },
            {
                "name": "Climate Tech Agent",
                "script": "climate_tech_agent_api.py",
                "status": "pending"
            }
        ]
        
        self.headers = {
            "apikey": SUPABASE_SERVICE_KEY,
            "Authorization": f"Bearer {SUPABASE_SERVICE_KEY}",
            "Content-Type": "application/json"
        }
        
    def get_product_count(self):
        """Get current product count from database"""
        try:
            response = requests.get(
                f"{SUPABASE_URL}/rest/v1/uses_products",
                headers=self.headers,
                params={"select": "count"}
            )
            if response.status_code == 200:
                data = response.json()
                return data[0]['count'] if data else 0
        except:
            return 0
            
    def run_agent(self, agent):
        """Run a single agent"""
        script_path = os.path.join(os.path.dirname(__file__), "specialized", agent["script"])
        
        if not os.path.exists(script_path):
            print(f"❌ Agent script not found: {script_path}")
            return False
            
        print(f"\n🚀 Running {agent['name']}...")
        print("=" * 60)
        
        try:
            # Run the agent
            result = subprocess.run(
                [sys.executable, script_path],
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                print(f"✅ {agent['name']} completed successfully")
                print(result.stdout)
                return True
            else:
                print(f"❌ {agent['name']} failed")
                print(result.stderr)
                return False
                
        except Exception as e:
            print(f"❌ Error running {agent['name']}: {str(e)}")
            return False
            
    def run_all_agents(self, delay_between=30):
        """Run all ready agents with delay between each"""
        print("🎯 PHASE 3 COORDINATOR - Emerging Technologies")
        print("=" * 60)
        print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Get initial product count
        initial_count = self.get_product_count()
        print(f"Initial product count: {initial_count}")
        
        # Run each agent
        successful = 0
        failed = 0
        
        for agent in self.agents:
            if agent["status"] == "ready":
                success = self.run_agent(agent)
                
                if success:
                    successful += 1
                else:
                    failed += 1
                    
                # Delay between agents
                if delay_between > 0:
                    print(f"\n⏱️ Waiting {delay_between} seconds before next agent...")
                    time.sleep(delay_between)
            else:
                print(f"\n⏭️ Skipping {agent['name']} (status: {agent['status']})")
        
        # Get final product count
        final_count = self.get_product_count()
        products_added = final_count - initial_count
        
        # Summary
        print("\n" + "=" * 60)
        print("📊 PHASE 3 SUMMARY")
        print("=" * 60)
        print(f"Agents run successfully: {successful}")
        print(f"Agents failed: {failed}")
        print(f"Products added: {products_added}")
        print(f"Total products now: {final_count}")
        print(f"Progress to 15K goal: {final_count/15000*100:.1f}%")
        print(f"Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
    def check_agent_status(self):
        """Check which agents are ready to run"""
        print("📋 AGENT STATUS CHECK")
        print("=" * 60)
        
        for agent in self.agents:
            script_path = os.path.join(os.path.dirname(__file__), "specialized", agent["script"])
            exists = os.path.exists(script_path)
            
            status_icon = "✅" if exists else "❌"
            print(f"{status_icon} {agent['name']}: {'Ready' if exists else 'Not implemented'}")
            
def main():
    coordinator = Phase3Coordinator()
    
    # Check if we're just checking status
    if len(sys.argv) > 1 and sys.argv[1] == "--status":
        coordinator.check_agent_status()
    else:
        # Run all agents
        coordinator.run_all_agents(delay_between=30)
        
if __name__ == "__main__":
    main()