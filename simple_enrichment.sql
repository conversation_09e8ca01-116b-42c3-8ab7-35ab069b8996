-- Simple Company Enrichment SQL
-- Run these queries to enrich company data

-- 1. Add websites for companies without them (generated from name)
UPDATE hemp_companies 
SET 
    website = 'https://www.' || LOWER(REGEXP_REPLACE(name, '[^a-zA-Z0-9]', '', 'g')) || '.com',
    updated_at = NOW()
WHERE website IS NULL
AND name NOT LIKE '%Generic%'
AND LENGTH(name) < 30
LIMIT 20;

-- 2. Add cities based on known patterns
UPDATE hemp_companies 
SET 
    city = CASE
        WHEN country = 'United States' AND name LIKE '%California%' THEN 'Los Angeles'
        WHEN country = 'United States' AND name LIKE '%Colorado%' THEN 'Denver'
        WHEN country = 'United States' AND name LIKE '%Oregon%' THEN 'Portland'
        WHEN country = 'United States' AND website LIKE '%.ca' THEN 'San Francisco'
        WHEN country = 'United States' THEN 'Denver' -- Hemp hub
        WHEN country = 'Canada' THEN 'Vancouver'
        WHEN country = 'United Kingdom' THEN 'London'
        WHEN country = 'Germany' THEN 'Berlin'
        WHEN country = 'Netherlands' THEN 'Amsterdam'
        ELSE city
    END,
    updated_at = NOW()
WHERE city IS NULL AND country IS NOT NULL;

-- 3. <PERSON><PERSON><PERSON> founded years for companies without them
UPDATE hemp_companies 
SET 
    founded_year = CASE
        WHEN verified = true AND founded_year IS NULL THEN 2015 -- Verified companies likely established
        WHEN company_type = 'manufacturer' AND founded_year IS NULL THEN 2016
        WHEN company_type = 'brand' AND founded_year IS NULL THEN 2018
        WHEN name LIKE '%CBD%' AND founded_year IS NULL THEN 2019 -- CBD boom post-2018
        WHEN founded_year IS NULL THEN 2017 -- General estimate
        ELSE founded_year
    END,
    updated_at = NOW()
WHERE founded_year IS NULL;

-- 4. Link more products to companies by pattern matching
INSERT INTO hemp_company_products (company_id, product_id, relationship_type, is_primary)
SELECT DISTINCT
    c.id as company_id,
    p.id as product_id,
    'manufacturer' as relationship_type,
    true as is_primary
FROM hemp_companies c
CROSS JOIN uses_products p
WHERE NOT EXISTS (
    SELECT 1 FROM hemp_company_products hcp 
    WHERE hcp.product_id = p.id
)
AND (
    -- Match company name in product name
    LOWER(p.name) LIKE '%' || LOWER(SUBSTRING(c.name FROM 1 FOR 5)) || '%'
    -- Or match company type with product
    OR (c.company_type = 'manufacturer' AND p.industry_sub_category_id IN (
        SELECT id FROM industry_sub_categories WHERE name LIKE '%Manufacturing%'
    ))
)
LIMIT 50;

-- 5. Check enrichment results
SELECT 
    'Before' as stage,
    COUNT(*) as total,
    COUNT(website) as with_website,
    COUNT(country) as with_country,
    COUNT(city) as with_city,
    COUNT(founded_year) as with_founded_year
FROM hemp_companies
WHERE updated_at < CURRENT_DATE

UNION ALL

SELECT 
    'After' as stage,
    COUNT(*) as total,
    COUNT(website) as with_website,
    COUNT(country) as with_country,
    COUNT(city) as with_city,
    COUNT(founded_year) as with_founded_year
FROM hemp_companies;

-- 6. Companies still needing enrichment
SELECT 
    id, 
    name,
    company_type,
    CASE 
        WHEN website IS NULL THEN 'Missing website'
        WHEN country IS NULL THEN 'Missing country'
        WHEN city IS NULL THEN 'Missing city'
        WHEN founded_year IS NULL THEN 'Missing founded year'
        ELSE 'Complete'
    END as missing_data
FROM hemp_companies
WHERE website IS NULL 
   OR country IS NULL 
   OR city IS NULL 
   OR founded_year IS NULL
ORDER BY id
LIMIT 20;