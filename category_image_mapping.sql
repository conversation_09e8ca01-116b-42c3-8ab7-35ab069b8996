-- Category Image Mapping Queries
-- Generated: 2025-07-26 01:29:20


-- Create category image mapping table
CREATE TABLE IF NOT EXISTS category_image_mappings (
    id SERIAL PRIMARY KEY,
    category VARCHAR(100),
    subcategory VARCHAR(100),
    image_url VARCHAR(500),
    keywords TEXT[],
    products_mapped INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);


-- Map food_beverages products to category images
UPDATE uses_products p
SET image_url = 'https://your-bucket.supabase.co/storage/v1/object/public/product-images/categories/hemp_food_beverages_default.jpg'
WHERE (p.image_url IS NULL OR p.image_url = '')
  AND (LOWER(p.name) LIKE '%seed%' OR LOWER(p.description) LIKE '%seed%' OR LOWER(p.name) LIKE '%oil%' OR LOWER(p.description) LIKE '%oil%' OR LOWER(p.name) LIKE '%protein%' OR LOWER(p.description) LIKE '%protein%' OR LOWER(p.name) LIKE '%flour%' OR LOWER(p.description) LIKE '%flour%' OR LOWER(p.name) LIKE '%milk%' OR LOWER(p.description) LIKE '%milk%' OR LOWER(p.name) LIKE '%tea%' OR LOWER(p.description) LIKE '%tea%' OR LOWER(p.name) LIKE '%coffee%' OR LOWER(p.description) LIKE '%coffee%' OR LOWER(p.name) LIKE '%granola%' OR LOWER(p.description) LIKE '%granola%' OR LOWER(p.name) LIKE '%snack%' OR LOWER(p.description) LIKE '%snack%' OR LOWER(p.name) LIKE '%edible%' OR LOWER(p.description) LIKE '%edible%')
  AND NOT EXISTS (
    SELECT 1 FROM uses_products p2 
    WHERE p2.id = p.id 
    AND p2.image_url LIKE '%/categories/%'
  );


-- Map health_wellness products to category images
UPDATE uses_products p
SET image_url = 'https://your-bucket.supabase.co/storage/v1/object/public/product-images/categories/hemp_health_wellness_default.jpg'
WHERE (p.image_url IS NULL OR p.image_url = '')
  AND (LOWER(p.name) LIKE '%cbd%' OR LOWER(p.description) LIKE '%cbd%' OR LOWER(p.name) LIKE '%cannabinoid%' OR LOWER(p.description) LIKE '%cannabinoid%' OR LOWER(p.name) LIKE '%tincture%' OR LOWER(p.description) LIKE '%tincture%' OR LOWER(p.name) LIKE '%capsule%' OR LOWER(p.description) LIKE '%capsule%' OR LOWER(p.name) LIKE '%balm%' OR LOWER(p.description) LIKE '%balm%' OR LOWER(p.name) LIKE '%cream%' OR LOWER(p.description) LIKE '%cream%' OR LOWER(p.name) LIKE '%supplement%' OR LOWER(p.description) LIKE '%supplement%')
  AND NOT EXISTS (
    SELECT 1 FROM uses_products p2 
    WHERE p2.id = p.id 
    AND p2.image_url LIKE '%/categories/%'
  );


-- Map textiles_fashion products to category images
UPDATE uses_products p
SET image_url = 'https://your-bucket.supabase.co/storage/v1/object/public/product-images/categories/hemp_textiles_fashion_default.jpg'
WHERE (p.image_url IS NULL OR p.image_url = '')
  AND (LOWER(p.name) LIKE '%fabric%' OR LOWER(p.description) LIKE '%fabric%' OR LOWER(p.name) LIKE '%textile%' OR LOWER(p.description) LIKE '%textile%' OR LOWER(p.name) LIKE '%clothing%' OR LOWER(p.description) LIKE '%clothing%' OR LOWER(p.name) LIKE '%fiber%' OR LOWER(p.description) LIKE '%fiber%' OR LOWER(p.name) LIKE '%yarn%' OR LOWER(p.description) LIKE '%yarn%' OR LOWER(p.name) LIKE '%rope%' OR LOWER(p.description) LIKE '%rope%' OR LOWER(p.name) LIKE '%canvas%' OR LOWER(p.description) LIKE '%canvas%' OR LOWER(p.name) LIKE '%denim%' OR LOWER(p.description) LIKE '%denim%')
  AND NOT EXISTS (
    SELECT 1 FROM uses_products p2 
    WHERE p2.id = p.id 
    AND p2.image_url LIKE '%/categories/%'
  );


-- Map building_construction products to category images
UPDATE uses_products p
SET image_url = 'https://your-bucket.supabase.co/storage/v1/object/public/product-images/categories/hemp_building_construction_default.jpg'
WHERE (p.image_url IS NULL OR p.image_url = '')
  AND (LOWER(p.name) LIKE '%hempcrete%' OR LOWER(p.description) LIKE '%hempcrete%' OR LOWER(p.name) LIKE '%insulation%' OR LOWER(p.description) LIKE '%insulation%' OR LOWER(p.name) LIKE '%panel%' OR LOWER(p.description) LIKE '%panel%' OR LOWER(p.name) LIKE '%board%' OR LOWER(p.description) LIKE '%board%' OR LOWER(p.name) LIKE '%construction%' OR LOWER(p.description) LIKE '%construction%' OR LOWER(p.name) LIKE '%building%' OR LOWER(p.description) LIKE '%building%')
  AND NOT EXISTS (
    SELECT 1 FROM uses_products p2 
    WHERE p2.id = p.id 
    AND p2.image_url LIKE '%/categories/%'
  );


-- Map cosmetics_personal products to category images
UPDATE uses_products p
SET image_url = 'https://your-bucket.supabase.co/storage/v1/object/public/product-images/categories/hemp_cosmetics_personal_default.jpg'
WHERE (p.image_url IS NULL OR p.image_url = '')
  AND (LOWER(p.name) LIKE '%cosmetic%' OR LOWER(p.description) LIKE '%cosmetic%' OR LOWER(p.name) LIKE '%beauty%' OR LOWER(p.description) LIKE '%beauty%' OR LOWER(p.name) LIKE '%soap%' OR LOWER(p.description) LIKE '%soap%' OR LOWER(p.name) LIKE '%shampoo%' OR LOWER(p.description) LIKE '%shampoo%' OR LOWER(p.name) LIKE '%lotion%' OR LOWER(p.description) LIKE '%lotion%' OR LOWER(p.name) LIKE '%serum%' OR LOWER(p.description) LIKE '%serum%' OR LOWER(p.name) LIKE '%skincare%' OR LOWER(p.description) LIKE '%skincare%')
  AND NOT EXISTS (
    SELECT 1 FROM uses_products p2 
    WHERE p2.id = p.id 
    AND p2.image_url LIKE '%/categories/%'
  );


-- Map industrial_materials products to category images
UPDATE uses_products p
SET image_url = 'https://your-bucket.supabase.co/storage/v1/object/public/product-images/categories/hemp_industrial_materials_default.jpg'
WHERE (p.image_url IS NULL OR p.image_url = '')
  AND (LOWER(p.name) LIKE '%plastic%' OR LOWER(p.description) LIKE '%plastic%' OR LOWER(p.name) LIKE '%composite%' OR LOWER(p.description) LIKE '%composite%' OR LOWER(p.name) LIKE '%biodegradable%' OR LOWER(p.description) LIKE '%biodegradable%' OR LOWER(p.name) LIKE '%packaging%' OR LOWER(p.description) LIKE '%packaging%' OR LOWER(p.name) LIKE '%material%' OR LOWER(p.description) LIKE '%material%')
  AND NOT EXISTS (
    SELECT 1 FROM uses_products p2 
    WHERE p2.id = p.id 
    AND p2.image_url LIKE '%/categories/%'
  );

