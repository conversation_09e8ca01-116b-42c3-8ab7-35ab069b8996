#!/usr/bin/env python3
"""
Fast batch migration of images to Supabase Storage
Uploads in parallel for speed
"""
import os
import sys
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
from dotenv import load_dotenv
from supabase import create_client, Client
import psycopg2
from psycopg2.extras import execute_batch
import time

load_dotenv()

# Initialize Supabase client
SUPABASE_URL = os.getenv('SUPABASE_URL') or os.getenv('VITE_SUPABASE_URL')
SUPABASE_KEY = os.getenv('SUPABASE_SERVICE_ROLE_KEY') or os.getenv('SUPABASE_ANON_KEY') or os.getenv('VITE_SUPABASE_ANON_KEY')

if not SUPABASE_URL or not SUPABASE_KEY:
    print("❌ Error: Supabase credentials not found")
    sys.exit(1)

supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

# Database connection
DATABASE_URL = os.getenv('DATABASE_URL')
conn = psycopg2.connect(DATABASE_URL)
cursor = conn.cursor()

def upload_single_image(image_path: Path):
    """Upload a single image and return the mapping"""
    try:
        filename = image_path.name
        product_id = filename.split('_')[0] if '_' in filename else None
        storage_path = f"generated/{filename}"
        
        # Read and upload
        with open(image_path, 'rb') as f:
            image_data = f.read()
        
        response = supabase.storage.from_('product-images').upload(
            path=storage_path,
            file=image_data,
            file_options={"content-type": "image/png", "upsert": "true"}
        )
        
        # Get public URL
        public_url = supabase.storage.from_('product-images').get_public_url(storage_path)
        
        return {
            'success': True,
            'old_url': f"/generated_images/{filename}",
            'new_url': public_url,
            'product_id': product_id,
            'filename': filename
        }
    except Exception as e:
        return {
            'success': False,
            'filename': image_path.name,
            'error': str(e)
        }

def main():
    print("🚀 Fast Image Migration to Supabase Storage")
    print("=" * 60)
    
    # Get all images
    image_dir = Path('generated_images')
    images = list(image_dir.glob('*.png'))
    total = len(images)
    
    print(f"📊 Found {total} images to upload")
    print("📤 Starting parallel upload (10 threads)...")
    
    start_time = time.time()
    results = []
    failed = []
    
    # Upload in parallel
    with ThreadPoolExecutor(max_workers=10) as executor:
        # Submit all tasks
        futures = {executor.submit(upload_single_image, img): img for img in images}
        
        # Process completed uploads
        completed = 0
        for future in as_completed(futures):
            completed += 1
            result = future.result()
            
            if result['success']:
                results.append(result)
                print(f"✅ [{completed}/{total}] {result['filename']}")
            else:
                failed.append(result)
                print(f"❌ [{completed}/{total}] {result['filename']}: {result['error']}")
            
            # Show progress every 50 images
            if completed % 50 == 0:
                elapsed = time.time() - start_time
                rate = completed / elapsed
                eta = (total - completed) / rate
                print(f"\n⏱️  Progress: {completed}/{total} ({completed/total*100:.1f}%) - ETA: {eta:.0f}s\n")
    
    print(f"\n✅ Upload complete! Uploaded {len(results)} images in {time.time() - start_time:.1f}s")
    
    if failed:
        print(f"❌ Failed uploads: {len(failed)}")
    
    # Update database
    if results:
        print(f"\n📝 Updating database with {len(results)} new URLs...")
        
        # Prepare batch update
        update_data = []
        for r in results:
            if r['product_id']:
                update_data.append((r['new_url'], int(r['product_id'])))
        
        if update_data:
            execute_batch(
                cursor,
                "UPDATE uses_products SET image_url = %s WHERE id = %s",
                update_data
            )
            conn.commit()
            print(f"✅ Updated {len(update_data)} database records")
    
    cursor.close()
    conn.close()
    
    print("\n✅ Migration complete!")
    print("\n📌 Next steps:")
    print("1. Verify images in your app")
    print("2. Run: git rm -r --cached generated_images/")
    print("3. Add 'generated_images/' to .gitignore")
    print("4. Commit and push")

if __name__ == "__main__":
    # Check how many are already uploaded
    cursor.execute("""
        SELECT COUNT(*) FROM uses_products 
        WHERE image_url LIKE '%supabase.co/storage%/generated/%'
    """)
    already_uploaded = cursor.fetchone()[0]
    
    if already_uploaded > 200:
        print(f"ℹ️  {already_uploaded} images already uploaded to Supabase")
        response = input("Continue with remaining images? (yes/no): ")
        if response.lower() != 'yes':
            print("Cancelled")
            sys.exit(0)
    
    main()