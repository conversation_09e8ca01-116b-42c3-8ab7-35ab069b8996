-- SQL statements to queue products with marijuana/smoking imagery for regeneration
-- Generated: 2025-07-25 10:26:36
-- Total products: 7

BEGIN;


-- Product: <PERSON><PERSON> <PERSON> Smoking Blend (ID: 1697)
-- Issue: Contains 'smoking' in URL and name
INSERT INTO image_generation_queue (
    product_id,
    product_name,
    prompt,
    priority,
    status,
    requested_by,
    metadata
) VALUES (
    1697,
    'Hemp Flower Smoking Blend',
    'professional product photography, high quality, commercial grade, clean composition, clinical presentation, pharmaceutical grade, medical aesthetic, professional medical product, no smoking imagery, clinical presentation, no human faces, no portraits, no people, no marijuana leaves, no smoking, no recreational drugs, no living rooms, no home interiors, no furniture, no keyboards, no computers, no office equipment',
    9,
    'pending',
    'fix_marijuana_images',
    '{"issue": "Contains 'smoking' in URL and name", "previous_imagery": "marijuana/smoking related"}'::jsonb
) ON CONFLICT (product_id) 
WHERE status = 'pending' 
DO UPDATE SET 
    prompt = EXCLUDED.prompt,
    priority = EXCLUDED.priority,
    updated_at = NOW();


-- Product: HempPlant Restore: Targeted Muscle & Joint Relief Roll-On (ID: 1473)
-- Issue: May have inappropriate imagery
INSERT INTO image_generation_queue (
    product_id,
    product_name,
    prompt,
    priority,
    status,
    requested_by,
    metadata
) VALUES (
    1473,
    'HempPlant Restore: Targeted Muscle & Joint Relief Roll-On',
    'professional product photography, high quality, commercial grade, clean composition, clinical presentation, pharmaceutical grade, medical aesthetic, professional medical product, no smoking imagery, clinical presentation, no human faces, no portraits, no people, no marijuana leaves, no smoking, no recreational drugs, no living rooms, no home interiors, no furniture, no keyboards, no computers, no office equipment',
    9,
    'pending',
    'fix_marijuana_images',
    '{"issue": "May have inappropriate imagery", "previous_imagery": "marijuana/smoking related"}'::jsonb
) ON CONFLICT (product_id) 
WHERE status = 'pending' 
DO UPDATE SET 
    prompt = EXCLUDED.prompt,
    priority = EXCLUDED.priority,
    updated_at = NOW();


-- Product: Hemp Root & CBD Joint Pain Relief Patches – "Targeted Relief" (ID: 1618)
-- Issue: Potential cannabis-related imagery
INSERT INTO image_generation_queue (
    product_id,
    product_name,
    prompt,
    priority,
    status,
    requested_by,
    metadata
) VALUES (
    1618,
    'Hemp Root & CBD Joint Pain Relief Patches – "Targeted Relief"',
    'professional product photography, high quality, commercial grade, clean composition, clinical presentation, pharmaceutical grade, medical aesthetic, professional medical product, no smoking imagery, clinical presentation, no human faces, no portraits, no people, no marijuana leaves, no smoking, no recreational drugs, no living rooms, no home interiors, no furniture, no keyboards, no computers, no office equipment',
    9,
    'pending',
    'fix_marijuana_images',
    '{"issue": "Potential cannabis-related imagery", "previous_imagery": "marijuana/smoking related"}'::jsonb
) ON CONFLICT (product_id) 
WHERE status = 'pending' 
DO UPDATE SET 
    prompt = EXCLUDED.prompt,
    priority = EXCLUDED.priority,
    updated_at = NOW();


-- Product: HempRoot Muscle & Joint Relief Patch (ID: 1450)
-- Issue: May contain inappropriate medical imagery
INSERT INTO image_generation_queue (
    product_id,
    product_name,
    prompt,
    priority,
    status,
    requested_by,
    metadata
) VALUES (
    1450,
    'HempRoot Muscle & Joint Relief Patch',
    'professional product photography, high quality, commercial grade, clean composition, clinical presentation, pharmaceutical grade, medical aesthetic, professional medical product, no smoking imagery, clinical presentation, no human faces, no portraits, no people, no marijuana leaves, no smoking, no recreational drugs, no living rooms, no home interiors, no furniture, no keyboards, no computers, no office equipment',
    9,
    'pending',
    'fix_marijuana_images',
    '{"issue": "May contain inappropriate medical imagery", "previous_imagery": "marijuana/smoking related"}'::jsonb
) ON CONFLICT (product_id) 
WHERE status = 'pending' 
DO UPDATE SET 
    prompt = EXCLUDED.prompt,
    priority = EXCLUDED.priority,
    updated_at = NOW();


-- Product: HempRoot Muscle & Joint Relief Roll-On with Targeted CBD Delivery (ID: 1561)
-- Issue: Potential cannabis imagery
INSERT INTO image_generation_queue (
    product_id,
    product_name,
    prompt,
    priority,
    status,
    requested_by,
    metadata
) VALUES (
    1561,
    'HempRoot Muscle & Joint Relief Roll-On with Targeted CBD Delivery',
    'professional product photography, high quality, commercial grade, clean composition, clinical presentation, pharmaceutical grade, medical aesthetic, professional medical product, no smoking imagery, clinical presentation, no human faces, no portraits, no people, no marijuana leaves, no smoking, no recreational drugs, no living rooms, no home interiors, no furniture, no keyboards, no computers, no office equipment',
    9,
    'pending',
    'fix_marijuana_images',
    '{"issue": "Potential cannabis imagery", "previous_imagery": "marijuana/smoking related"}'::jsonb
) ON CONFLICT (product_id) 
WHERE status = 'pending' 
DO UPDATE SET 
    prompt = EXCLUDED.prompt,
    priority = EXCLUDED.priority,
    updated_at = NOW();


-- Product: HempRoot Muscle & Joint Soothing Gel (ID: 1679)
-- Issue: May have cannabis-related imagery
INSERT INTO image_generation_queue (
    product_id,
    product_name,
    prompt,
    priority,
    status,
    requested_by,
    metadata
) VALUES (
    1679,
    'HempRoot Muscle & Joint Soothing Gel',
    'professional product photography, high quality, commercial grade, clean composition, clinical presentation, pharmaceutical grade, medical aesthetic, professional medical product, no smoking imagery, clinical presentation, no human faces, no portraits, no people, no marijuana leaves, no smoking, no recreational drugs, no living rooms, no home interiors, no furniture, no keyboards, no computers, no office equipment',
    9,
    'pending',
    'fix_marijuana_images',
    '{"issue": "May have cannabis-related imagery", "previous_imagery": "marijuana/smoking related"}'::jsonb
) ON CONFLICT (product_id) 
WHERE status = 'pending' 
DO UPDATE SET 
    prompt = EXCLUDED.prompt,
    priority = EXCLUDED.priority,
    updated_at = NOW();


-- Product: High-Potency CBD Oil 5000mg (ID: 1420)
-- Issue: High-potency CBD may have cannabis imagery
INSERT INTO image_generation_queue (
    product_id,
    product_name,
    prompt,
    priority,
    status,
    requested_by,
    metadata
) VALUES (
    1420,
    'High-Potency CBD Oil 5000mg',
    'professional product photography, high quality, commercial grade, clean composition, clinical presentation, pharmaceutical grade, medical aesthetic, professional medical product, no smoking imagery, clinical presentation, no human faces, no portraits, no people, no marijuana leaves, no smoking, no recreational drugs, no living rooms, no home interiors, no furniture, no keyboards, no computers, no office equipment',
    9,
    'pending',
    'fix_marijuana_images',
    '{"issue": "High-potency CBD may have cannabis imagery", "previous_imagery": "marijuana/smoking related"}'::jsonb
) ON CONFLICT (product_id) 
WHERE status = 'pending' 
DO UPDATE SET 
    prompt = EXCLUDED.prompt,
    priority = EXCLUDED.priority,
    updated_at = NOW();

COMMIT;
