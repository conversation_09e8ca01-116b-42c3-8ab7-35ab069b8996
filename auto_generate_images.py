#!/usr/bin/env python3
"""
Automatically generate images for new products
Designed to run continuously alongside agents
"""

import os
import time
import psycopg2
from datetime import datetime
import subprocess
import sys

def log(message):
    """Log with timestamp"""
    print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] {message}")

def get_products_needing_images(limit=50):
    """Get count of products needing images"""
    conn = psycopg2.connect(os.environ['DATABASE_URL'])
    cur = conn.cursor()
    
    cur.execute("""
        SELECT COUNT(*) 
        FROM uses_products 
        WHERE image_url IS NULL 
        AND ai_generated_image_url IS NULL 
        AND original_image_url IS NULL
    """)
    count = cur.fetchone()[0]
    
    cur.close()
    conn.close()
    
    return count

def generate_images_batch(size=25):
    """Generate a batch of images"""
    try:
        result = subprocess.run([
            sys.executable,
            "generate_product_images.py",
            "--limit", 
            str(size)
        ], capture_output=True, text=True, timeout=600)  # 10 minute timeout
        
        if result.returncode == 0:
            # Extract success count
            if "Successful:" in result.stdout:
                for line in result.stdout.split('\n'):
                    if "Successful:" in line:
                        return True, line.strip()
            return True, f"Generated {size} images"
        else:
            return False, f"Error: {result.stderr[:200]}"
    except subprocess.TimeoutExpired:
        return False, "Timeout after 10 minutes"
    except Exception as e:
        return False, f"Exception: {str(e)}"

def main():
    log("🎨 AUTO IMAGE GENERATOR STARTED")
    log("Will check for products needing images every 5 minutes")
    log("Press Ctrl+C to stop")
    
    total_generated = 0
    cycles = 0
    
    try:
        while True:
            cycles += 1
            
            # Check for products needing images
            count = get_products_needing_images()
            
            if count > 0:
                log(f"Found {count} products without images")
                
                # Generate in small batches
                batch_size = min(25, count)  # Max 25 at a time
                log(f"Generating batch of {batch_size} images...")
                
                success, message = generate_images_batch(batch_size)
                
                if success:
                    total_generated += batch_size
                    log(f"✅ {message}")
                    log(f"Total generated this session: {total_generated}")
                else:
                    log(f"❌ Failed: {message}")
                
                # Brief pause after generation
                time.sleep(30)
            else:
                log("✅ All products have images!")
            
            # Wait 5 minutes before next check
            log(f"Sleeping for 5 minutes... (Cycle {cycles}, Total generated: {total_generated})")
            time.sleep(300)
            
    except KeyboardInterrupt:
        log("\n🛑 Stopped by user")
        log(f"Final stats: {cycles} cycles, {total_generated} images generated")

if __name__ == "__main__":
    # Verify environment
    if not os.environ.get('DATABASE_URL'):
        print("❌ DATABASE_URL not set!")
        print("Run: source .env")
        sys.exit(1)
    
    if not os.environ.get('REPLICATE_API_TOKEN'):
        print("❌ REPLICATE_API_TOKEN not set!")
        print("Run: source .env")
        sys.exit(1)
    
    main()