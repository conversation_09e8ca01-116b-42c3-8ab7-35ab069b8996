#!/usr/bin/env python3
"""
Delete all identified duplicates from the database
"""

import requests
import json
from datetime import datetime
from typing import List, Dict
from difflib import SequenceMatcher

# Service role key for authentication
service_role_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt0b3F6bnFtbG54cnR2dWJld3l6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODQ5MTc3NiwiZXhwIjoyMDY0MDY3Nzc2fQ.hxEiOy6xebv7I4CYJEU49oCEXRvmU6iz2l8Kg-NnvTs"
supabase_url = "https://ktoqznqmlnxrtvubewyz.supabase.co"

headers = {
    "apikey": service_role_key,
    "Authorization": f"Bearer {service_role_key}",
    "Content-Type": "application/json",
    "Prefer": "return=minimal"
}

def normalize_name(name: str) -> str:
    """Normalize product name for comparison"""
    # Remove extra spaces
    name = ' '.join(name.split())
    
    # Remove redundant words
    import re
    name = re.sub(r'\b(\w+)\s+\1\b', r'\1', name)
    
    # Standardize Hemp variations
    name = name.replace('Hemp-Based', 'Hemp')
    name = name.replace('Hemp-Derived', 'Hemp')
    
    # Remove variation letters
    name = re.sub(r'\s+(Plus|Pro|Elite|Ultra|Max|Prime|Select|Premium)\s+[A-Z]\b', r' \\1', name)
    
    return name.strip()

def calculate_similarity(name1: str, name2: str) -> float:
    """Calculate similarity between two product names"""
    norm1 = normalize_name(name1)
    norm2 = normalize_name(name2)
    
    if norm1 == norm2:
        return 1.0
    
    return SequenceMatcher(None, norm1.lower(), norm2.lower()).ratio()

def fetch_all_products() -> List[Dict]:
    """Fetch all products from database"""
    print("Fetching all products...")
    all_products = []
    offset = 0
    limit = 1000
    
    while True:
        response = requests.get(
            f"{supabase_url}/rest/v1/uses_products",
            headers=headers,
            params={
                "select": "id,name,description,source_agent,created_at,plant_part_id,industry_sub_category_id,confidence_score",
                "order": "id",
                "offset": offset,
                "limit": limit
            }
        )
        
        if response.status_code != 200:
            print(f"Error fetching products: {response.text}")
            break
            
        batch = response.json()
        if not batch:
            break
            
        all_products.extend(batch)
        offset += limit
        
        if len(batch) < limit:
            break
    
    print(f"Fetched {len(all_products)} products")
    return all_products

def find_duplicate_groups(products: List[Dict]) -> List[List[Dict]]:
    """Group products that are likely duplicates"""
    print("\nAnalyzing for duplicates...")
    
    groups = []
    processed = set()
    
    for i, product1 in enumerate(products):
        if product1['id'] in processed:
            continue
            
        group = [product1]
        processed.add(product1['id'])
        
        for j, product2 in enumerate(products[i+1:], i+1):
            if product2['id'] in processed:
                continue
                
            similarity = calculate_similarity(product1['name'], product2['name'])
            
            # Consider duplicates if similarity > 0.85 AND same plant part/industry
            if (similarity > 0.85 and 
                product1['plant_part_id'] == product2['plant_part_id'] and
                product1['industry_sub_category_id'] == product2['industry_sub_category_id']):
                
                group.append(product2)
                processed.add(product2['id'])
        
        if len(group) > 1:
            groups.append(group)
    
    print(f"Found {len(groups)} duplicate groups")
    return groups

def delete_duplicates(groups: List[List[Dict]]) -> int:
    """Delete duplicate products, keeping the best one from each group"""
    total_deleted = 0
    failed_deletes = 0
    
    print(f"\nDeleting duplicates from {len(groups)} groups...")
    
    for i, group in enumerate(groups):
        # Sort by: confidence score (higher better), created date (older better), name length (shorter better)
        sorted_group = sorted(group, key=lambda x: (
            -(x.get('confidence_score', 0) or 0),  # Negative for descending
            x['created_at'],
            len(x['name']),
            x['id']
        ))
        
        keep = sorted_group[0]
        to_delete = sorted_group[1:]
        
        # Progress indicator
        if i % 50 == 0:
            print(f"Processing group {i+1}/{len(groups)}...")
        
        for product in to_delete:
            # Delete using the REST API
            response = requests.delete(
                f"{supabase_url}/rest/v1/uses_products?id=eq.{product['id']}",
                headers=headers
            )
            
            if response.status_code in [200, 204]:
                total_deleted += 1
            else:
                failed_deletes += 1
                if failed_deletes < 5:  # Only show first few errors
                    print(f"  Failed to delete {product['name']} (ID: {product['id']}): {response.status_code}")
    
    print(f"\nSuccessfully deleted: {total_deleted} products")
    if failed_deletes > 0:
        print(f"Failed to delete: {failed_deletes} products")
    
    return total_deleted

def get_final_count() -> int:
    """Get the final count of products"""
    response = requests.get(
        f"{supabase_url}/rest/v1/uses_products",
        headers=headers,
        params={
            "select": "count",
            "limit": "1"
        }
    )
    
    if response.status_code == 200:
        # Parse the content-range header to get total count
        content_range = response.headers.get('content-range', '')
        if '/' in content_range:
            return int(content_range.split('/')[-1])
    
    return -1

def main():
    print("=" * 60)
    print("HEMP DATABASE DUPLICATE DELETION")
    print("=" * 60)
    
    # Get initial count
    initial_count = get_final_count()
    print(f"\nInitial product count: {initial_count}")
    
    # Fetch and analyze products
    products = fetch_all_products()
    groups = find_duplicate_groups(products)
    
    # Calculate totals
    total_duplicates = sum(len(g) - 1 for g in groups)
    print(f"\nTotal duplicates to delete: {total_duplicates}")
    
    # Delete duplicates
    if total_duplicates > 0:
        deleted = delete_duplicates(groups)
        
        # Get final count
        final_count = get_final_count()
        
        print("\n" + "=" * 60)
        print("DELETION COMPLETE")
        print("=" * 60)
        print(f"Initial count: {initial_count}")
        print(f"Deleted: {deleted}")
        print(f"Final count: {final_count}")
        print(f"Actual reduction: {initial_count - final_count}")
    else:
        print("\nNo duplicates found to delete!")

if __name__ == "__main__":
    main()