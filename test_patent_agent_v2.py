#!/usr/bin/env python3
"""
Test the new patent mining agent V2
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.agents.specialized.patent_mining_agent_v2 import PatentMiningAgentV2

class TestPatentAgentV2(PatentMiningAgentV2):
    def __init__(self):
        super().__init__()
        # Override with just 2 search terms for quick testing
        self.search_terms = [
            "hemp fiber composite",
            "hemp battery electrode"
        ]
        print(f"Test mode: Limited to {len(self.search_terms)} search terms")

if __name__ == "__main__":
    print("🧪 Running Patent Mining Agent V2 in TEST MODE")
    print("This will test Google Patents and Patent Lens APIs")
    print("-" * 60)
    
    agent = TestPatentAgentV2()
    
    # First test the APIs directly
    print("\n1. Testing Google Patents API...")
    google_results = agent.search_google_patents("hemp composite")
    print(f"   Found {len(google_results)} results")
    if google_results:
        print(f"   Sample: {google_results[0].get('patent_title', 'No title')[:60]}...")
    
    print("\n2. Testing Patent Lens API...")
    lens_results = agent.search_patent_lens("hemp fiber")
    print(f"   Found {len(lens_results)} results")
    if lens_results:
        print(f"   Sample: {lens_results[0].get('patent_title', 'No title')[:60]}...")
    
    # Now run the full cycle
    print("\n3. Running full discovery cycle...")
    results = agent.run_discovery_cycle()
    
    print(f"\n📊 Test Results:")
    print(f"Products discovered: {results}")
    print(f"Agent total: {agent.discovered_count}")