-- Category Image Mapping for Hemp Products
-- Generated to provide immediate visual coverage for 4,000+ products
-- Total cost: $0.00 (using placeholder images)

-- First, let's see current image coverage
SELECT 
    COUNT(*) as total_products,
    COUNT(CASE WHEN image_url IS NOT NULL AND image_url != '' THEN 1 END) as with_images,
    COUNT(CASE WHEN image_url IS NULL OR image_url = '' THEN 1 END) as without_images
FROM uses_products;

-- Begin mapping category images
BEGIN;


-- Map Hemp Seeds products
UPDATE uses_products
SET 
    image_url = 'https://ktoqznqmlnxrtvubewyz.supabase.co/storage/v1/object/public/product-images/categories/hemp_food_beverages_hemp_seeds.jpg',
    updated_at = CURRENT_TIMESTAMP
WHERE 
    (image_url IS NULL OR image_url = '' OR image_url LIKE '%placeholder%')
    AND (LOWER(name) LIKE '%seed%' OR LOWER(description) LIKE '%seed%' OR LOWER(name) LIKE '%hearts%' OR LOWER(description) LIKE '%hearts%' OR LOWER(name) LIKE '%hulled%' OR LOWER(description) LIKE '%hulled%' OR LOWER(name) LIKE '%shelled%' OR LOWER(description) LIKE '%shelled%')
    AND id IN (
        SELECT id FROM uses_products
        WHERE (image_url IS NULL OR image_url = '' OR image_url LIKE '%placeholder%')
        AND (LOWER(name) LIKE '%seed%' OR LOWER(description) LIKE '%seed%' OR LOWER(name) LIKE '%hearts%' OR LOWER(description) LIKE '%hearts%' OR LOWER(name) LIKE '%hulled%' OR LOWER(description) LIKE '%hulled%' OR LOWER(name) LIKE '%shelled%' OR LOWER(description) LIKE '%shelled%')
        LIMIT 500  -- Process in batches to avoid timeouts
    );

-- Log the update
DO $$
DECLARE
    updated_count INTEGER;
BEGIN
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RAISE NOTICE 'Updated % products with % image', updated_count, 'hemp_seeds';
END $$;


-- Map Hemp Oil Culinary products
UPDATE uses_products
SET 
    image_url = 'https://ktoqznqmlnxrtvubewyz.supabase.co/storage/v1/object/public/product-images/categories/hemp_food_beverages_hemp_oil_culinary.jpg',
    updated_at = CURRENT_TIMESTAMP
WHERE 
    (image_url IS NULL OR image_url = '' OR image_url LIKE '%placeholder%')
    AND (LOWER(name) LIKE '%cooking oil%' OR LOWER(description) LIKE '%cooking oil%' OR LOWER(name) LIKE '%culinary oil%' OR LOWER(description) LIKE '%culinary oil%' OR LOWER(name) LIKE '%hemp oil%' OR LOWER(description) LIKE '%hemp oil%' OR LOWER(name) LIKE '%kitchen%' OR LOWER(description) LIKE '%kitchen%')
    AND id IN (
        SELECT id FROM uses_products
        WHERE (image_url IS NULL OR image_url = '' OR image_url LIKE '%placeholder%')
        AND (LOWER(name) LIKE '%cooking oil%' OR LOWER(description) LIKE '%cooking oil%' OR LOWER(name) LIKE '%culinary oil%' OR LOWER(description) LIKE '%culinary oil%' OR LOWER(name) LIKE '%hemp oil%' OR LOWER(description) LIKE '%hemp oil%' OR LOWER(name) LIKE '%kitchen%' OR LOWER(description) LIKE '%kitchen%')
        LIMIT 500  -- Process in batches to avoid timeouts
    );

-- Log the update
DO $$
DECLARE
    updated_count INTEGER;
BEGIN
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RAISE NOTICE 'Updated % products with % image', updated_count, 'hemp_oil_culinary';
END $$;


-- Map Hemp Protein products
UPDATE uses_products
SET 
    image_url = 'https://ktoqznqmlnxrtvubewyz.supabase.co/storage/v1/object/public/product-images/categories/hemp_food_beverages_hemp_protein.jpg',
    updated_at = CURRENT_TIMESTAMP
WHERE 
    (image_url IS NULL OR image_url = '' OR image_url LIKE '%placeholder%')
    AND (LOWER(name) LIKE '%protein%' OR LOWER(description) LIKE '%protein%' OR LOWER(name) LIKE '%powder%' OR LOWER(description) LIKE '%powder%' OR LOWER(name) LIKE '%supplement%' OR LOWER(description) LIKE '%supplement%' OR LOWER(name) LIKE '%fitness%' OR LOWER(description) LIKE '%fitness%')
    AND id IN (
        SELECT id FROM uses_products
        WHERE (image_url IS NULL OR image_url = '' OR image_url LIKE '%placeholder%')
        AND (LOWER(name) LIKE '%protein%' OR LOWER(description) LIKE '%protein%' OR LOWER(name) LIKE '%powder%' OR LOWER(description) LIKE '%powder%' OR LOWER(name) LIKE '%supplement%' OR LOWER(description) LIKE '%supplement%' OR LOWER(name) LIKE '%fitness%' OR LOWER(description) LIKE '%fitness%')
        LIMIT 500  -- Process in batches to avoid timeouts
    );

-- Log the update
DO $$
DECLARE
    updated_count INTEGER;
BEGIN
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RAISE NOTICE 'Updated % products with % image', updated_count, 'hemp_protein';
END $$;


-- Map Hemp Beverages products
UPDATE uses_products
SET 
    image_url = 'https://ktoqznqmlnxrtvubewyz.supabase.co/storage/v1/object/public/product-images/categories/hemp_food_beverages_hemp_beverages.jpg',
    updated_at = CURRENT_TIMESTAMP
WHERE 
    (image_url IS NULL OR image_url = '' OR image_url LIKE '%placeholder%')
    AND (LOWER(name) LIKE '%tea%' OR LOWER(description) LIKE '%tea%' OR LOWER(name) LIKE '%coffee%' OR LOWER(description) LIKE '%coffee%' OR LOWER(name) LIKE '%drink%' OR LOWER(description) LIKE '%drink%' OR LOWER(name) LIKE '%beverage%' OR LOWER(description) LIKE '%beverage%' OR LOWER(name) LIKE '%milk%' OR LOWER(description) LIKE '%milk%' OR LOWER(name) LIKE '%juice%' OR LOWER(description) LIKE '%juice%')
    AND id IN (
        SELECT id FROM uses_products
        WHERE (image_url IS NULL OR image_url = '' OR image_url LIKE '%placeholder%')
        AND (LOWER(name) LIKE '%tea%' OR LOWER(description) LIKE '%tea%' OR LOWER(name) LIKE '%coffee%' OR LOWER(description) LIKE '%coffee%' OR LOWER(name) LIKE '%drink%' OR LOWER(description) LIKE '%drink%' OR LOWER(name) LIKE '%beverage%' OR LOWER(description) LIKE '%beverage%' OR LOWER(name) LIKE '%milk%' OR LOWER(description) LIKE '%milk%' OR LOWER(name) LIKE '%juice%' OR LOWER(description) LIKE '%juice%')
        LIMIT 500  -- Process in batches to avoid timeouts
    );

-- Log the update
DO $$
DECLARE
    updated_count INTEGER;
BEGIN
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RAISE NOTICE 'Updated % products with % image', updated_count, 'hemp_beverages';
END $$;


-- Map Cbd Oils products
UPDATE uses_products
SET 
    image_url = 'https://ktoqznqmlnxrtvubewyz.supabase.co/storage/v1/object/public/product-images/categories/hemp_health_wellness_cbd_oils.jpg',
    updated_at = CURRENT_TIMESTAMP
WHERE 
    (image_url IS NULL OR image_url = '' OR image_url LIKE '%placeholder%')
    AND (LOWER(name) LIKE '%cbd%' OR LOWER(description) LIKE '%cbd%' OR LOWER(name) LIKE '%cannabinoid%' OR LOWER(description) LIKE '%cannabinoid%' OR LOWER(name) LIKE '%tincture%' OR LOWER(description) LIKE '%tincture%' OR LOWER(name) LIKE '%extract%' OR LOWER(description) LIKE '%extract%' OR LOWER(name) LIKE '%drops%' OR LOWER(description) LIKE '%drops%')
    AND id IN (
        SELECT id FROM uses_products
        WHERE (image_url IS NULL OR image_url = '' OR image_url LIKE '%placeholder%')
        AND (LOWER(name) LIKE '%cbd%' OR LOWER(description) LIKE '%cbd%' OR LOWER(name) LIKE '%cannabinoid%' OR LOWER(description) LIKE '%cannabinoid%' OR LOWER(name) LIKE '%tincture%' OR LOWER(description) LIKE '%tincture%' OR LOWER(name) LIKE '%extract%' OR LOWER(description) LIKE '%extract%' OR LOWER(name) LIKE '%drops%' OR LOWER(description) LIKE '%drops%')
        LIMIT 500  -- Process in batches to avoid timeouts
    );

-- Log the update
DO $$
DECLARE
    updated_count INTEGER;
BEGIN
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RAISE NOTICE 'Updated % products with % image', updated_count, 'cbd_oils';
END $$;


-- Map Topicals products
UPDATE uses_products
SET 
    image_url = 'https://ktoqznqmlnxrtvubewyz.supabase.co/storage/v1/object/public/product-images/categories/hemp_health_wellness_topicals.jpg',
    updated_at = CURRENT_TIMESTAMP
WHERE 
    (image_url IS NULL OR image_url = '' OR image_url LIKE '%placeholder%')
    AND (LOWER(name) LIKE '%balm%' OR LOWER(description) LIKE '%balm%' OR LOWER(name) LIKE '%cream%' OR LOWER(description) LIKE '%cream%' OR LOWER(name) LIKE '%lotion%' OR LOWER(description) LIKE '%lotion%' OR LOWER(name) LIKE '%salve%' OR LOWER(description) LIKE '%salve%' OR LOWER(name) LIKE '%ointment%' OR LOWER(description) LIKE '%ointment%' OR LOWER(name) LIKE '%rub%' OR LOWER(description) LIKE '%rub%')
    AND id IN (
        SELECT id FROM uses_products
        WHERE (image_url IS NULL OR image_url = '' OR image_url LIKE '%placeholder%')
        AND (LOWER(name) LIKE '%balm%' OR LOWER(description) LIKE '%balm%' OR LOWER(name) LIKE '%cream%' OR LOWER(description) LIKE '%cream%' OR LOWER(name) LIKE '%lotion%' OR LOWER(description) LIKE '%lotion%' OR LOWER(name) LIKE '%salve%' OR LOWER(description) LIKE '%salve%' OR LOWER(name) LIKE '%ointment%' OR LOWER(description) LIKE '%ointment%' OR LOWER(name) LIKE '%rub%' OR LOWER(description) LIKE '%rub%')
        LIMIT 500  -- Process in batches to avoid timeouts
    );

-- Log the update
DO $$
DECLARE
    updated_count INTEGER;
BEGIN
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RAISE NOTICE 'Updated % products with % image', updated_count, 'topicals';
END $$;


-- Map Capsules products
UPDATE uses_products
SET 
    image_url = 'https://ktoqznqmlnxrtvubewyz.supabase.co/storage/v1/object/public/product-images/categories/hemp_health_wellness_capsules.jpg',
    updated_at = CURRENT_TIMESTAMP
WHERE 
    (image_url IS NULL OR image_url = '' OR image_url LIKE '%placeholder%')
    AND (LOWER(name) LIKE '%capsule%' OR LOWER(description) LIKE '%capsule%' OR LOWER(name) LIKE '%pill%' OR LOWER(description) LIKE '%pill%' OR LOWER(name) LIKE '%tablet%' OR LOWER(description) LIKE '%tablet%' OR LOWER(name) LIKE '%softgel%' OR LOWER(description) LIKE '%softgel%' OR LOWER(name) LIKE '%supplement%' OR LOWER(description) LIKE '%supplement%')
    AND id IN (
        SELECT id FROM uses_products
        WHERE (image_url IS NULL OR image_url = '' OR image_url LIKE '%placeholder%')
        AND (LOWER(name) LIKE '%capsule%' OR LOWER(description) LIKE '%capsule%' OR LOWER(name) LIKE '%pill%' OR LOWER(description) LIKE '%pill%' OR LOWER(name) LIKE '%tablet%' OR LOWER(description) LIKE '%tablet%' OR LOWER(name) LIKE '%softgel%' OR LOWER(description) LIKE '%softgel%' OR LOWER(name) LIKE '%supplement%' OR LOWER(description) LIKE '%supplement%')
        LIMIT 500  -- Process in batches to avoid timeouts
    );

-- Log the update
DO $$
DECLARE
    updated_count INTEGER;
BEGIN
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RAISE NOTICE 'Updated % products with % image', updated_count, 'capsules';
END $$;


-- Map Raw Fiber products
UPDATE uses_products
SET 
    image_url = 'https://ktoqznqmlnxrtvubewyz.supabase.co/storage/v1/object/public/product-images/categories/hemp_textiles_fashion_raw_fiber.jpg',
    updated_at = CURRENT_TIMESTAMP
WHERE 
    (image_url IS NULL OR image_url = '' OR image_url LIKE '%placeholder%')
    AND (LOWER(name) LIKE '%fiber%' OR LOWER(description) LIKE '%fiber%' OR LOWER(name) LIKE '%fibre%' OR LOWER(description) LIKE '%fibre%' OR LOWER(name) LIKE '%raw hemp%' OR LOWER(description) LIKE '%raw hemp%' OR LOWER(name) LIKE '%bast%' OR LOWER(description) LIKE '%bast%')
    AND id IN (
        SELECT id FROM uses_products
        WHERE (image_url IS NULL OR image_url = '' OR image_url LIKE '%placeholder%')
        AND (LOWER(name) LIKE '%fiber%' OR LOWER(description) LIKE '%fiber%' OR LOWER(name) LIKE '%fibre%' OR LOWER(description) LIKE '%fibre%' OR LOWER(name) LIKE '%raw hemp%' OR LOWER(description) LIKE '%raw hemp%' OR LOWER(name) LIKE '%bast%' OR LOWER(description) LIKE '%bast%')
        LIMIT 500  -- Process in batches to avoid timeouts
    );

-- Log the update
DO $$
DECLARE
    updated_count INTEGER;
BEGIN
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RAISE NOTICE 'Updated % products with % image', updated_count, 'raw_fiber';
END $$;


-- Map Fabric products
UPDATE uses_products
SET 
    image_url = 'https://ktoqznqmlnxrtvubewyz.supabase.co/storage/v1/object/public/product-images/categories/hemp_textiles_fashion_fabric.jpg',
    updated_at = CURRENT_TIMESTAMP
WHERE 
    (image_url IS NULL OR image_url = '' OR image_url LIKE '%placeholder%')
    AND (LOWER(name) LIKE '%fabric%' OR LOWER(description) LIKE '%fabric%' OR LOWER(name) LIKE '%textile%' OR LOWER(description) LIKE '%textile%' OR LOWER(name) LIKE '%cloth%' OR LOWER(description) LIKE '%cloth%' OR LOWER(name) LIKE '%material%' OR LOWER(description) LIKE '%material%' OR LOWER(name) LIKE '%canvas%' OR LOWER(description) LIKE '%canvas%')
    AND id IN (
        SELECT id FROM uses_products
        WHERE (image_url IS NULL OR image_url = '' OR image_url LIKE '%placeholder%')
        AND (LOWER(name) LIKE '%fabric%' OR LOWER(description) LIKE '%fabric%' OR LOWER(name) LIKE '%textile%' OR LOWER(description) LIKE '%textile%' OR LOWER(name) LIKE '%cloth%' OR LOWER(description) LIKE '%cloth%' OR LOWER(name) LIKE '%material%' OR LOWER(description) LIKE '%material%' OR LOWER(name) LIKE '%canvas%' OR LOWER(description) LIKE '%canvas%')
        LIMIT 500  -- Process in batches to avoid timeouts
    );

-- Log the update
DO $$
DECLARE
    updated_count INTEGER;
BEGIN
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RAISE NOTICE 'Updated % products with % image', updated_count, 'fabric';
END $$;


-- Map Clothing products
UPDATE uses_products
SET 
    image_url = 'https://ktoqznqmlnxrtvubewyz.supabase.co/storage/v1/object/public/product-images/categories/hemp_textiles_fashion_clothing.jpg',
    updated_at = CURRENT_TIMESTAMP
WHERE 
    (image_url IS NULL OR image_url = '' OR image_url LIKE '%placeholder%')
    AND (LOWER(name) LIKE '%clothing%' OR LOWER(description) LIKE '%clothing%' OR LOWER(name) LIKE '%apparel%' OR LOWER(description) LIKE '%apparel%' OR LOWER(name) LIKE '%shirt%' OR LOWER(description) LIKE '%shirt%' OR LOWER(name) LIKE '%pants%' OR LOWER(description) LIKE '%pants%' OR LOWER(name) LIKE '%dress%' OR LOWER(description) LIKE '%dress%' OR LOWER(name) LIKE '%wear%' OR LOWER(description) LIKE '%wear%')
    AND id IN (
        SELECT id FROM uses_products
        WHERE (image_url IS NULL OR image_url = '' OR image_url LIKE '%placeholder%')
        AND (LOWER(name) LIKE '%clothing%' OR LOWER(description) LIKE '%clothing%' OR LOWER(name) LIKE '%apparel%' OR LOWER(description) LIKE '%apparel%' OR LOWER(name) LIKE '%shirt%' OR LOWER(description) LIKE '%shirt%' OR LOWER(name) LIKE '%pants%' OR LOWER(description) LIKE '%pants%' OR LOWER(name) LIKE '%dress%' OR LOWER(description) LIKE '%dress%' OR LOWER(name) LIKE '%wear%' OR LOWER(description) LIKE '%wear%')
        LIMIT 500  -- Process in batches to avoid timeouts
    );

-- Log the update
DO $$
DECLARE
    updated_count INTEGER;
BEGIN
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RAISE NOTICE 'Updated % products with % image', updated_count, 'clothing';
END $$;


-- Map Hempcrete products
UPDATE uses_products
SET 
    image_url = 'https://ktoqznqmlnxrtvubewyz.supabase.co/storage/v1/object/public/product-images/categories/hemp_building_construction_hempcrete.jpg',
    updated_at = CURRENT_TIMESTAMP
WHERE 
    (image_url IS NULL OR image_url = '' OR image_url LIKE '%placeholder%')
    AND (LOWER(name) LIKE '%hempcrete%' OR LOWER(description) LIKE '%hempcrete%' OR LOWER(name) LIKE '%concrete%' OR LOWER(description) LIKE '%concrete%' OR LOWER(name) LIKE '%building block%' OR LOWER(description) LIKE '%building block%' OR LOWER(name) LIKE '%construction%' OR LOWER(description) LIKE '%construction%')
    AND id IN (
        SELECT id FROM uses_products
        WHERE (image_url IS NULL OR image_url = '' OR image_url LIKE '%placeholder%')
        AND (LOWER(name) LIKE '%hempcrete%' OR LOWER(description) LIKE '%hempcrete%' OR LOWER(name) LIKE '%concrete%' OR LOWER(description) LIKE '%concrete%' OR LOWER(name) LIKE '%building block%' OR LOWER(description) LIKE '%building block%' OR LOWER(name) LIKE '%construction%' OR LOWER(description) LIKE '%construction%')
        LIMIT 500  -- Process in batches to avoid timeouts
    );

-- Log the update
DO $$
DECLARE
    updated_count INTEGER;
BEGIN
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RAISE NOTICE 'Updated % products with % image', updated_count, 'hempcrete';
END $$;


-- Map Insulation products
UPDATE uses_products
SET 
    image_url = 'https://ktoqznqmlnxrtvubewyz.supabase.co/storage/v1/object/public/product-images/categories/hemp_building_construction_insulation.jpg',
    updated_at = CURRENT_TIMESTAMP
WHERE 
    (image_url IS NULL OR image_url = '' OR image_url LIKE '%placeholder%')
    AND (LOWER(name) LIKE '%insulation%' OR LOWER(description) LIKE '%insulation%' OR LOWER(name) LIKE '%insulating%' OR LOWER(description) LIKE '%insulating%' OR LOWER(name) LIKE '%thermal%' OR LOWER(description) LIKE '%thermal%' OR LOWER(name) LIKE '%acoustic%' OR LOWER(description) LIKE '%acoustic%')
    AND id IN (
        SELECT id FROM uses_products
        WHERE (image_url IS NULL OR image_url = '' OR image_url LIKE '%placeholder%')
        AND (LOWER(name) LIKE '%insulation%' OR LOWER(description) LIKE '%insulation%' OR LOWER(name) LIKE '%insulating%' OR LOWER(description) LIKE '%insulating%' OR LOWER(name) LIKE '%thermal%' OR LOWER(description) LIKE '%thermal%' OR LOWER(name) LIKE '%acoustic%' OR LOWER(description) LIKE '%acoustic%')
        LIMIT 500  -- Process in batches to avoid timeouts
    );

-- Log the update
DO $$
DECLARE
    updated_count INTEGER;
BEGIN
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RAISE NOTICE 'Updated % products with % image', updated_count, 'insulation';
END $$;


-- Map Panels products
UPDATE uses_products
SET 
    image_url = 'https://ktoqznqmlnxrtvubewyz.supabase.co/storage/v1/object/public/product-images/categories/hemp_building_construction_panels.jpg',
    updated_at = CURRENT_TIMESTAMP
WHERE 
    (image_url IS NULL OR image_url = '' OR image_url LIKE '%placeholder%')
    AND (LOWER(name) LIKE '%panel%' OR LOWER(description) LIKE '%panel%' OR LOWER(name) LIKE '%board%' OR LOWER(description) LIKE '%board%' OR LOWER(name) LIKE '%sheet%' OR LOWER(description) LIKE '%sheet%' OR LOWER(name) LIKE '%particle%' OR LOWER(description) LIKE '%particle%' OR LOWER(name) LIKE '%fiber board%' OR LOWER(description) LIKE '%fiber board%')
    AND id IN (
        SELECT id FROM uses_products
        WHERE (image_url IS NULL OR image_url = '' OR image_url LIKE '%placeholder%')
        AND (LOWER(name) LIKE '%panel%' OR LOWER(description) LIKE '%panel%' OR LOWER(name) LIKE '%board%' OR LOWER(description) LIKE '%board%' OR LOWER(name) LIKE '%sheet%' OR LOWER(description) LIKE '%sheet%' OR LOWER(name) LIKE '%particle%' OR LOWER(description) LIKE '%particle%' OR LOWER(name) LIKE '%fiber board%' OR LOWER(description) LIKE '%fiber board%')
        LIMIT 500  -- Process in batches to avoid timeouts
    );

-- Log the update
DO $$
DECLARE
    updated_count INTEGER;
BEGIN
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RAISE NOTICE 'Updated % products with % image', updated_count, 'panels';
END $$;


-- Map Skincare products
UPDATE uses_products
SET 
    image_url = 'https://ktoqznqmlnxrtvubewyz.supabase.co/storage/v1/object/public/product-images/categories/hemp_cosmetics_personal_skincare.jpg',
    updated_at = CURRENT_TIMESTAMP
WHERE 
    (image_url IS NULL OR image_url = '' OR image_url LIKE '%placeholder%')
    AND (LOWER(name) LIKE '%skincare%' OR LOWER(description) LIKE '%skincare%' OR LOWER(name) LIKE '%serum%' OR LOWER(description) LIKE '%serum%' OR LOWER(name) LIKE '%moisturizer%' OR LOWER(description) LIKE '%moisturizer%' OR LOWER(name) LIKE '%face%' OR LOWER(description) LIKE '%face%' OR LOWER(name) LIKE '%anti-aging%' OR LOWER(description) LIKE '%anti-aging%')
    AND id IN (
        SELECT id FROM uses_products
        WHERE (image_url IS NULL OR image_url = '' OR image_url LIKE '%placeholder%')
        AND (LOWER(name) LIKE '%skincare%' OR LOWER(description) LIKE '%skincare%' OR LOWER(name) LIKE '%serum%' OR LOWER(description) LIKE '%serum%' OR LOWER(name) LIKE '%moisturizer%' OR LOWER(description) LIKE '%moisturizer%' OR LOWER(name) LIKE '%face%' OR LOWER(description) LIKE '%face%' OR LOWER(name) LIKE '%anti-aging%' OR LOWER(description) LIKE '%anti-aging%')
        LIMIT 500  -- Process in batches to avoid timeouts
    );

-- Log the update
DO $$
DECLARE
    updated_count INTEGER;
BEGIN
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RAISE NOTICE 'Updated % products with % image', updated_count, 'skincare';
END $$;


-- Map Soap products
UPDATE uses_products
SET 
    image_url = 'https://ktoqznqmlnxrtvubewyz.supabase.co/storage/v1/object/public/product-images/categories/hemp_cosmetics_personal_soap.jpg',
    updated_at = CURRENT_TIMESTAMP
WHERE 
    (image_url IS NULL OR image_url = '' OR image_url LIKE '%placeholder%')
    AND (LOWER(name) LIKE '%soap%' OR LOWER(description) LIKE '%soap%' OR LOWER(name) LIKE '%bar%' OR LOWER(description) LIKE '%bar%' OR LOWER(name) LIKE '%wash%' OR LOWER(description) LIKE '%wash%' OR LOWER(name) LIKE '%cleanser%' OR LOWER(description) LIKE '%cleanser%')
    AND id IN (
        SELECT id FROM uses_products
        WHERE (image_url IS NULL OR image_url = '' OR image_url LIKE '%placeholder%')
        AND (LOWER(name) LIKE '%soap%' OR LOWER(description) LIKE '%soap%' OR LOWER(name) LIKE '%bar%' OR LOWER(description) LIKE '%bar%' OR LOWER(name) LIKE '%wash%' OR LOWER(description) LIKE '%wash%' OR LOWER(name) LIKE '%cleanser%' OR LOWER(description) LIKE '%cleanser%')
        LIMIT 500  -- Process in batches to avoid timeouts
    );

-- Log the update
DO $$
DECLARE
    updated_count INTEGER;
BEGIN
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RAISE NOTICE 'Updated % products with % image', updated_count, 'soap';
END $$;


-- Map Haircare products
UPDATE uses_products
SET 
    image_url = 'https://ktoqznqmlnxrtvubewyz.supabase.co/storage/v1/object/public/product-images/categories/hemp_cosmetics_personal_haircare.jpg',
    updated_at = CURRENT_TIMESTAMP
WHERE 
    (image_url IS NULL OR image_url = '' OR image_url LIKE '%placeholder%')
    AND (LOWER(name) LIKE '%shampoo%' OR LOWER(description) LIKE '%shampoo%' OR LOWER(name) LIKE '%conditioner%' OR LOWER(description) LIKE '%conditioner%' OR LOWER(name) LIKE '%hair%' OR LOWER(description) LIKE '%hair%' OR LOWER(name) LIKE '%scalp%' OR LOWER(description) LIKE '%scalp%')
    AND id IN (
        SELECT id FROM uses_products
        WHERE (image_url IS NULL OR image_url = '' OR image_url LIKE '%placeholder%')
        AND (LOWER(name) LIKE '%shampoo%' OR LOWER(description) LIKE '%shampoo%' OR LOWER(name) LIKE '%conditioner%' OR LOWER(description) LIKE '%conditioner%' OR LOWER(name) LIKE '%hair%' OR LOWER(description) LIKE '%hair%' OR LOWER(name) LIKE '%scalp%' OR LOWER(description) LIKE '%scalp%')
        LIMIT 500  -- Process in batches to avoid timeouts
    );

-- Log the update
DO $$
DECLARE
    updated_count INTEGER;
BEGIN
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RAISE NOTICE 'Updated % products with % image', updated_count, 'haircare';
END $$;


-- Map Bioplastic products
UPDATE uses_products
SET 
    image_url = 'https://ktoqznqmlnxrtvubewyz.supabase.co/storage/v1/object/public/product-images/categories/hemp_industrial_materials_bioplastic.jpg',
    updated_at = CURRENT_TIMESTAMP
WHERE 
    (image_url IS NULL OR image_url = '' OR image_url LIKE '%placeholder%')
    AND (LOWER(name) LIKE '%plastic%' OR LOWER(description) LIKE '%plastic%' OR LOWER(name) LIKE '%bioplastic%' OR LOWER(description) LIKE '%bioplastic%' OR LOWER(name) LIKE '%polymer%' OR LOWER(description) LIKE '%polymer%' OR LOWER(name) LIKE '%biodegradable%' OR LOWER(description) LIKE '%biodegradable%')
    AND id IN (
        SELECT id FROM uses_products
        WHERE (image_url IS NULL OR image_url = '' OR image_url LIKE '%placeholder%')
        AND (LOWER(name) LIKE '%plastic%' OR LOWER(description) LIKE '%plastic%' OR LOWER(name) LIKE '%bioplastic%' OR LOWER(description) LIKE '%bioplastic%' OR LOWER(name) LIKE '%polymer%' OR LOWER(description) LIKE '%polymer%' OR LOWER(name) LIKE '%biodegradable%' OR LOWER(description) LIKE '%biodegradable%')
        LIMIT 500  -- Process in batches to avoid timeouts
    );

-- Log the update
DO $$
DECLARE
    updated_count INTEGER;
BEGIN
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RAISE NOTICE 'Updated % products with % image', updated_count, 'bioplastic';
END $$;


-- Map Composites products
UPDATE uses_products
SET 
    image_url = 'https://ktoqznqmlnxrtvubewyz.supabase.co/storage/v1/object/public/product-images/categories/hemp_industrial_materials_composites.jpg',
    updated_at = CURRENT_TIMESTAMP
WHERE 
    (image_url IS NULL OR image_url = '' OR image_url LIKE '%placeholder%')
    AND (LOWER(name) LIKE '%composite%' OR LOWER(description) LIKE '%composite%' OR LOWER(name) LIKE '%compound%' OR LOWER(description) LIKE '%compound%' OR LOWER(name) LIKE '%reinforced%' OR LOWER(description) LIKE '%reinforced%' OR LOWER(name) LIKE '%material%' OR LOWER(description) LIKE '%material%')
    AND id IN (
        SELECT id FROM uses_products
        WHERE (image_url IS NULL OR image_url = '' OR image_url LIKE '%placeholder%')
        AND (LOWER(name) LIKE '%composite%' OR LOWER(description) LIKE '%composite%' OR LOWER(name) LIKE '%compound%' OR LOWER(description) LIKE '%compound%' OR LOWER(name) LIKE '%reinforced%' OR LOWER(description) LIKE '%reinforced%' OR LOWER(name) LIKE '%material%' OR LOWER(description) LIKE '%material%')
        LIMIT 500  -- Process in batches to avoid timeouts
    );

-- Log the update
DO $$
DECLARE
    updated_count INTEGER;
BEGIN
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RAISE NOTICE 'Updated % products with % image', updated_count, 'composites';
END $$;


-- Map Packaging products
UPDATE uses_products
SET 
    image_url = 'https://ktoqznqmlnxrtvubewyz.supabase.co/storage/v1/object/public/product-images/categories/hemp_industrial_materials_packaging.jpg',
    updated_at = CURRENT_TIMESTAMP
WHERE 
    (image_url IS NULL OR image_url = '' OR image_url LIKE '%placeholder%')
    AND (LOWER(name) LIKE '%packaging%' OR LOWER(description) LIKE '%packaging%' OR LOWER(name) LIKE '%container%' OR LOWER(description) LIKE '%container%' OR LOWER(name) LIKE '%package%' OR LOWER(description) LIKE '%package%' OR LOWER(name) LIKE '%wrapper%' OR LOWER(description) LIKE '%wrapper%')
    AND id IN (
        SELECT id FROM uses_products
        WHERE (image_url IS NULL OR image_url = '' OR image_url LIKE '%placeholder%')
        AND (LOWER(name) LIKE '%packaging%' OR LOWER(description) LIKE '%packaging%' OR LOWER(name) LIKE '%container%' OR LOWER(description) LIKE '%container%' OR LOWER(name) LIKE '%package%' OR LOWER(description) LIKE '%package%' OR LOWER(name) LIKE '%wrapper%' OR LOWER(description) LIKE '%wrapper%')
        LIMIT 500  -- Process in batches to avoid timeouts
    );

-- Log the update
DO $$
DECLARE
    updated_count INTEGER;
BEGIN
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RAISE NOTICE 'Updated % products with % image', updated_count, 'packaging';
END $$;


COMMIT;

-- Verify the results
WITH coverage_stats AS (
    SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN image_url IS NOT NULL AND image_url != '' THEN 1 END) as with_images,
        COUNT(CASE WHEN image_url LIKE '%/categories/%' THEN 1 END) as category_images,
        COUNT(CASE WHEN image_url IS NULL OR image_url = '' THEN 1 END) as without_images
    FROM uses_products
)
SELECT 
    total,
    with_images,
    category_images,
    without_images,
    ROUND(with_images::numeric * 100 / total, 2) as coverage_percent,
    ROUND(category_images::numeric * 100 / total, 2) as category_percent
FROM coverage_stats;

-- Show sample of mapped products
SELECT 
    id,
    name,
    image_url,
    plant_part_id,
    industry_sub_category_id
FROM uses_products
WHERE image_url LIKE '%/categories/%'
LIMIT 10;
