#!/bin/bash
# Safe automation starter for Hemp Database with quality controls

echo "======================================"
echo "🚀 HEMP DATABASE SAFE AUTOMATION"
echo "======================================"
echo ""
echo "This runs AI discovery with:"
echo "✅ No duplicate products"
echo "✅ No ** formatting issues"
echo "✅ High-quality entries only"
echo "✅ Automatic cleanup after each batch"
echo "⏰ Runs every hour automatically"
echo ""
echo "Choose how to run:"
echo ""
echo "1. 🟢 Quick Test - Run once now"
echo "2. 🔵 Background - Run continuously"
echo "3. 📺 Screen - Run in screen session"
echo "4. 📊 Check Status"
echo "5. 🛑 Stop Automation"
echo ""
read -p "Select (1-5): " choice

case $choice in
    1)
        echo ""
        echo "🧪 Running one cycle with quality checks..."
        source venv_dedup/bin/activate
        python start_safe_automation.py --run
        ;;
        
    2)
        echo ""
        echo "🚀 Starting safe background automation..."
        echo ""
        # Create logs directory if it doesn't exist
        mkdir -p logs
        
        # Stop any existing automation
        pkill -f start_safe_automation.py 2>/dev/null
        
        # Start in background
        source venv_dedup/bin/activate
        nohup python start_safe_automation.py --continuous > logs/automation_$(date +%Y%m%d_%H%M%S).log 2>&1 &
        
        echo "✅ Safe automation started in background!"
        echo ""
        echo "📋 Commands:"
        echo "  View logs:    tail -f logs/safe_automation_*.log"
        echo "  Check latest: tail -100 logs/safe_automation_*.log | grep -E '(✅|❌|🚀)'"
        echo "  Stop:         pkill -f start_safe_automation.py"
        echo ""
        ;;
        
    3)
        echo ""
        echo "📺 Starting in screen..."
        echo ""
        
        # Check if screen is installed
        if ! command -v screen &> /dev/null; then
            echo "❌ Screen not installed. Install with:"
            echo "   sudo apt-get install screen"
            echo ""
            echo "Or use option 2 for background mode."
            exit 1
        fi
        
        # Stop any existing automation
        screen -X -S hemp-auto quit 2>/dev/null
        
        # Create screen session
        screen -dmS hemp-auto bash -c "source venv_dedup/bin/activate && python start_safe_automation.py --continuous"
        
        echo "✅ Safe automation started in screen!"
        echo ""
        echo "📋 Commands:"
        echo "  View:    screen -r hemp-auto"
        echo "  Detach:  Press Ctrl+A, then D"
        echo "  List:    screen -ls"
        echo "  Stop:    screen -X -S hemp-auto quit"
        echo ""
        ;;
        
    4)
        echo ""
        echo "📊 Checking automation status..."
        echo ""
        
        # Check for running processes
        if pgrep -f start_safe_automation.py > /dev/null; then
            echo "✅ Automation is RUNNING"
            echo ""
            echo "Process details:"
            ps aux | grep start_safe_automation.py | grep -v grep
        else
            echo "❌ Automation is NOT running"
        fi
        
        echo ""
        echo "Recent activity:"
        if [ -f logs/safe_automation_*.log ]; then
            tail -20 logs/safe_automation_*.log | grep -E "(✅|❌|🚀|Added:|Cleanup:|Quality:)"
        fi
        
        echo ""
        echo "Database status:"
        source venv_dedup/bin/activate
        python simple_agent_dashboard.py 2>/dev/null | grep -E "(Total products:|Last Active:|RECOMMENDED)"
        ;;
        
    5)
        echo ""
        echo "🛑 Stopping automation..."
        pkill -f start_safe_automation.py
        screen -X -S hemp-auto quit 2>/dev/null
        echo "✅ Automation stopped"
        ;;
esac