#!/usr/bin/env python3
"""
Quick Agent Status Check
Shows current database agent statistics and health
"""

import requests
import json
from datetime import datetime, timedelta
import os

# Supabase configuration
SUPABASE_URL = "https://ktoqznqmlnxrtvubewyz.supabase.co"
SERVICE_ROLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.hxEiOy6xebv7I4CYJEU49oCEXRvmU6iz2l8Kg-NnvTs"

headers = {
    "apikey": SERVICE_ROLE_KEY,
    "Authorization": f"Bearer {SERVICE_ROLE_KEY}",
    "Content-Type": "application/json"
}

def get_database_stats():
    """Get overall database statistics"""
    response = requests.get(
        f"{SUPABASE_URL}/rest/v1/uses_products",
        headers=headers,
        params={
            "select": "count",
            "limit": "1"
        }
    )
    
    if response.status_code == 200:
        content_range = response.headers.get('content-range', '')
        if '/' in content_range:
            total = content_range.split('/')[-1]
            if total != '*':
                return int(total)
    
    # Fallback: count all products
    response = requests.get(
        f"{SUPABASE_URL}/rest/v1/uses_products",
        headers=headers,
        params={
            "select": "id",
            "order": "id"
        }
    )
    
    if response.status_code == 200:
        return len(response.json())
    
    return 0

def get_agent_activity(hours=24):
    """Get agent activity for the last N hours"""
    cutoff_time = (datetime.now() - timedelta(hours=hours)).isoformat()
    
    response = requests.get(
        f"{SUPABASE_URL}/rest/v1/uses_products",
        headers=headers,
        params={
            "select": "source_agent,created_at,confidence_score",
            "created_at": f"gte.{cutoff_time}",
            "order": "created_at.desc"
        }
    )
    
    if response.status_code != 200:
        return []
    
    return response.json()

def analyze_agent_stats(products):
    """Analyze agent statistics from product list"""
    agent_stats = {}
    
    for product in products:
        agent = product.get('source_agent', 'Unknown')
        if agent not in agent_stats:
            agent_stats[agent] = {
                'count': 0,
                'last_active': None,
                'total_confidence': 0
            }
        
        agent_stats[agent]['count'] += 1
        agent_stats[agent]['total_confidence'] += product.get('confidence_score', 0) or 0
        
        created_at = product.get('created_at')
        if created_at and (not agent_stats[agent]['last_active'] or created_at > agent_stats[agent]['last_active']):
            agent_stats[agent]['last_active'] = created_at
    
    # Calculate averages
    for agent, stats in agent_stats.items():
        if stats['count'] > 0:
            stats['avg_confidence'] = round(stats['total_confidence'] / stats['count'], 2)
        else:
            stats['avg_confidence'] = 0
    
    return agent_stats

def format_time_ago(timestamp):
    """Format timestamp as time ago"""
    if not timestamp:
        return "Never"
    
    dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00').replace('+00:00', ''))
    now = datetime.now()
    diff = now - dt.replace(tzinfo=None)
    
    if diff.total_seconds() < 60:
        return "Just now"
    elif diff.total_seconds() < 3600:
        mins = int(diff.total_seconds() / 60)
        return f"{mins} min{'s' if mins != 1 else ''} ago"
    elif diff.total_seconds() < 86400:
        hours = int(diff.total_seconds() / 3600)
        return f"{hours} hour{'s' if hours != 1 else ''} ago"
    else:
        days = int(diff.total_seconds() / 86400)
        return f"{days} day{'s' if days != 1 else ''} ago"

def main():
    print("="*60)
    print("HEMP DATABASE AGENT STATUS CHECK")
    print("="*60)
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
    
    # Get total database count
    total_products = get_database_stats()
    print(f"Total Products in Database: {total_products:,}")
    print(f"Progress to 10K Goal: {total_products/100:.1f}%")
    print(f"Progress to 5K Milestone: {total_products/50:.1f}%\n")
    
    # Get agent activity
    print("Agent Activity (Last 24 Hours):")
    print("-" * 60)
    
    products_24h = get_agent_activity(24)
    if not products_24h:
        print("No agent activity in the last 24 hours")
    else:
        agent_stats = analyze_agent_stats(products_24h)
        
        # Prepare table data
        table_data = []
        for agent, stats in sorted(agent_stats.items(), key=lambda x: x[1]['count'], reverse=True):
            if agent and agent != 'None':
                table_data.append([
                    agent[:40],  # Truncate long names
                    stats['count'],
                    stats['avg_confidence'],
                    format_time_ago(stats['last_active'])
                ])
        
        if table_data:
            # Print header
            print(f"{'Agent Name':<40} {'Products':>10} {'Avg Conf':>10} {'Last Active':>15}")
            print("-" * 80)
            
            # Print data
            for row in table_data:
                print(f"{row[0]:<40} {row[1]:>10} {row[2]:>10} {row[3]:>15}")
            
            print(f"\nTotal Products Added (24h): {len(products_24h)}")
        
    # Check for active processes
    print("\n" + "-" * 60)
    print("Checking for Active Processes:")
    
    # Check if automation is running
    if os.path.exists("/tmp/hemp_automation.lock"):
        print("✅ Automation lock file found - agents may be running")
    else:
        print("❌ No automation lock file - agents likely not running")
    
    # Recent activity summary
    print("\nAgent Performance Summary:")
    if products_24h:
        products_1h = [p for p in products_24h if 
                      (datetime.now() - datetime.fromisoformat(p['created_at'].replace('Z', '+00:00').replace('+00:00', '').replace('+00:00', ''))).total_seconds() < 3600]
        products_6h = [p for p in products_24h if 
                      (datetime.now() - datetime.fromisoformat(p['created_at'].replace('Z', '+00:00').replace('+00:00', '').replace('+00:00', ''))).total_seconds() < 21600]
        
        print(f"- Last 1 hour: {len(products_1h)} products")
        print(f"- Last 6 hours: {len(products_6h)} products")
        print(f"- Last 24 hours: {len(products_24h)} products")
        
        # Calculate growth rate
        if len(products_24h) > 0:
            daily_rate = len(products_24h)
            days_to_5k = max(0, (5000 - total_products) / daily_rate)
            days_to_10k = max(0, (10000 - total_products) / daily_rate)
            
            print(f"\nProjected Timeline (at current rate):")
            print(f"- Days to 5K: {days_to_5k:.1f}")
            print(f"- Days to 10K: {days_to_10k:.1f}")
    
    print("\n" + "="*60)

if __name__ == "__main__":
    main()