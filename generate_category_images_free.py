#!/usr/bin/env python3
"""
Free Alternative: Generate Category Images Using Placeholder Service
Cost: $0.00
"""

import os
import json
import requests
from PIL import Image, ImageDraw, ImageFont
import io

def create_placeholder_image(text, category, filename):
    """Create a nice placeholder image with text"""
    # Create image
    width, height = 800, 800
    
    # Category colors
    colors = {
        'food_beverages': '#4CAF50',      # Green
        'health_wellness': '#2196F3',      # Blue
        'textiles_fashion': '#9C27B0',     # Purple
        'building_construction': '#FF9800', # Orange
        'cosmetics_personal': '#E91E63',   # Pink
        'industrial_materials': '#607D8B'  # Blue Grey
    }
    
    bg_color = colors.get(category, '#795548')
    
    # Create image with gradient-like effect
    img = Image.new('RGB', (width, height), bg_color)
    draw = ImageDraw.Draw(img)
    
    # Add subtle gradient effect
    for i in range(height):
        alpha = i / height
        r, g, b = tuple(int(c * (0.7 + 0.3 * alpha)) for c in img.getpixel((0, 0)))
        draw.rectangle([(0, i), (width, i+1)], fill=(r, g, b))
    
    # Add hemp leaf icon (simple representation)
    leaf_color = (255, 255, 255, 180)
    center_x, center_y = width // 2, height // 2 - 50
    
    # Draw simple hemp leaf shape
    points = [
        (center_x, center_y - 100),
        (center_x - 30, center_y - 60),
        (center_x - 50, center_y - 20),
        (center_x - 30, center_y + 20),
        (center_x, center_y + 40),
        (center_x + 30, center_y + 20),
        (center_x + 50, center_y - 20),
        (center_x + 30, center_y - 60)
    ]
    draw.polygon(points, fill=leaf_color)
    
    # Add text
    try:
        # Try to use a nice font if available
        font_large = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf", 40)
        font_small = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf", 24)
    except:
        # Fallback to default font
        font_large = ImageFont.load_default()
        font_small = ImageFont.load_default()
    
    # Draw text
    text_lines = text.split(' - ')
    y_offset = center_y + 100
    
    for line in text_lines:
        bbox = draw.textbbox((0, 0), line, font=font_large)
        text_width = bbox[2] - bbox[0]
        text_x = (width - text_width) // 2
        draw.text((text_x, y_offset), line, fill='white', font=font_large)
        y_offset += 60
    
    # Add "Hemp Product" subtitle
    subtitle = "Premium Hemp Product"
    bbox = draw.textbbox((0, 0), subtitle, font=font_small)
    text_width = bbox[2] - bbox[0]
    draw.text(((width - text_width) // 2, height - 100), subtitle, fill='white', font=font_small)
    
    # Save image
    os.makedirs('generated_category_images', exist_ok=True)
    filepath = f'generated_category_images/{filename}'
    img.save(filepath, 'JPEG', quality=95)
    
    return filepath

def use_placeholder_service(text, filename):
    """Use free placeholder service as backup"""
    # Using placeholder.com service (free)
    url = f"https://via.placeholder.com/800x800/4CAF50/FFFFFF.jpg?text={text.replace(' ', '+')}"
    
    try:
        response = requests.get(url)
        if response.status_code == 200:
            os.makedirs('generated_category_images', exist_ok=True)
            filepath = f'generated_category_images/{filename}'
            with open(filepath, 'wb') as f:
                f.write(response.content)
            return filepath
    except:
        pass
    return None

def main():
    print("🆓 FREE CATEGORY IMAGE GENERATOR")
    print("=" * 60)
    print("Cost: $0.00 - Using placeholder images")
    print()
    
    # Load the queue
    try:
        with open('phase1_implementation.json', 'r') as f:
            data = json.load(f)
            queue = data['generation_queue']
    except:
        print("❌ Could not load phase1_implementation.json")
        return
    
    print(f"📋 Generating {len(queue)} category placeholder images...")
    print()
    
    results = []
    
    for i, item in enumerate(queue, 1):
        print(f"[{i}/{len(queue)}] Creating: {item['subcategory']}")
        
        # Create nice text for the image
        text = item['subcategory'].replace('_', ' ').title()
        
        # Try custom placeholder first
        try:
            filepath = create_placeholder_image(
                text, 
                item['category'],
                item['filename']
            )
            print(f"✅ Created: {filepath}")
            results.append({
                'category': item['category'],
                'subcategory': item['subcategory'],
                'filename': item['filename'],
                'filepath': filepath,
                'type': 'custom_placeholder'
            })
        except Exception as e:
            # Fallback to web service
            print(f"⚠️  Custom failed, trying web service...")
            filepath = use_placeholder_service(text, item['filename'])
            if filepath:
                print(f"✅ Downloaded: {filepath}")
                results.append({
                    'category': item['category'],
                    'subcategory': item['subcategory'],
                    'filename': item['filename'],
                    'filepath': filepath,
                    'type': 'web_placeholder'
                })
            else:
                print(f"❌ Failed to create placeholder")
    
    # Save results
    with open('generated_category_images/results_free.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print("\n" + "=" * 60)
    print(f"✅ GENERATION COMPLETE")
    print(f"Images created: {len(results)}/{len(queue)}")
    print(f"Total cost: $0.00")
    
    print(f"\n📁 Images saved in: generated_category_images/")
    print(f"📋 Results saved in: generated_category_images/results_free.json")
    
    print("\n🎯 BENEFITS:")
    print("• Immediate visual improvement for 4,000+ products")
    print("• Zero cost")
    print("• Can upgrade to AI images later")
    print("• Better than empty image slots")
    
    print("\n🚀 NEXT STEPS:")
    print("1. Review the generated placeholders")
    print("2. Upload to Supabase storage")
    print("3. Run category_image_mapping.sql")
    print("4. Later: Upgrade to AI images for key categories")

if __name__ == "__main__":
    # Check if PIL is installed
    try:
        import PIL
        main()
    except ImportError:
        print("❌ PIL not installed. Installing...")
        os.system("pip install Pillow")
        print("\n✅ Installed. Please run the script again.")