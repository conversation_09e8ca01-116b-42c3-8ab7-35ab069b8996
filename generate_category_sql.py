#!/usr/bin/env python3
"""
Generate SQL to map category images to products
"""

import json

# Category mapping rules
MAPPING_RULES = {
    'hemp_seeds': ['seed', 'hearts', 'hulled', 'shelled'],
    'hemp_oil_culinary': ['cooking oil', 'culinary oil', 'hemp oil', 'kitchen'],
    'hemp_protein': ['protein', 'powder', 'supplement', 'fitness'],
    'hemp_beverages': ['tea', 'coffee', 'drink', 'beverage', 'milk', 'juice'],
    'cbd_oils': ['cbd', 'cannabinoid', 'tincture', 'extract', 'drops'],
    'topicals': ['balm', 'cream', 'lotion', 'salve', 'ointment', 'rub'],
    'capsules': ['capsule', 'pill', 'tablet', 'softgel', 'supplement'],
    'raw_fiber': ['fiber', 'fibre', 'raw hemp', 'bast'],
    'fabric': ['fabric', 'textile', 'cloth', 'material', 'canvas'],
    'clothing': ['clothing', 'apparel', 'shirt', 'pants', 'dress', 'wear'],
    'hempcrete': ['hempcrete', 'concrete', 'building block', 'construction'],
    'insulation': ['insulation', 'insulating', 'thermal', 'acoustic'],
    'panels': ['panel', 'board', 'sheet', 'particle', 'fiber board'],
    'skincare': ['skincare', 'serum', 'moisturizer', 'face', 'anti-aging'],
    'soap': ['soap', 'bar', 'wash', 'cleanser'],
    'haircare': ['shampoo', 'conditioner', 'hair', 'scalp'],
    'bioplastic': ['plastic', 'bioplastic', 'polymer', 'biodegradable'],
    'composites': ['composite', 'compound', 'reinforced', 'material'],
    'packaging': ['packaging', 'container', 'package', 'wrapper']
}

def generate_mapping_sql():
    """Generate SQL to map products to category images"""
    
    # Base URL for Supabase storage (update after upload)
    base_url = "https://ktoqznqmlnxrtvubewyz.supabase.co/storage/v1/object/public/product-images/categories"
    
    sql_queries = []
    
    # Add header
    sql_queries.append("""-- Category Image Mapping for Hemp Products
-- Generated to provide immediate visual coverage for 4,000+ products
-- Total cost: $0.00 (using placeholder images)

-- First, let's see current image coverage
SELECT 
    COUNT(*) as total_products,
    COUNT(CASE WHEN image_url IS NOT NULL AND image_url != '' THEN 1 END) as with_images,
    COUNT(CASE WHEN image_url IS NULL OR image_url = '' THEN 1 END) as without_images
FROM uses_products;

-- Begin mapping category images
BEGIN;
""")
    
    # Generate mapping for each category
    for subcategory, keywords in MAPPING_RULES.items():
        # Build condition
        conditions = []
        for kw in keywords:
            conditions.append(f"LOWER(name) LIKE '%{kw}%'")
            conditions.append(f"LOWER(description) LIKE '%{kw}%'")
        
        condition_str = " OR ".join(conditions)
        
        # Determine main category
        if subcategory in ['hemp_seeds', 'hemp_oil_culinary', 'hemp_protein', 'hemp_beverages']:
            main_cat = 'food_beverages'
        elif subcategory in ['cbd_oils', 'topicals', 'capsules']:
            main_cat = 'health_wellness'
        elif subcategory in ['raw_fiber', 'fabric', 'clothing']:
            main_cat = 'textiles_fashion'
        elif subcategory in ['hempcrete', 'insulation', 'panels']:
            main_cat = 'building_construction'
        elif subcategory in ['skincare', 'soap', 'haircare']:
            main_cat = 'cosmetics_personal'
        else:
            main_cat = 'industrial_materials'
        
        image_url = f"{base_url}/hemp_{main_cat}_{subcategory}.jpg"
        
        sql_queries.append(f"""
-- Map {subcategory.replace('_', ' ').title()} products
UPDATE uses_products
SET 
    image_url = '{image_url}',
    updated_at = CURRENT_TIMESTAMP
WHERE 
    (image_url IS NULL OR image_url = '' OR image_url LIKE '%placeholder%')
    AND ({condition_str})
    AND id IN (
        SELECT id FROM uses_products
        WHERE (image_url IS NULL OR image_url = '' OR image_url LIKE '%placeholder%')
        AND ({condition_str})
        LIMIT 500  -- Process in batches to avoid timeouts
    );

-- Log the update
DO $$
DECLARE
    updated_count INTEGER;
BEGIN
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RAISE NOTICE 'Updated % products with % image', updated_count, '{subcategory}';
END $$;
""")
    
    # Add commit and verification
    sql_queries.append("""
COMMIT;

-- Verify the results
WITH coverage_stats AS (
    SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN image_url IS NOT NULL AND image_url != '' THEN 1 END) as with_images,
        COUNT(CASE WHEN image_url LIKE '%/categories/%' THEN 1 END) as category_images,
        COUNT(CASE WHEN image_url IS NULL OR image_url = '' THEN 1 END) as without_images
    FROM uses_products
)
SELECT 
    total,
    with_images,
    category_images,
    without_images,
    ROUND(with_images::numeric * 100 / total, 2) as coverage_percent,
    ROUND(category_images::numeric * 100 / total, 2) as category_percent
FROM coverage_stats;

-- Show sample of mapped products
SELECT 
    id,
    name,
    image_url,
    plant_part_id,
    industry_sub_category_id
FROM uses_products
WHERE image_url LIKE '%/categories/%'
LIMIT 10;
""")
    
    return '\n'.join(sql_queries)

def main():
    print("📝 GENERATING CATEGORY MAPPING SQL")
    print("=" * 60)
    
    # Generate SQL
    sql = generate_mapping_sql()
    
    # Save to file
    with open('apply_category_images.sql', 'w') as f:
        f.write(sql)
    
    print("✅ SQL generated: apply_category_images.sql")
    
    # Summary
    print("\n📊 MAPPING SUMMARY:")
    print(f"• {len(MAPPING_RULES)} category images")
    print(f"• Covering ~4,000 products without images")
    print(f"• Zero cost (placeholder images)")
    
    print("\n🚀 NEXT STEPS:")
    print("1. Upload images from generated_category_images/ to Supabase")
    print("2. Update the base_url in the SQL if needed")
    print("3. Run the SQL in Supabase SQL Editor")
    print("4. Check quality dashboard for improved coverage")
    
    print("\n💡 TIP: Even placeholder category images are better than empty slots!")

if __name__ == "__main__":
    main()