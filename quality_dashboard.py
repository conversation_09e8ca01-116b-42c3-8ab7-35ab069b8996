#!/usr/bin/env python3
"""
Hemp Database Quality Monitoring Dashboard
Shows real-time quality metrics and improvement tracking
"""

import os
import psycopg2
from datetime import datetime
from dotenv import load_dotenv
from urllib.parse import urlparse

# Load environment variables
load_dotenv()

def get_db_connection():
    """Create database connection"""
    database_url = os.getenv('DATABASE_URL')
    if not database_url:
        print("❌ Error: DATABASE_URL not found in environment variables")
        return None
    
    # Parse the database URL
    result = urlparse(database_url)
    
    conn = psycopg2.connect(
        database=result.path[1:],
        user=result.username,
        password=result.password,
        host=result.hostname,
        port=result.port
    )
    return conn

def display_dashboard():
    """Display quality metrics dashboard"""
    conn = get_db_connection()
    if not conn:
        return
    
    cur = conn.cursor()
    
    # Clear screen
    os.system('clear' if os.name == 'posix' else 'cls')
    
    print("=" * 80)
    print("🌿 HEMP DATABASE QUALITY DASHBOARD".center(80))
    print("=" * 80)
    print(f"\n📅 Report Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Get main quality metrics
    cur.execute("SELECT * FROM quality_metrics_dashboard")
    metrics = cur.fetchone()
    
    if metrics:
        print("\n📊 OVERALL METRICS:")
        print("-" * 80)
        print(f"  Total Products: {metrics[1]:,}")
        print(f"  Average Quality Score: {metrics[2]}/100")
        print(f"  High Quality Products (75+): {metrics[3]:,} ({metrics[3]/metrics[1]*100:.1f}%)")
        print(f"  Medium Quality Products (50-74): {metrics[4]:,} ({metrics[4]/metrics[1]*100:.1f}%)")
        print(f"  Low Quality Products (<50): {metrics[5]:,} ({metrics[5]/metrics[1]*100:.1f}%)")
        
        print("\n📷 IMAGE COVERAGE:")
        print("-" * 80)
        print(f"  Products with Images: {metrics[6]:,} ({metrics[10]}%)")
        print(f"  Missing Images: {metrics[1] - metrics[6]:,} ({100 - float(metrics[10]):.2f}%)")
        
        print("\n📈 COMMERCIALIZATION STAGES:")
        print("-" * 80)
        print(f"  Products with Stages: {metrics[7]:,} ({metrics[11]}%)")
        print(f"  Missing Stages: {metrics[1] - metrics[7]:,} ({100 - float(metrics[11]):.2f}%)")
        
        print("\n🏢 COMPANY ASSOCIATIONS:")
        print("-" * 80)
        print(f"  Linked Companies: {metrics[8]:,}")
        print(f"  Products without Companies: {metrics[1] - metrics[8]:,}")
    
    # Get top industries by quality
    print("\n🏭 TOP INDUSTRIES BY QUALITY:")
    print("-" * 80)
    print(f"{'Industry':<40} {'Products':<10} {'Avg Score':<12} {'Images':<10}")
    print("-" * 80)
    
    cur.execute("""
        SELECT industry_name, product_count, avg_quality_score, image_coverage_percent 
        FROM industry_quality_metrics 
        ORDER BY avg_quality_score DESC 
        LIMIT 5
    """)
    
    for row in cur.fetchall():
        print(f"{row[0]:<40} {row[1]:<10} {row[2]:<12} {row[3]}%")
    
    # Get products needing immediate attention
    print("\n🚨 PRODUCTS NEEDING IMMEDIATE ATTENTION:")
    print("-" * 80)
    
    cur.execute("""
        SELECT primary_issue, COUNT(*) as count 
        FROM products_needing_enrichment 
        WHERE priority = 'high'
        GROUP BY primary_issue
        ORDER BY count DESC
    """)
    
    for row in cur.fetchall():
        issue_name = row[0].replace('_', ' ').title()
        print(f"  {issue_name}: {row[1]:,} products")
    
    # Check for potential duplicates
    print("\n🔍 DUPLICATE CHECK:")
    print("-" * 80)
    
    cur.execute("SELECT COUNT(*) FROM check_potential_duplicates()")
    dup_count = cur.fetchone()[0]
    print(f"  Potential Duplicates Found: {dup_count}")
    
    # Improvement summary
    print("\n✨ RECENT IMPROVEMENTS:")
    print("-" * 80)
    print("  ✅ Standardized commercialization stages (1,146 products)")
    print("  ✅ Merged duplicate companies")
    print("  ✅ Fixed data integrity issues")
    
    print("\n🎯 NEXT PRIORITIES:")
    print("-" * 80)
    print("  1. Generate missing images (5,072 products)")
    print("  2. Assign commercialization stages (5,011 products)")
    print("  3. Enhance short descriptions (361 products)")
    
    print("\n" + "=" * 80)
    
    conn.close()

if __name__ == "__main__":
    display_dashboard()