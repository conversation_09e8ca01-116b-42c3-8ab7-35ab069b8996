#!/usr/bin/env python3
"""
Simple template fixer using direct connection
"""

import psycopg2
import sys

# Direct connection string
DATABASE_URL = "host=db.ktoqznqmlnxrtvubewyz.supabase.co port=5432 dbname=postgres user=postgres password=$4HQZgassmo sslmode=require"

def generate_description(name, industry_id):
    """Generate meaningful description based on product name"""
    name_lower = name.lower()
    
    if "protein" in name_lower:
        return "High-quality plant-based protein product derived from hemp seeds. Contains all essential amino acids making it a complete protein source. Rich in minerals, fiber, and omega fatty acids. Sustainably produced with minimal environmental impact."
    elif "oil" in name_lower:
        return "Premium hemp-derived oil product extracted using sustainable methods. Features ideal omega-3 to omega-6 ratio for optimal health benefits. Versatile applications from nutritional supplements to industrial uses."
    elif "fiber" in name_lower or "textile" in name_lower or "fabric" in name_lower:
        return "Advanced hemp fiber material offering superior strength and sustainability. Natural antimicrobial and UV-resistant properties ensure long-lasting performance. Breathable and biodegradable."
    elif "concrete" in name_lower or "hempcrete" in name_lower or "building" in name_lower:
        return "Innovative hemp-based construction material combining sustainability with performance. Provides excellent thermal and acoustic properties while being fire-resistant. Carbon-negative material."
    elif "insulation" in name_lower:
        return "High-performance hemp insulation providing excellent thermal resistance. Naturally resistant to mold, pests, and fire without chemical treatments. Helps regulate indoor humidity."
    elif "food" in name_lower or "edible" in name_lower or "granola" in name_lower or "milk" in name_lower:
        return "Nutritious hemp-based food product rich in essential nutrients. Offers unique nutritional benefits with pleasant nutty flavor. Sustainably grown and minimally processed."
    elif "cosmetic" in name_lower or "skincare" in name_lower or "lotion" in name_lower or "shampoo" in name_lower:
        return "Luxurious hemp-infused beauty product harnessing essential fatty acids and antioxidants. Provides deep moisturization suitable for all skin types. Free from harsh chemicals."
    elif "paper" in name_lower:
        return "Sustainable hemp paper product that's stronger and longer-lasting than tree-based alternatives. Naturally acid-free with less environmental impact in production."
    elif "plastic" in name_lower or "bioplastic" in name_lower:
        return "Revolutionary hemp-based bioplastic offering comparable performance to petroleum plastics. Fully biodegradable and compostable, reducing environmental footprint."
    elif "seed" in name_lower:
        return "Premium hemp seeds packed with complete protein, omega fatty acids, and minerals. Versatile superfood with nutty flavor perfect for various culinary applications."
    elif "rope" in name_lower:
        return "Durable natural hemp rope offering excellent tensile strength and weather resistance. Biodegradable and stronger when wet, ideal for marine and outdoor applications."
    elif "clothing" in name_lower or "shirt" in name_lower or "jeans" in name_lower or "shoe" in name_lower:
        return "Sustainable hemp clothing combining comfort with environmental consciousness. Naturally breathable, antimicrobial, and becomes softer with each wash."
    elif "pet" in name_lower:
        return "Natural hemp pet product providing safe and sustainable solutions for animal care. Hypoallergenic and environmentally friendly alternative to synthetic materials."
    else:
        return f"Innovative hemp-based product leveraging the unique properties of hemp including strength, sustainability, and versatility. Manufactured using environmentally responsible processes."

print("Connecting to database...")
try:
    conn = psycopg2.connect(DATABASE_URL)
    cur = conn.cursor()
    
    # Get template products
    cur.execute("""
        SELECT id, name, industry_sub_category_id
        FROM uses_products
        WHERE description LIKE '{%'
           OR description LIKE '%cultural%'
           OR description LIKE '%traditional%'
        ORDER BY id
    """)
    
    products = cur.fetchall()
    total = len(products)
    print(f"Found {total} products to fix")
    
    updated = 0
    for product_id, name, industry_id in products:
        description = generate_description(name, industry_id or 1)
        
        cur.execute("""
            UPDATE uses_products 
            SET description = %s, updated_at = NOW()
            WHERE id = %s
        """, (description, product_id))
        
        updated += 1
        if updated % 50 == 0:
            conn.commit()
            print(f"Updated {updated}/{total} ({updated/total*100:.1f}%)")
    
    conn.commit()
    print(f"\nSuccessfully updated {updated} descriptions!")
    
except Exception as e:
    print(f"Error: {e}")
    sys.exit(1)
finally:
    if 'conn' in locals():
        conn.close()