#!/usr/bin/env python3
"""
Classify companies by type based on their names and descriptions
"""

import os
import sys
from supabase import create_client, Client
from datetime import datetime
import re

# Initialize Supabase client
url = os.environ.get("VITE_SUPABASE_URL")
key = os.environ.get("SUPABASE_SERVICE_ROLE_KEY")

if not url or not key:
    print("Error: Missing environment variables")
    sys.exit(1)

supabase: Client = create_client(url, key)

def get_companies_without_type():
    """Get companies that don't have a type"""
    try:
        response = supabase.table('hemp_companies').select('*').is_('company_type', 'null').execute()
        return response.data
    except Exception as e:
        print(f"Error getting companies: {e}")
        return []

def classify_company_type(company):
    """Classify company type based on name and description"""
    name = company.get('name', '').lower()
    desc = company.get('description', '').lower()
    combined = f"{name} {desc}"
    
    # Type patterns
    type_patterns = {
        'manufacturer': [
            'manufactur', 'produc', 'make', 'creat', 'build', 'construct',
            'process', 'extract', 'refin', 'mill', 'press', 'industrial',
            'factory', 'plant', 'facilit', 'operation'
        ],
        'brand': [
            'brand', 'label', 'collection', 'line', 'series', 'signature',
            'premium', 'artisan', 'boutique', 'luxury', 'organic', 'natural',
            'wellness', 'lifestyle', 'care', 'beauty', 'skincare'
        ],
        'distributor': [
            'distribut', 'wholesale', 'supplier', 'vendor', 'dealer',
            'import', 'export', 'trade', 'supply', 'logistics'
        ],
        'retailer': [
            'retail', 'store', 'shop', 'market', 'outlet', 'boutique',
            'e-commerce', 'online', 'direct', 'consumer'
        ],
        'grower': [
            'grow', 'farm', 'cultivat', 'harvest', 'agricultur', 'crop',
            'field', 'greenhouse', 'garden', 'plantation'
        ],
        'processor': [
            'process', 'extract', 'refin', 'decorticat', 'fiber', 'oil',
            'cbd', 'cannabinoid', 'isolat', 'distillat'
        ],
        'technology': [
            'tech', 'technolog', 'innovat', 'solution', 'system', 'software',
            'platform', 'digital', 'smart', 'advanced', 'research', 'develop'
        ],
        'contractor': [
            'contract', 'build', 'construct', 'install', 'service',
            'consult', 'architect', 'engineer', 'design'
        ],
        'organization': [
            'institute', 'association', 'foundation', 'society', 'council',
            'coalition', 'alliance', 'network', 'community', 'collective'
        ]
    }
    
    # Known company classifications
    known_types = {
        # Strains/Cultivars are brands
        'bubba kush': 'brand',
        'cherry wine': 'brand', 
        'hawaiian haze': 'brand',
        'sour space': 'brand',
        'candy hemp': 'brand',
        
        # Generic companies
        'generic': 'manufacturer',
        'membrane-filtered': 'technology',
        'full spectrum': 'brand',
        
        # Specific known companies
        'florrent': 'technology',
        'honeywell': 'technology',
        'volkswagen': 'manufacturer',
        'nature\'s path': 'brand',
        
        # Product-named companies
        'hemp hearts': 'brand',
        'hempcrete panel': 'manufacturer',
        'hempcrete insulation': 'manufacturer',
        'hempcreteacousticpanels': 'manufacturer',
        'hempseedproteinisolates': 'processor',
        
        # Wellness/Beauty brands
        'aurabloom': 'brand',
        'auraboost': 'brand',
        'dermabloom': 'brand',
        'sonobloom': 'brand',
        'aromasculpt': 'brand',
        'auramist': 'brand',
        'aurasync': 'brand',
        'cannacalm': 'brand',
        'cannafocus': 'brand',
        'cannasleep': 'brand',
        'dermarenew': 'brand',
        'hempease': 'brand',
        'hempfiberskin': 'brand',
        'hemphydrate': 'brand',
        'hemprenew': 'brand',
        'hemproot': 'brand',
        'hempshine': 'brand',
        'mindful mist': 'brand',
        'musclemend': 'brand',
        'night bloom': 'brand',
        'rejuvehemp': 'brand',
        'reliefrise': 'brand',
        'respireeasy': 'brand',
        'sleepscape': 'brand',
        'sleepsound': 'brand',
        'sonocalm': 'brand',
        'terpfusion': 'brand',
        'tranquil': 'brand',
        'aura balance': 'brand',
        'aurashine': 'brand',
        'breatheeasy': 'brand',
        'dermahemp': 'brand',
        'focusflow': 'brand',
        'hempleaf': 'brand',
        'hempzen': 'brand',
        'moodscape collection': 'brand',
        'rejuveskin': 'brand',
        'skinrenew': 'brand',
        'sleepwell': 'brand',
        'terpenefusion': 'brand',
        'cannabloom': 'brand',
        'neurobloom': 'brand',
        
        # Chemical compounds as brands
        'cbda': 'brand',
        'thcv': 'brand',
        'cbn': 'brand',
        
        # Specific products as companies
        'terpene-enhanced muscle rub': 'brand',
        'bionoid': 'technology',
        'hempearth': 'manufacturer',
        'hempsmith': 'retailer',
        'hempful farms': 'grower'
    }
    
    # Check known types first
    for known_name, known_type in known_types.items():
        if known_name in name:
            return known_type
    
    # Score each type based on pattern matches
    type_scores = {}
    
    for company_type, patterns in type_patterns.items():
        score = 0
        for pattern in patterns:
            if pattern in combined:
                score += 1
        if score > 0:
            type_scores[company_type] = score
    
    # Return the type with highest score
    if type_scores:
        best_type = max(type_scores.items(), key=lambda x: x[1])
        return best_type[0]
    
    # Default classifications based on name patterns
    if any(word in name for word in ['cbd', 'cbg', 'thc', 'terpene', 'cannabinoid']):
        return 'brand'
    
    if 'hemp' in name and any(word in name for word in ['seed', 'oil', 'protein', 'heart']):
        return 'brand'
    
    # Default to brand for unclassified
    return 'brand'

def update_company_type(company_id, company_type):
    """Update company with type"""
    try:
        response = supabase.table('hemp_companies').update({
            'company_type': company_type,
            'updated_at': datetime.utcnow().isoformat()
        }).eq('id', company_id).execute()
        return True
    except Exception as e:
        print(f"Error updating company: {e}")
        return False

def main():
    print("🏢 Company Type Classification")
    print("=" * 60)
    
    # Get companies without type
    companies = get_companies_without_type()
    print(f"\nFound {len(companies)} companies without type classification")
    
    if not companies:
        print("All companies already have types!")
        return
    
    # Classify companies
    classified_count = 0
    type_counts = {}
    
    for company in companies:
        name = company['name']
        company_type = classify_company_type(company)
        
        print(f"\n{name}")
        print(f"  → Type: {company_type}")
        
        if update_company_type(company['id'], company_type):
            classified_count += 1
            type_counts[company_type] = type_counts.get(company_type, 0) + 1
        else:
            print(f"  ❌ Failed to update")
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 CLASSIFICATION SUMMARY")
    print("=" * 60)
    print(f"Total companies classified: {classified_count}")
    
    if type_counts:
        print("\nClassification breakdown:")
        for company_type, count in sorted(type_counts.items(), key=lambda x: x[1], reverse=True):
            print(f"  - {company_type}: {count}")
    
    print(f"\n✅ Successfully classified {classified_count} companies!")

if __name__ == "__main__":
    main()