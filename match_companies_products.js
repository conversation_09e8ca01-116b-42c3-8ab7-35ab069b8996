import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load .env
dotenv.config({ path: join(__dirname, '.env') });

const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Industry-specific company databases
const industryCompanies = {
  textiles: ['Patagonia', "Levi's", 'HempFlax', 'EnviroTextiles', 'Hemp Fortex', 'Jungmaven', 'WAMA', 'prAna'],
  food: ['Manitoba Harvest', 'Nutiva', 'Navitas Organics', "Bob's Red Mill", '365 Whole Foods', 'Living Harvest'],
  construction: ['HempWood', 'Hempcrete Natural Building', 'IsoHemp', 'Hemp Block USA', 'Hempitecture', 'Just BioFiber'],
  cosmetics: ["Dr. Bronner's", 'The Body Shop', 'Elixinol', 'Endoca', "Kiehl's"],
  automotive: ['BMW', 'Mercedes-Benz', 'Ford', 'Porsche', 'Volkswagen'],
  paper: ['Hemp Press', 'Tree Free Hemp', 'Living Tree Paper', 'Hemp Garden'],
};

// Product keywords to industry mapping
const productKeywords = {
  textiles: ['fabric', 'textile', 'clothing', 'shirt', 'pants', 'denim', 'canvas', 'underwear', 'apparel'],
  food: ['seed', 'oil', 'protein', 'hearts', 'flour', 'milk', 'butter', 'powder', 'nutrition'],
  construction: ['concrete', 'insulation', 'board', 'block', 'hempcrete', 'hurd', 'panel', 'building'],
  cosmetics: ['cream', 'lotion', 'soap', 'shampoo', 'balm', 'serum', 'skin', 'beauty'],
  automotive: ['composite', 'panel', 'dashboard', 'door panel', 'biocomposite', 'automotive'],
  paper: ['paper', 'pulp', 'cardboard', 'packaging'],
};

// Brand indicators in product names
const brandIndicators = [
  { pattern: /\s+by\s+([A-Z][a-zA-Z\s&']+?)(?:\s*$|\s*[,.])/, type: 'by' },
  { pattern: /\s+from\s+([A-Z][a-zA-Z\s&]+?)(?:\s*$|\s*[,.])/, type: 'from' },
  { pattern: /^([A-Za-z]+[A-Za-z0-9]*)[™®]/, type: 'trademark' },
  { pattern: /^([A-Z][a-zA-Z0-9\s&]+):\s+/, type: 'colon' },
  { pattern: /^([A-Z][a-zA-Z0-9\s&]+)\s+-\s+/, type: 'dash' },
  { pattern: /\(([A-Z][a-zA-Z0-9\s&]+)\)/, type: 'parentheses' },
];

function identifyProductIndustry(product) {
  const productName = product.name?.toLowerCase() || '';
  const productDesc = product.description?.toLowerCase() || '';
  
  const industryScores = {};
  
  // Check product name and description for keywords
  for (const [industry, keywords] of Object.entries(productKeywords)) {
    industryScores[industry] = 0;
    
    for (const keyword of keywords) {
      if (productName.includes(keyword)) {
        industryScores[industry] += 2;
      }
      if (productDesc.includes(keyword)) {
        industryScores[industry] += 1;
      }
    }
  }
  
  // Return industry with highest score
  let maxScore = 0;
  let bestIndustry = null;
  
  for (const [industry, score] of Object.entries(industryScores)) {
    if (score > maxScore) {
      maxScore = score;
      bestIndustry = industry;
    }
  }
  
  return maxScore > 0 ? bestIndustry : null;
}

function extractBrandFromProduct(productName) {
  const brands = [];
  
  for (const { pattern } of brandIndicators) {
    const matches = productName.match(pattern);
    if (matches && matches[1]) {
      const brand = matches[1].trim();
      if (brand.length > 2 && !['Hemp', 'Organic', 'Natural', 'Premium'].includes(brand)) {
        brands.push(brand);
      }
    }
  }
  
  return [...new Set(brands)];
}

function calculateSimilarity(str1, str2) {
  const longer = str1.length > str2.length ? str1 : str2;
  const shorter = str1.length > str2.length ? str2 : str1;
  
  if (longer.length === 0) return 1.0;
  
  const editDistance = levenshteinDistance(longer, shorter);
  return (longer.length - editDistance) / longer.length;
}

function levenshteinDistance(str1, str2) {
  const matrix = [];
  
  for (let i = 0; i <= str2.length; i++) {
    matrix[i] = [i];
  }
  
  for (let j = 0; j <= str1.length; j++) {
    matrix[0][j] = j;
  }
  
  for (let i = 1; i <= str2.length; i++) {
    for (let j = 1; j <= str1.length; j++) {
      if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
        matrix[i][j] = matrix[i - 1][j - 1];
      } else {
        matrix[i][j] = Math.min(
          matrix[i - 1][j - 1] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j] + 1
        );
      }
    }
  }
  
  return matrix[str2.length][str1.length];
}

function fuzzyMatchCompany(brandName, companies) {
  let bestMatch = null;
  let bestScore = 0;
  
  const brandLower = brandName.toLowerCase();
  
  for (const company of companies) {
    const companyName = company.name;
    const companyLower = companyName.toLowerCase();
    
    // Exact match
    if (brandLower === companyLower) {
      return { company, score: 1.0 };
    }
    
    // Calculate similarity
    let similarity = calculateSimilarity(brandLower, companyLower);
    
    // Boost score if one contains the other
    if (brandLower.includes(companyLower) || companyLower.includes(brandLower)) {
      similarity = Math.max(similarity, 0.8);
    }
    
    // Check for common variations
    const variations = [
      brandLower.replace(/\s+/g, ''),
      brandLower.replace(/-/g, ''),
      brandLower.replace(/'s$/g, ''),
      brandLower.replace(/\s+inc$/g, ''),
      brandLower.replace(/\s+llc$/g, ''),
      brandLower.replace(/\s+ltd$/g, ''),
    ];
    
    for (const variation of variations) {
      if (variation === companyLower.replace(/\s+/g, '')) {
        similarity = Math.max(similarity, 0.9);
      }
    }
    
    if (similarity > bestScore) {
      bestScore = similarity;
      bestMatch = company;
    }
  }
  
  return { company: bestMatch, score: bestScore };
}

async function createCompanyProductLink(product, company, relationshipType, matchMethod) {
  try {
    // Check if link already exists
    const { data: existing } = await supabase
      .from('hemp_company_products')
      .select('*')
      .eq('product_id', product.id)
      .eq('company_id', company.id);
    
    if (!existing || existing.length === 0) {
      // Create new link
      await supabase
        .from('hemp_company_products')
        .insert({
          company_id: company.id,
          product_id: product.id,
          relationship_type: relationshipType,
          is_primary: true,
          match_method: matchMethod
        });
    }
    
    // Update product with primary company
    await supabase
      .from('uses_products')
      .update({ primary_company_id: company.id })
      .eq('id', product.id);
      
  } catch (error) {
    console.error(`❌ Error linking product ${product.name} to company ${company.name}: ${error.message}`);
  }
}

async function findOrCreateGenericCompany(name, industry) {
  // Check if it exists
  const { data: existing } = await supabase
    .from('hemp_companies')
    .select('*')
    .eq('name', name);
  
  if (existing && existing.length > 0) {
    return existing[0];
  }
  
  // Create new generic company
  try {
    const { data: result } = await supabase
      .from('hemp_companies')
      .insert({
        name: name,
        description: `Generic company representing various ${industry} manufacturers in the hemp industry.`,
        company_type: 'manufacturer',
        verified: false
      })
      .select();
    
    return result && result.length > 0 ? result[0] : null;
  } catch (error) {
    console.error(`❌ Error creating generic company ${name}: ${error.message}`);
    return null;
  }
}

async function matchProductsToCompanies() {
  console.log('🎯 IMPROVED COMPANY-PRODUCT MATCHING');
  console.log('='.repeat(60));
  
  // Fetch all companies
  console.log('\n📊 Loading companies...');
  const { data: companies, error: companiesError } = await supabase
    .from('hemp_companies')
    .select('*');
  
  if (companiesError) {
    console.error('Error fetching companies:', companiesError);
    return;
  }
  
  console.log(`✅ Loaded ${companies.length} companies`);
  
  // Fetch unmatched products
  console.log('\n📦 Loading unmatched products...');
  const { data: products, error: productsError } = await supabase
    .from('uses_products')
    .select('*')
    .is('primary_company_id', null);
  
  if (productsError) {
    console.error('Error fetching products:', productsError);
    return;
  }
  
  console.log(`✅ Found ${products.length} products without companies`);
  
  // Track statistics
  let matchedCount = 0;
  const industryMatches = {};
  const matchMethods = {};
  
  console.log('\n🔄 Starting matching process...');
  
  for (let i = 0; i < products.length; i++) {
    if (i % 50 === 0) {
      console.log(`Progress: ${i}/${products.length} products processed...`);
    }
    
    const product = products[i];
    const productName = product.name;
    let matched = false;
    
    // Method 1: Extract brand from product name
    const extractedBrands = extractBrandFromProduct(productName);
    
    for (const brand of extractedBrands) {
      const { company, score } = fuzzyMatchCompany(brand, companies);
      
      if (company && score > 0.8) {
        await createCompanyProductLink(product, company, 'manufacturer', `brand_extraction_${score.toFixed(2)}`);
        matchedCount++;
        matchMethods.brand_extraction = (matchMethods.brand_extraction || 0) + 1;
        matched = true;
        break;
      }
    }
    
    if (matched) continue;
    
    // Method 2: Industry-based matching
    const industry = identifyProductIndustry(product);
    
    if (industry && industryCompanies[industry]) {
      const industryCompanyNames = industryCompanies[industry];
      
      // Find these companies in our database
      for (const companyName of industryCompanyNames) {
        const { company, score } = fuzzyMatchCompany(companyName, companies);
        
        if (company && score > 0.9) {
          // Check if product name contains company name
          if (productName.toLowerCase().includes(company.name.toLowerCase())) {
            await createCompanyProductLink(product, company, 'manufacturer', `industry_match_${score.toFixed(2)}`);
            matchedCount++;
            matchMethods.industry_match = (matchMethods.industry_match || 0) + 1;
            industryMatches[industry] = (industryMatches[industry] || 0) + 1;
            matched = true;
            break;
          }
        }
      }
    }
    
    if (matched) continue;
    
    // Method 3: Create generic industry company if no match
    if (industry && !matched) {
      const genericCompanyName = `Generic ${industry.charAt(0).toUpperCase() + industry.slice(1)} Company`;
      const genericCompany = await findOrCreateGenericCompany(genericCompanyName, industry);
      
      if (genericCompany) {
        await createCompanyProductLink(product, genericCompany, 'manufacturer', 'generic_industry');
        matchedCount++;
        matchMethods.generic_industry = (matchMethods.generic_industry || 0) + 1;
      }
    }
  }
  
  // Print summary
  console.log('\n📊 MATCHING SUMMARY');
  console.log('='.repeat(60));
  console.log(`Total products processed: ${products.length}`);
  console.log(`Successfully matched: ${matchedCount} (${(matchedCount/products.length*100).toFixed(1)}%)`);
  
  console.log('\n📈 Match Methods:');
  for (const [method, count] of Object.entries(matchMethods)) {
    console.log(`  ${method}: ${count} matches`);
  }
  
  console.log('\n🏭 Industry Matches:');
  for (const [industry, count] of Object.entries(industryMatches)) {
    console.log(`  ${industry}: ${count} matches`);
  }
}

matchProductsToCompanies().catch(console.error);