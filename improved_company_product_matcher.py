#!/usr/bin/env python3
"""
Improved Company-Product Matcher with fuzzy matching and industry-specific logic
"""
import os
import sys
import re
from difflib import SequenceMatcher
from collections import defaultdict

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from supabase import create_client, Client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Initialize Supabase client
supabase: Client = create_client(
    os.getenv("VITE_SUPABASE_URL"),
    os.getenv("SUPABASE_SERVICE_ROLE_KEY")
)

class ImprovedCompanyProductMatcher:
    def __init__(self):
        # Industry-specific company databases
        self.industry_companies = {
            'textiles': ['Patagonia', 'Levi\'s', 'HempFlax', 'EnviroTextiles', 'Hemp Fortex'],
            'food': ['Manitoba Harvest', 'Nutiva', 'Navitas Organics', '<PERSON>\'s Red Mill', '365 Whole Foods'],
            'construction': ['HempWood', 'Hempcrete Natural Building', 'IsoHemp', 'HempBlock USA'],
            'cosmetics': ['Dr<PERSON> Bron<PERSON>\'s', 'The Body Shop', 'Elixinol', 'Endoca'],
            'automotive': ['BMW', 'Mercedes-Benz', 'Ford', 'Porsche'],
            'paper': ['Hemp Press', 'Tree Free Hemp', 'Living Tree Paper'],
        }
        
        # Product keywords to industry mapping
        self.product_keywords = {
            'textiles': ['fabric', 'textile', 'clothing', 'shirt', 'pants', 'denim', 'canvas'],
            'food': ['seed', 'oil', 'protein', 'hearts', 'flour', 'milk', 'butter'],
            'construction': ['concrete', 'insulation', 'board', 'block', 'hempcrete', 'hurd'],
            'cosmetics': ['cream', 'lotion', 'soap', 'shampoo', 'balm', 'serum'],
            'automotive': ['composite', 'panel', 'dashboard', 'door panel', 'biocomposite'],
            'paper': ['paper', 'pulp', 'cardboard', 'packaging'],
        }
        
        # Known brand indicators in product names
        self.brand_indicators = {
            'by': r'\s+by\s+([A-Z][a-zA-Z\s&\']+?)(?:\s*$|\s*[,.])',
            'from': r'\s+from\s+([A-Z][a-zA-Z\s&]+?)(?:\s*$|\s*[,.])',
            'trademark': r'^([A-Za-z]+[A-Za-z0-9]*)[™®]',
            'colon': r'^([A-Z][a-zA-Z0-9\s&]+):\s+',
            'dash': r'^([A-Z][a-zA-Z0-9\s&]+)\s+-\s+',
            'parentheses': r'\(([A-Z][a-zA-Z0-9\s&]+)\)',
        }
        
    def identify_product_industry(self, product):
        """Identify which industry a product belongs to"""
        product_name = product.get('name', '').lower()
        product_desc = product.get('description', '').lower()
        
        industry_scores = defaultdict(int)
        
        # Check product name and description for keywords
        for industry, keywords in self.product_keywords.items():
            for keyword in keywords:
                if keyword in product_name:
                    industry_scores[industry] += 2
                if keyword in product_desc:
                    industry_scores[industry] += 1
                    
        # Return industry with highest score
        if industry_scores:
            return max(industry_scores.items(), key=lambda x: x[1])[0]
        return None
        
    def extract_brand_from_product(self, product_name):
        """Extract potential brand/company name from product name"""
        brands = []
        
        for indicator_type, pattern in self.brand_indicators.items():
            matches = re.findall(pattern, product_name)
            if matches:
                brands.extend(matches)
                
        # Clean and deduplicate
        cleaned_brands = []
        for brand in brands:
            brand = brand.strip()
            if len(brand) > 2 and brand not in ['Hemp', 'Organic', 'Natural', 'Premium']:
                cleaned_brands.append(brand)
                
        return list(set(cleaned_brands))
        
    def fuzzy_match_company(self, brand_name, companies):
        """Find best matching company using fuzzy matching"""
        best_match = None
        best_score = 0
        
        brand_lower = brand_name.lower()
        
        for company in companies:
            company_name = company['name']
            company_lower = company_name.lower()
            
            # Exact match
            if brand_lower == company_lower:
                return company, 1.0
                
            # Calculate similarity
            similarity = SequenceMatcher(None, brand_lower, company_lower).ratio()
            
            # Boost score if one contains the other
            if brand_lower in company_lower or company_lower in brand_lower:
                similarity = max(similarity, 0.8)
                
            # Check for common variations
            variations = [
                brand_lower.replace(' ', ''),
                brand_lower.replace('-', ''),
                brand_lower.replace('\'s', ''),
                brand_lower.replace(' inc', ''),
                brand_lower.replace(' llc', ''),
                brand_lower.replace(' ltd', ''),
            ]
            
            for variation in variations:
                if variation == company_lower.replace(' ', ''):
                    similarity = max(similarity, 0.9)
                    
            if similarity > best_score:
                best_score = similarity
                best_match = company
                
        return best_match, best_score
        
    def match_products_to_companies(self):
        """Main matching process with improved logic"""
        print("🎯 IMPROVED COMPANY-PRODUCT MATCHING")
        print("=" * 60)
        
        # Fetch all companies
        print("📊 Loading companies...")
        companies_response = supabase.table('hemp_companies').select("*").execute()
        companies = companies_response.data
        print(f"✅ Loaded {len(companies)} companies")
        
        # Fetch unmatched products
        print("\n📦 Loading unmatched products...")
        products_response = supabase.table('uses_products').select("*").is_('primary_company_id', 'null').execute()
        products = products_response.data
        print(f"✅ Found {len(products)} products without companies")
        
        # Track statistics
        matched_count = 0
        industry_matches = defaultdict(int)
        match_methods = defaultdict(int)
        
        print("\n🔄 Starting matching process...")
        
        for i, product in enumerate(products):
            if i % 50 == 0:
                print(f"Progress: {i}/{len(products)} products processed...")
                
            product_name = product['name']
            matched = False
            
            # Method 1: Extract brand from product name
            extracted_brands = self.extract_brand_from_product(product_name)
            
            for brand in extracted_brands:
                company, score = self.fuzzy_match_company(brand, companies)
                
                if company and score > 0.8:  # 80% similarity threshold
                    self._create_company_product_link(product, company, 'manufacturer', f'brand_extraction_{score:.2f}')
                    matched_count += 1
                    match_methods['brand_extraction'] += 1
                    matched = True
                    break
                    
            if matched:
                continue
                
            # Method 2: Industry-based matching
            industry = self.identify_product_industry(product)
            
            if industry and industry in self.industry_companies:
                industry_company_names = self.industry_companies[industry]
                
                # Find these companies in our database
                for company_name in industry_company_names:
                    company, score = self.fuzzy_match_company(company_name, companies)
                    
                    if company and score > 0.9:  # Higher threshold for industry matching
                        # Check if product name contains company name
                        if company['name'].lower() in product_name.lower():
                            self._create_company_product_link(product, company, 'manufacturer', f'industry_match_{score:.2f}')
                            matched_count += 1
                            match_methods['industry_match'] += 1
                            industry_matches[industry] += 1
                            matched = True
                            break
                            
            if matched:
                continue
                
            # Method 3: Create generic industry company if no match
            if industry and not matched:
                generic_company_name = f"Generic {industry.title()} Company"
                generic_company = self._find_or_create_generic_company(generic_company_name, industry)
                
                if generic_company:
                    self._create_company_product_link(product, generic_company, 'manufacturer', 'generic_industry')
                    matched_count += 1
                    match_methods['generic_industry'] += 1
                    
        # Print summary
        print("\n📊 MATCHING SUMMARY")
        print("=" * 60)
        print(f"Total products processed: {len(products)}")
        print(f"Successfully matched: {matched_count} ({matched_count/len(products)*100:.1f}%)")
        
        print("\n📈 Match Methods:")
        for method, count in match_methods.items():
            print(f"  {method}: {count} matches")
            
        print("\n🏭 Industry Matches:")
        for industry, count in industry_matches.items():
            print(f"  {industry}: {count} matches")
            
    def _create_company_product_link(self, product, company, relationship_type, match_method):
        """Create link between product and company"""
        try:
            # Check if link already exists
            existing = supabase.table('hemp_company_products').select("*").eq(
                'product_id', product['id']
            ).eq('company_id', company['id']).execute()
            
            if not existing.data:
                # Create new link
                supabase.table('hemp_company_products').insert({
                    'company_id': company['id'],
                    'product_id': product['id'],
                    'relationship_type': relationship_type,
                    'is_primary': True,
                    'match_method': match_method
                }).execute()
                
            # Update product with primary company
            supabase.table('uses_products').update({
                'primary_company_id': company['id']
            }).eq('id', product['id']).execute()
            
        except Exception as e:
            print(f"❌ Error linking product {product['name']} to company {company['name']}: {e}")
            
    def _find_or_create_generic_company(self, name, industry):
        """Find or create a generic company for an industry"""
        # Check if it exists
        existing = supabase.table('hemp_companies').select("*").eq('name', name).execute()
        
        if existing.data:
            return existing.data[0]
            
        # Create new generic company
        try:
            result = supabase.table('hemp_companies').insert({
                'name': name,
                'description': f'Generic company representing various {industry} manufacturers in the hemp industry.',
                'company_type': 'manufacturer',
                'verified': False
            }).execute()
            
            return result.data[0] if result.data else None
            
        except Exception as e:
            print(f"❌ Error creating generic company {name}: {e}")
            return None

def main():
    matcher = ImprovedCompanyProductMatcher()
    matcher.match_products_to_companies()

if __name__ == "__main__":
    main()