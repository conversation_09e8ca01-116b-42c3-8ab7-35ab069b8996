# Hemp Database Enhancement Implementation Guide

This guide covers the implementation of three critical enhancements based on the January 2025 evaluation report.

## Overview

We've implemented three priority enhancements:

1. **Multi-Use Products Schema** - Enables products to have multiple plant parts and industries
2. **Advanced Deduplication Pipeline** - Multi-stage matching (exact, fuzzy, semantic, component)
3. **Source Provenance & Verification** - Tracks data sources, confidence scores, and verification status

## 1. Multi-Use Products Schema Enhancement

### Problem Solved
Previously, products could only have one plant part and one industry, forcing duplication or loss of nuance for products like hempcrete (uses both hurd and fiber) or hemp plastics (used in automotive and packaging).

### Implementation

#### New Tables
- `product_plant_parts` - Junction table for multiple plant parts per product
- `product_industry_subcategories` - Junction table for multiple industries per product

#### Key Features
- Maintains backward compatibility (existing FKs remain)
- Enforces at least one primary plant part and industry
- Tracks usage percentages and relevance scores
- Includes helper views for easy querying

#### Usage Example
```sql
-- Add multiple plant parts to hempcrete
INSERT INTO product_plant_parts (product_id, plant_part_id, is_primary, usage_percentage, usage_description)
VALUES 
    (1234, 2, TRUE, 70.0, 'Hemp hurd provides the bulk material'),
    (1234, 5, FALSE, 30.0, 'Bast fiber adds structural reinforcement');

-- Query all plant parts for a product
SELECT * FROM product_all_plant_parts WHERE product_id = 1234;
```

## 2. Advanced Deduplication Pipeline

### Problem Solved
The basic fuzzy matching (85% threshold) was catching only obvious duplicates. Many semantic duplicates (same product, different names) were missed.

### Implementation

#### Four-Stage Matching
1. **Exact Match** - Normalized name comparison
2. **Fuzzy Match** - Token sort/set ratio using RapidFuzz
3. **Semantic Match** - Sentence embeddings with cosine similarity
4. **Component Match** - Extracts and compares materials, processes, applications

#### Key Features
- Weighted scoring system
- Caching for embeddings
- Batch processing capabilities
- Automated merge functionality
- Detailed reporting

#### Usage Example
```python
from deduplication.advanced_deduplication_pipeline import AdvancedDeduplicator

# Initialize
dedup = AdvancedDeduplicator(db_url)
dedup.connect()

# Find duplicates for a product
duplicates = dedup.find_duplicates(product_id=1234)

# Merge duplicates
if duplicates:
    duplicate_ids = [d.product_id for d in duplicates if d.similarity_score > 0.9]
    dedup.merge_duplicates(primary_id=1234, duplicate_ids=duplicate_ids)
```

## 3. Source Provenance & Verification

### Problem Solved
95% of products were unverified with no source tracking. Users couldn't assess data reliability or trace information back to sources.

### Implementation

#### New Columns in uses_products
- `source_url` - Where the data came from
- `source_type` - Category of source (manufacturer, research paper, etc.)
- `confidence_score` - Calculated 0-1 score based on source quality and completeness
- `verification_status` - Current verification state
- `canonical_product_id` - Links duplicates to master record

#### Supporting Tables
- `uses_products_versions` - Full audit trail of changes
- `trusted_sources` - Whitelist of reliable sources
- `product_sources` - Multiple sources per product
- `verification_queue` - Human review workflow

#### Confidence Scoring
Automatic calculation based on:
- Source type (40% weight)
- Data completeness (30% weight)
- Verification status (20% weight)
- Data freshness (10% weight)

#### Usage Example
```sql
-- Queue product for verification
SELECT queue_for_verification(1234, 'Low confidence score', 'high');

-- Verify a product
SELECT verify_product(1234, '<EMAIL>', 'Confirmed with manufacturer');

-- Update confidence scores
UPDATE uses_products 
SET confidence_score = calculate_confidence_score(id)
WHERE confidence_score IS NULL;
```

## Installation Steps

### 1. Database Schema Updates

```bash
# Connect to your Supabase project
# Run in SQL Editor:

# First, run the multi-use products schema
\i migrations/multi_use_products_schema.sql

# Then, add source provenance
\i migrations/source_provenance_schema.sql

# Or run the combined script
\i apply_schema_enhancements.sql
```

### 2. Install Python Dependencies

```bash
# For deduplication pipeline
cd src/deduplication
pip install -r requirements.txt
```

### 3. Configure Environment

```bash
# Set your database URL
export DATABASE_URL="********************************/dbname"
```

### 4. Run Initial Migration

```sql
-- Migrate existing data to junction tables
-- This is already included in the migration scripts
-- but verify it ran successfully:

SELECT COUNT(*) FROM product_plant_parts;
SELECT COUNT(*) FROM product_industry_subcategories;
```

## Testing & Verification

### 1. Test Multi-Use Products

```sql
-- Add a product with multiple parts
INSERT INTO product_plant_parts (product_id, plant_part_id, is_primary, usage_percentage)
SELECT 
    id, 
    (SELECT id FROM plant_parts WHERE name = 'Hemp Hurd'),
    FALSE,
    30.0
FROM uses_products 
WHERE name LIKE '%hempcrete%'
LIMIT 1;

-- Verify
SELECT * FROM product_all_plant_parts 
WHERE product_name LIKE '%hempcrete%';
```

### 2. Test Deduplication

```bash
# Run the demo script
python run_deduplication_demo.py
```

### 3. Test Verification System

```sql
-- Check confidence scores
SELECT 
    verification_status,
    COUNT(*) as count,
    AVG(confidence_score) as avg_confidence
FROM uses_products
GROUP BY verification_status;
```

## Maintenance & Operations

### Regular Tasks

1. **Weekly Deduplication Run**
   ```python
   # Run batch deduplication
   python -c "from deduplication.advanced_deduplication_pipeline import AdvancedDeduplicator; 
   dedup = AdvancedDeduplicator(db_url); 
   dedup.connect(); 
   dedup.batch_find_duplicates(1000)"
   ```

2. **Update Confidence Scores**
   ```sql
   -- Run monthly
   UPDATE uses_products 
   SET confidence_score = calculate_confidence_score(id)
   WHERE last_verified_at < CURRENT_DATE - INTERVAL '30 days'
      OR confidence_score IS NULL;
   ```

3. **Process Verification Queue**
   ```sql
   -- Get pending verifications
   SELECT * FROM verification_queue 
   WHERE status = 'pending' 
   ORDER BY priority DESC, queued_at ASC 
   LIMIT 20;
   ```

### Monitoring

```sql
-- Dashboard query
SELECT 
    'Total Products' as metric,
    COUNT(*) as value
FROM uses_products
WHERE verification_status != 'deprecated'
UNION ALL
SELECT 
    'Verified Products',
    COUNT(*)
FROM uses_products
WHERE verification_status = 'verified'
UNION ALL
SELECT 
    'Multi-Part Products',
    COUNT(DISTINCT product_id)
FROM product_plant_parts
WHERE product_id IN (
    SELECT product_id 
    FROM product_plant_parts 
    GROUP BY product_id 
    HAVING COUNT(*) > 1
)
UNION ALL
SELECT 
    'Average Confidence Score',
    ROUND(AVG(confidence_score) * 100)
FROM uses_products
WHERE confidence_score IS NOT NULL;
```

## Troubleshooting

### Common Issues

1. **Duplicate Key Violations**
   - Check for existing entries before inserting
   - Use ON CONFLICT clauses

2. **Slow Semantic Matching**
   - Reduce batch size
   - Use GPU if available
   - Implement better caching

3. **Memory Issues with Embeddings**
   - Process in smaller batches
   - Clear cache periodically
   - Use lighter model (e.g., 'all-MiniLM-L6-v2')

## Future Enhancements

1. **API Integration**
   - Expose deduplication as API endpoint
   - Real-time duplicate checking on insert
   - Webhook notifications for verifications

2. **ML Improvements**
   - Fine-tune embeddings on hemp domain
   - Train custom NER for component extraction
   - Implement active learning for verification

3. **UI Components**
   - Duplicate review interface
   - Verification workflow dashboard
   - Source management tools

## Conclusion

These enhancements address the core data quality issues identified in the evaluation report:

- **Multi-use products** eliminate the need for duplicates
- **Advanced deduplication** catches semantic duplicates
- **Source provenance** enables trust and verification

With these improvements, the Hemp Database can scale to 10,000+ products while maintaining high data quality and user trust.