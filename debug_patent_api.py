#!/usr/bin/env python3
"""
Debug USPTO API to see what's happening
"""

import requests
import json

def test_uspto_api():
    base_url = "https://api.patentsview.org/patents/query"
    
    # Simple test query
    payload = {
        "q": {
            "_text_any": {
                "patent_title": "hemp"
            }
        },
        "f": ["patent_number", "patent_title", "patent_date"],
        "o": {
            "page": 1,
            "per_page": 5
        }
    }
    
    print("Testing USPTO API...")
    print(f"URL: {base_url}")
    print(f"Payload: {json.dumps(payload, indent=2)}")
    
    try:
        response = requests.post(base_url, json=payload)
        print(f"\nStatus Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"\nResponse: {json.dumps(data, indent=2)}")
            
            if 'patents' in data:
                print(f"\nFound {len(data['patents'])} patents")
                for patent in data['patents'][:3]:
                    print(f"- {patent.get('patent_number')}: {patent.get('patent_title')}")
        else:
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"Exception: {e}")

if __name__ == "__main__":
    test_uspto_api()