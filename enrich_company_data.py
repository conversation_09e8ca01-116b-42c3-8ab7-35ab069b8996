#!/usr/bin/env python3
"""
Company Data Enrichment - Improve company data quality by fetching additional information
"""
import os
import sys
import requests
import json
import time
from datetime import datetime
from urllib.parse import urlparse, urljoin
from bs4 import BeautifulSoup
import re

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from supabase import create_client, Client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Initialize Supabase client
supabase: Client = create_client(
    os.getenv("VITE_SUPABASE_URL"),
    os.getenv("SUPABASE_SERVICE_ROLE_KEY")
)

class CompanyDataEnricher:
    def __init__(self):
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        # Known legitimate hemp companies with verified data
        self.verified_companies = {
            'Manitoba Harvest': {
                'website': 'https://manitobaharvest.com',
                'description': 'Leading hemp food manufacturer specializing in hemp hearts, protein powder, and hemp oil. Pioneer in the hemp food industry.',
                'founded_year': 1998,
                'country': 'Canada',
                'company_type': 'manufacturer'
            },
            'Charlotte\'s Web': {
                'website': 'https://www.charlottesweb.com',
                'description': 'Premium hemp extract and CBD wellness products. Known for high-quality, full-spectrum hemp extracts.',
                'founded_year': 2013,
                'country': 'United States',
                'company_type': 'manufacturer'
            },
            'HempFlax': {
                'website': 'https://hempflax.com',
                'description': 'European leader in hemp cultivation and processing. Produces hemp fiber, shives, and seeds for various industries.',
                'founded_year': 1993,
                'country': 'Netherlands',
                'company_type': 'processor'
            },
            'Nutiva': {
                'website': 'https://nutiva.com',
                'description': 'Organic superfoods company offering hemp seeds, hemp protein, and hemp oil products.',
                'founded_year': 1999,
                'country': 'United States',
                'company_type': 'manufacturer'
            },
            'Dr. Bronner\'s': {
                'website': 'https://www.drbronner.com',
                'description': 'Organic and fair trade personal care products, including hemp-based soaps and lotions.',
                'founded_year': 1948,
                'country': 'United States',
                'company_type': 'manufacturer'
            },
            'Bob\'s Red Mill': {
                'website': 'https://www.bobsredmill.com',
                'description': 'Natural foods company offering hemp hearts and hemp protein powder among other whole grain products.',
                'founded_year': 1978,
                'country': 'United States',
                'company_type': 'manufacturer'
            },
            'Elixinol': {
                'website': 'https://elixinol.com',
                'description': 'Global leader in hemp-derived CBD products for wellness and skincare.',
                'founded_year': 2014,
                'country': 'United States',
                'company_type': 'manufacturer'
            },
            'Hemp Inc': {
                'website': 'https://hempinc.com',
                'description': 'Industrial hemp products company focused on processing, manufacturing, and marketing hemp-based products.',
                'founded_year': 2008,
                'country': 'United States',
                'company_type': 'manufacturer'
            }
        }
        
    def extract_company_info_from_website(self, url):
        """Extract company information from their website"""
        try:
            response = requests.get(url, headers=self.headers, timeout=10)
            if response.status_code != 200:
                return None
                
            soup = BeautifulSoup(response.text, 'html.parser')
            info = {}
            
            # Extract meta description
            meta_desc = soup.find('meta', attrs={'name': 'description'})
            if meta_desc and meta_desc.get('content'):
                info['description'] = meta_desc['content'][:500]
                
            # Look for about page
            about_links = soup.find_all('a', href=re.compile(r'about|company|story', re.I))
            if about_links:
                info['has_about_page'] = True
                
            # Extract social media links
            social_patterns = {
                'facebook': r'facebook\.com/[\w\-\.]+',
                'twitter': r'twitter\.com/[\w\-\.]+',
                'linkedin': r'linkedin\.com/company/[\w\-\.]+',
                'instagram': r'instagram\.com/[\w\-\.]+',
            }
            
            for platform, pattern in social_patterns.items():
                links = soup.find_all('a', href=re.compile(pattern, re.I))
                if links:
                    info[f'{platform}_url'] = links[0]['href']
                    
            # Look for contact information
            email_pattern = r'[\w\.-]+@[\w\.-]+\.\w+'
            emails = re.findall(email_pattern, response.text)
            business_emails = [e for e in emails if not e.endswith('.png') and not e.endswith('.jpg')]
            if business_emails:
                info['contact_email'] = business_emails[0]
                
            return info
            
        except Exception as e:
            print(f"Error extracting from {url}: {e}")
            return None
            
    def enrich_company(self, company):
        """Enrich a single company with additional data"""
        enriched_data = {}
        name = company.get('name', '')
        
        # Check if it's a known verified company
        if name in self.verified_companies:
            verified_data = self.verified_companies[name]
            enriched_data.update(verified_data)
            enriched_data['verified'] = True
            print(f"✅ Found verified data for {name}")
            
        # If company has a website, try to extract more info
        website = company.get('website') or enriched_data.get('website')
        if website and not website.startswith('http'):
            website = f'https://{website}'
            
        if website:
            print(f"🌐 Fetching data from {website}...")
            web_info = self.extract_company_info_from_website(website)
            if web_info:
                # Only update description if current one is generic
                current_desc = company.get('description', '')
                if ('is a hemp product brand' in current_desc or 
                    'identified from product names' in current_desc or
                    not current_desc):
                    if web_info.get('description'):
                        enriched_data['description'] = web_info['description']
                        
                # Add social media links
                for key, value in web_info.items():
                    if key.endswith('_url') or key == 'contact_email':
                        enriched_data[key] = value
                        
        # Clean up company type
        if company.get('company_type') == 'Brand' and enriched_data.get('company_type'):
            enriched_data['company_type'] = enriched_data['company_type']
            
        return enriched_data
        
    def find_and_merge_duplicates(self, companies):
        """Find and merge duplicate companies"""
        print("\n🔍 Looking for duplicate companies...")
        
        merged_count = 0
        processed = set()
        
        for i, company1 in enumerate(companies):
            if company1['id'] in processed:
                continue
                
            name1 = company1['name'].lower().strip()
            
            for j, company2 in enumerate(companies[i+1:], i+1):
                if company2['id'] in processed:
                    continue
                    
                name2 = company2['name'].lower().strip()
                
                # Check for exact match or very similar names
                if (name1 == name2 or 
                    name1.replace(' ', '') == name2.replace(' ', '') or
                    (name1 in name2 or name2 in name1) and abs(len(name1) - len(name2)) < 5):
                    
                    print(f"🔄 Merging '{company1['name']}' and '{company2['name']}'")
                    
                    # Merge data (keep the one with more information)
                    merged = self._merge_company_data(company1, company2)
                    
                    # Update the primary company
                    try:
                        supabase.table('hemp_companies').update(merged).eq('id', company1['id']).execute()
                        
                        # Update products to point to primary company
                        supabase.table('hemp_company_products').update({
                            'company_id': company1['id']
                        }).eq('company_id', company2['id']).execute()
                        
                        supabase.table('uses_products').update({
                            'primary_company_id': company1['id']
                        }).eq('primary_company_id', company2['id']).execute()
                        
                        # Delete duplicate
                        supabase.table('hemp_companies').delete().eq('id', company2['id']).execute()
                        
                        processed.add(company2['id'])
                        merged_count += 1
                        
                    except Exception as e:
                        print(f"❌ Error merging companies: {e}")
                        
        print(f"✅ Merged {merged_count} duplicate companies")
        
    def _merge_company_data(self, company1, company2):
        """Merge two company records, keeping the best data"""
        merged = company1.copy()
        
        # Fields where we want to keep non-empty values
        fields_to_merge = [
            'description', 'website', 'country', 'city', 'state_province',
            'founded_year', 'company_type', 'logo_url'
        ]
        
        for field in fields_to_merge:
            if not merged.get(field) and company2.get(field):
                merged[field] = company2[field]
            elif merged.get(field) and company2.get(field):
                # For description, keep the longer one
                if field == 'description' and len(company2[field]) > len(merged[field]):
                    merged[field] = company2[field]
                    
        # Merge verification status (OR operation)
        merged['verified'] = company1.get('verified', False) or company2.get('verified', False)
        
        return merged

def main():
    print("🏢 COMPANY DATA ENRICHMENT")
    print("=" * 60)
    
    enricher = CompanyDataEnricher()
    
    # Fetch all companies
    print("📊 Fetching companies from database...")
    response = supabase.table('hemp_companies').select("*").order('name').execute()
    companies = response.data
    print(f"✅ Found {len(companies)} companies")
    
    # Find and merge duplicates first
    enricher.find_and_merge_duplicates(companies)
    
    # Re-fetch companies after merging
    response = supabase.table('hemp_companies').select("*").order('name').execute()
    companies = response.data
    
    # Enrich companies
    print(f"\n🔧 Enriching {len(companies)} companies...")
    enriched_count = 0
    
    for i, company in enumerate(companies):
        print(f"\n[{i+1}/{len(companies)}] Processing {company['name']}...")
        
        enriched_data = enricher.enrich_company(company)
        
        if enriched_data:
            try:
                # Update company with enriched data
                supabase.table('hemp_companies').update(enriched_data).eq('id', company['id']).execute()
                enriched_count += 1
                print(f"✅ Updated {company['name']}")
                
                # Rate limiting
                time.sleep(0.5)
                
            except Exception as e:
                print(f"❌ Error updating {company['name']}: {e}")
                
    print(f"\n✅ Enriched {enriched_count} companies")
    
    # Generate summary report
    print("\n📊 ENRICHMENT SUMMARY")
    print("=" * 60)
    
    # Re-fetch to get updated data
    response = supabase.table('hemp_companies').select("*").execute()
    companies = response.data
    
    with_websites = len([c for c in companies if c.get('website')])
    with_descriptions = len([c for c in companies if c.get('description') and 
                           'is a hemp product brand' not in c.get('description', '')])
    verified = len([c for c in companies if c.get('verified')])
    
    print(f"Total Companies: {len(companies)}")
    print(f"With Websites: {with_websites} ({with_websites/len(companies)*100:.1f}%)")
    print(f"With Quality Descriptions: {with_descriptions} ({with_descriptions/len(companies)*100:.1f}%)")
    print(f"Verified: {verified} ({verified/len(companies)*100:.1f}%)")

if __name__ == "__main__":
    main()