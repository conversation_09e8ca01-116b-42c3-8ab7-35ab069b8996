-- Apply Schema Enhancements for Hemp Database
-- This script combines all three priority enhancements
-- Run this in Supabase SQL Editor
-- Author: Database Enhancement Team
-- Date: January 2025

-- =====================================================
-- ENHANCEMENT 1: MULTI-USE PRODUCTS (Many-to-Many)
-- =====================================================

\echo 'Creating junction tables for multi-use products...'
\i migrations/multi_use_products_schema.sql

-- =====================================================
-- ENHANCEMENT 2: SOURCE PROVENANCE & VERIFICATION
-- =====================================================

\echo 'Adding source provenance and verification tracking...'
\i migrations/source_provenance_schema.sql

-- =====================================================
-- ENHANCEMENT 3: PRODUCT ALIASES (For Deduplication)
-- =====================================================

\echo 'Creating product aliases table for deduplication...'

-- Product aliases table to store alternate names
CREATE TABLE IF NOT EXISTS product_aliases (
    id SERIAL PRIMARY KEY,
    product_id INTEGER NOT NULL REFERENCES uses_products(id) ON DELETE CASCADE,
    alias_name VARCHAR(255) NOT NULL,
    alias_type VARCHAR(50) DEFAULT 'synonym' CHECK (alias_type IN (
        'synonym',
        'trade_name',
        'brand_name',
        'common_name',
        'scientific_name',
        'abbreviation',
        'translation'
    )),
    language_code VARCHAR(10) DEFAULT 'en',
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    UNIQUE(product_id, alias_name)
);

CREATE INDEX idx_product_aliases_name ON product_aliases(lower(alias_name));
CREATE INDEX idx_product_aliases_product ON product_aliases(product_id);
CREATE INDEX idx_product_aliases_type ON product_aliases(alias_type);

-- =====================================================
-- EXAMPLE DATA AND VERIFICATION QUERIES
-- =====================================================

\echo 'Running verification queries...'

-- Check if all tables were created
SELECT table_name, 
       CASE 
           WHEN table_name IN (
               'product_plant_parts',
               'product_industry_subcategories',
               'uses_products_versions',
               'trusted_sources',
               'product_sources',
               'verification_queue',
               'product_aliases'
           ) THEN '✓ Created'
           ELSE '✗ Missing'
       END as status
FROM information_schema.tables
WHERE table_schema = 'public'
AND table_name IN (
    'product_plant_parts',
    'product_industry_subcategories',
    'uses_products_versions',
    'trusted_sources',
    'product_sources',
    'verification_queue',
    'product_aliases'
)
ORDER BY table_name;

-- Check if columns were added to uses_products
SELECT column_name, data_type
FROM information_schema.columns
WHERE table_schema = 'public'
AND table_name = 'uses_products'
AND column_name IN (
    'source_url',
    'source_type',
    'confidence_score',
    'verification_status',
    'last_verified_at',
    'verified_by',
    'canonical_product_id',
    'data_version'
)
ORDER BY column_name;

-- =====================================================
-- GRANT PERMISSIONS
-- =====================================================

\echo 'Granting permissions...'

-- Grant permissions on new tables
GRANT SELECT, INSERT, UPDATE, DELETE ON product_aliases TO authenticated;
GRANT USAGE ON SEQUENCE product_aliases_id_seq TO authenticated;

-- =====================================================
-- SUMMARY STATISTICS
-- =====================================================

\echo 'Enhancement Summary:'

SELECT 
    'Multi-use Products' as enhancement,
    COUNT(DISTINCT product_id) as products_migrated,
    'product_plant_parts' as table_name
FROM product_plant_parts
UNION ALL
SELECT 
    'Industry Associations' as enhancement,
    COUNT(DISTINCT product_id) as products_migrated,
    'product_industry_subcategories' as table_name
FROM product_industry_subcategories
UNION ALL
SELECT 
    'Verification Queue' as enhancement,
    COUNT(*) as products_migrated,
    'verification_queue' as table_name
FROM verification_queue
WHERE status = 'pending';

\echo 'Schema enhancements completed successfully!'