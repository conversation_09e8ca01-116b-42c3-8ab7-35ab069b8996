#!/bin/bash
# Start Academic Research and Patent Mining Agents for Database Expansion

echo "🚀 Starting Hemp Database Expansion Agents"
echo "========================================"
echo "Target: 10,000 products"
echo "Current: ~1,500 products"
echo "Focus: Underrepresented industries"
echo ""

# Activate virtual environment
source venv_dedup/bin/activate

# Export database URL if not set
if [ -z "$DATABASE_URL" ]; then
    export DATABASE_URL="postgresql://postgres:%<EMAIL>:5432/postgres"
fi

# Create logs directory if it doesn't exist
mkdir -p logs

# Start agents using the mega coordinator
echo "Starting Mega Agent Coordinator (Phase 1)..."
echo "This will run:"
echo "- Academic Research Agent (PubMed mining)"
echo "- Patent Mining Agent (124+ patents)"
echo "- Targeting underrepresented industries"
echo ""

# Run continuously with 30 second breaks between cycles
python src/agents/mega_agent_coordinator_v2.py --continuous 0.5 >> logs/expansion_agents_$(date +%Y%m%d_%H%M%S).log 2>&1 &

PID=$!
echo "Agents started with PID: $PID"
echo ""
echo "Monitor progress with:"
echo "  tail -f logs/expansion_agents_*.log"
echo "  python src/agents/mega_agent_coordinator_v2.py --report"
echo ""
echo "To stop: kill $PID"