-- Company Enrichment SQL Queries
-- Run these via Supabase MCP


-- Add websites for companies based on common patterns
UPDATE hemp_companies SET
    website = CASE 
        WHEN name = 'Elixinol' THEN 'https://elixinol.com'
        WHEN name = 'Manitoba Harvest' THEN 'https://manitobaharvest.com'
        WHEN name = 'Charlotte''s Web' THEN 'https://charlottesweb.com'
        WHEN name = 'CV Sciences' THEN 'https://cvsciences.com'
        WHEN name = 'Nutiva' THEN 'https://nutiva.com'
        WHEN name = 'Dr. Bron<PERSON>''s' THEN 'https://drbronner.com'
        WHEN name = 'Patagonia Provisions' THEN 'https://patagoniaprovisions.com'
        WHEN name = 'Evo Hemp' THEN 'https://evohemp.com'
        WHEN name = 'Hemp Oil Canada' THEN 'https://hempoilcan.com'
        WHEN name = 'Hempz' THEN 'https://hempz.com'
        WHEN name = 'The Body Shop' THEN 'https://thebodyshop.com'
        WHEN name = 'Pacific Hemp' THEN 'https://pacifichemp.com'
        ELSE website
    END,
    updated_at = NOW()
WHERE website IS NULL 
  AND name IN ('Elixinol', 'Manitoba Harvest', 'Charlotte''s Web', 'CV Sciences', 
               'Nutiva', 'Dr. Bronner''s', 'Patagonia Provisions', 'Evo Hemp',
               'Hemp Oil Canada', 'Hempz', 'The Body Shop', 'Pacific Hemp');



-- Add countries for well-known companies
UPDATE hemp_companies SET
    country = CASE 
        WHEN name IN ('Charlotte''s Web', 'CV Sciences', 'Nutiva', 'Dr. Bronner''s', 
                      'Patagonia Provisions', 'Evo Hemp') THEN 'United States'
        WHEN name IN ('Manitoba Harvest', 'Hemp Oil Canada') THEN 'Canada'
        WHEN name = 'Elixinol' THEN 'Australia'
        WHEN name = 'The Body Shop' THEN 'United Kingdom'
        WHEN name LIKE '%USA%' OR name LIKE '%America%' THEN 'United States'
        WHEN name LIKE '%Canada%' OR name LIKE '%Canadian%' THEN 'Canada'
        WHEN name LIKE '%UK%' OR name LIKE '%British%' THEN 'United Kingdom'
        WHEN name LIKE '%Euro%' OR name LIKE '%EU%' THEN 'European Union'
        ELSE country
    END,
    updated_at = NOW()
WHERE country IS NULL;



-- Add founded years for known companies
UPDATE hemp_companies SET
    founded_year = CASE 
        WHEN name = 'Dr. Bronner''s' THEN 1948
        WHEN name = 'The Body Shop' THEN 1976
        WHEN name = 'Manitoba Harvest' THEN 1998
        WHEN name = 'Nutiva' THEN 1999
        WHEN name = 'Hemp Oil Canada' THEN 1998
        WHEN name = 'Charlotte''s Web' THEN 2013
        WHEN name = 'CV Sciences' THEN 2010
        WHEN name = 'Elixinol' THEN 2014
        WHEN name = 'Hempz' THEN 1998
        ELSE founded_year
    END,
    updated_at = NOW()
WHERE founded_year IS NULL
  AND name IN ('Dr. Bronner''s', 'The Body Shop', 'Manitoba Harvest', 'Nutiva',
               'Hemp Oil Canada', 'Charlotte''s Web', 'CV Sciences', 'Elixinol', 'Hempz');



-- Standardize company types to lowercase
UPDATE hemp_companies 
SET company_type = LOWER(company_type)
WHERE company_type != LOWER(company_type);

-- Infer company types from names and descriptions
UPDATE hemp_companies SET
    company_type = CASE
        WHEN description LIKE '%manufacturer%' OR description LIKE '%manufacturing%' 
             OR description LIKE '%produces%' THEN 'manufacturer'
        WHEN description LIKE '%distributor%' OR description LIKE '%wholesale%' THEN 'distributor'
        WHEN description LIKE '%retailer%' OR description LIKE '%retail%' THEN 'retailer'
        WHEN description LIKE '%brand%' AND company_type = 'brand' THEN 'brand'
        WHEN description LIKE '%grower%' OR description LIKE '%cultivation%' THEN 'grower'
        WHEN description LIKE '%research%' OR description LIKE '%laboratory%' THEN 'research'
        ELSE company_type
    END,
    updated_at = NOW()
WHERE company_type IN ('Brand', 'brand');


-- Check results
SELECT COUNT(*) as total,
       COUNT(website) as with_website,
       COUNT(country) as with_country,
       COUNT(founded_year) as with_founded_year
FROM hemp_companies;
