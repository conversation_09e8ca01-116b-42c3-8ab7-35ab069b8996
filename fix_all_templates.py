#!/usr/bin/env python3
"""
Fix all remaining template descriptions in the database
Part of the Hemp Database Quality Improvement Initiative
"""

import os
import psycopg2
from datetime import datetime
import json
import random
from urllib.parse import urlparse
import socket

# Force IPv4 connections
original_getaddrinfo = socket.getaddrinfo
def getaddrinfo_ipv4_only(host, port, family=0, type=0, proto=0, flags=0):
    return original_getaddrinfo(host, port, socket.AF_INET, type, proto, flags)
socket.getaddrinfo = getaddrinfo_ipv4_only

# Database connection
DATABASE_URL = os.getenv('DATABASE_URL')
if not DATABASE_URL:
    print("ERROR: DATABASE_URL environment variable not set")
    exit(1)

# Parse database URL
url = urlparse(DATABASE_URL)
db_config = {
    'host': url.hostname,
    'port': url.port or 5432,
    'database': url.path[1:],
    'user': url.username,
    'password': url.password,
    'sslmode': 'require'
}

def generate_product_description(name, industry_id):
    """Generate a meaningful description based on product name and industry"""
    
    name_lower = name.lower()
    
    # Industry-specific descriptions
    industry_descriptions = {
        1: "consumer goods",
        2: "footwear and accessories", 
        3: "home textiles",
        4: "apparel and fashion",
        5: "industrial textiles",
        6: "housing construction materials",
        7: "commercial building products",
        8: "insulation solutions",
        9: "structural materials",
        10: "breakfast and cereals",
        11: "beverage products",
        12: "nutritional supplements",
        13: "cooking ingredients",
        14: "health foods",
        15: "body care products",
        16: "hair care solutions",
        17: "skincare formulations",
        18: "wellness products",
        19: "medical treatments",
        20: "therapeutic applications",
        21: "automotive components",
        22: "advanced electronics",
        23: "environmental solutions",
        24: "sustainable plastics",
        25: "pharmaceutical ingredients",
        26: "industrial applications",
        27: "packaging materials",
        28: "agricultural products",
        29: "pet care items",
        30: "paper products",
        31: "maritime applications",
        32: "fiber processing equipment"
    }
    
    industry_context = industry_descriptions.get(industry_id, "specialized applications")
    
    # Generate description based on keywords in product name
    if "protein" in name_lower:
        return f"High-quality plant-based protein product derived from hemp seeds. This nutritional powerhouse contains all essential amino acids, making it a complete protein source for {industry_context}. Rich in minerals, fiber, and omega fatty acids. Easily digestible and suitable for various dietary requirements including vegan, gluten-free, and allergen-free diets. Sustainably produced with minimal environmental impact."
    
    elif "oil" in name_lower:
        return f"Premium hemp-derived oil product optimized for {industry_context}. Extracted using sustainable methods to preserve beneficial compounds including essential fatty acids, vitamins, and minerals. Features an ideal omega-3 to omega-6 ratio for optimal health benefits. Versatile applications ranging from nutritional supplements to industrial uses. Cold-processed to maintain maximum potency and freshness."
    
    elif "fiber" in name_lower or "textile" in name_lower:
        return f"Advanced hemp fiber material engineered for {industry_context}. Offers superior strength, durability, and sustainability compared to conventional materials. Natural antimicrobial and UV-resistant properties ensure long-lasting performance. Breathable and moisture-wicking characteristics provide comfort and functionality. Biodegradable at end of life, supporting circular economy principles."
    
    elif "concrete" in name_lower or "hempcrete" in name_lower:
        return f"Innovative hemp-based construction material designed for {industry_context}. Combines hemp hurds with mineral binders to create a lightweight, insulating building material. Provides excellent thermal and acoustic properties while being fire-resistant and pest-deterrent. Carbon-negative material that continues to sequester CO2 over its lifetime. Ideal for sustainable construction projects."
    
    elif "plastic" in name_lower or "bioplastic" in name_lower:
        return f"Revolutionary hemp-based bioplastic developed for {industry_context}. This sustainable alternative to petroleum-based plastics offers comparable performance with environmental benefits. Fully biodegradable and compostable under appropriate conditions. Reduces carbon footprint and plastic pollution while maintaining functionality. Suitable for various manufacturing processes and applications."
    
    elif "insulation" in name_lower:
        return f"High-performance hemp insulation material tailored for {industry_context}. Provides excellent thermal resistance and acoustic dampening while maintaining breathability. Naturally resistant to mold, pests, and fire without chemical treatments. Helps regulate indoor humidity and air quality for healthier living spaces. Easy to install and handle without protective equipment."
    
    elif "food" in name_lower or "edible" in name_lower:
        return f"Nutritious hemp-based food product crafted for {industry_context}. Rich in essential nutrients including protein, omega fatty acids, and minerals. Offers unique nutritional benefits with a pleasant, nutty flavor profile. Sustainably grown and minimally processed to retain maximum nutritional value. Suitable for various dietary preferences and restrictions."
    
    elif "cosmetic" in name_lower or "skincare" in name_lower or "beauty" in name_lower:
        return f"Luxurious hemp-infused beauty product formulated for {industry_context}. Harnesses the power of hemp's essential fatty acids and antioxidants for skin health. Provides deep moisturization and nourishment without clogging pores. Suitable for all skin types including sensitive skin. Free from harsh chemicals and synthetic additives."
    
    elif "paper" in name_lower:
        return f"Sustainable hemp paper product manufactured for {industry_context}. Produces stronger, longer-lasting paper than tree-based alternatives with less environmental impact. Naturally acid-free ensuring archival quality and resistance to yellowing. Requires fewer chemicals in processing and grows rapidly for sustainable production. Ideal for various printing and writing applications."
    
    elif "composite" in name_lower:
        return f"Advanced hemp composite material engineered for {industry_context}. Combines hemp fibers with matrix materials to create lightweight, strong, and sustainable composites. Offers excellent strength-to-weight ratio and impact resistance. Suitable for replacing traditional materials in various applications. Reduces environmental footprint while maintaining or exceeding performance standards."
    
    else:
        # Generic but informative description
        return f"Innovative hemp-based product designed specifically for {industry_context}. Leverages the unique properties of hemp including strength, sustainability, and versatility. Manufactured using environmentally responsible processes that minimize waste and energy consumption. Offers superior performance characteristics while supporting sustainable development goals. Represents the future of eco-friendly materials in modern applications."

def fix_template_descriptions():
    """Fix all template descriptions in the database"""
    
    conn = psycopg2.connect(**db_config)
    cur = conn.cursor()
    
    try:
        # Get all products with template descriptions
        cur.execute("""
            SELECT id, name, industry_sub_category_id
            FROM uses_products
            WHERE description LIKE '{%'
               OR description LIKE '%cultural%'
               OR description LIKE '%traditional%'
            ORDER BY id
        """)
        
        products = cur.fetchall()
        total = len(products)
        print(f"Found {total} products with template descriptions to fix")
        
        # Update in batches of 50
        batch_size = 50
        updated = 0
        
        for i in range(0, total, batch_size):
            batch = products[i:i+batch_size]
            
            # Build update query
            updates = []
            for product_id, name, industry_id in batch:
                description = generate_product_description(name, industry_id or 1)
                # Escape single quotes for SQL
                description = description.replace("'", "''")
                updates.append(f"WHEN id = {product_id} THEN '{description}'")
            
            if updates:
                update_sql = f"""
                    UPDATE uses_products 
                    SET description = CASE 
                        {' '.join(updates)}
                    END,
                    updated_at = NOW()
                    WHERE id IN ({','.join(str(p[0]) for p in batch)})
                """
                
                cur.execute(update_sql)
                updated += len(batch)
                
                print(f"Updated {updated}/{total} descriptions ({updated/total*100:.1f}%)")
                
                # Commit every 200 updates
                if updated % 200 == 0:
                    conn.commit()
                    print("Committed changes to database")
        
        # Final commit
        conn.commit()
        print(f"\nSuccessfully updated {updated} product descriptions!")
        
        # Check remaining templates
        cur.execute("""
            SELECT COUNT(*)
            FROM uses_products
            WHERE description LIKE '{%'
               OR description LIKE '%cultural%'
               OR description LIKE '%traditional%'
        """)
        
        remaining = cur.fetchone()[0]
        print(f"Remaining template descriptions: {remaining}")
        
    except Exception as e:
        print(f"Error: {e}")
        conn.rollback()
    finally:
        cur.close()
        conn.close()

if __name__ == "__main__":
    fix_template_descriptions()