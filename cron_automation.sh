#!/bin/bash
# Cron automation script for Hemp Database Expansion
# Runs every hour to ensure agents are always active

# Set up environment
export PATH="/usr/local/bin:/usr/bin:/bin"
export DATABASE_URL="postgresql://postgres:%<EMAIL>:5432/postgres"
export SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt0b3F6bnFtbG54cnR2dWJld3l6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg0OTE3NzYsImV4cCI6MjA2NDA2Nzc3Nn0.Cyu74ipNL2Fq6wTqzFOGCLW9mg46fRGJqkapgsumUGs"

# Project directory
PROJECT_DIR="/home/<USER>/projects/HQz-Ai-DB-MCP-3"
cd "$PROJECT_DIR"

# Log file
LOG_DIR="$PROJECT_DIR/logs"
mkdir -p "$LOG_DIR"
LOG_FILE="$LOG_DIR/cron_automation_$(date +%Y%m%d).log"

echo "[$(date)] Starting automation check..." >> "$LOG_FILE"

# Check if mega agent coordinator is running
if ! pgrep -f "mega_agent_coordinator_v2.py" > /dev/null; then
    echo "[$(date)] Agents not running. Starting..." >> "$LOG_FILE"
    
    # Activate virtual environment and start agents
    source venv_dedup/bin/activate
    nohup python src/agents/mega_agent_coordinator_v2.py --continuous 24 >> "$LOG_DIR/expansion_agents_$(date +%Y%m%d_%H%M%S).log" 2>&1 &
    
    echo "[$(date)] Agents started with PID: $!" >> "$LOG_FILE"
else
    echo "[$(date)] Agents already running" >> "$LOG_FILE"
fi

# Get current stats
source venv_dedup/bin/activate
STATS=$(python -c "
import psycopg2
import os
conn = psycopg2.connect(os.environ['DATABASE_URL'])
cur = conn.cursor()
cur.execute('SELECT COUNT(*) FROM uses_products')
count = cur.fetchone()[0]
print(f'Total products: {count}')
conn.close()
" 2>/dev/null)

echo "[$(date)] $STATS" >> "$LOG_FILE"

# Clean up old logs (keep last 7 days)
find "$LOG_DIR" -name "expansion_agents_*.log" -mtime +7 -delete
find "$LOG_DIR" -name "cron_automation_*.log" -mtime +30 -delete

echo "[$(date)] Automation check complete" >> "$LOG_FILE"
echo "---" >> "$LOG_FILE"