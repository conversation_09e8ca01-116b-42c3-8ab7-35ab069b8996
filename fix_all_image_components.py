#!/usr/bin/env python3
"""Fix all React components to handle both imageUrl and image_url field names."""

import os
import re

def fix_image_url_references(file_path):
    """Fix image URL references in a file to handle both naming conventions."""
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    changes_made = []
    
    # Pattern to find product.image_url usage (excluding already fixed ones)
    patterns = [
        # Direct image_url usage without imageUrl check
        (r'(product\.image_url)(?!\s*\|\|\s*product\.imageUrl)', 
         r'(product.imageUrl || product.image_url)'),
        
        # In src attributes
        (r'src=\{product\.image_url(\s*\|\|)', 
         r'src={(product.imageUrl || product.image_url)\1'),
        
        # In conditionals
        (r'(product\.image_url)\?\.includes',
         r'(product.imageUrl || product.image_url)?.includes'),
    ]
    
    for pattern, replacement in patterns:
        matches = list(re.finditer(pattern, content))
        if matches:
            content = re.sub(pattern, replacement, content)
            changes_made.append(f"Fixed {len(matches)} occurrences of pattern: {pattern}")
    
    # Also ensure interface extensions where needed
    if 'interface' in content and 'product:' in content and 'HempProduct' in content:
        # Check if we need to add image_url to interfaces
        interface_pattern = r'(product:\s*HempProduct)(?!.*\{.*image_url)'
        if re.search(interface_pattern, content):
            content = re.sub(
                interface_pattern,
                r'\1 & { image_url?: string }',
                content
            )
            changes_made.append("Extended interface to include image_url")
    
    if content != original_content:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        return True, changes_made
    
    return False, []

def main():
    """Fix all component files."""
    
    components_dir = '/home/<USER>/projects/HQz-Ai-DB-MCP-3/HempResourceHub/client/src'
    
    # Files to check and fix
    files_to_fix = [
        'pages/product-detail.tsx',
        'components/product/PokedexProductCard.tsx',
        'components/product/interactive-product-card.tsx',
        'components/product/product-detail-view.tsx',
        'components/product/modern-product-card.tsx',
    ]
    
    print("Fixing React components to handle both imageUrl and image_url...")
    print("-" * 60)
    
    fixed_count = 0
    
    for file_path in files_to_fix:
        full_path = os.path.join(components_dir, file_path)
        if os.path.exists(full_path):
            fixed, changes = fix_image_url_references(full_path)
            if fixed:
                print(f"\n✅ Fixed: {file_path}")
                for change in changes:
                    print(f"   - {change}")
                fixed_count += 1
            else:
                print(f"⚪ No changes needed: {file_path}")
        else:
            print(f"❌ File not found: {file_path}")
    
    print(f"\n{'='*60}")
    print(f"Total files fixed: {fixed_count}")
    print("\nAll components should now handle both imageUrl and image_url field names!")
    print("\nNext steps:")
    print("1. Refresh your browser (Ctrl+Shift+R or Cmd+Shift+R)")
    print("2. Check the Products page - images should now be displaying")
    print("3. If still not working, check browser console for errors")

if __name__ == "__main__":
    main()