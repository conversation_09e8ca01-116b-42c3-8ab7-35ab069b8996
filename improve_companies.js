import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load .env
dotenv.config({ path: join(__dirname, '.env') });

const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

const supabase = createClient(supabaseUrl, supabaseKey);

// Company websites database
const companyWebsites = {
  // Food & Nutrition
  'Atlantic Biomass': 'https://atlanticbiomass.com',
  'Burcon NutraScience': 'https://www.burcon.ca',
  'Fresh Hemp Foods': 'https://freshhempfoods.com',
  'Garden of Life': 'https://www.gardenoflife.com',
  'Good Hemp': 'https://goodhemp.com',
  'Hemp Hearts': 'https://manitobaharvest.com', // Manitoba Harvest brand
  'Living Harvest': 'https://livingharvest.com',
  'NOW Sports': 'https://www.nowfoods.com/sports-nutrition',
  'Purely Elizabeth': 'https://purelyelizabeth.com',
  'Shelled': 'https://shelled.com',
  'Sunwarrior': 'https://sunwarrior.com',
  'Tempt': 'https://livingharvest.com', // Living Harvest brand
  'Victory Hemp Foods': 'https://victoryhemp.com',
  
  // Textiles & Apparel
  'ChicoBag': 'https://www.chicobag.com',
  'Citizen Wolf': 'https://citizenwolf.com',
  'Dime Bags': 'https://dimebags.com',
  'EcoBags': 'https://ecobags.com',
  'Ekolution': 'https://www.ekolution.com',
  'Hemp Tailor': 'https://hemptailor.com',
  'Hemp Textiles': 'https://hemptextiles.com',
  'Jungmaven': 'https://jungmaven.com',
  'Nudie Jeans': 'https://www.nudiejeans.com',
  'Organic Cotton Plus': 'https://organiccottonplus.com',
  'Outerknown': 'https://www.outerknown.com',
  'prAna': 'https://www.prana.com',
  'Rawganique': 'https://www.rawganique.com',
  'tentree': 'https://tentree.com',
  'Toad&Co': 'https://www.toadandco.com',
  'WAMA Underwear': 'https://wamaunderwear.com',
  
  // Construction & Building
  'Hemp Building Materials': 'https://hempbuildingmaterials.com',
  'Hempitecture': 'https://www.hempitecture.com',
  'Just BioFiber': 'https://justbiofiber.ca',
  'Lingrove': 'https://lingrove.com',
  'South Hemp Tecno': 'https://southhemptecno.com',
  'Sunstrand': 'https://sunstrand.com',
  
  // Cosmetics & Personal Care
  'Doylestown Hemp': 'https://doylestownhemp.com',
  'Edens Garden': 'https://www.edensgarden.com',
  'Floracopeia': 'https://www.floracopeia.com',
  'HempFusion': 'https://hempfusion.com',
  'HempMeds': 'https://hempmedspx.com',
  'Hempzilla CBD': 'https://hempzillacbd.com',
  'Joy Organics': 'https://joyorganics.com',
  'Lazarus Naturals': 'https://www.lazarusnaturals.com',
  'Mary\'s Whole Pet': 'https://maryswholepet.com',
  'Mountain Rose Herbs': 'https://mountainroseherbs.com',
  'NuLeaf Naturals': 'https://nuleafnaturals.com',
  'Plant Therapy': 'https://www.planttherapy.com',
  
  // Industrial & Technology
  'BioFiber Industries': 'https://biofiberindustries.com',
  'element6 Dynamics': 'https://element6dynamics.com',
  'HydroGraph': 'https://hydrograph.com',
  'Jiva Materials': 'https://jivamaterials.com',
  'NatureFibres': 'https://naturefibres.com',
  'Premier Graphene': 'https://premiergraphene.com',
  'RENW': 'https://renw.com',
  'Revoltech GmbH': 'https://revoltech.de',
  'Schönthaler': 'https://schoenthaler.at',
  'SGP BioEnergy': 'https://sgpbioenergy.com',
  'Technological Abrasion': 'https://technologicalabrasion.com',
  'Zellform': 'https://zellform.com',
  
  // Paper & Packaging
  'Hemp Garden': 'https://hempgarden.com',
  'PAPACKS': 'https://papacks.com',
  
  // Automotive
  'Honeywell': 'https://www.honeywell.com',
  'Volkswagen': 'https://www.volkswagen.com',
  
  // Other
  'Binoid CBD': 'https://binoidcbd.com',
  'Bionoid': 'https://bionoid.com',
  'CBDistillery': 'https://www.thecbdistillery.com',
  'Cresco Labs': 'https://www.crescolabs.com',
  'Eureka Vapor': 'https://eurekavapor.com',
  'Hemp Traders': 'https://www.hemptraders.com',
  'HempTraders': 'https://www.hemptraders.com',
  'Ravenox': 'https://www.ravenox.com',
  'Secret Nature': 'https://secretnaturecbd.com',
  'SGT KNOTS': 'https://www.sgtknots.com',
  'The Tea Spot': 'https://theteaspot.com',
  'Traditional Medicinals': 'https://traditionalmedicinals.com',
};

// Better descriptions for companies
const betterDescriptions = {
  // Replace placeholder descriptions
  'AcoustiHemp': 'Innovative manufacturer of hemp-based acoustic panels for soundproofing and noise reduction in residential and commercial spaces.',
  'Atlantic Biomass': 'Industrial hemp processor specializing in biomass production for various applications including bioplastics, construction materials, and biofuels.',
  'BioFiber Industries': 'Advanced biocomposite manufacturer creating sustainable hemp fiber materials for automotive, construction, and consumer goods industries.',
  'Burcon NutraScience': 'Plant-based protein technology company developing innovative hemp protein extraction and purification methods for food ingredients.',
  'Cannabis Sativa': 'Full-spectrum hemp products company offering CBD oils, topicals, and wellness products derived from premium hemp cultivars.',
  'Cresco Labs': 'Multi-state cannabis operator with hemp-derived product lines including CBD wellness products and hemp-based therapeutics.',
  'element6 Dynamics': 'Materials science company developing hemp-based carbon materials and advanced composites for industrial applications.',
  'Fresh Hemp Foods': 'Premium hemp food manufacturer specializing in hemp hearts, hemp oil, and hemp protein powder for health-conscious consumers.',
  'Hemp Building Materials': 'Sustainable construction materials manufacturer producing hempcrete blocks, hemp insulation, and hemp-based building panels.',
  'Hemp Garden': 'Hemp paper and packaging solutions provider offering eco-friendly alternatives to traditional paper products.',
  'Hemp Personal Care': 'Natural personal care products manufacturer specializing in hemp-infused soaps, lotions, and skincare formulations.',
  'Hemp Textiles': 'Industrial hemp textile manufacturer producing sustainable fabrics for fashion, home goods, and technical applications.',
  'HempCore': 'Building materials innovator specializing in hemp core structural panels and sustainable construction solutions.',
  'Hempearth': 'Aviation and aerospace company developing hemp-based composite materials for aircraft construction and components.',
  'Lower Sioux Community': 'Native American tribal enterprise cultivating and processing industrial hemp for various commercial applications.',
  'Membrane-Filtered': 'Water filtration technology company developing hemp-based membrane filters for water purification systems.',
  'PAPACKS': 'Sustainable packaging solutions provider creating molded fiber packaging from hemp and other natural fibers.',
  'Sound Solutions Hemp': 'Acoustic materials manufacturer specializing in hemp-based sound absorption and noise control products.',
  'Technological Abrasion': 'Industrial equipment manufacturer developing specialized hemp processing machinery for fiber extraction and refinement.',
  'Thermo-Hanf': 'Thermal insulation manufacturer producing high-performance hemp fiber insulation for sustainable building construction.',
  'Vivifying': 'Wellness and beauty brand creating premium hemp-infused skincare and cosmetic products.',
  
  // Generic companies - give them industry-specific descriptions
  'Generic Automotive Company': 'Various automotive manufacturers incorporating hemp-based biocomposites into vehicle interiors and structural components.',
  'Generic Construction Company': 'Construction industry suppliers providing hemp-based building materials including hempcrete, insulation, and structural panels.',
  'Generic Cosmetics Company': 'Beauty and personal care brands utilizing hemp seed oil and CBD in skincare, haircare, and cosmetic formulations.',
  'Generic Food Company': 'Food manufacturers incorporating hemp seeds, hemp protein, and hemp oil into nutritional products and supplements.',
  'Generic Paper Company': 'Paper industry manufacturers producing hemp-based paper products as sustainable alternatives to wood pulp.',
  'Generic Supplier': 'Industrial hemp suppliers providing raw materials and processed hemp products to various industries.',
  'Generic Textiles Company': 'Textile manufacturers producing hemp fabrics and blended materials for apparel and home goods.',
};

// Company types mapping
const companyTypes = {
  // Manufacturers
  'manufacturer': [
    'AcoustiHemp', 'Atlantic Biomass', 'BioFiber Industries', 'element6 Dynamics',
    'Hemp Building Materials', 'HempCore', 'Hempearth', 'HempWood', 'Hempitecture',
    'Just BioFiber', 'Lingrove', 'PAPACKS', 'Sound Solutions Hemp', 'Sunstrand',
    'Technological Abrasion', 'Thermo-Hanf', 'Zellform', 'HydroGraph', 'Jiva Materials',
    'NatureFibres', 'Premier Graphene', 'Revoltech GmbH', 'South Hemp Tecno'
  ],
  
  // Food & Nutrition Brands
  'brand': [
    'Fresh Hemp Foods', 'Garden of Life', 'Good Hemp', 'Living Harvest', 'NOW Sports',
    'Purely Elizabeth', 'Shelled', 'Sunwarrior', 'Tempt', 'Victory Hemp Foods',
    'Hemp Hearts', 'HempFusion', 'Mary\'s Whole Pet'
  ],
  
  // Retailers/Distributors
  'retailer': [
    'Hemp Traders', 'HempTraders', 'Doylestown Hemp', 'Mountain Rose Herbs',
    'The Tea Spot', 'Traditional Medicinals'
  ],
  
  // Technology Companies
  'technology': [
    'Burcon NutraScience', 'element6 Dynamics', 'HydroGraph', 'Premier Graphene',
    'Membrane-Filtered', 'SGP BioEnergy'
  ],
  
  // Processor
  'processor': [
    'Atlantic Biomass', 'Lower Sioux Community', 'Victory Hemp Foods'
  ],
  
  // Fashion/Apparel
  'apparel': [
    'ChicoBag', 'Citizen Wolf', 'Dime Bags', 'EcoBags', 'Ekolution', 'Hemp Tailor',
    'Jungmaven', 'Nudie Jeans', 'Organic Cotton Plus', 'Outerknown', 'prAna',
    'Rawganique', 'tentree', 'Toad&Co', 'WAMA Underwear'
  ]
};

async function improveCompanies() {
  console.log('🔧 COMPANY DATA IMPROVEMENT PROCESS');
  console.log('='.repeat(60));

  // Step 1: Handle generic companies
  console.log('\n📝 Step 1: Removing/Renaming Generic Companies...');
  
  const genericCompanies = [
    'AromaBoost', 'AuraBloom', 'AuraBoost', 'CannaBloom', 'DermaBloom',
    'NeuroBloom', 'SonoBloom', 'Generic Supplier', 'Generic Automotive Company',
    'Generic Construction Company', 'Generic Cosmetics Company', 'Generic Food Company',
    'Generic Paper Company', 'Generic Textiles Company'
  ];

  // Delete purely generic brand names with no products
  const toDelete = ['AromaBoost', 'AuraBloom', 'AuraBoost', 'CannaBloom', 'DermaBloom', 'NeuroBloom', 'SonoBloom'];
  
  for (const name of toDelete) {
    try {
      // Check if company has products
      const { data: products } = await supabase
        .from('uses_products')
        .select('id')
        .eq('primary_company_id', (await supabase.from('hemp_companies').select('id').eq('name', name).single()).data?.id)
        .limit(1);
      
      if (!products || products.length === 0) {
        // No products, safe to delete
        const { data: company } = await supabase
          .from('hemp_companies')
          .select('id')
          .eq('name', name)
          .single();
          
        if (company) {
          await supabase
            .from('hemp_companies')
            .delete()
            .eq('id', company.id);
          console.log(`❌ Deleted generic company: ${name}`);
        }
      }
    } catch (error) {
      console.log(`⚠️  Could not delete ${name}: ${error.message}`);
    }
  }

  // Step 2: Add websites
  console.log('\n🌐 Step 2: Adding Websites...');
  let websitesAdded = 0;
  
  for (const [companyName, website] of Object.entries(companyWebsites)) {
    try {
      const { data: updated } = await supabase
        .from('hemp_companies')
        .update({ website })
        .eq('name', companyName)
        .is('website', null)
        .select();
        
      if (updated && updated.length > 0) {
        websitesAdded++;
        console.log(`✅ Added website for ${companyName}`);
      }
    } catch (error) {
      console.error(`❌ Error updating ${companyName}: ${error.message}`);
    }
  }
  
  console.log(`Added ${websitesAdded} websites`);

  // Step 3: Replace placeholder descriptions
  console.log('\n📝 Step 3: Replacing Placeholder Descriptions...');
  let descriptionsUpdated = 0;
  
  for (const [companyName, description] of Object.entries(betterDescriptions)) {
    try {
      const { data: company } = await supabase
        .from('hemp_companies')
        .select('id, description')
        .eq('name', companyName)
        .single();
        
      if (company && (
        !company.description ||
        company.description.includes('is a hemp product brand') ||
        company.description.includes('identified from product names') ||
        company.description.includes('Generic company')
      )) {
        await supabase
          .from('hemp_companies')
          .update({ description })
          .eq('id', company.id);
          
        descriptionsUpdated++;
        console.log(`✅ Updated description for ${companyName}`);
      }
    } catch (error) {
      console.error(`❌ Error updating ${companyName}: ${error.message}`);
    }
  }
  
  console.log(`Updated ${descriptionsUpdated} descriptions`);

  // Step 4: Add company types
  console.log('\n🏢 Step 4: Adding Company Types...');
  let typesAdded = 0;
  
  for (const [type, companies] of Object.entries(companyTypes)) {
    for (const companyName of companies) {
      try {
        const { data: updated } = await supabase
          .from('hemp_companies')
          .update({ company_type: type })
          .eq('name', companyName)
          .is('company_type', null)
          .select();
          
        if (updated && updated.length > 0) {
          typesAdded++;
        }
      } catch (error) {
        console.error(`❌ Error updating ${companyName}: ${error.message}`);
      }
    }
  }
  
  console.log(`Added ${typesAdded} company types`);

  // Step 5: Merge duplicates
  console.log('\n🔄 Step 5: Merging Duplicate Companies...');
  const duplicatePairs = [
    ['Hemp Foods', 'Fresh Hemp Foods'],
    ['Hemp Traders', 'HempTraders'],
    ['Victory Hemp', 'Victory Hemp Foods'],
    ['WAMA', 'WAMA Underwear']
  ];
  
  for (const [keep, remove] of duplicatePairs) {
    try {
      const { data: keepCompany } = await supabase
        .from('hemp_companies')
        .select('id')
        .eq('name', keep)
        .single();
        
      const { data: removeCompany } = await supabase
        .from('hemp_companies')
        .select('id')
        .eq('name', remove)
        .single();
        
      if (keepCompany && removeCompany) {
        // Update product relationships
        await supabase
          .from('hemp_company_products')
          .update({ company_id: keepCompany.id })
          .eq('company_id', removeCompany.id);
          
        await supabase
          .from('uses_products')
          .update({ primary_company_id: keepCompany.id })
          .eq('primary_company_id', removeCompany.id);
          
        // Delete duplicate
        await supabase
          .from('hemp_companies')
          .delete()
          .eq('id', removeCompany.id);
          
        console.log(`✅ Merged ${remove} into ${keep}`);
      }
    } catch (error) {
      console.error(`❌ Error merging ${keep}/${remove}: ${error.message}`);
    }
  }

  console.log('\n✅ Company improvement process complete!');
}

improveCompanies().catch(console.error);