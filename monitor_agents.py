#!/usr/bin/env python3
"""
Real-time Agent Monitoring Dashboard
Shows status of all running agents and recent activity
"""

import os
import time
import subprocess
from datetime import datetime, timedelta
import glob

def clear_screen():
    os.system('clear' if os.name == 'posix' else 'cls')

def get_running_agents():
    """Get list of running Python agents"""
    try:
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
        lines = result.stdout.split('\n')
        agents = []
        for line in lines:
            if 'python' in line and any(x in line for x in ['agent', 'automation', 'mega', 'monitor']):
                if 'grep' not in line and 'monitor_agents.py' not in line:
                    parts = line.split()
                    if len(parts) > 10:
                        pid = parts[1]
                        cpu = parts[2]
                        mem = parts[3]
                        cmd = ' '.join(parts[10:])
                        agents.append({
                            'pid': pid,
                            'cpu': cpu,
                            'mem': mem,
                            'cmd': cmd
                        })
        return agents
    except:
        return []

def get_recent_logs(minutes=5):
    """Get recent log entries"""
    cutoff_time = datetime.now() - timedelta(minutes=minutes)
    log_files = glob.glob('logs/*.log')
    recent_entries = []
    
    for log_file in sorted(log_files, key=os.path.getmtime, reverse=True)[:5]:
        try:
            with open(log_file, 'r') as f:
                lines = f.readlines()[-50:]  # Last 50 lines
                for line in lines:
                    if any(keyword in line for keyword in ['Added', 'SUCCESS', 'ERROR', 'products', 'Agent']):
                        recent_entries.append((log_file, line.strip()))
        except:
            pass
    
    return recent_entries[-10:]  # Last 10 entries

def get_agent_stats():
    """Get agent statistics from logs"""
    stats = {
        'products_added_today': 0,
        'agents_run_today': 0,
        'errors_today': 0
    }
    
    today = datetime.now().strftime('%Y%m%d')
    log_files = glob.glob(f'logs/*_{today}*.log')
    
    for log_file in log_files:
        try:
            with open(log_file, 'r') as f:
                content = f.read()
                stats['products_added_today'] += content.count('Added product')
                stats['agents_run_today'] += content.count('Agent starting')
                stats['errors_today'] += content.count('ERROR')
        except:
            pass
    
    return stats

def main():
    while True:
        clear_screen()
        
        print("=" * 60)
        print("🚀 HEMP DATABASE AGENT MONITOR".center(60))
        print("=" * 60)
        print(f"\n📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Running agents
        agents = get_running_agents()
        print(f"\n📊 RUNNING AGENTS ({len(agents)}):")
        print("-" * 60)
        if agents:
            for agent in agents:
                cmd_short = agent['cmd'][:50] + '...' if len(agent['cmd']) > 50 else agent['cmd']
                print(f"  ✓ PID: {agent['pid']} | CPU: {agent['cpu']}% | MEM: {agent['mem']}%")
                print(f"    {cmd_short}")
        else:
            print("  ⚠️  No agents currently running")
        
        # Statistics
        stats = get_agent_stats()
        print(f"\n📈 TODAY'S STATISTICS:")
        print("-" * 60)
        print(f"  • Products Added: {stats['products_added_today']}")
        print(f"  • Agent Runs: {stats['agents_run_today']}")
        print(f"  • Errors: {stats['errors_today']}")
        
        # Recent activity
        print(f"\n📜 RECENT ACTIVITY (Last 5 min):")
        print("-" * 60)
        entries = get_recent_logs()
        if entries:
            for log_file, entry in entries:
                log_name = os.path.basename(log_file)
                print(f"  [{log_name}] {entry[:80]}...")
        else:
            print("  No recent activity")
        
        # Commands
        print(f"\n⌨️  COMMANDS:")
        print("-" * 60)
        print("  • Start automation: ./RUN_AUTOMATION.sh")
        print("  • View specific log: tail -f logs/<logname>")
        print("  • Stop all agents: pkill -f 'python.*agent'")
        print("  • Press Ctrl+C to exit this monitor")
        
        print("\n" + "=" * 60)
        print("Refreshing in 5 seconds...")
        
        time.sleep(5)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Monitor stopped.")