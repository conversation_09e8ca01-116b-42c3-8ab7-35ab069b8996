import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config();

const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function analyzeCompanies() {
  console.log('🏢 COMPANY DATA ANALYSIS');
  console.log('='.repeat(60));

  // Get all companies
  const { data: companies, error: companiesError } = await supabase
    .from('hemp_companies')
    .select('*')
    .order('name');

  if (companiesError) {
    console.error('Error fetching companies:', companiesError);
    return;
  }

  console.log(`\nTotal companies: ${companies.length}`);

  // Check companies with websites
  const companiesWithWebsites = companies.filter(c => c.website);
  console.log(`Companies with websites: ${companiesWithWebsites.length}`);

  // Check verified companies
  const verifiedCompanies = companies.filter(c => c.verified);
  console.log(`Verified companies: ${verifiedCompanies.length}`);

  // Check companies with descriptions
  const companiesWithDesc = companies.filter(c => c.description);
  console.log(`Companies with descriptions: ${companiesWithDesc.length}`);

  // Sample companies
  console.log('\n📋 Sample Companies:');
  companies.slice(0, 10).forEach(company => {
    console.log(`\n${company.name}`);
    console.log(`  Type: ${company.company_type || 'Not specified'}`);
    console.log(`  Website: ${company.website || 'None'}`);
    console.log(`  Verified: ${company.verified ? 'Yes' : 'No'}`);
    console.log(`  Description: ${company.description ? company.description.substring(0, 100) + '...' : 'None'}`);
  });

  // Check product-company relationships
  const { data: productRelations, error: relError } = await supabase
    .from('hemp_company_products')
    .select('company_id')
    .limit(1000);

  if (!relError && productRelations) {
    const uniqueCompaniesWithProducts = new Set(productRelations.map(r => r.company_id));
    console.log(`\n📦 Companies with products: ${uniqueCompaniesWithProducts.size}`);
  }

  // Check products with primary_company_id
  const { count: productsWithCompany } = await supabase
    .from('uses_products')
    .select('*', { count: 'exact', head: true })
    .not('primary_company_id', 'is', null);

  console.log(`Products with primary_company_id: ${productsWithCompany || 0}`);

  // Get products without companies
  const { data: productsWithoutCompany } = await supabase
    .from('uses_products')
    .select('name')
    .is('primary_company_id', null)
    .limit(20);

  console.log('\n🚫 Sample products without companies:');
  productsWithoutCompany?.forEach(p => {
    console.log(`  - ${p.name}`);
  });
}

analyzeCompanies().catch(console.error);