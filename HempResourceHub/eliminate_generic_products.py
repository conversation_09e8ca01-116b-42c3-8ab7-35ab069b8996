#!/usr/bin/env python3
"""
Eliminate generic template-generated products from the database
"""

import os
from dotenv import load_dotenv
from supabase import create_client, Client
import time

load_dotenv()

# Initialize Supabase client
url = os.getenv("VITE_SUPABASE_URL")
key = os.getenv("SUPABASE_SERVICE_ROLE_KEY") or os.getenv("VITE_SUPABASE_ANON_KEY")
supabase: Client = create_client(url, key)

def eliminate_generic_products():
    """Remove generic template-generated products"""
    
    print("Analyzing generic products to eliminate...")
    
    # Get IDs of products to delete
    response = supabase.table('uses_products').select('id, name').or_(
        'name.like.Advanced Hemp%,'
        'name.like.Innovative Hemp%,'
        'name.like.Premium Hemp%,'
        'name.like.Sustainable Hemp%,'
        'name.like.Eco-Friendly Hemp%,'
        'name.like.Traditional Hemp%,'
        'name.like.Modern Hemp%,'
        'name.like.Revolutionary Hemp%,'
        'name.like.Next-Generation Hemp%,'
        'name.like.High-Performance Hemp%,'
        'name.like.Superior Hemp%,'
        'name.like.%Heritage Hemp%'
    ).execute()
    
    all_generic = response.data
    
    # Filter out legitimate products
    legitimate_keywords = ['Hemp Oil', 'Hemp Protein', 'Hemp Concrete', 'Hemp Fiber Board', 
                          'Hemp Seed', 'Hemp Extract', 'Hemp Plastic', 'Hemp Paper', 
                          'Hemp Textile', 'Hemp Insulation']
    
    products_to_delete = []
    for product in all_generic:
        # Check if it's a legitimate product
        is_legitimate = any(keyword in product['name'] for keyword in legitimate_keywords)
        
        # Additional checks for overly generic names
        is_generic = False
        if any(x in product['name'].lower() for x in [
            'technology', 'solution', 'application', 'material',
            'for fiber', 'based', 'with', 'from', 'using'
        ]):
            is_generic = True
            
        if not is_legitimate and is_generic:
            products_to_delete.append(product)
    
    print(f"Found {len(products_to_delete)} generic products to eliminate")
    
    # Show some examples
    print("\nExamples of products to be deleted:")
    for product in products_to_delete[:10]:
        print(f"  - {product['name']}")
    
    if len(products_to_delete) > 10:
        print(f"  ... and {len(products_to_delete) - 10} more")
    
    # Auto-confirm for automation
    print(f"\nProceeding with deletion of {len(products_to_delete)} generic products...")
    
    # Delete in batches
    batch_size = 50
    deleted_count = 0
    
    for i in range(0, len(products_to_delete), batch_size):
        batch = products_to_delete[i:i+batch_size]
        ids_to_delete = [p['id'] for p in batch]
        
        try:
            # Delete dependencies first
            supabase.table('image_generation_history').delete().in_('queue_id', 
                supabase.table('image_generation_queue').select('id').in_('product_id', ids_to_delete).execute().data
            ).execute()
            
            supabase.table('image_generation_queue').delete().in_('product_id', ids_to_delete).execute()
            supabase.table('content_queue').delete().in_('product_id', ids_to_delete).execute()
            supabase.table('hemp_company_products').delete().in_('product_id', ids_to_delete).execute()
            
            # Delete the products
            supabase.table('uses_products').delete().in_('id', ids_to_delete).execute()
            
            deleted_count += len(batch)
            print(f"Deleted {deleted_count}/{len(products_to_delete)} products...")
            
        except Exception as e:
            print(f"Error deleting batch: {e}")
            continue
        
        time.sleep(0.5)  # Rate limiting
    
    print(f"\n✅ Successfully deleted {deleted_count} generic products")
    
    # Get final count
    response = supabase.table('uses_products').select('id', count='exact').execute()
    print(f"Final product count: {response.count}")

def consolidate_cultural_products():
    """Keep only one cultural product per culture"""
    
    print("\n\nConsolidating cultural heritage products...")
    
    # Get all cultural products
    response = supabase.table('uses_products').select('id, name').like('name', '%Heritage Hemp%').execute()
    cultural_products = response.data
    
    # Group by culture
    cultures = {}
    for product in cultural_products:
        culture = product['name'].split(' Hemp ')[0]
        if culture not in cultures:
            cultures[culture] = []
        cultures[culture].append(product)
    
    # Keep only one per culture
    products_to_keep = []
    products_to_delete = []
    
    for culture, products in cultures.items():
        if len(products) > 1:
            # Keep the first one (or the one with specific keywords)
            kept = None
            for p in products:
                if 'Craft' in p['name'] or 'Artwork' in p['name']:
                    kept = p
                    break
            if not kept:
                kept = products[0]
            
            products_to_keep.append(kept)
            for p in products:
                if p['id'] != kept['id']:
                    products_to_delete.append(p)
        else:
            products_to_keep.append(products[0])
    
    print(f"Will keep {len(products_to_keep)} cultural products, delete {len(products_to_delete)}")
    
    if products_to_delete:
        print(f"\nProceeding with consolidating cultural products...")
        if True:  # Auto-confirm
            ids_to_delete = [p['id'] for p in products_to_delete]
            
            # Delete dependencies
            supabase.table('image_generation_history').delete().in_('queue_id', 
                supabase.table('image_generation_queue').select('id').in_('product_id', ids_to_delete).execute().data
            ).execute()
            
            supabase.table('image_generation_queue').delete().in_('product_id', ids_to_delete).execute()
            supabase.table('content_queue').delete().in_('product_id', ids_to_delete).execute()
            supabase.table('hemp_company_products').delete().in_('product_id', ids_to_delete).execute()
            
            # Delete the products
            supabase.table('uses_products').delete().in_('id', ids_to_delete).execute()
            
            print(f"✅ Consolidated cultural products from {len(cultural_products)} to {len(products_to_keep)}")

if __name__ == "__main__":
    eliminate_generic_products()
    consolidate_cultural_products()