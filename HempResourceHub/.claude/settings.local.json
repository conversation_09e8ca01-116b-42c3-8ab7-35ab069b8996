{"permissions": {"allow": ["mcp__playwright__browser_navigate", "mcp__playwright__browser_take_screenshot", "mcp__playwright__browser_wait_for", "mcp__playwright__browser_evaluate", "Bash(npm run dev:*)", "Bash(npx:*)", "<PERSON><PERSON>(curl:*)", "mcp__desktop-commander__start_process", "mcp__desktop-commander__force_terminate", "Bash(rm:*)", "mcp__desktop-commander__read_process_output", "<PERSON><PERSON>(pkill:*)", "Bash(kill:*)", "mcp__playwright__browser_snapshot"], "deny": []}}