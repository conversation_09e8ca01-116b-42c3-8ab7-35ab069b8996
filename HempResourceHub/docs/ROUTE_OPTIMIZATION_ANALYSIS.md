# Route Optimization Analysis

## Current Route Structure

### Redundant Routes (Need Consolidation)

1. **Products Routes** (7 different product-related routes):
   - `/products` (ProductsDiscoveryPage) - Main products page
   - `/products-explorer` (ProductsExplorerPage) - Redundant
   - `/products-list` (AllProductsPage) - Redundant
   - `/products-table` (ProductsDataTablePage) - Redundant
   - `/hemp-dex-unified` (HempDexUnified) - Redundant
   - `/hemp-dex-enhanced` (HempDexEnhanced) - Keep as enhanced version
   - `/product-showcase` (ProductShowcasePage) - Redundant

2. **Admin Routes** (Multiple admin interfaces):
   - `/admin` (AdminPage)
   - `/admin-dashboard` (AdminDashboardRedesigned) - Should be consolidated
   - `/admin-settings` (AdminSettings) - Keep as sub-route

3. **Debug/Test Routes** (Should be removed in production):
   - `/supabase-test`
   - `/supabase-industries`
   - `/supabase-connection`
   - `/debug`
   - `/debug-supabase`
   - `/test-navigation`

4. **Redirect Routes** (Already being redirected):
   - `/companies` → `/hemp-companies`
   - `/hemp-dex` → `/hemp-dex-unified`
   - `/hemp-dex-old` → `/products`
   - `/all-products` → `/products`
   - `/products-by-category` → `/hemp-dex-unified?tab=plant-parts`
   - `/product-listing` → `/products`

## Optimized Route Structure

### Core Public Routes
- `/` - Home
- `/about` - About Hemp
- `/products` - Products Discovery (Main)
- `/products/enhanced` - Enhanced Product Explorer (formerly hemp-dex-enhanced)
- `/product/:id` - Product Detail
- `/products/new` - Add New Product (Protected)
- `/plant-parts` - Plant Parts Overview
- `/plant-part/:id` - Plant Part Detail
- `/industries` - Industries Overview
- `/companies` - Hemp Companies
- `/research` - Research Papers
- `/research/:id` - Research Detail
- `/search` - Advanced Search
- `/dashboard` - User Dashboard (Protected)

### Authentication Routes
- `/login` - Login
- `/register` - Register
- `/auth/callback` - OAuth Callback

### Admin Routes (Protected)
- `/admin` - Admin Dashboard
- `/admin/settings` - Admin Settings

### Legal/Info Routes
- `/privacy` - Privacy Policy
- `/terms` - Terms of Service
- `/contact` - Contact Us
- `/sitemap` - Sitemap

## Routes to Remove
1. All test/debug routes
2. Redundant product routes
3. Legacy redirects (keep redirects but remove from main navigation)

## Sitemap Structure for Footer

### Main Sections
1. **Products & Data**
   - Browse Products
   - Enhanced Explorer
   - Plant Parts
   - Industries
   - Companies

2. **Research & Resources**
   - Research Papers
   - About Hemp
   - Documentation

3. **Account**
   - Dashboard
   - Login/Register

4. **Legal**
   - Privacy Policy
   - Terms of Service
   - Contact
   - Sitemap