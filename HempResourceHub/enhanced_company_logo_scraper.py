#!/usr/bin/env python3
"""
Enhanced Company Logo Scraper
Retrieves actual company logos from websites using multiple strategies
"""

import os
import sys
import requests
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
import time
import json
from typing import Optional, Dict, List
import re
from dotenv import load_dotenv
from supabase import create_client, Client

# Load environment variables
load_dotenv()

# Supabase configuration
SUPABASE_URL = os.getenv("SUPABASE_URL", "https://ktoqznqmlnxrtvubewyz.supabase.co")
SUPABASE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

if not SUPABASE_KEY:
    print("Error: SUPABASE_SERVICE_ROLE_KEY not found in environment variables")
    sys.exit(1)

# Initialize Supabase client
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

class CompanyLogoScraper:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
    
    def find_logo_url(self, website_url: str) -> Optional[str]:
        """Find logo URL using multiple strategies"""
        if not website_url:
            return None
            
        try:
            # Ensure URL has protocol
            if not website_url.startswith(('http://', 'https://')):
                website_url = f'https://{website_url}'
            
            print(f"Fetching logo from: {website_url}")
            
            # Try multiple strategies
            strategies = [
                self._find_logo_in_html,
                self._find_logo_by_common_paths,
                self._find_favicon_as_logo,
                self._find_og_image
            ]
            
            for strategy in strategies:
                logo_url = strategy(website_url)
                if logo_url and self._validate_logo_url(logo_url):
                    return logo_url
                    
        except Exception as e:
            print(f"Error finding logo for {website_url}: {e}")
            
        return None
    
    def _find_logo_in_html(self, url: str) -> Optional[str]:
        """Parse HTML to find logo images"""
        try:
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Common logo selectors
            selectors = [
                ('img', {'class': re.compile(r'logo', re.I)}),
                ('img', {'id': re.compile(r'logo', re.I)}),
                ('img', {'alt': re.compile(r'logo', re.I)}),
                ('img', {'src': re.compile(r'logo', re.I)}),
                ('a', {'class': re.compile(r'logo', re.I)}),
                ('div', {'class': re.compile(r'logo', re.I)}),
                ('header img', {}),
                ('.logo img', {}),
                ('#logo img', {}),
                ('[class*="logo"] img', {}),
                ('[id*="logo"] img', {}),
            ]
            
            for selector in selectors:
                if isinstance(selector, tuple):
                    tag, attrs = selector
                    elements = soup.find_all(tag, attrs)
                else:
                    elements = soup.select(selector)
                    
                for elem in elements:
                    # Get image source
                    img_src = None
                    if elem.name == 'img':
                        img_src = elem.get('src')
                    else:
                        img = elem.find('img')
                        if img:
                            img_src = img.get('src')
                    
                    if img_src:
                        absolute_url = urljoin(url, img_src)
                        if self._is_likely_logo(absolute_url):
                            return absolute_url
                            
        except Exception as e:
            print(f"Error parsing HTML: {e}")
            
        return None
    
    def _find_logo_by_common_paths(self, url: str) -> Optional[str]:
        """Check common logo paths"""
        base_url = f"{urlparse(url).scheme}://{urlparse(url).netloc}"
        
        common_paths = [
            '/logo.png', '/logo.jpg', '/logo.svg', '/logo.webp',
            '/images/logo.png', '/images/logo.jpg', '/images/logo.svg',
            '/img/logo.png', '/img/logo.jpg', '/img/logo.svg',
            '/assets/logo.png', '/assets/logo.jpg', '/assets/logo.svg',
            '/assets/images/logo.png', '/assets/images/logo.jpg',
            '/media/logo.png', '/media/logo.jpg', '/media/logo.svg',
            '/static/logo.png', '/static/logo.jpg', '/static/logo.svg',
        ]
        
        for path in common_paths:
            logo_url = urljoin(base_url, path)
            try:
                response = self.session.head(logo_url, timeout=5)
                if response.status_code == 200:
                    return logo_url
            except:
                continue
                
        return None
    
    def _find_favicon_as_logo(self, url: str) -> Optional[str]:
        """Use high-quality favicon as fallback"""
        try:
            response = self.session.get(url, timeout=10)
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Look for apple-touch-icon (usually higher quality)
            apple_icon = soup.find('link', {'rel': re.compile(r'apple-touch-icon', re.I)})
            if apple_icon and apple_icon.get('href'):
                return urljoin(url, apple_icon['href'])
                
            # Look for high-res favicon
            favicon = soup.find('link', {'rel': 'icon', 'sizes': re.compile(r'(192|256|512)', re.I)})
            if favicon and favicon.get('href'):
                return urljoin(url, favicon['href'])
                
        except:
            pass
            
        return None
    
    def _find_og_image(self, url: str) -> Optional[str]:
        """Use Open Graph image if it looks like a logo"""
        try:
            response = self.session.get(url, timeout=10)
            soup = BeautifulSoup(response.text, 'html.parser')
            
            og_image = soup.find('meta', {'property': 'og:image'})
            if og_image and og_image.get('content'):
                img_url = urljoin(url, og_image['content'])
                if self._is_likely_logo(img_url):
                    return img_url
                    
        except:
            pass
            
        return None
    
    def _is_likely_logo(self, url: str) -> bool:
        """Check if URL is likely a logo"""
        if not url:
            return False
            
        url_lower = url.lower()
        
        # Positive indicators
        if any(word in url_lower for word in ['logo', 'brand', 'identity']):
            return True
            
        # Negative indicators
        if any(word in url_lower for word in ['banner', 'hero', 'background', 'cover', 'slide']):
            return False
            
        # File type check
        return url_lower.endswith(('.png', '.jpg', '.jpeg', '.svg', '.webp', '.gif'))
    
    def _validate_logo_url(self, url: str) -> bool:
        """Validate that the logo URL is accessible"""
        try:
            response = self.session.head(url, timeout=5)
            return response.status_code == 200
        except:
            return False

def update_company_logos():
    """Update company logos in the database"""
    scraper = CompanyLogoScraper()
    
    # Get companies without logos but with websites
    print("Fetching companies without logos...")
    
    companies = supabase.table('hemp_companies') \
        .select('id, name, website, logo_url') \
        .is_('logo_url', 'null') \
        .not_.is_('website', 'null') \
        .limit(50) \
        .execute()
    
    if not companies.data:
        print("No companies found that need logos")
        return
    
    print(f"Found {len(companies.data)} companies to process")
    
    updated_count = 0
    for company in companies.data:
        company_id = company['id']
        company_name = company['name']
        website = company['website']
        
        print(f"\nProcessing: {company_name}")
        
        # Find logo
        logo_url = scraper.find_logo_url(website)
        
        if logo_url:
            print(f"  Found logo: {logo_url}")
            
            # Update in database
            try:
                supabase.table('hemp_companies') \
                    .update({'logo_url': logo_url}) \
                    .eq('id', company_id) \
                    .execute()
                
                updated_count += 1
                print(f"  ✓ Updated successfully")
            except Exception as e:
                print(f"  ✗ Error updating database: {e}")
        else:
            print(f"  ✗ No logo found")
        
        # Rate limiting
        time.sleep(1)
    
    print(f"\n{'='*50}")
    print(f"Logo update complete!")
    print(f"Updated {updated_count} out of {len(companies.data)} companies")

def test_single_company(company_name: str):
    """Test logo scraping for a single company"""
    scraper = CompanyLogoScraper()
    
    # Get company from database
    result = supabase.table('hemp_companies') \
        .select('*') \
        .ilike('name', f'%{company_name}%') \
        .limit(1) \
        .execute()
    
    if not result.data:
        print(f"Company '{company_name}' not found")
        return
    
    company = result.data[0]
    print(f"Testing company: {company['name']}")
    print(f"Website: {company['website']}")
    
    if not company['website']:
        print("No website URL available")
        return
    
    logo_url = scraper.find_logo_url(company['website'])
    
    if logo_url:
        print(f"Found logo: {logo_url}")
        
        # Auto-update in test mode
        print("Updating database...")
        supabase.table('hemp_companies') \
            .update({'logo_url': logo_url}) \
            .eq('id', company['id']) \
            .execute()
        print("✓ Logo updated successfully")
    else:
        print("✗ No logo found")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        # Test mode for single company
        company_name = ' '.join(sys.argv[1:])
        test_single_company(company_name)
    else:
        # Batch update mode
        update_company_logos()