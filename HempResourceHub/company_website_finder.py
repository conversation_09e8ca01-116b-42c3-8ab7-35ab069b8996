#!/usr/bin/env python3
"""
Company Website Finder and Validator
Finds websites for companies without URLs and validates existing ones
"""

import os
import sys
import requests
from urllib.parse import urlparse, quote
import time
import json
from typing import Optional, Dict, List, Tuple
from dotenv import load_dotenv
from supabase import create_client, Client
import re

# Load environment variables
load_dotenv()

# Supabase configuration
SUPABASE_URL = os.getenv("SUPABASE_URL", "https://ktoqznqmlnxrtvubewyz.supabase.co")
SUPABASE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

if not SUPABASE_KEY:
    print("Error: SUPABASE_SERVICE_ROLE_KEY not found in environment variables")
    sys.exit(1)

# Initialize Supabase client
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

class CompanyWebsiteFinder:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # Common hemp company domain patterns
        self.domain_patterns = [
            '{name}.com',
            '{name}hemp.com',
            '{name}company.com',
            '{name}inc.com',
            '{name}brands.com',
            '{name}products.com',
            'hemp{name}.com',
            'the{name}.com',
            '{name}cbd.com',
            '{name}wellness.com',
            '{name}natural.com',
            '{name}organic.com',
        ]
    
    def find_website(self, company_name: str, description: str = "") -> Optional[str]:
        """Try to find a company's website using various strategies"""
        
        # Clean company name for URL
        clean_name = self._clean_company_name(company_name)
        
        # Strategy 1: Try common domain patterns
        print(f"  Searching for website of '{company_name}'...")
        
        for pattern in self.domain_patterns:
            domain = pattern.format(name=clean_name)
            url = f"https://{domain}"
            
            if self._validate_url(url):
                print(f"    ✓ Found: {url}")
                return url
        
        # Strategy 2: Google search (using DuckDuckGo as fallback)
        search_url = self._search_for_website(company_name, description)
        if search_url:
            print(f"    ✓ Found via search: {search_url}")
            return search_url
        
        # Strategy 3: Check social media patterns
        social_urls = self._check_social_media(company_name)
        if social_urls:
            print(f"    ✓ Found social: {social_urls[0]}")
            return social_urls[0]
        
        print(f"    ✗ No website found")
        return None
    
    def _clean_company_name(self, name: str) -> str:
        """Clean company name for domain checking"""
        # Remove common suffixes
        name = re.sub(r'\s*(inc|llc|ltd|co|company|corp|corporation)\.?\s*$', '', name, flags=re.I)
        
        # Remove special characters and spaces
        name = re.sub(r'[^a-zA-Z0-9]', '', name)
        
        return name.lower()
    
    def _validate_url(self, url: str, timeout: int = 5) -> bool:
        """Check if a URL is valid and accessible"""
        try:
            response = self.session.head(url, timeout=timeout, allow_redirects=True)
            
            # Check if it's a valid response
            if response.status_code < 400:
                # Additional check: ensure it's not a domain parking page
                if response.status_code == 200:
                    try:
                        # Do a GET request to check content
                        get_response = self.session.get(url, timeout=timeout)
                        content_lower = get_response.text.lower()
                        
                        # Check for domain parking indicators
                        parking_indicators = [
                            'domain for sale',
                            'this domain is parked',
                            'buy this domain',
                            'domain parking',
                            'coming soon',
                            'under construction'
                        ]
                        
                        if any(indicator in content_lower for indicator in parking_indicators):
                            return False
                            
                    except:
                        pass
                
                return True
                
        except requests.exceptions.RequestException:
            pass
        
        return False
    
    def _search_for_website(self, company_name: str, description: str) -> Optional[str]:
        """Search for company website using DuckDuckGo"""
        try:
            # Use DuckDuckGo HTML interface
            search_query = f"{company_name} hemp company official website"
            if description:
                # Add key terms from description
                key_terms = re.findall(r'\b(CBD|hemp|cannabis|THC|wellness|organic)\b', description, re.I)
                if key_terms:
                    search_query += " " + " ".join(set(key_terms[:2]))
            
            search_url = f"https://html.duckduckgo.com/html/?q={quote(search_query)}"
            
            response = self.session.get(search_url, timeout=10)
            
            if response.status_code == 200:
                # Extract URLs from search results
                urls = re.findall(r'href="(https?://[^"]+)"', response.text)
                
                # Filter and validate URLs
                for url in urls[:10]:  # Check first 10 results
                    # Skip DuckDuckGo internal URLs
                    if 'duckduckgo.com' in url:
                        continue
                    
                    # Clean up URL
                    url = url.split('?')[0]  # Remove query parameters
                    
                    # Get domain
                    domain = urlparse(url).netloc
                    
                    # Check if domain contains company name
                    clean_name = self._clean_company_name(company_name)
                    if clean_name in domain.lower():
                        if self._validate_url(url):
                            return url
                
        except Exception as e:
            print(f"    Search error: {e}")
        
        return None
    
    def _check_social_media(self, company_name: str) -> List[str]:
        """Check for social media profiles as fallback"""
        found_urls = []
        clean_name = self._clean_company_name(company_name)
        
        social_patterns = [
            f"https://www.instagram.com/{clean_name}",
            f"https://www.facebook.com/{clean_name}",
            f"https://twitter.com/{clean_name}",
            f"https://www.linkedin.com/company/{clean_name}",
        ]
        
        for url in social_patterns:
            if self._validate_url(url, timeout=3):
                found_urls.append(url)
        
        return found_urls

def validate_existing_websites():
    """Validate all existing company websites"""
    print("Validating existing company websites...")
    
    companies = supabase.table('hemp_companies') \
        .select('id, name, website') \
        .not_.is_('website', 'null') \
        .execute()
    
    if not companies.data:
        print("No companies with websites found")
        return
    
    print(f"Validating {len(companies.data)} websites...")
    
    finder = CompanyWebsiteFinder()
    invalid_count = 0
    
    for company in companies.data:
        company_id = company['id']
        company_name = company['name']
        website = company['website']
        
        print(f"\nValidating: {company_name} - {website}")
        
        if not finder._validate_url(website):
            print(f"  ✗ Invalid or inaccessible")
            invalid_count += 1
            
            # Try to find a new website
            new_website = finder.find_website(company_name)
            
            if new_website and new_website != website:
                print(f"  → Updating to: {new_website}")
                
                supabase.table('hemp_companies') \
                    .update({'website': new_website}) \
                    .eq('id', company_id) \
                    .execute()
            else:
                # Mark as invalid by setting to NULL
                print(f"  → Removing invalid URL")
                supabase.table('hemp_companies') \
                    .update({'website': None}) \
                    .eq('id', company_id) \
                    .execute()
        else:
            print(f"  ✓ Valid")
        
        time.sleep(0.5)  # Rate limiting
    
    print(f"\n{'='*50}")
    print(f"Validation complete!")
    print(f"Found {invalid_count} invalid websites")

def find_missing_websites():
    """Find websites for companies without them"""
    print("Finding websites for companies without URLs...")
    
    # Get companies without websites, prioritizing those with more products
    companies = supabase.table('hemp_companies') \
        .select('*') \
        .is_('website', 'null') \
        .execute()
    
    if not companies.data:
        print("All companies have websites")
        return
    
    # Sort by product count (we'll need to get this separately)
    print(f"Processing {len(companies.data)} companies without websites...")
    
    finder = CompanyWebsiteFinder()
    found_count = 0
    
    for company in companies.data:
        company_id = company['id']
        company_name = company['name']
        description = company.get('description', '')
        
        print(f"\nProcessing: {company_name}")
        
        website = finder.find_website(company_name, description)
        
        if website:
            print(f"  → Updating database with: {website}")
            
            supabase.table('hemp_companies') \
                .update({'website': website}) \
                .eq('id', company_id) \
                .execute()
            
            found_count += 1
        
        time.sleep(1)  # Rate limiting
    
    print(f"\n{'='*50}")
    print(f"Website search complete!")
    print(f"Found websites for {found_count} out of {len(companies.data)} companies")

def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Find and validate company websites')
    parser.add_argument('--validate', action='store_true', help='Validate existing websites')
    parser.add_argument('--find', action='store_true', help='Find missing websites')
    parser.add_argument('--company', type=str, help='Process specific company')
    
    args = parser.parse_args()
    
    if args.company:
        # Process specific company
        finder = CompanyWebsiteFinder()
        
        result = supabase.table('hemp_companies') \
            .select('*') \
            .ilike('name', f'%{args.company}%') \
            .limit(1) \
            .execute()
        
        if result.data:
            company = result.data[0]
            print(f"Processing: {company['name']}")
            
            if company['website']:
                print(f"Current website: {company['website']}")
                if finder._validate_url(company['website']):
                    print("✓ Website is valid")
                else:
                    print("✗ Website is invalid")
            
            website = finder.find_website(company['name'], company.get('description', ''))
            if website:
                print(f"Found website: {website}")
        else:
            print(f"Company '{args.company}' not found")
    
    elif args.validate:
        validate_existing_websites()
    
    elif args.find:
        find_missing_websites()
    
    else:
        # Do both
        find_missing_websites()
        print("\n" + "="*70 + "\n")
        validate_existing_websites()

if __name__ == "__main__":
    main()