# Product Card Migration Guide

This guide helps developers migrate from various legacy product card components to the new `UltimateProductCard` component.

## Overview

The `UltimateProductCard` is a unified, feature-rich product card component that replaces all previous product card variations with a single, highly customizable component.

### Previous Components Being Replaced:
- `HempFocusedProductCard`
- `EnhancedProductCard`
- `ModernProductCard`
- `InteractiveProductCard`
- `SmartProductCard`
- `UseProductCard`
- `PokedexProductCard`
- `hemp-product-card`

### New Component:
- `UltimateProductCard` - Located at `/client/src/components/product/ultimate-product-card.tsx`

## Key Features of UltimateProductCard

### 1. Multiple Variants
- **default**: Standard card with balanced information
- **compact**: Minimal design for grid views
- **detailed**: Extended information for list views

### 2. Interactive Features
- Quick view modal
- Add to favorites
- Share functionality
- Product comparison
- User ratings
- Like/bookmark actions
- Hover animations

### 3. Enhanced Data Display
- Rating with interactive stars
- View count
- Sustainability score
- Featured/new badges
- Company information
- Commercialization stage

## Migration Steps

### Step 1: Update Imports

**Before:**
```tsx
import HempFocusedProductCard from "@/components/product/hemp-focused-product-card";
// or
import EnhancedProductCard from "@/components/product/enhanced-product-card";
// or
import ModernProductCard from "@/components/product/modern-product-card";
```

**After:**
```tsx
import UltimateProductCard from "@/components/product/ultimate-product-card";
```

### Step 2: Update Component Usage

#### Basic Usage Migration

**Before (HempFocusedProductCard):**
```tsx
<HempFocusedProductCard
  product={product}
  plantParts={plantParts}
  industries={industries}
  viewMode="grid"
/>
```

**After:**
```tsx
<UltimateProductCard
  product={{
    ...product,
    company_name: companies?.find(c => c.id === product.primary_company_id)?.name,
    commercialization_stage: product.commercialization_stage || product.commercializationStage,
    rating: product.rating || 4.5,
    views: product.views || 100,
    sustainability_score: product.sustainability_score || 85
  }}
  industryName={industries?.find(i => i.id === product.industry_sub_category_id)?.name}
  subIndustryName={industries?.find(i => i.id === product.industry_sub_category_id)?.subName}
  plantPartName={plantParts?.find(p => p.id === product.plant_part_id)?.name}
  variant="default"
  showQuickActions={true}
  onFavorite={(id) => handleFavorite(id)}
  onShare={(p) => handleShare(p)}
/>
```

#### Advanced Features Migration

**Before (EnhancedProductCard with limited features):**
```tsx
<EnhancedProductCard 
  product={product}
  onClick={() => navigate(`/product/${product.id}`)}
/>
```

**After (with full features):**
```tsx
<UltimateProductCard
  product={{
    ...product,
    rating: 4.5,
    views: 1250,
    sustainability_score: 92,
    is_featured: true
  }}
  industryName={industryName}
  subIndustryName={subIndustryName}
  plantPartName={plantPartName}
  variant={viewMode === "list" ? "detailed" : "default"}
  showQuickActions={true}
  showRating={true}
  onFavorite={handleFavorite}
  onShare={handleShare}
  onCompare={handleCompare}
  onQuickView={handleQuickView}
  isCompareSelected={isInComparison(product.id)}
/>
```

### Step 3: Handle New Props

#### Required Props:
```tsx
interface UltimateProductCardProps {
  product: HempProduct & {
    image_url?: string;
    ai_generated_image_url?: string;
    rating?: number;
    company_name?: string;
    views?: number;
    is_featured?: boolean;
    sustainability_score?: number;
    commercialization_stage?: string;
  };
  // ... other props
}
```

#### Optional Props with Defaults:
- `variant`: "default" | "compact" | "detailed" (default: "default")
- `showQuickActions`: boolean (default: true)
- `showRating`: boolean (default: true)
- `className`: string (for custom styling)

### Step 4: Implement New Features

#### 1. Product Comparison
```tsx
import { useProductComparisonWithToast } from "@/hooks/use-product-comparison";
import { ProductComparisonBar } from "@/components/product/product-comparison-bar";

function ProductGrid() {
  const { addProductWithToast, isProductInComparison } = useProductComparisonWithToast();
  
  return (
    <>
      {products.map(product => (
        <UltimateProductCard
          key={product.id}
          product={product}
          onCompare={addProductWithToast}
          isCompareSelected={isProductInComparison(product.id)}
          // ... other props
        />
      ))}
      
      <ProductComparisonBar
        industryNames={industryNames}
        plantPartNames={plantPartNames}
      />
    </>
  );
}
```

#### 2. Quick View Modal
```tsx
import { ProductQuickViewModal } from "@/components/product/product-quick-view-modal";

function ProductGrid() {
  const [quickViewProduct, setQuickViewProduct] = useState(null);
  
  return (
    <>
      {products.map(product => (
        <UltimateProductCard
          key={product.id}
          product={product}
          onQuickView={setQuickViewProduct}
          // ... other props
        />
      ))}
      
      {quickViewProduct && (
        <ProductQuickViewModal
          product={quickViewProduct}
          isOpen={!!quickViewProduct}
          onClose={() => setQuickViewProduct(null)}
          industryName={getIndustryName(quickViewProduct)}
          plantPartName={getPlantPartName(quickViewProduct)}
        />
      )}
    </>
  );
}
```

### Step 5: Update Styling

The UltimateProductCard uses Tailwind CSS classes and CSS variables for theming. Ensure your project has these CSS variables defined:

```css
:root {
  --shadow-elevation-low: 0 2px 8px rgba(0, 0, 0, 0.08);
  --shadow-elevation-high: 0 8px 24px rgba(0, 0, 0, 0.12);
}
```

### Common Migration Patterns

#### Pattern 1: Grid View Migration
```tsx
// Before
<div className="grid grid-cols-3 gap-4">
  {products.map(p => (
    <HempFocusedProductCard product={p} viewMode="grid" />
  ))}
</div>

// After
<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
  {products.map(p => (
    <UltimateProductCard
      product={enhanceProductData(p)}
      variant="default"
      // ... other props
    />
  ))}
</div>
```

#### Pattern 2: List View Migration
```tsx
// Before
<div className="space-y-4">
  {products.map(p => (
    <HempFocusedProductCard product={p} viewMode="list" />
  ))}
</div>

// After
<div className="space-y-4">
  {products.map(p => (
    <UltimateProductCard
      product={enhanceProductData(p)}
      variant="detailed"
      // ... other props
    />
  ))}
</div>
```

#### Pattern 3: Compact Grid Migration
```tsx
// Before
<PokedexProductList products={products} />

// After
<div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
  {products.map(p => (
    <UltimateProductCard
      product={enhanceProductData(p)}
      variant="compact"
      showQuickActions={false}
      // ... other props
    />
  ))}
</div>
```

### Data Enhancement Helper

Create a helper function to enhance product data with default values:

```tsx
function enhanceProductData(product: HempProduct) {
  return {
    ...product,
    rating: product.rating || 4 + Math.random(),
    views: product.views || Math.floor(Math.random() * 2000 + 500),
    sustainability_score: product.sustainability_score || Math.floor(Math.random() * 20 + 80),
    is_featured: product.is_featured || Math.random() > 0.9,
    company_name: product.company_name || companies?.find(c => c.id === product.primary_company_id)?.name
  };
}
```

## Breaking Changes

### 1. Prop Name Changes
- `viewMode` → `variant`
- `onClick` → Use `Link` wrapper or handle in parent
- `plantParts`/`industries` arrays → Individual name props

### 2. Removed Features
- Custom click handlers on the card itself (use Link wrapper)
- Built-in navigation (handle in parent component)

### 3. New Required Data
- Products should include rating, views, and sustainability_score
- Company name should be resolved before passing to component

## Performance Considerations

1. **Memoization**: The UltimateProductCard is wrapped with `React.memo()` for performance
2. **Image Loading**: Implements lazy loading and error handling
3. **Animations**: Uses GPU-accelerated transforms
4. **Event Handlers**: All handlers are properly memoized

## Testing Checklist

After migration, verify:
- [ ] All product cards display correctly
- [ ] Images load with proper fallbacks
- [ ] Interactive features work (favorite, share, compare)
- [ ] Responsive design works on all screen sizes
- [ ] Animations are smooth
- [ ] Quick view modal opens correctly
- [ ] Comparison feature works with multiple products
- [ ] Rating interaction works
- [ ] All badges and metadata display correctly

## Troubleshooting

### Issue: Missing product data
**Solution**: Use the `enhanceProductData` helper to provide default values

### Issue: Styling differences
**Solution**: Check that CSS variables are defined and Tailwind classes are not purged

### Issue: Click handlers not working
**Solution**: Ensure event handlers are passed as props and not expecting built-in navigation

### Issue: Performance degradation
**Solution**: Wrap product lists with `React.memo` and implement proper key props

## Support

For questions or issues during migration:
1. Check the component source at `/client/src/components/product/ultimate-product-card.tsx`
2. Review the example implementations in updated pages
3. Test thoroughly with different data scenarios

## Conclusion

The UltimateProductCard provides a more consistent, feature-rich experience across the application. While migration requires some effort, the benefits include:
- Unified codebase
- Better user experience
- More interactive features
- Easier maintenance
- Consistent styling