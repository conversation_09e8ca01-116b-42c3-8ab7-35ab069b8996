# Product Card Migration Quick Checklist

## Pre-Migration Checklist
- [ ] Identify all pages using old product card components
- [ ] Review current card features and interactions
- [ ] Plan data enhancement strategy for missing fields
- [ ] Set up comparison and quick view infrastructure

## Migration Steps

### 1. Update Imports
- [ ] Replace old component imports with `UltimateProductCard`
- [ ] Import comparison hooks if needed
- [ ] Import quick view modal if needed

### 2. Data Preparation
- [ ] Add `rating` field (default: 4-5)
- [ ] Add `views` field (default: 100-2000)
- [ ] Add `sustainability_score` field (default: 70-100)
- [ ] Add `company_name` resolution
- [ ] Add `is_featured` flag if needed

### 3. Component Props Update
- [ ] Change `viewMode` to `variant`
- [ ] Add `industryName` prop
- [ ] Add `plantPartName` prop
- [ ] Add event handlers (onFavorite, onShare, etc.)
- [ ] Set `showQuickActions` preference

### 4. Feature Implementation
- [ ] Implement favorite handler
- [ ] Implement share handler
- [ ] Add comparison functionality (optional)
- [ ] Add quick view modal (optional)
- [ ] Add rating interaction (optional)

### 5. Layout Adjustments
- [ ] Update grid classes for responsive design
- [ ] Adjust spacing between cards
- [ ] Update container widths if needed

### 6. Testing
- [ ] Test on desktop (1920x1080)
- [ ] Test on tablet (768px)
- [ ] Test on mobile (375px)
- [ ] Verify all interactions work
- [ ] Check image loading and fallbacks
- [ ] Verify animations are smooth

## Component Mapping Reference

| Old Component | New Variant | Notes |
|--------------|-------------|-------|
| HempFocusedProductCard (grid) | variant="default" | Standard card |
| HempFocusedProductCard (list) | variant="detailed" | Extended info |
| EnhancedProductCard | variant="default" | Add interactive features |
| ModernProductCard | variant="default" | Similar styling |
| PokedexProductCard | variant="compact" | Minimal design |

## Common Issues & Solutions

| Issue | Solution |
|-------|----------|
| Missing rating/views | Use default values or fetch from API |
| Broken layout | Update grid classes to new responsive system |
| Click not working | Remove onClick, use Link wrapper |
| No hover effects | Ensure Tailwind classes aren't purged |
| Comparison not working | Import and setup comparison hooks |

## Code Snippets

### Basic Migration
```tsx
// Minimal migration
<UltimateProductCard
  product={{...product, rating: 4.5, views: 500}}
  variant="default"
/>
```

### Full Features
```tsx
// All features enabled
<UltimateProductCard
  product={enhancedProduct}
  industryName={industryName}
  plantPartName={plantPartName}
  variant="default"
  showQuickActions={true}
  onFavorite={handleFavorite}
  onShare={handleShare}
  onCompare={handleCompare}
  onQuickView={handleQuickView}
  isCompareSelected={isSelected}
/>
```

### With Comparison Bar
```tsx
// Don't forget the comparison bar!
<ProductComparisonBar
  industryNames={industryNames}
  plantPartNames={plantPartNames}
/>
```

## Final Verification
- [ ] All old components removed
- [ ] No TypeScript errors
- [ ] UI matches design requirements
- [ ] Performance is maintained or improved
- [ ] User interactions work as expected