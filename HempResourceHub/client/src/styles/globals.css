@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Modern Color Palette - Sophisticated & Clean */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    
    --primary: 142 76% 36%;
    --primary-foreground: 355.7 100% 97.3%;
    
    --secondary: 263 70% 50%;
    --secondary-foreground: 210 40% 98%;
    
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 142 76% 36%;
    
    --radius: 0.75rem;
    
    /* Custom properties for enhanced design */
    --shadow-color: 220 30% 5%;
    --shadow-elevation-low: 0px 0.5px 1.1px hsl(var(--shadow-color) / 0.02),
                            0px 0.8px 1.8px hsl(var(--shadow-color) / 0.028),
                            0px 1.5px 3.4px hsl(var(--shadow-color) / 0.035);
    --shadow-elevation-medium: 0px 0.5px 1.1px hsl(var(--shadow-color) / 0.021),
                               0px 2.1px 4.7px hsl(var(--shadow-color) / 0.032),
                               0px 9.5px 21.3px hsl(var(--shadow-color) / 0.052);
    --shadow-elevation-high: 0px 0.5px 1.1px hsl(var(--shadow-color) / 0.02),
                             0px 3.7px 8.3px hsl(var(--shadow-color) / 0.03),
                             0px 14px 31.3px hsl(var(--shadow-color) / 0.04),
                             0px 33.3px 74.4px hsl(var(--shadow-color) / 0.06);
  }
 
  .dark {
    /* Sophisticated Dark Mode - Not pure black */
    --background: 220 25% 7%;
    --foreground: 210 40% 98%;
    
    --card: 220 25% 9%;
    --card-foreground: 210 40% 98%;
    
    --popover: 220 25% 7%;
    --popover-foreground: 210 40% 98%;
    
    --primary: 142 70% 45%;
    --primary-foreground: 220 25% 7%;
    
    --secondary: 263 70% 65%;
    --secondary-foreground: 220 25% 7%;
    
    --muted: 220 20% 20%;
    --muted-foreground: 215 20% 65%;
    
    --accent: 220 20% 20%;
    --accent-foreground: 210 40% 98%;
    
    --destructive: 0 70% 50%;
    --destructive-foreground: 210 40% 98%;
    
    --border: 220 20% 18%;
    --input: 220 20% 18%;
    --ring: 142 70% 45%;
    
    /* Dark mode shadow adjustments */
    --shadow-color: 220 40% 2%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', system-ui, sans-serif;
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
    font-variation-settings: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  /* Improved focus states */
  *:focus-visible {
    @apply outline-none ring-2 ring-ring ring-offset-2 ring-offset-background;
  }
  
  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }
  
  /* Better selection colors */
  ::selection {
    @apply bg-primary/20 text-primary-foreground;
  }
}

@layer components {
  /* Modern card styles */
  .card-modern {
    @apply bg-card rounded-xl border border-border/50 shadow-[var(--shadow-elevation-low)] transition-all duration-200;
  }
  
  .card-modern:hover {
    @apply shadow-[var(--shadow-elevation-medium)] border-border;
  }
  
  /* Glass morphism effect */
  .glass {
    @apply bg-background/80 backdrop-blur-xl border border-border/50;
  }
  
  /* Gradient backgrounds */
  .gradient-primary {
    @apply bg-gradient-to-br from-primary/90 to-primary;
  }
  
  .gradient-secondary {
    @apply bg-gradient-to-br from-secondary/90 to-secondary;
  }
  
  .gradient-mesh {
    background-image: 
      radial-gradient(at 27% 37%, hsla(215, 98%, 61%, 0.15) 0px, transparent 50%),
      radial-gradient(at 97% 21%, hsla(125, 98%, 72%, 0.15) 0px, transparent 50%),
      radial-gradient(at 52% 99%, hsla(354, 98%, 61%, 0.15) 0px, transparent 50%);
  }
  
  /* Text styles */
  .text-gradient {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary;
  }
  
  /* Button improvements */
  .btn-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary/90 shadow-[var(--shadow-elevation-low)] hover:shadow-[var(--shadow-elevation-medium)] transition-all duration-200;
  }
  
  .btn-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-secondary/90 shadow-[var(--shadow-elevation-low)] hover:shadow-[var(--shadow-elevation-medium)] transition-all duration-200;
  }
  
  /* Input improvements */
  .input-modern {
    @apply bg-background border-input focus:border-primary transition-colors duration-200;
  }
  
  /* Skeleton loading */
  .skeleton {
    @apply animate-pulse bg-muted/50 rounded-md;
  }
}

@layer utilities {
  /* Spacing scale */
  .section-padding {
    @apply px-4 sm:px-6 lg:px-8;
  }
  
  .section-y-padding {
    @apply py-8 sm:py-12 lg:py-16;
  }
  
  /* Container improvements */
  .container-custom {
    @apply container mx-auto section-padding;
  }
  
  /* Grid layouts */
  .grid-responsive {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6;
  }
  
  /* Animations */
  .animate-in {
    animation: animate-in 0.5s ease-out;
  }
  
  @keyframes animate-in {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .animate-fade-in {
    animation: fade-in 0.3s ease-out;
  }
  
  @keyframes fade-in {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
  
  /* Hover animations */
  .hover-lift {
    @apply transition-transform duration-200 hover:-translate-y-1;
  }
  
  .hover-grow {
    @apply transition-transform duration-200 hover:scale-105;
  }
  
  /* Loading states */
  .loading-shimmer {
    background: linear-gradient(
      90deg,
      hsl(var(--muted) / 0.5) 0%,
      hsl(var(--muted) / 0.3) 50%,
      hsl(var(--muted) / 0.5) 100%
    );
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
  }
  
  @keyframes shimmer {
    0% {
      background-position: 200% 0;
    }
    100% {
      background-position: -200% 0;
    }
  }
  
  /* Scrollbar styling */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted)) transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-track {
    @apply bg-transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb {
    @apply bg-muted rounded-full;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/30;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --border: 214.3 31.8% 31.4%;
  }
  
  .dark {
    --border: 220 20% 68%;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}