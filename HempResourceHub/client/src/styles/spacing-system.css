/* 
 * Global Spacing System
 * Based on 4-point grid system for consistent UI spacing
 */

:root {
  /* Base spacing unit (4px) */
  --space-unit: 0.25rem;
  
  /* Spacing scale */
  --space-0: 0;                          /* 0px */
  --space-1: calc(var(--space-unit) * 1);  /* 4px */
  --space-2: calc(var(--space-unit) * 2);  /* 8px */
  --space-3: calc(var(--space-unit) * 3);  /* 12px */
  --space-4: calc(var(--space-unit) * 4);  /* 16px */
  --space-5: calc(var(--space-unit) * 5);  /* 20px */
  --space-6: calc(var(--space-unit) * 6);  /* 24px */
  --space-7: calc(var(--space-unit) * 7);  /* 28px */
  --space-8: calc(var(--space-unit) * 8);  /* 32px */
  --space-9: calc(var(--space-unit) * 9);  /* 36px */
  --space-10: calc(var(--space-unit) * 10); /* 40px */
  --space-12: calc(var(--space-unit) * 12); /* 48px */
  --space-14: calc(var(--space-unit) * 14); /* 56px */
  --space-16: calc(var(--space-unit) * 16); /* 64px */
  --space-20: calc(var(--space-unit) * 20); /* 80px */
  --space-24: calc(var(--space-unit) * 24); /* 96px */
  --space-32: calc(var(--space-unit) * 32); /* 128px */
  
  /* Line height scale */
  --line-height-tight: 1.1;
  --line-height-snug: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;
  
  /* Letter spacing */
  --letter-spacing-tighter: -0.05em;
  --letter-spacing-tight: -0.025em;
  --letter-spacing-normal: 0;
  --letter-spacing-wide: 0.025em;
  --letter-spacing-wider: 0.05em;
  --letter-spacing-widest: 0.1em;
  
  /* Component spacing */
  --component-padding-xs: var(--space-2) var(--space-3);
  --component-padding-sm: var(--space-3) var(--space-4);
  --component-padding-md: var(--space-4) var(--space-6);
  --component-padding-lg: var(--space-5) var(--space-8);
  --component-padding-xl: var(--space-6) var(--space-10);
  
  /* Section spacing */
  --section-spacing-sm: var(--space-8);
  --section-spacing-md: var(--space-12);
  --section-spacing-lg: var(--space-16);
  --section-spacing-xl: var(--space-24);
  
  /* Container padding */
  --container-padding-mobile: var(--space-4);
  --container-padding-tablet: var(--space-6);
  --container-padding-desktop: var(--space-8);
}

/* Typography with proper line height */
.text-xs {
  font-size: 0.75rem;
  line-height: var(--line-height-normal);
}

.text-sm {
  font-size: 0.875rem;
  line-height: var(--line-height-normal);
}

.text-base {
  font-size: 1rem;
  line-height: var(--line-height-normal);
}

.text-lg {
  font-size: 1.125rem;
  line-height: var(--line-height-relaxed);
}

.text-xl {
  font-size: 1.25rem;
  line-height: var(--line-height-snug);
}

.text-2xl {
  font-size: 1.5rem;
  line-height: var(--line-height-snug);
}

.text-3xl {
  font-size: 1.875rem;
  line-height: var(--line-height-tight);
}

.text-4xl {
  font-size: 2.25rem;
  line-height: var(--line-height-tight);
}

/* Heading specific line heights */
h1, .h1 {
  line-height: var(--line-height-tight);
  margin-top: 0;
  margin-bottom: var(--space-6);
}

h2, .h2 {
  line-height: var(--line-height-snug);
  margin-top: var(--space-10);
  margin-bottom: var(--space-5);
}

h3, .h3 {
  line-height: var(--line-height-snug);
  margin-top: var(--space-8);
  margin-bottom: var(--space-4);
}

h4, .h4, h5, .h5, h6, .h6 {
  line-height: var(--line-height-normal);
  margin-top: var(--space-6);
  margin-bottom: var(--space-3);
}

/* Paragraph spacing */
p {
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--space-4);
}

p:last-child {
  margin-bottom: 0;
}

/* List spacing */
ul, ol {
  margin-bottom: var(--space-4);
  padding-left: var(--space-6);
}

li {
  margin-bottom: var(--space-2);
  line-height: var(--line-height-normal);
}

li:last-child {
  margin-bottom: 0;
}

/* Component spacing utilities */
.spacing-xs { padding: var(--component-padding-xs); }
.spacing-sm { padding: var(--component-padding-sm); }
.spacing-md { padding: var(--component-padding-md); }
.spacing-lg { padding: var(--component-padding-lg); }
.spacing-xl { padding: var(--component-padding-xl); }

/* Section spacing utilities */
.section-spacing-sm { padding: var(--section-spacing-sm) 0; }
.section-spacing-md { padding: var(--section-spacing-md) 0; }
.section-spacing-lg { padding: var(--section-spacing-lg) 0; }
.section-spacing-xl { padding: var(--section-spacing-xl) 0; }

/* Margin utilities following 4-point grid */
.mt-0 { margin-top: var(--space-0); }
.mt-1 { margin-top: var(--space-1); }
.mt-2 { margin-top: var(--space-2); }
.mt-3 { margin-top: var(--space-3); }
.mt-4 { margin-top: var(--space-4); }
.mt-5 { margin-top: var(--space-5); }
.mt-6 { margin-top: var(--space-6); }
.mt-8 { margin-top: var(--space-8); }
.mt-10 { margin-top: var(--space-10); }
.mt-12 { margin-top: var(--space-12); }
.mt-16 { margin-top: var(--space-16); }

.mb-0 { margin-bottom: var(--space-0); }
.mb-1 { margin-bottom: var(--space-1); }
.mb-2 { margin-bottom: var(--space-2); }
.mb-3 { margin-bottom: var(--space-3); }
.mb-4 { margin-bottom: var(--space-4); }
.mb-5 { margin-bottom: var(--space-5); }
.mb-6 { margin-bottom: var(--space-6); }
.mb-8 { margin-bottom: var(--space-8); }
.mb-10 { margin-bottom: var(--space-10); }
.mb-12 { margin-bottom: var(--space-12); }
.mb-16 { margin-bottom: var(--space-16); }

/* Padding utilities */
.p-0 { padding: var(--space-0); }
.p-1 { padding: var(--space-1); }
.p-2 { padding: var(--space-2); }
.p-3 { padding: var(--space-3); }
.p-4 { padding: var(--space-4); }
.p-5 { padding: var(--space-5); }
.p-6 { padding: var(--space-6); }
.p-8 { padding: var(--space-8); }
.p-10 { padding: var(--space-10); }
.p-12 { padding: var(--space-12); }
.p-16 { padding: var(--space-16); }

/* Gap utilities for flexbox/grid */
.gap-1 { gap: var(--space-1); }
.gap-2 { gap: var(--space-2); }
.gap-3 { gap: var(--space-3); }
.gap-4 { gap: var(--space-4); }
.gap-5 { gap: var(--space-5); }
.gap-6 { gap: var(--space-6); }
.gap-8 { gap: var(--space-8); }
.gap-10 { gap: var(--space-10); }
.gap-12 { gap: var(--space-12); }
.gap-16 { gap: var(--space-16); }

/* Responsive container padding */
.container {
  padding-left: var(--container-padding-mobile);
  padding-right: var(--container-padding-mobile);
}

@media (min-width: 768px) {
  .container {
    padding-left: var(--container-padding-tablet);
    padding-right: var(--container-padding-tablet);
  }
}

@media (min-width: 1024px) {
  .container {
    padding-left: var(--container-padding-desktop);
    padding-right: var(--container-padding-desktop);
  }
}

/* Card and component spacing */
.card {
  padding: var(--space-6);
  margin-bottom: var(--space-4);
}

.card-compact {
  padding: var(--space-4);
}

.card-spacious {
  padding: var(--space-8);
}

/* Button spacing */
.btn {
  padding: var(--space-3) var(--space-6);
  line-height: var(--line-height-normal);
}

.btn-sm {
  padding: var(--space-2) var(--space-4);
}

.btn-lg {
  padding: var(--space-4) var(--space-8);
}

/* Form element spacing */
input,
textarea,
select {
  padding: var(--space-3) var(--space-4);
  line-height: var(--line-height-normal);
}

.form-group {
  margin-bottom: var(--space-6);
}

.form-label {
  margin-bottom: var(--space-2);
  line-height: var(--line-height-normal);
}

/* Navigation spacing */
.nav-item {
  padding: var(--space-3) var(--space-4);
  margin: 0 var(--space-1);
}

.nav-link {
  padding: var(--space-2) var(--space-3);
  line-height: var(--line-height-normal);
}

/* Prevent text overlap */
.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-break {
  word-wrap: break-word;
  word-break: break-word;
}

/* Ensure minimum touch targets */
.interactive {
  min-height: 44px;
  min-width: 44px;
}

/* Stack spacing for vertical layouts */
.stack > * + * {
  margin-top: var(--space-4);
}

.stack-sm > * + * {
  margin-top: var(--space-2);
}

.stack-lg > * + * {
  margin-top: var(--space-8);
}

/* Inline spacing for horizontal layouts */
.inline > * + * {
  margin-left: var(--space-4);
}

.inline-sm > * + * {
  margin-left: var(--space-2);
}

.inline-lg > * + * {
  margin-left: var(--space-8);
}