# Hemp Resource Hub Design System

## Color Palette

### Primary Colors
- **Black (Primary)**: `#000000` - Main brand color, used for backgrounds and primary elements
- **Purple (Secondary)**: `#8b5cf6` - System actions, interactive elements, secondary highlights
- **Hemp Green (Accent)**: `#22c55e` - Hemp-specific elements, success states, CTAs

### Color Usage Guidelines

#### Backgrounds
- **Main Background**: Pure black (`#000000`)
- **Card Background**: `#0a0a0a` (dark-card)
- **Hover States**: `#111111` (dark-hover)
- **Borders**: `#1a1a1a` (dark-border)

#### Text Colors
- **Primary Text**: White (`#ffffff`)
- **Secondary Text**: Gray-400 (`#9ca3af`)
- **Muted Text**: Gray-500 (`#6b7280`)

#### Status Colors
- **Growing Stage**: Hemp green with 20% opacity background
- **Established Stage**: Purple with 20% opacity background
- **Research Stage**: Purple with 20% opacity background
- **Speculative Stage**: Gray with 20% opacity background

## Typography

### Font Stack
- **Brand Font**: SweetLeaf (for hemp-related branding only)
- **Headings**: Inter (weights 600-900)
- **Body Text**: Source Sans 3
- **Data/Stats**: Space Grotesk

### Usage
- Use `.font-hemp` class for hemp branding elements
- All headings use Inter by default
- Body text uses Source Sans 3 for readability

## Component Standards

### Product Cards

#### Unified Product Card (`hemp-focused-product-card.tsx`)
This is the primary product card component that should be used throughout the app.

**Features:**
- Three view modes: grid, list, mobile
- Prominent plant part display
- Benefits-focused content hierarchy
- Commercialization stage badges with icons
- AI-generated image indicators
- Company associations

**Visual Hierarchy:**
1. Image with plant part overlay
2. Product name
3. Industry category
4. Commercialization stage
5. Key benefits (2-3 max in grid view)
6. Company associations (if available)

#### Deprecated Components
The following product card variants should be phased out:
- `enhanced-product-card.tsx` (replaced by hemp-focused)
- `product-card.tsx` (too basic)
- `interactive-product-card.tsx` (too complex)
- `modern-product-card.tsx` (inconsistent styling)
- `UseProductCard.tsx` (redundant)
- `PokedexProductCard.tsx` (off-brand)
- `hemp-product-card.tsx` (UI library version)

### Button Styles

#### Primary Actions
- Background: Hemp green (`#22c55e`)
- Hover: Hemp green 600 (`#16a34a`)
- Text: White

#### Secondary Actions
- Background: Purple (`#8b5cf6`)
- Hover: Purple dark (`#6b21a8`)
- Text: White

#### Outline/Ghost
- Border: Current theme color with 50% opacity
- Background: Transparent
- Hover: Theme color with 10% opacity

### Form Elements

#### Inputs
- Background: Black (`#000000`)
- Border: Dark border (`#1a1a1a`)
- Focus: Hemp green border with ring
- Text: White
- Placeholder: Gray-500

#### Select/Dropdown
- Same styling as inputs
- Dropdown background: Dark card (`#0a0a0a`)

### Spacing System
- Base unit: 4px
- Common spacings: 8px, 16px, 24px, 32px, 48px, 64px
- Use Tailwind classes: `space-y-4`, `gap-4`, `p-4`, etc.

### Responsive Breakpoints
- Mobile: < 640px
- Tablet: 640px - 1024px
- Desktop: > 1024px

### Animation Guidelines
- Use subtle transitions (300ms duration)
- Hover effects: scale, shadow, color change
- Avoid excessive animations
- Focus on user feedback and state changes

## Implementation Checklist

### Phase 1: Color Standardization ✅
- [x] Update Tailwind config with new color system
- [x] Replace marine theme with dark theme
- [x] Update CSS variables in theme.css
- [x] Create hemp-focused product card

### Phase 2: Component Consolidation (In Progress)
- [ ] Migrate all product displays to hemp-focused-product-card
- [ ] Remove deprecated product card components
- [ ] Update all pages to use new color classes
- [ ] Standardize button and form styling

### Phase 3: Mobile Optimization
- [ ] Enhance touch targets (min 44px)
- [ ] Improve mobile navigation
- [ ] Optimize card layouts for small screens
- [ ] Add swipe gestures where appropriate

### Phase 4: Information Architecture
- [ ] Prioritize hemp-specific data in all views
- [ ] Add plant part filters to search
- [ ] Enhance benefit display prominence
- [ ] Improve industry categorization visibility