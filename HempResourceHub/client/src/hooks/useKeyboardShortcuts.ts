import { useEffect } from "react";
import { useLocation } from "wouter";

interface ShortcutConfig {
  key: string;
  ctrlKey?: boolean;
  shiftKey?: boolean;
  altKey?: boolean;
  metaKey?: boolean;
  handler: () => void;
  description: string;
}

/**
 * Custom hook for managing keyboard shortcuts throughout the application
 */
export function useKeyboardShortcuts(shortcuts: ShortcutConfig[]) {
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Don't trigger shortcuts when typing in input fields
      const target = e.target as HTMLElement;
      const isInput = target.tagName === 'INPUT' || 
                     target.tagName === 'TEXTAREA' || 
                     target.contentEditable === 'true';
      
      if (isInput && !e.ctrlKey && !e.metaKey) {
        return;
      }

      shortcuts.forEach(shortcut => {
        const keyMatch = e.key.toLowerCase() === shortcut.key.toLowerCase();
        const ctrlMatch = !shortcut.ctrlKey || e.ctrlKey;
        const shiftMatch = !shortcut.shiftKey || e.shiftKey;
        const altMatch = !shortcut.altKey || e.altKey;
        const metaMatch = !shortcut.metaKey || e.metaKey;

        if (keyMatch && ctrlMatch && shiftMatch && altMatch && metaMatch) {
          e.preventDefault();
          shortcut.handler();
        }
      });
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [shortcuts]);
}

/**
 * Global keyboard shortcuts for the application
 */
export function useGlobalKeyboardShortcuts() {
  const [, setLocation] = useLocation();

  const shortcuts: ShortcutConfig[] = [
    {
      key: '/',
      handler: () => {
        // Focus search input
        const searchInput = document.querySelector('#product-search, [aria-label*="Search"]') as HTMLInputElement;
        if (searchInput) {
          searchInput.focus();
        }
      },
      description: 'Focus search'
    },
    {
      key: 'g',
      ctrlKey: true,
      handler: () => setLocation('/'),
      description: 'Go to home'
    },
    {
      key: 'p',
      ctrlKey: true,
      handler: () => setLocation('/products'),
      description: 'Go to products'
    },
    {
      key: 'a',
      ctrlKey: true,
      handler: () => setLocation('/dashboard'),
      description: 'Go to analytics'
    },
    {
      key: 'Escape',
      handler: () => {
        // Close modals, dropdowns, or mobile menu
        const closeButton = document.querySelector('[aria-label*="Close"]') as HTMLButtonElement;
        if (closeButton) {
          closeButton.click();
        }
      },
      description: 'Close modal/menu'
    },
    {
      key: '?',
      shiftKey: true,
      handler: () => {
        // Show keyboard shortcuts help
        console.log('Keyboard shortcuts:', shortcuts.map(s => `${s.key}: ${s.description}`));
      },
      description: 'Show keyboard shortcuts'
    }
  ];

  useKeyboardShortcuts(shortcuts);
}