import { create } from "zustand";
import { persist } from "zustand/middleware";
import { HempProduct } from "@shared/schema";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";

interface ComparisonProduct extends HempProduct {
  image_url?: string;
  ai_generated_image_url?: string;
  rating?: number;
  company_name?: string;
  sustainability_score?: number;
  views?: number;
}

interface ProductComparisonStore {
  products: ComparisonProduct[];
  isComparisonOpen: boolean;
  maxProducts: number;
  addProduct: (product: ComparisonProduct) => boolean;
  removeProduct: (productId: number) => void;
  clearComparison: () => void;
  toggleComparison: () => void;
  openComparison: () => void;
  closeComparison: () => void;
  isProductInComparison: (productId: number) => boolean;
}

export const useProductComparison = create<ProductComparisonStore>()(
  persist(
    (set, get) => ({
      products: [],
      isComparisonOpen: false,
      maxProducts: 4,
      
      addProduct: (product) => {
        const { products, maxProducts } = get();
        
        // Check if product already exists
        if (products.some(p => p.id === product.id)) {
          return false;
        }
        
        // Check if max products reached
        if (products.length >= maxProducts) {
          return false;
        }
        
        set({ products: [...products, product] });
        return true;
      },
      
      removeProduct: (productId) => {
        set(state => ({
          products: state.products.filter(p => p.id !== productId)
        }));
      },
      
      clearComparison: () => {
        set({ products: [], isComparisonOpen: false });
      },
      
      toggleComparison: () => {
        set(state => ({ isComparisonOpen: !state.isComparisonOpen }));
      },
      
      openComparison: () => {
        set({ isComparisonOpen: true });
      },
      
      closeComparison: () => {
        set({ isComparisonOpen: false });
      },
      
      isProductInComparison: (productId) => {
        return get().products.some(p => p.id === productId);
      }
    }),
    {
      name: "hemp-product-comparison"
    }
  )
);

// Hook for using comparison with toast notifications
export const useProductComparisonWithToast = () => {
  const { toast } = useToast();
  const comparison = useProductComparison();
  
  const addProductWithToast = (product: ComparisonProduct) => {
    if (comparison.isProductInComparison(product.id)) {
      toast({
        title: "Already in comparison",
        description: `${product.name} is already in your comparison list.`,
        variant: "default"
      });
      return false;
    }
    
    if (comparison.products.length >= comparison.maxProducts) {
      toast({
        title: "Comparison limit reached",
        description: `You can compare up to ${comparison.maxProducts} products at once.`,
        variant: "destructive"
      });
      return false;
    }
    
    const success = comparison.addProduct(product);
    if (success) {
      toast({
        title: "Added to comparison",
        description: `${product.name} has been added to comparison.`,
        action: (
          <Button
            size="sm"
            variant="outline"
            onClick={() => comparison.openComparison()}
          >
            View Comparison
          </Button>
        )
      });
    }
    
    return success;
  };
  
  const removeProductWithToast = (productId: number, productName?: string) => {
    comparison.removeProduct(productId);
    toast({
      title: "Removed from comparison",
      description: productName ? `${productName} has been removed.` : "Product removed from comparison."
    });
  };
  
  return {
    ...comparison,
    addProductWithToast,
    removeProductWithToast
  };
};