import { useMemo } from "react";
import { useAllHempProducts } from "./use-product-data";
import { useIndustries } from "./use-plant-data";

interface IndustryDistribution {
  name: string;
  value: number;
  percentage: number;
  color: string;
}

export function useIndustryDistribution() {
  const { data: products, isLoading: productsLoading } = useAllHempProducts();
  const { data: industries, isLoading: industriesLoading } = useIndustries();

  const distribution = useMemo(() => {
    if (!products || !industries || products.length === 0) return [];

    // Count products by industry
    const industryCounts = products.reduce((acc, product) => {
      const industryId = product.industry_id || product.industryId;
      if (industryId) {
        acc[industryId] = (acc[industryId] || 0) + 1;
      }
      return acc;
    }, {} as Record<number, number>);

    // Create distribution data
    const totalProducts = products.length;
    const colors = [
      "#22c55e", // green-500
      "#10b981", // emerald-500
      "#06b6d4", // cyan-500
      "#3b82f6", // blue-500
      "#6366f1", // indigo-500
      "#8b5cf6", // violet-500
      "#ec4899", // pink-500
      "#f59e0b", // amber-500
    ];

    const distributionData: IndustryDistribution[] = Object.entries(industryCounts)
      .map(([industryId, count], index) => {
        const industry = industries.find(i => i.id === parseInt(industryId));
        return {
          name: industry?.name || "Unknown",
          value: count,
          percentage: Math.round((count / totalProducts) * 100),
          color: colors[index % colors.length]
        };
      })
      .sort((a, b) => b.value - a.value) // Sort by count descending
      .slice(0, 8); // Top 8 industries

    // If there are more than 8 industries, add "Others"
    const othersCount = totalProducts - distributionData.reduce((sum, item) => sum + item.value, 0);
    if (othersCount > 0) {
      distributionData.push({
        name: "Others",
        value: othersCount,
        percentage: Math.round((othersCount / totalProducts) * 100),
        color: "#6b7280" // gray-500
      });
    }

    return distributionData;
  }, [products, industries]);

  return {
    data: distribution,
    isLoading: productsLoading || industriesLoading
  };
}