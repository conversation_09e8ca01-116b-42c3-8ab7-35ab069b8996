import { useEffect, useRef, useState } from "react";
import { useLocation } from "wouter";

/**
 * Custom hook for managing focus on route changes
 * Ensures proper focus management for keyboard navigation and screen readers
 */
export function useFocusManagement() {
  const [location] = useLocation();
  const previousLocation = useRef(location);

  useEffect(() => {
    // Only handle focus on route change
    if (location !== previousLocation.current) {
      previousLocation.current = location;

      // Focus the main content area
      const mainContent = document.getElementById("main-content");
      if (mainContent) {
        // Make the element focusable if it isn't already
        if (!mainContent.hasAttribute("tabindex")) {
          mainContent.setAttribute("tabindex", "-1");
        }
        
        // Focus the element
        mainContent.focus();
        
        // Announce the page change to screen readers
        const pageTitle = document.title;
        const announcement = document.createElement("div");
        announcement.setAttribute("role", "status");
        announcement.setAttribute("aria-live", "polite");
        announcement.setAttribute("aria-atomic", "true");
        announcement.className = "sr-only";
        announcement.textContent = `Navigated to ${pageTitle}`;
        
        document.body.appendChild(announcement);
        
        // Remove the announcement after a short delay
        setTimeout(() => {
          document.body.removeChild(announcement);
        }, 1000);
      }
    }
  }, [location]);
}

/**
 * Hook for managing focus trap within a container
 * Useful for modals, dropdowns, and other overlays
 */
export function useFocusTrap(isActive: boolean) {
  const containerRef = useRef<HTMLElement>(null);
  const previousActiveElement = useRef<HTMLElement | null>(null);

  useEffect(() => {
    if (!isActive || !containerRef.current) return;

    // Store the currently focused element
    previousActiveElement.current = document.activeElement as HTMLElement;

    const container = containerRef.current;
    const focusableElements = container.querySelectorAll<HTMLElement>(
      'a[href], button, textarea, input[type="text"], input[type="radio"], input[type="checkbox"], select, [tabindex]:not([tabindex="-1"])'
    );

    const firstFocusable = focusableElements[0];
    const lastFocusable = focusableElements[focusableElements.length - 1];

    // Focus the first focusable element
    firstFocusable?.focus();

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key !== "Tab") return;

      if (e.shiftKey) {
        // Shift + Tab
        if (document.activeElement === firstFocusable) {
          e.preventDefault();
          lastFocusable?.focus();
        }
      } else {
        // Tab
        if (document.activeElement === lastFocusable) {
          e.preventDefault();
          firstFocusable?.focus();
        }
      }
    };

    container.addEventListener("keydown", handleKeyDown);

    return () => {
      container.removeEventListener("keydown", handleKeyDown);
      
      // Restore focus to the previously focused element
      if (previousActiveElement.current) {
        previousActiveElement.current.focus();
      }
    };
  }, [isActive]);

  return containerRef;
}

/**
 * Hook for managing roving tabindex pattern
 * Useful for navigation menus, tab lists, etc.
 */
export function useRovingTabIndex(items: HTMLElement[]) {
  const [focusedIndex, setFocusedIndex] = useState(0);

  useEffect(() => {
    items.forEach((item, index) => {
      item.setAttribute("tabindex", index === focusedIndex ? "0" : "-1");
    });
  }, [items, focusedIndex]);

  const handleKeyDown = (e: KeyboardEvent) => {
    switch (e.key) {
      case "ArrowRight":
      case "ArrowDown":
        e.preventDefault();
        setFocusedIndex((prev) => (prev + 1) % items.length);
        break;
      case "ArrowLeft":
      case "ArrowUp":
        e.preventDefault();
        setFocusedIndex((prev) => (prev - 1 + items.length) % items.length);
        break;
      case "Home":
        e.preventDefault();
        setFocusedIndex(0);
        break;
      case "End":
        e.preventDefault();
        setFocusedIndex(items.length - 1);
        break;
    }
  };

  useEffect(() => {
    items[focusedIndex]?.focus();
  }, [focusedIndex, items]);

  return { handleKeyDown, focusedIndex, setFocusedIndex };
}