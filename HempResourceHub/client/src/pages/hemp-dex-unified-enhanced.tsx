import React, { useState, useEffect, useMemo } from "react";
import { useLocation } from "wouter";
import { useAllHempProducts } from "@/hooks/use-product-data";
import { usePlantParts, useIndustries, usePlantTypes } from "@/hooks/use-plant-data";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { 
  Search, 
  Filter, 
  Package, 
  Leaf, 
  Factory, 
  TreePine,
  Wheat,
  Flower,
  X,
  Grid3X3,
  List,
  Table as TableIcon,
  ChevronDown,
  SlidersHorizontal
} from "lucide-react";
import { LoadingState } from "@/components/ui/loading-spinner";
import { ErrorState } from "@/components/ui/error-state";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Sheet,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON>etDescription,
  SheetHeader,
  <PERSON>et<PERSON><PERSON><PERSON>,
  She<PERSON><PERSON>rigger,
} from "@/components/ui/sheet";

// Import new virtualized components
import VirtualizedProductGrid from "@/components/product/virtualized-product-grid";
import { VirtualizedProductTable, defaultProductColumns, TableColumn } from "@/components/product/virtualized-product-table";
import { AdvancedFilterPanel } from "@/components/filters/advanced-filter-panel";

interface Filters {
  search: string;
  plantParts: number[];
  industries: number[];
  stages: string[];
  sustainabilityRange?: [number, number];
  hasImage?: boolean;
  hasCompany?: boolean;
  hasTechnicalSpecs?: boolean;
  sortBy: 'name' | 'name-desc' | 'stage' | 'plantPart' | 'industry' | 'sustainability' | 'newest' | 'relevance';
  viewMode: 'grid' | 'list' | 'table';
  groupBy: 'none' | 'plantPart' | 'industry' | 'stage';
}

const HempDexEnhanced = () => {
  const [location, setLocation] = useLocation();
  const { data: products, isLoading: productsLoading, error: productsError } = useAllHempProducts();
  const { data: plantParts, isLoading: partsLoading, error: partsError } = usePlantParts();
  const { data: industries, isLoading: industriesLoading, error: industriesError } = useIndustries();
  const { data: plantTypes } = usePlantTypes();

  // State for user interactions
  const [favorites, setFavorites] = useState<number[]>(() => {
    const saved = localStorage.getItem('hemp-favorites');
    return saved ? JSON.parse(saved) : [];
  });
  const [bookmarks, setBookmarks] = useState<number[]>(() => {
    const saved = localStorage.getItem('hemp-bookmarks');
    return saved ? JSON.parse(saved) : [];
  });
  const [selectedRows, setSelectedRows] = useState<number[]>([]);
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  
  // Table column preferences
  const [tableColumns, setTableColumns] = useState<TableColumn[]>(() => {
    const saved = localStorage.getItem('hemp-table-columns');
    return saved ? JSON.parse(saved) : defaultProductColumns;
  });
  
  // Parse URL parameters
  const urlParams = new URLSearchParams(window.location.search);
  const tabParam = urlParams.get('tab');
  const searchParam = urlParams.get('search');
  const partsParam = urlParams.get('parts');
  const industriesParam = urlParams.get('industries');
  const stagesParam = urlParams.get('stages');
  const sortParam = urlParams.get('sort') as any;
  const groupParam = urlParams.get('group') as any;
  const viewParam = urlParams.get('view') as any;
  
  const [filters, setFilters] = useState<Filters>({
    search: searchParam || '',
    plantParts: partsParam ? partsParam.split(',').map(Number) : [],
    industries: industriesParam ? industriesParam.split(',').map(Number) : [],
    stages: stagesParam ? stagesParam.split(',') : [],
    sustainabilityRange: [0, 10],
    hasImage: false,
    hasCompany: false,
    hasTechnicalSpecs: false,
    sortBy: sortParam || 'name',
    viewMode: viewParam || 'grid',
    groupBy: groupParam || 'none'
  });
  
  const [activeTab, setActiveTab] = useState(tabParam || 'all');

  // Handler functions for user interactions
  const handleFavorite = (productId: number) => {
    const newFavorites = favorites.includes(productId)
      ? favorites.filter(id => id !== productId)
      : [...favorites, productId];
    setFavorites(newFavorites);
    localStorage.setItem('hemp-favorites', JSON.stringify(newFavorites));
  };

  const handleBookmark = (productId: number) => {
    const newBookmarks = bookmarks.includes(productId)
      ? bookmarks.filter(id => id !== productId)
      : [...bookmarks, productId];
    setBookmarks(newBookmarks);
    localStorage.setItem('hemp-bookmarks', JSON.stringify(newBookmarks));
  };

  const handleShare = (product: any) => {
    if (navigator.share) {
      navigator.share({
        title: product.name,
        text: product.description,
        url: `${window.location.origin}/product/${product.id}`
      });
    } else {
      navigator.clipboard.writeText(`${window.location.origin}/product/${product.id}`);
      // Add toast notification here
    }
  };

  // Update filters when URL search parameter changes
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const searchParam = urlParams.get('search');
    if (searchParam && searchParam !== filters.search) {
      setFilters(prev => ({ ...prev, search: searchParam }));
    }
  }, [location]);
  
  // Get unique stages from products
  const uniqueStages = useMemo(() => {
    if (!products) return [];
    return [...new Set(products.map(p => p.commercialization_stage).filter(Boolean))];
  }, [products]);
  
  // Simple icons for plant parts
  const getPlantPartIcon = (name: string) => {
    const nameLower = name.toLowerCase();
    if (nameLower.includes('seed')) return <Wheat className="h-5 w-5" />;
    if (nameLower.includes('fiber') || nameLower.includes('bast')) return <TreePine className="h-5 w-5" />;
    if (nameLower.includes('flower')) return <Flower className="h-5 w-5" />;
    if (nameLower.includes('hurd') || nameLower.includes('shiv')) return <Package className="h-5 w-5" />;
    return <Leaf className="h-5 w-5" />;
  };
  
  // Simple icons for industries
  const getIndustryIcon = (name: string) => {
    const nameLower = name.toLowerCase();
    if (nameLower.includes('industrial') || nameLower.includes('manufacturing')) return <Factory className="h-5 w-5" />;
    return <Package className="h-5 w-5" />;
  };
  
  // Enhanced filter function with all filters
  const filteredProducts = useMemo(() => {
    if (!products) return [];
    
    let result = [...products];
    
    // Search filter
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      result = result.filter(product => 
        product.name.toLowerCase().includes(searchLower) ||
        product.description?.toLowerCase().includes(searchLower) ||
        product.benefits_advantages?.some(b => b.toLowerCase().includes(searchLower)) ||
        product.keywords?.some(k => k.toLowerCase().includes(searchLower))
      );
    }
    
    // Plant part filter
    if (filters.plantParts.length > 0) {
      result = result.filter(product => 
        filters.plantParts.includes(product.plant_part_id)
      );
    }
    
    // Industry filter
    if (filters.industries.length > 0) {
      result = result.filter(product => {
        return product.industry_sub_category_id && filters.industries.length > 0;
      });
    }
    
    // Stage filter
    if (filters.stages.length > 0) {
      result = result.filter(product => 
        filters.stages.includes(product.commercialization_stage || '')
      );
    }
    
    // Sustainability filter
    if (filters.sustainabilityRange) {
      result = result.filter(product => {
        const score = product.sustainability_score || 0;
        return score >= filters.sustainabilityRange[0] && score <= filters.sustainabilityRange[1];
      });
    }
    
    // Data availability filters
    if (filters.hasImage) {
      result = result.filter(product => product.image_url || product.ai_generated_image_url);
    }
    if (filters.hasCompany) {
      result = result.filter(product => product.primary_company_id);
    }
    if (filters.hasTechnicalSpecs) {
      result = result.filter(product => product.technical_specifications);
    }
    
    // Sorting
    result.sort((a, b) => {
      switch (filters.sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'name-desc':
          return b.name.localeCompare(a.name);
        case 'stage':
          return (a.commercialization_stage || '').localeCompare(b.commercialization_stage || '');
        case 'plantPart':
          return a.plant_part_id - b.plant_part_id;
        case 'industry':
          return (a.industry_sub_category_id || 0) - (b.industry_sub_category_id || 0);
        case 'sustainability':
          return (b.sustainability_score || 0) - (a.sustainability_score || 0);
        case 'newest':
          return new Date(b.created_at || 0).getTime() - new Date(a.created_at || 0).getTime();
        case 'relevance':
        default:
          if (filters.search) {
            const searchLower = filters.search.toLowerCase();
            const aScore = (a.name.toLowerCase().includes(searchLower) ? 10 : 0) +
                          (a.description?.toLowerCase().includes(searchLower) ? 5 : 0);
            const bScore = (b.name.toLowerCase().includes(searchLower) ? 10 : 0) +
                          (b.description?.toLowerCase().includes(searchLower) ? 5 : 0);
            return bScore - aScore;
          }
          return 0;
      }
    });
    
    return result;
  }, [products, filters]);
  
  // Group products if needed
  const groupedProducts = useMemo(() => {
    if (filters.groupBy === 'none') return { 'All Products': filteredProducts };
    
    const groups: Record<string, typeof filteredProducts> = {};
    
    filteredProducts.forEach(product => {
      let groupKey = 'Other';
      
      if (filters.groupBy === 'plantPart') {
        const part = plantParts?.find(p => p.id === product.plant_part_id);
        groupKey = part?.name || 'Unknown Part';
      } else if (filters.groupBy === 'stage') {
        groupKey = product.commercialization_stage || 'Unknown Stage';
      } else if (filters.groupBy === 'industry') {
        const industry = industries?.find(i => i.id === product.industry_sub_category_id);
        groupKey = industry?.name || 'Unknown Industry';
      }
      
      if (!groups[groupKey]) groups[groupKey] = [];
      groups[groupKey].push(product);
    });
    
    return groups;
  }, [filteredProducts, filters.groupBy, plantParts, industries]);
  
  // Handle category selection
  const handlePlantPartSelect = (partId: number) => {
    setFilters(prev => ({
      ...prev,
      plantParts: prev.plantParts.includes(partId)
        ? prev.plantParts.filter(id => id !== partId)
        : [...prev.plantParts, partId]
    }));
  };
  
  const handleIndustrySelect = (industryId: number) => {
    setFilters(prev => ({
      ...prev,
      industries: prev.industries.includes(industryId)
        ? prev.industries.filter(id => id !== industryId)
        : [...prev.industries, industryId]
    }));
  };
  
  const handleStageSelect = (stage: string) => {
    setFilters(prev => ({
      ...prev,
      stages: prev.stages.includes(stage)
        ? prev.stages.filter(s => s !== stage)
        : [...prev.stages, stage]
    }));
  };
  
  const clearFilters = () => {
    setFilters({
      search: '',
      plantParts: [],
      industries: [],
      stages: [],
      sustainabilityRange: [0, 10],
      hasImage: false,
      hasCompany: false,
      hasTechnicalSpecs: false,
      sortBy: 'name',
      viewMode: filters.viewMode,
      groupBy: 'none'
    });
  };
  
  const hasActiveFilters = filters.search || 
    filters.plantParts.length > 0 || 
    filters.industries.length > 0 || 
    filters.stages.length > 0 ||
    (filters.sustainabilityRange && (filters.sustainabilityRange[0] > 0 || filters.sustainabilityRange[1] < 10)) ||
    filters.hasImage ||
    filters.hasCompany ||
    filters.hasTechnicalSpecs;
  
  const isLoading = productsLoading || partsLoading || industriesLoading;
  const hasError = productsError || partsError || industriesError;
  
  // Create lookup tables for names
  const industryNames = useMemo(() => {
    const names: Record<number, string> = {};
    industries?.forEach(industry => {
      names[industry.id] = industry.name;
    });
    return names;
  }, [industries]);
  
  const subIndustryNames = industryNames; // Same as industryNames for now
  
  const plantPartNames = useMemo(() => {
    const names: Record<number, string> = {};
    plantParts?.forEach(part => {
      names[part.id] = part.name;
    });
    return names;
  }, [plantParts]);
  
  // Update URL when filters change
  useEffect(() => {
    const params = new URLSearchParams();
    if (filters.search) params.set('search', filters.search);
    if (filters.plantParts.length) params.set('parts', filters.plantParts.join(','));
    if (filters.industries.length) params.set('industries', filters.industries.join(','));
    if (filters.stages.length) params.set('stages', filters.stages.join(','));
    if (filters.sortBy !== 'name') params.set('sort', filters.sortBy);
    if (filters.viewMode !== 'grid') params.set('view', filters.viewMode);
    if (filters.groupBy !== 'none') params.set('group', filters.groupBy);
    
    const newURL = `${window.location.pathname}?${params.toString()}`;
    window.history.replaceState({}, '', newURL);
  }, [filters]);
  
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-950 to-gray-900">
      {/* Header */}
      <div className="bg-gradient-to-r from-hemp-800 via-hemp-600 to-hemp-500 p-1">
        <div className="bg-black p-6">
          <div className="max-w-7xl mx-auto">
            <div className="flex items-center justify-between mb-4">
              <h1 className="text-3xl font-bold text-white">HempDex™</h1>
              <div className="flex items-center gap-4">
                {/* View Mode Toggle */}
                <div className="flex items-center bg-gray-800 rounded-lg p-1">
                  <Button
                    variant={filters.viewMode === 'grid' ? 'secondary' : 'ghost'}
                    size="sm"
                    onClick={() => setFilters(prev => ({ ...prev, viewMode: 'grid' }))}
                    className="gap-2"
                  >
                    <Grid3X3 className="h-4 w-4" />
                    Grid
                  </Button>
                  <Button
                    variant={filters.viewMode === 'list' ? 'secondary' : 'ghost'}
                    size="sm"
                    onClick={() => setFilters(prev => ({ ...prev, viewMode: 'list' }))}
                    className="gap-2"
                  >
                    <List className="h-4 w-4" />
                    List
                  </Button>
                  <Button
                    variant={filters.viewMode === 'table' ? 'secondary' : 'ghost'}
                    size="sm"
                    onClick={() => setFilters(prev => ({ ...prev, viewMode: 'table' }))}
                    className="gap-2"
                  >
                    <TableIcon className="h-4 w-4" />
                    Table
                  </Button>
                </div>
                
                {/* Sort Dropdown */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="sm" className="gap-2">
                      Sort by
                      <ChevronDown className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-48">
                    <DropdownMenuItem onClick={() => setFilters(prev => ({ ...prev, sortBy: 'name' }))}>
                      Name (A-Z)
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setFilters(prev => ({ ...prev, sortBy: 'name-desc' }))}>
                      Name (Z-A)
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setFilters(prev => ({ ...prev, sortBy: 'stage' }))}>
                      Commercialization Stage
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setFilters(prev => ({ ...prev, sortBy: 'plantPart' }))}>
                      Plant Part
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setFilters(prev => ({ ...prev, sortBy: 'industry' }))}>
                      Industry
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setFilters(prev => ({ ...prev, sortBy: 'sustainability' }))}>
                      Sustainability Score
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setFilters(prev => ({ ...prev, sortBy: 'newest' }))}>
                      Newest First
                    </DropdownMenuItem>
                    {filters.search && (
                      <DropdownMenuItem onClick={() => setFilters(prev => ({ ...prev, sortBy: 'relevance' }))}>
                        Relevance
                      </DropdownMenuItem>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
                
                {/* Group By Dropdown */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="sm" className="gap-2">
                      Group by
                      <ChevronDown className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => setFilters(prev => ({ ...prev, groupBy: 'none' }))}>
                      No grouping
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setFilters(prev => ({ ...prev, groupBy: 'plantPart' }))}>
                      Plant Part
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setFilters(prev => ({ ...prev, groupBy: 'industry' }))}>
                      Industry
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setFilters(prev => ({ ...prev, groupBy: 'stage' }))}>
                      Stage
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
                
                {/* Advanced Filters Toggle */}
                <Sheet open={showAdvancedFilters} onOpenChange={setShowAdvancedFilters}>
                  <SheetTrigger asChild>
                    <Button variant="outline" size="sm" className="gap-2">
                      <SlidersHorizontal className="h-4 w-4" />
                      Advanced
                    </Button>
                  </SheetTrigger>
                  <SheetContent className="w-[400px] sm:w-[540px]">
                    <SheetHeader>
                      <SheetTitle>Advanced Filters</SheetTitle>
                      <SheetDescription>
                        Fine-tune your search with advanced filtering options
                      </SheetDescription>
                    </SheetHeader>
                    <div className="mt-6">
                      <AdvancedFilterPanel
                        filters={filters}
                        onFiltersChange={setFilters}
                        plantParts={plantParts || []}
                        industries={industries || []}
                        stages={uniqueStages}
                        onClose={() => setShowAdvancedFilters(false)}
                      />
                    </div>
                  </SheetContent>
                </Sheet>
              </div>
            </div>
            
            {/* Search Bar */}
            <div className="relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <Input
                type="text"
                placeholder="Search products by name, benefits, or keywords..."
                value={filters.search}
                onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                className="w-full pl-12 pr-4 py-3 bg-gray-900 border-gray-700 text-white placeholder-gray-400 rounded-xl"
              />
            </div>
          </div>
        </div>
      </div>
      
      {/* Quick Filters Tabs */}
      <div className="bg-gray-900 border-b border-gray-800">
        <div className="max-w-7xl mx-auto">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <div className="px-4">
              <TabsList className="w-full justify-start bg-transparent border-0 h-auto p-0">
                <TabsTrigger 
                  value="all" 
                  className="rounded-none border-b-2 border-transparent data-[state=active]:border-hemp-500 data-[state=active]:bg-transparent px-6 py-3"
                >
                  All Products
                </TabsTrigger>
                <TabsTrigger 
                  value="plant-parts"
                  className="rounded-none border-b-2 border-transparent data-[state=active]:border-hemp-500 data-[state=active]:bg-transparent px-6 py-3"
                >
                  By Plant Part
                </TabsTrigger>
                <TabsTrigger 
                  value="industries"
                  className="rounded-none border-b-2 border-transparent data-[state=active]:border-hemp-500 data-[state=active]:bg-transparent px-6 py-3"
                >
                  By Industry
                </TabsTrigger>
                <TabsTrigger 
                  value="stages"
                  className="rounded-none border-b-2 border-transparent data-[state=active]:border-hemp-500 data-[state=active]:bg-transparent px-6 py-3"
                >
                  By Stage
                </TabsTrigger>
              </TabsList>
            </div>
            
            <div className="px-4 py-6">
              <TabsContent value="all" className="mt-0">
                <p className="text-gray-400">
                  Showing all products. Use filters or browse by category.
                </p>
              </TabsContent>
              
              <TabsContent value="plant-parts" className="mt-0">
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3">
                  {plantParts?.map(part => {
                    const count = products?.filter(p => p.plant_part_id === part.id).length || 0;
                    const isSelected = filters.plantParts.includes(part.id);
                    
                    return (
                      <button
                        key={part.id}
                        onClick={() => handlePlantPartSelect(part.id)}
                        className={`p-4 rounded-lg border-2 transition-all ${
                          isSelected 
                            ? 'border-green-500 bg-green-500/20' 
                            : 'border-gray-700 bg-gray-800 hover:border-gray-600'
                        }`}
                      >
                        <div className="flex flex-col items-center gap-2">
                          <div className={isSelected ? 'text-green-400' : 'text-gray-400'}>
                            {getPlantPartIcon(part.name)}
                          </div>
                          <span className="text-sm font-medium text-white">{part.name}</span>
                          <span className="text-xs text-gray-400">{count} products</span>
                        </div>
                      </button>
                    );
                  })}
                </div>
              </TabsContent>
              
              <TabsContent value="industries" className="mt-0">
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3">
                  {industries?.map(industry => {
                    const count = products?.length || 0;
                    const isSelected = filters.industries.includes(industry.id);
                    
                    return (
                      <button
                        key={industry.id}
                        onClick={() => handleIndustrySelect(industry.id)}
                        className={`p-4 rounded-lg border-2 transition-all ${
                          isSelected 
                            ? 'border-green-500 bg-green-500/20' 
                            : 'border-gray-700 bg-gray-800 hover:border-gray-600'
                        }`}
                      >
                        <div className="flex flex-col items-center gap-2">
                          <div className={isSelected ? 'text-green-400' : 'text-gray-400'}>
                            {getIndustryIcon(industry.name)}
                          </div>
                          <span className="text-sm font-medium text-white">{industry.name}</span>
                          <span className="text-xs text-gray-400">{count} products</span>
                        </div>
                      </button>
                    );
                  })}
                </div>
              </TabsContent>
              
              <TabsContent value="stages" className="mt-0">
                <div className="flex flex-wrap gap-3">
                  {uniqueStages.map(stage => {
                    const count = products?.filter(p => p.commercialization_stage === stage).length || 0;
                    const isSelected = filters.stages.includes(stage);
                    
                    return (
                      <button
                        key={stage}
                        onClick={() => handleStageSelect(stage)}
                        className={`px-6 py-3 rounded-lg border-2 transition-all ${
                          isSelected 
                            ? 'border-green-500 bg-green-500/20' 
                            : 'border-gray-700 bg-gray-800 hover:border-gray-600'
                        }`}
                      >
                        <span className="font-medium text-white">{stage}</span>
                        <span className="ml-2 text-sm text-gray-400">({count})</span>
                      </button>
                    );
                  })}
                </div>
              </TabsContent>
            </div>
          </Tabs>
        </div>
      </div>
      
      {/* Active Filters Bar */}
      {hasActiveFilters && (
        <div className="bg-gray-800/50 border-b border-gray-700">
          <div className="max-w-7xl mx-auto px-4 py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 flex-wrap">
                <span className="text-sm text-gray-400">Active filters:</span>
                
                {filters.search && (
                  <Badge variant="secondary" className="bg-hemp-500/20 text-hemp-400">
                    Search: "{filters.search}"
                    <X
                      className="ml-1 h-3 w-3 cursor-pointer"
                      onClick={() => setFilters(prev => ({ ...prev, search: '' }))}
                    />
                  </Badge>
                )}
                
                {filters.plantParts.map(partId => {
                  const part = plantParts?.find(p => p.id === partId);
                  return part ? (
                    <Badge key={partId} variant="secondary">
                      {part.name}
                      <X
                        className="ml-1 h-3 w-3 cursor-pointer"
                        onClick={() => handlePlantPartSelect(partId)}
                      />
                    </Badge>
                  ) : null;
                })}
                
                {filters.industries.map(industryId => {
                  const industry = industries?.find(i => i.id === industryId);
                  return industry ? (
                    <Badge key={industryId} variant="outline">
                      {industry.name}
                      <X
                        className="ml-1 h-3 w-3 cursor-pointer"
                        onClick={() => handleIndustrySelect(industryId)}
                      />
                    </Badge>
                  ) : null;
                })}
                
                {filters.stages.map(stage => (
                  <Badge key={stage} variant="secondary" className="bg-yellow-500/20 text-yellow-400">
                    {stage}
                    <X 
                      className="ml-1 h-3 w-3 cursor-pointer" 
                      onClick={() => handleStageSelect(stage)}
                    />
                  </Badge>
                ))}
                
                {filters.sustainabilityRange && (filters.sustainabilityRange[0] > 0 || filters.sustainabilityRange[1] < 10) && (
                  <Badge variant="secondary" className="bg-green-500/20 text-green-400">
                    Sustainability: {filters.sustainabilityRange[0]}-{filters.sustainabilityRange[1]}
                    <X 
                      className="ml-1 h-3 w-3 cursor-pointer" 
                      onClick={() => setFilters(prev => ({ ...prev, sustainabilityRange: [0, 10] }))}
                    />
                  </Badge>
                )}
              </div>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={clearFilters}
                className="text-gray-400 hover:text-white"
              >
                Clear all
              </Button>
            </div>
          </div>
        </div>
      )}
      
      {/* Results */}
      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="mb-4 flex items-center justify-between">
          <p className="text-gray-400">
            Showing {filteredProducts.length} of {products?.length || 0} products
          </p>
          
          {filters.groupBy !== 'none' && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setFilters(prev => ({ ...prev, groupBy: 'none' }))}
              className="text-gray-400"
            >
              Remove grouping
            </Button>
          )}
        </div>
        
        {hasError ? (
          <ErrorState 
            title="Unable to load products"
            message="There was an error loading the product data. Please check your connection and try again."
            onRetry={() => window.location.reload()}
          />
        ) : isLoading ? (
          <LoadingState message="Loading hemp products..." />
        ) : (
          <div>
            {/* Table View */}
            {filters.viewMode === 'table' ? (
              <VirtualizedProductTable
                products={filteredProducts}
                columns={tableColumns}
                isLoading={isLoading}
                onSort={(column, direction) => {
                  const sortMap: Record<string, Filters['sortBy']> = {
                    name: direction === 'asc' ? 'name' : 'name-desc',
                    plantPart: 'plantPart',
                    industry: 'industry',
                    stage: 'stage',
                    sustainability: 'sustainability'
                  };
                  setFilters(prev => ({ ...prev, sortBy: sortMap[column] || 'name' }));
                }}
                sortColumn={filters.sortBy.replace('-desc', '')}
                sortDirection={filters.sortBy.includes('-desc') ? 'desc' : 'asc'}
                onRowClick={(product) => setLocation(`/product/${product.id}`)}
                selectedRows={selectedRows}
                onSelectionChange={setSelectedRows}
                enableSelection={true}
                enableExpansion={true}
                onFavorite={handleFavorite}
                onBookmark={handleBookmark}
                onShare={handleShare}
                favorites={favorites}
                bookmarks={bookmarks}
              />
            ) : (
              /* Grid/List View with Grouping */
              Object.entries(groupedProducts).map(([groupName, groupProducts]) => (
                <div key={groupName} className="mb-8">
                  {filters.groupBy !== 'none' && (
                    <h3 className="text-xl font-semibold text-white mb-4">
                      {groupName} ({groupProducts.length})
                    </h3>
                  )}
                  
                  {filters.viewMode === 'grid' ? (
                    <VirtualizedProductGrid
                      products={groupProducts}
                      columns={3}
                      isLoading={isLoading}
                      industryNames={industryNames}
                      subIndustryNames={subIndustryNames}
                      plantPartNames={plantPartNames}
                      onFavorite={handleFavorite}
                      onBookmark={handleBookmark}
                      onShare={handleShare}
                      favorites={favorites}
                      bookmarks={bookmarks}
                    />
                  ) : (
                    /* List View - Uses table component in single column mode */
                    <VirtualizedProductTable
                      products={groupProducts}
                      columns={[
                        { id: 'name', label: 'Product', accessor: 'name', minWidth: 300 },
                        { id: 'plantPart', label: 'Plant Part', accessor: (p: any) => p.plantPart?.name },
                        { id: 'stage', label: 'Stage', accessor: 'commercialization_stage' },
                      ]}
                      isLoading={isLoading}
                      onRowClick={(product) => setLocation(`/product/${product.id}`)}
                      enableSelection={false}
                      enableExpansion={true}
                      onFavorite={handleFavorite}
                      onBookmark={handleBookmark}
                      onShare={handleShare}
                      favorites={favorites}
                      bookmarks={bookmarks}
                    />
                  )}
                </div>
              ))
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default HempDexEnhanced;