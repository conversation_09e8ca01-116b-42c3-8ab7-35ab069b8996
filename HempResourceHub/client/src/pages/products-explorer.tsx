import React from "react";
import { MarketplaceCatalog } from "@/components/products/marketplace-catalog";
import { Helmet } from "react-helmet";
import { createBreadcrumb } from "@/components/ui/breadcrumb";

export default function ProductsExplorerPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-green-900/20">
      <Helmet>
        <title>Hemp Products Marketplace | HempQuarterz</title>
        <meta
          name="description"
          content="Discover innovative hemp products across industries. From sustainable materials to wellness products, explore the versatility of industrial hemp."
        />
      </Helmet>

      <div className="container mx-auto px-4 py-12">
        <div className="max-w-7xl mx-auto">
          {/* Breadcrumb */}
          <div className="mb-6">
            {createBreadcrumb([
              { href: "/", label: "Home" },
              { href: "/products-explorer", label: "Products", isCurrent: true },
            ])}
          </div>

          <MarketplaceCatalog />
        </div>
      </div>
    </div>
  );
}