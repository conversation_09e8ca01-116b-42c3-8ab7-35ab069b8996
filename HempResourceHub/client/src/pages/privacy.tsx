import { PageLayout } from "@/components/layout/page-layout";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Shield, Lock, Eye, Database, Mail, Globe, AlertCircle, CheckCircle2 } from "lucide-react";

export default function PrivacyPage() {
  const sections = [
    {
      icon: <Database className="h-6 w-6 text-primary" />,
      title: "Information We Collect",
      content: [
        "Account information (email, username) when you register",
        "Usage data to improve our services",
        "Product searches and browsing history",
        "Feedback and communications you send us",
      ],
    },
    {
      icon: <Shield className="h-6 w-6 text-primary" />,
      title: "How We Protect Your Data",
      content: [
        "Industry-standard encryption for data transmission",
        "Secure servers with regular security audits",
        "Limited access to personal information",
        "Regular security updates and patches",
      ],
    },
    {
      icon: <Eye className="h-6 w-6 text-primary" />,
      title: "How We Use Your Information",
      content: [
        "To provide and improve our hemp database services",
        "To personalize your experience",
        "To send important updates about the platform",
        "To analyze usage patterns and optimize performance",
      ],
    },
    {
      icon: <Globe className="h-6 w-6 text-primary" />,
      title: "Information Sharing",
      content: [
        "We never sell your personal information",
        "Data shared only with your explicit consent",
        "Anonymous analytics data may be used for research",
        "Legal compliance when required by law",
      ],
    },
  ];

  const rights = [
    "Access your personal data",
    "Request correction of inaccurate data",
    "Request deletion of your data",
    "Export your data in a portable format",
    "Opt-out of marketing communications",
    "Lodge a complaint with supervisory authorities",
  ];

  return (
    <PageLayout title="Privacy Policy">
      <div className="container mx-auto py-8 px-4 max-w-4xl">
        <div className="mb-8 text-center">
          <div className="flex justify-center mb-4">
            <Lock className="h-16 w-16 text-primary" />
          </div>
          <h1 className="text-4xl font-bold mb-4">Privacy Policy</h1>
          <p className="text-muted-foreground">
            Last updated: {new Date().toLocaleDateString()}
          </p>
        </div>

        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Our Commitment to Privacy</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">
              At HempQuarterz®, we are committed to protecting your privacy and ensuring the security 
              of your personal information. This privacy policy explains how we collect, use, and 
              safeguard your data when you use our hemp database platform.
            </p>
          </CardContent>
        </Card>

        <div className="space-y-6 mb-8">
          {sections.map((section, index) => (
            <Card key={index}>
              <CardHeader>
                <CardTitle className="flex items-center gap-3">
                  {section.icon}
                  {section.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {section.content.map((item, idx) => (
                    <li key={idx} className="flex items-start gap-2">
                      <CheckCircle2 className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                      <span className="text-muted-foreground">{item}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>

        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <AlertCircle className="h-6 w-6 text-primary" />
              Your Rights
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="mb-4 text-muted-foreground">
              Under data protection laws, you have the right to:
            </p>
            <ul className="grid gap-2 md:grid-cols-2">
              {rights.map((right, index) => (
                <li key={index} className="flex items-start gap-2">
                  <CheckCircle2 className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                  <span className="text-muted-foreground">{right}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>

        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <Mail className="h-6 w-6 text-primary" />
              Contact Us
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">
              If you have any questions about this privacy policy or how we handle your data, 
              please contact us at:
            </p>
            <div className="mt-4 p-4 bg-muted rounded-lg">
              <p className="font-medium">Email: <EMAIL></p>
              <p className="font-medium">Address: HempQuarterz Privacy Team</p>
            </div>
          </CardContent>
        </Card>

        <div className="text-center text-sm text-muted-foreground">
          <p>
            By using HempQuarterz®, you agree to the terms outlined in this privacy policy.
          </p>
        </div>
      </div>
    </PageLayout>
  );
}