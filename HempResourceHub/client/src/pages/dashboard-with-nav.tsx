import { useState, useMemo } from "react";
import Navbar from "@/components/layout/navbar";
import Footer from "@/components/layout/footer";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { 
  TrendingUp, 
  TrendingDown,
  Users, 
  Package,
  Building2,
  Globe,
  ChartBar,
  Calendar,
  Filter,
  Download,
  MoreHorizontal,
  Activity,
  Leaf,
  BarChart3,
  PieChart,
  LineChart,
  ArrowUpRight,
  ArrowDownRight,
  ChevronRight
} from "lucide-react";
import { Link } from "wouter";
import { useProductStats, useAllHempProducts } from "@/hooks/use-product-data";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/lib/supabase-client";

// KPI Card Component
const KPICard = ({ title, value, change, trend, icon: Icon, description }: any) => (
  <Card className="bg-marine-card border-marine-border p-6 hover:bg-marine-hover transition-colors">
    <div className="flex items-start justify-between">
      <div className="flex-1">
        <div className="flex items-center gap-2 mb-1">
          <Icon className="h-4 w-4 text-marine-text-secondary" />
          <p className="text-sm text-marine-text-secondary">{title}</p>
        </div>
        <p className="text-3xl font-bold text-white mb-2">{value}</p>
        <div className="flex items-center gap-2">
          <Badge variant={trend === "up" ? "success" : "destructive"} className="flex items-center gap-1">
            {trend === "up" ? <TrendingUp className="h-3 w-3" /> : <TrendingDown className="h-3 w-3" />}
            {change}%
          </Badge>
          <span className="text-xs text-marine-text-secondary">{description}</span>
        </div>
      </div>
      <Button variant="ghost" size="icon" className="h-8 w-8">
        <MoreHorizontal className="h-4 w-4" />
      </Button>
    </div>
  </Card>
);

// Chart Card Component
const ChartCard = ({ title, children, action }: any) => (
  <Card className="bg-marine-card border-marine-border p-6">
    <div className="flex items-center justify-between mb-6">
      <h3 className="text-lg font-semibold text-white">{title}</h3>
      {action && (
        <Button variant="ghost" size="sm" className="text-marine-text-secondary hover:text-white">
          {action}
          <ChevronRight className="ml-1 h-4 w-4" />
        </Button>
      )}
    </div>
    {children}
  </Card>
);

// Product Row Component
const ProductRow = ({ product }: any) => (
  <Link href={`/product/${product.id}`}>
    <div className="flex items-center gap-4 p-4 rounded-lg hover:bg-marine-hover transition-colors cursor-pointer">
      {product.primary_image_url ? (
        <img 
          src={product.primary_image_url} 
          alt={product.name}
          className="w-12 h-12 rounded-lg object-cover"
        />
      ) : (
        <div className="w-12 h-12 rounded-lg bg-marine-border flex items-center justify-center">
          <Package className="h-6 w-6 text-marine-text-secondary" />
        </div>
      )}
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium text-white truncate">{product.name}</p>
        <p className="text-xs text-marine-text-secondary">{product.sub_industry_name || product.industry_name}</p>
      </div>
      <Badge variant="outline" className="shrink-0">
        {product.plant_part_name}
      </Badge>
    </div>
  </Link>
);

export default function DashboardWithNav() {
  const [timeRange, setTimeRange] = useState("7d");
  
  // Fetch real data
  const { data: productStats } = useProductStats();
  const { data: products } = useAllHempProducts();
  
  // Fetch companies
  const { data: companies } = useQuery({
    queryKey: ["companies"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("hemp_companies")
        .select("*");
      if (error) throw error;
      return data;
    }
  });
  
  // Fetch industries
  const { data: industries } = useQuery({
    queryKey: ["industries"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("industry_sub_categories")
        .select("*");
      if (error) throw error;
      return data;
    }
  });
  
  // Fetch plant parts
  const { data: plantParts } = useQuery({
    queryKey: ["plantParts"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("plant_parts")
        .select("*");
      if (error) throw error;
      return data;
    }
  });

  // Calculate real statistics
  const totalProducts = productStats?.totalProducts || 0;
  const totalCompanies = companies?.length || 0;
  const totalIndustries = industries?.length || 0;
  const totalPlantParts = plantParts?.length || 0;

  // Calculate growth (mock for now)
  const productGrowth = 12.5;
  const companyGrowth = 8.2;
  const industryGrowth = 3.4;
  const revenueGrowth = 18.7;

  // Top industries by product count
  const topIndustries = useMemo(() => {
    if (!products) return [];
    
    const industryCounts = products.reduce((acc: any, product: any) => {
      const industry = product.sub_industry_name || product.industry_name || "Unknown";
      acc[industry] = (acc[industry] || 0) + 1;
      return acc;
    }, {});

    return Object.entries(industryCounts)
      .sort(([,a]: any, [,b]: any) => b - a)
      .slice(0, 5)
      .map(([name, count]) => ({ name, count }));
  }, [products]);

  // Plant part distribution
  const plantPartDistribution = useMemo(() => {
    if (!products) return [];
    
    const partCounts = products.reduce((acc: any, product: any) => {
      const part = product.plant_part_name || "Unknown";
      acc[part] = (acc[part] || 0) + 1;
      return acc;
    }, {});

    return Object.entries(partCounts)
      .sort(([,a]: any, [,b]: any) => b - a)
      .map(([name, count]) => ({ name, count }));
  }, [products]);

  // Recent products
  const recentProducts = products?.slice(0, 4) || [];

  return (
    <div className="min-h-screen bg-marine-bg flex flex-col">
      <Navbar />
      
      <main className="flex-1 w-full">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
            <div>
              <h1 className="text-3xl font-bold text-white flex items-center gap-2">
                <Activity className="h-8 w-8 text-primary" />
                Hemp Industry Analytics
              </h1>
              <p className="text-marine-text-secondary mt-1">
                Real-time insights and market intelligence
              </p>
            </div>
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                className="text-marine-text-secondary"
                onClick={() => {}}
              >
                <Calendar className="mr-2 h-4 w-4" />
                Last 7 days
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="text-marine-text-secondary"
              >
                <Download className="mr-2 h-4 w-4" />
                Export
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="text-marine-text-secondary"
              >
                <Filter className="mr-2 h-4 w-4" />
                Filters
              </Button>
            </div>
          </div>

          {/* KPI Cards */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
            <KPICard
              title="Total Products"
              value={totalProducts.toLocaleString()}
              change={productGrowth}
              trend="up"
              icon={Package}
              description="from last month"
            />
            <KPICard
              title="Active Companies"
              value={totalCompanies}
              change={companyGrowth}
              trend="up"
              icon={Building2}
              description="from last month"
            />
            <KPICard
              title="Industry Growth"
              value={`+${industryGrowth}%`}
              change={industryGrowth}
              trend="up"
              icon={TrendingUp}
              description="YoY increase"
            />
            <KPICard
              title="Global Reach"
              value="47"
              change={2.1}
              trend="up"
              icon={Globe}
              description="new markets"
            />
          </div>

          {/* Main Content Tabs */}
          <Tabs defaultValue="overview" className="space-y-6">
            <TabsList className="bg-marine-card border-marine-border">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="industries">Industries</TabsTrigger>
              <TabsTrigger value="products">Products</TabsTrigger>
              <TabsTrigger value="trends">Trends</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Top Industries */}
                <ChartCard title="Top Industries by Products" action="View all">
                  <div className="space-y-4">
                    {topIndustries.map((industry, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className={`w-2 h-2 rounded-full bg-primary`} />
                          <span className="text-sm text-marine-text">{industry.name}</span>
                        </div>
                        <span className="text-sm font-medium text-white">{industry.count}</span>
                      </div>
                    ))}
                  </div>
                </ChartCard>

                {/* Plant Part Distribution */}
                <ChartCard title="Plant Part Distribution" action="View details">
                  <div className="space-y-4">
                    {plantPartDistribution.slice(0, 5).map((part, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <Leaf className="h-4 w-4 text-green-500" />
                          <span className="text-sm text-marine-text">{part.name}</span>
                        </div>
                        <span className="text-sm font-medium text-white">{part.count}</span>
                      </div>
                    ))}
                  </div>
                </ChartCard>
              </div>

              {/* Featured Products */}
              <Card className="bg-marine-card border-marine-border p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold text-white flex items-center gap-2">
                    <Package className="h-5 w-5 text-primary" />
                    Featured Products
                  </h3>
                  <Link href="/products">
                    <Button variant="ghost" size="sm" className="text-marine-text-secondary hover:text-white">
                      View all
                      <ArrowUpRight className="ml-1 h-4 w-4" />
                    </Button>
                  </Link>
                </div>
                <div className="space-y-2">
                  {recentProducts.map((product: any) => (
                    <ProductRow key={product.id} product={product} />
                  ))}
                </div>
              </Card>
            </TabsContent>

            <TabsContent value="industries" className="space-y-6">
              <Card className="bg-marine-card border-marine-border p-6">
                <h3 className="text-lg font-semibold text-white mb-4">Industry Analysis</h3>
                <p className="text-marine-text-secondary">
                  Detailed industry breakdown and performance metrics coming soon.
                </p>
              </Card>
            </TabsContent>

            <TabsContent value="products" className="space-y-6">
              <Card className="bg-marine-card border-marine-border p-6">
                <h3 className="text-lg font-semibold text-white mb-4">Product Analytics</h3>
                <p className="text-marine-text-secondary">
                  Comprehensive product performance and trend analysis coming soon.
                </p>
              </Card>
            </TabsContent>

            <TabsContent value="trends" className="space-y-6">
              <Card className="bg-marine-card border-marine-border p-6">
                <h3 className="text-lg font-semibold text-white mb-4">Market Trends</h3>
                <p className="text-marine-text-secondary">
                  Real-time market trends and predictive analytics coming soon.
                </p>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </main>

      <Footer />
    </div>
  );
}