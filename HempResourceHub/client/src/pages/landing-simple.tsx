import { useState } from "react";
import { <PERSON>, ArrowR<PERSON>, Leaf } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useLocation } from "wouter";

export default function SimpleLandingPage() {
  const [, setLocation] = useLocation();
  const [searchQuery, setSearchQuery] = useState("");

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      setLocation(`/products?search=${encodeURIComponent(searchQuery)}`);
    }
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="border-b">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Leaf className="h-6 w-6 text-green-600" />
            <span className="text-xl font-bold">HempQuarterz</span>
          </div>
          <nav className="hidden md:flex items-center space-x-6">
            <a href="/products" className="text-gray-600 hover:text-gray-900">Products</a>
            <a href="/hemp-companies" className="text-gray-600 hover:text-gray-900">Companies</a>
            <a href="/research" className="text-gray-600 hover:text-gray-900">Research</a>
            <a href="/about" className="text-gray-600 hover:text-gray-900">About</a>
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto max-w-4xl text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Industrial Hemp Database
          </h1>
          <p className="text-xl text-gray-600 mb-8">
            Discover 2,500+ hemp products from 200+ companies worldwide
          </p>

          {/* Search Bar */}
          <form onSubmit={handleSearch} className="max-w-2xl mx-auto mb-12">
            <div className="relative flex">
              <Input
                type="text"
                placeholder="Search products, companies, or research..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="flex-1 pr-4"
              />
              <Button type="submit" className="ml-2">
                <Search className="h-4 w-4 mr-2" />
                Search
              </Button>
            </div>
          </form>

          {/* Quick Links */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-3xl mx-auto">
            <Button
              variant="outline"
              className="h-auto flex flex-col items-center p-6 hover:bg-gray-50"
              onClick={() => setLocation("/products")}
            >
              <div className="text-2xl font-bold text-green-600 mb-2">2,562+</div>
              <div className="text-sm text-gray-600 mb-2">Hemp Products</div>
              <ArrowRight className="h-4 w-4" />
            </Button>

            <Button
              variant="outline"
              className="h-auto flex flex-col items-center p-6 hover:bg-gray-50"
              onClick={() => setLocation("/hemp-companies")}
            >
              <div className="text-2xl font-bold text-purple-600 mb-2">204</div>
              <div className="text-sm text-gray-600 mb-2">Companies</div>
              <ArrowRight className="h-4 w-4" />
            </Button>

            <Button
              variant="outline"
              className="h-auto flex flex-col items-center p-6 hover:bg-gray-50"
              onClick={() => setLocation("/research")}
            >
              <div className="text-2xl font-bold text-blue-600 mb-2">1,000+</div>
              <div className="text-sm text-gray-600 mb-2">Research Papers</div>
              <ArrowRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4 max-w-6xl">
          <h2 className="text-3xl font-bold text-center mb-12">Why Use Our Database?</h2>
          
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="text-4xl mb-4">🔍</div>
              <h3 className="text-xl font-semibold mb-2">Comprehensive Search</h3>
              <p className="text-gray-600">Find products by industry, plant part, or company</p>
            </div>
            
            <div className="text-center">
              <div className="text-4xl mb-4">✅</div>
              <h3 className="text-xl font-semibold mb-2">Verified Data</h3>
              <p className="text-gray-600">All entries are researched and verified</p>
            </div>
            
            <div className="text-center">
              <div className="text-4xl mb-4">🌍</div>
              <h3 className="text-xl font-semibold mb-2">Global Coverage</h3>
              <p className="text-gray-600">Products and companies from around the world</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">Ready to explore?</h2>
          <p className="text-xl text-gray-600 mb-8">Start discovering hemp innovations today</p>
          <Button 
            size="lg"
            onClick={() => setLocation("/products")}
            className="bg-green-600 hover:bg-green-700"
          >
            Browse All Products
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t py-8">
        <div className="container mx-auto px-4 text-center text-gray-600">
          <p>&copy; 2025 HempQuarterz. All rights reserved.</p>
        </div>
      </footer>
    </div>
  );
}