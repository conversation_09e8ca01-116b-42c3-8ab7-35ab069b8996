import React, { useState, useEffect } from "react";
import { useRoute } from "wouter";
import { Helmet } from "react-helmet";
import { AppLayout } from "@/components/layout/AppLayout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import { 
  ArrowLeft,
  Share2,
  Bookmark,
  ExternalLink,
  Building2,
  Leaf,
  Factory,
  TrendingUp,
  Award,
  Globe,
  Calendar,
  Users,
  BarChart3,
  Lightbulb,
  Recycle,
  Shield,
  Zap,
  ChevronRight
} from "lucide-react";
import { useAllHempProducts } from "@/hooks/use-product-data";
import { usePlantParts, useIndustries } from "@/hooks/use-plant-data";
import { EnhancedModernProductCard } from "@/components/product/enhanced-modern-product-card";
import { cn } from "@/lib/utils";
import { componentStyles } from "@/lib/design-system";

const EnhancedProductDetail = () => {
  const [match, params] = useRoute("/product/:id");
  const productId = params?.id ? parseInt(params.id) : null;
  
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [activeImageIndex, setActiveImageIndex] = useState(0);
  
  // Data hooks
  const { data: products, isLoading, error } = useAllHempProducts();
  const { data: plantParts } = usePlantParts();
  const { data: industries } = useIndustries();

  // Find the specific product
  const product = products?.find(p => p.id === productId);
  const plantPart = plantParts?.find(p => p.id === product?.plantPartId);
  const industry = industries?.find(i => i.id === product?.industryId);

  // Related products (same plant part or industry)
  const relatedProducts = products?.filter(p => 
    p.id !== productId && 
    (p.plantPartId === product?.plantPartId || p.industryId === product?.industryId)
  ).slice(0, 4) || [];

  // Check if bookmarked
  useEffect(() => {
    if (productId) {
      const bookmarks = JSON.parse(localStorage.getItem('hemp-bookmarks') || '[]');
      setIsBookmarked(bookmarks.includes(productId));
    }
  }, [productId]);

  // Handle bookmark toggle
  const handleBookmark = () => {
    if (!productId) return;
    
    const bookmarks = JSON.parse(localStorage.getItem('hemp-bookmarks') || '[]');
    const newBookmarks = isBookmarked
      ? bookmarks.filter((id: number) => id !== productId)
      : [...bookmarks, productId];
    
    localStorage.setItem('hemp-bookmarks', JSON.stringify(newBookmarks));
    setIsBookmarked(!isBookmarked);
  };

  // Handle share
  const handleShare = () => {
    if (!product) return;
    
    if (navigator.share) {
      navigator.share({
        title: product.name,
        text: product.description,
        url: window.location.href
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <AppLayout>
        <div className="min-h-screen bg-black text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="animate-pulse space-y-8">
              <div className="h-8 bg-gray-800 rounded w-1/3" />
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div className="aspect-square bg-gray-800 rounded-xl" />
                <div className="space-y-4">
                  <div className="h-8 bg-gray-800 rounded w-3/4" />
                  <div className="h-4 bg-gray-800 rounded w-1/2" />
                  <div className="space-y-2">
                    <div className="h-4 bg-gray-800 rounded" />
                    <div className="h-4 bg-gray-800 rounded" />
                    <div className="h-4 bg-gray-800 rounded w-3/4" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </AppLayout>
    );
  }

  // Error or product not found
  if (error || !product) {
    return (
      <AppLayout>
        <div className="min-h-screen bg-black text-white flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold mb-4">Product Not Found</h1>
            <p className="text-gray-400 mb-6">The product you're looking for doesn't exist or has been removed.</p>
            <Button onClick={() => window.history.back()} className="bg-purple-500 hover:bg-purple-600">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Go Back
            </Button>
          </div>
        </div>
      </AppLayout>
    );
  }

  // Product images (including AI generated and original)
  const productImages = [
    product.ai_generated_image_url,
    product.image_url,
    product.imageUrl,
    '/images/unknown-hemp-image.png'
  ].filter(Boolean);

  // Get commercialization stage color
  const getStageColor = (stage?: string) => {
    switch (stage?.toLowerCase()) {
      case 'research': return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'development': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      case 'pilot': return 'bg-orange-500/20 text-orange-400 border-orange-500/30';
      case 'commercial': return 'bg-green-500/20 text-green-400 border-green-500/30';
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  return (
    <AppLayout>
      <Helmet>
        <title>{product.name} - HempQuarterz® Industrial Hemp Database</title>
        <meta name="description" content={product.description} />
        <meta property="og:title" content={product.name} />
        <meta property="og:description" content={product.description} />
        <meta property="og:image" content={product.ai_generated_image_url || product.image_url} />
      </Helmet>

      <div className="min-h-screen bg-black text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Breadcrumbs */}
          <Breadcrumb className="mb-8">
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink href="/" className="text-gray-400 hover:text-white">
                  Home
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbLink href="/hemp-discovery" className="text-gray-400 hover:text-white">
                  Products
                </BreadcrumbLink>
              </BreadcrumbItem>
              {plantPart && (
                <>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbLink 
                      href={`/hemp-discovery?categories=${product.plantPartId}`}
                      className="text-gray-400 hover:text-white"
                    >
                      {plantPart.name}
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                </>
              )}
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage className="text-white">
                  {product.name}
                </BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>

          {/* Main Content */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-12">
            {/* Product Images */}
            <div className="space-y-4">
              {/* Main Image */}
              <div className="aspect-square relative overflow-hidden rounded-2xl bg-gray-900">
                <img
                  src={productImages[activeImageIndex]}
                  alt={product.name}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = '/images/unknown-hemp-image.png';
                  }}
                />
                
                {/* Image Navigation */}
                {productImages.length > 1 && (
                  <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex gap-2">
                    {productImages.map((_, index) => (
                      <button
                        key={index}
                        onClick={() => setActiveImageIndex(index)}
                        className={cn(
                          "w-3 h-3 rounded-full transition-colors",
                          index === activeImageIndex ? "bg-white" : "bg-white/50"
                        )}
                      />
                    ))}
                  </div>
                )}
              </div>

              {/* Thumbnail Images */}
              {productImages.length > 1 && (
                <div className="flex gap-2 overflow-x-auto">
                  {productImages.map((image, index) => (
                    <button
                      key={index}
                      onClick={() => setActiveImageIndex(index)}
                      className={cn(
                        "flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 transition-colors",
                        index === activeImageIndex ? "border-purple-500" : "border-gray-700"
                      )}
                    >
                      <img
                        src={image}
                        alt={`${product.name} ${index + 1}`}
                        className="w-full h-full object-cover"
                      />
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Product Information */}
            <div className="space-y-6">
              {/* Header */}
              <div>
                <div className="flex items-start justify-between mb-4">
                  <h1 className="text-4xl font-bold text-white leading-tight">
                    {product.name}
                  </h1>
                  
                  <div className="flex gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleBookmark}
                      className={cn(
                        "h-10 w-10 p-0 rounded-full border border-gray-700",
                        isBookmarked && "bg-yellow-500/20 text-yellow-400 border-yellow-500/50"
                      )}
                    >
                      <Bookmark className={cn("h-5 w-5", isBookmarked && "fill-current")} />
                    </Button>
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleShare}
                      className="h-10 w-10 p-0 rounded-full border border-gray-700"
                    >
                      <Share2 className="h-5 w-5" />
                    </Button>
                  </div>
                </div>

                {/* Category Badges */}
                <div className="flex flex-wrap gap-2 mb-4">
                  {plantPart && (
                    <Badge className={componentStyles.hemp.badge}>
                      <Leaf className="h-3 w-3 mr-1" />
                      {plantPart.name}
                    </Badge>
                  )}
                  
                  {industry && (
                    <Badge className={componentStyles.interactive.badge}>
                      <Factory className="h-3 w-3 mr-1" />
                      {industry.name}
                    </Badge>
                  )}
                  
                  {product.commercializationStage && (
                    <Badge className={getStageColor(product.commercializationStage)}>
                      <TrendingUp className="h-3 w-3 mr-1" />
                      {product.commercializationStage}
                    </Badge>
                  )}
                </div>

                {/* Description */}
                <p className="text-lg text-gray-300 leading-relaxed">
                  {product.description}
                </p>
              </div>

              {/* Key Benefits */}
              {product.benefitsAdvantages && product.benefitsAdvantages.length > 0 && (
                <div>
                  <h3 className="text-xl font-semibold text-white mb-3 flex items-center">
                    <Award className="h-5 w-5 mr-2 text-green-400" />
                    Key Benefits
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    {product.benefitsAdvantages.map((benefit, index) => (
                      <Badge 
                        key={index}
                        variant="secondary"
                        className="bg-green-500/10 text-green-400 border-green-500/20"
                      >
                        {benefit}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Sustainability Aspects */}
              {product.sustainabilityAspects && product.sustainabilityAspects.length > 0 && (
                <div>
                  <h3 className="text-xl font-semibold text-white mb-3 flex items-center">
                    <Recycle className="h-5 w-5 mr-2 text-green-400" />
                    Sustainability
                  </h3>
                  <div className="space-y-2">
                    {product.sustainabilityAspects.map((aspect, index) => (
                      <div key={index} className="flex items-center gap-2 text-gray-300">
                        <div className="w-2 h-2 bg-green-400 rounded-full" />
                        {aspect}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex gap-4 pt-6 border-t border-gray-800">
                <Button className="flex-1 bg-purple-500 hover:bg-purple-600">
                  <Building2 className="h-4 w-4 mr-2" />
                  Find Suppliers
                </Button>
                
                <Button variant="outline" className="flex-1 border-gray-700 hover:bg-gray-800">
                  <BarChart3 className="h-4 w-4 mr-2" />
                  Market Analysis
                </Button>
                
                <Button variant="outline" className="border-gray-700 hover:bg-gray-800">
                  <ExternalLink className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Additional Information Sections */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
            {/* Manufacturing Process */}
            {product.manufacturingProcessesSummary && (
              <Card className={cn(componentStyles.interface.card, "p-6")}>
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                  <Factory className="h-5 w-5 mr-2 text-purple-400" />
                  Manufacturing
                </h3>
                <p className="text-gray-300 text-sm leading-relaxed">
                  {product.manufacturingProcessesSummary}
                </p>
              </Card>
            )}

            {/* Technical Specifications */}
            {product.technicalSpecifications && (
              <Card className={cn(componentStyles.interface.card, "p-6")}>
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                  <Zap className="h-5 w-5 mr-2 text-purple-400" />
                  Technical Specs
                </h3>
                <div className="space-y-2 text-sm">
                  {Object.entries(product.technicalSpecifications as Record<string, any>).map(([key, value]) => (
                    <div key={key} className="flex justify-between">
                      <span className="text-gray-400 capitalize">{key.replace(/([A-Z])/g, ' $1')}</span>
                      <span className="text-white">{String(value)}</span>
                    </div>
                  ))}
                </div>
              </Card>
            )}

            {/* Historical Context */}
            {product.historicalContextFacts && product.historicalContextFacts.length > 0 && (
              <Card className={cn(componentStyles.interface.card, "p-6")}>
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                  <Calendar className="h-5 w-5 mr-2 text-purple-400" />
                  Historical Context
                </h3>
                <div className="space-y-2">
                  {product.historicalContextFacts.slice(0, 3).map((fact, index) => (
                    <div key={index} className="flex items-start gap-2 text-sm text-gray-300">
                      <div className="w-1.5 h-1.5 bg-purple-400 rounded-full mt-2 flex-shrink-0" />
                      {fact}
                    </div>
                  ))}
                </div>
              </Card>
            )}
          </div>

          {/* Related Products */}
          {relatedProducts.length > 0 && (
            <div>
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-white">Related Products</h2>
                <Button variant="ghost" className="text-purple-400 hover:text-purple-300">
                  View All
                  <ChevronRight className="h-4 w-4 ml-1" />
                </Button>
              </div>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                {relatedProducts.map((relatedProduct) => {
                  const relatedPlantPart = plantParts?.find(p => p.id === relatedProduct.plantPartId);
                  const relatedIndustry = industries?.find(i => i.id === relatedProduct.industryId);
                  
                  return (
                    <EnhancedModernProductCard
                      key={relatedProduct.id}
                      product={{
                        ...relatedProduct,
                        rating: Math.random() * 2 + 3,
                        sustainability_score: Math.floor(Math.random() * 4) + 6,
                        trl_level: Math.floor(Math.random() * 9) + 1,
                        company_name: `Hemp Co. ${Math.floor(Math.random() * 100)}`
                      }}
                      industryName={relatedIndustry?.name}
                      plantPartName={relatedPlantPart?.name}
                      variant="compact"
                    />
                  );
                })}
              </div>
            </div>
          )}
        </div>
      </div>
    </AppLayout>
  );
};

export default EnhancedProductDetail;
