import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

export default function TestNavigationPage() {
  const [selectedIndustry, setSelectedIndustry] = useState<string | null>(null);
  const [selectedCollection, setSelectedCollection] = useState<string | null>(null);
  const [selectedApplication, setSelectedApplication] = useState<string | null>(null);
  const [clickLog, setClickLog] = useState<string[]>([]);

  const addToLog = (message: string) => {
    setClickLog(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const handleIndustryClick = (industryId: string) => {
    setSelectedIndustry(industryId);
    setSelectedCollection(null);
    setSelectedApplication(null);
    addToLog(`Industry clicked: ${industryId}`);
  };

  const handleCollectionClick = (collectionId: string) => {
    setSelectedCollection(collectionId);
    setSelectedIndustry(null);
    setSelectedApplication(null);
    addToLog(`Collection clicked: ${collectionId}`);
  };

  const handleApplicationClick = (appName: string) => {
    setSelectedApplication(appName);
    setSelectedIndustry(null);
    setSelectedCollection(null);
    addToLog(`Application clicked: ${appName}`);
  };

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-6">Test Navigation - Click Events</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        {/* Industries */}
        <Card 
          className={`cursor-pointer transition-all ${selectedIndustry === 'food' ? 'ring-2 ring-blue-500' : ''}`}
          onClick={() => handleIndustryClick('food')}
        >
          <CardContent className="p-4">
            <h3 className="font-semibold">Food & Nutrition</h3>
            <p className="text-sm text-gray-600">Click to filter by food industry</p>
          </CardContent>
        </Card>

        {/* Collections */}
        <Card 
          className={`cursor-pointer transition-all ${selectedCollection === 'hemp-kitchen' ? 'ring-2 ring-blue-500' : ''}`}
          onClick={() => handleCollectionClick('hemp-kitchen')}
        >
          <CardContent className="p-4">
            <h3 className="font-semibold">Hemp Kitchen</h3>
            <p className="text-sm text-gray-600">Click to view collection</p>
          </CardContent>
        </Card>

        {/* Applications */}
        <Card 
          className={`cursor-pointer transition-all ${selectedApplication === 'For Chefs' ? 'ring-2 ring-blue-500' : ''}`}
          onClick={() => handleApplicationClick('For Chefs')}
        >
          <CardContent className="p-4">
            <h3 className="font-semibold">For Chefs</h3>
            <p className="text-sm text-gray-600">Click to filter by application</p>
          </CardContent>
        </Card>
      </div>

      {/* Current Selection Display */}
      <div className="bg-gray-100 p-4 rounded-lg mb-6">
        <h3 className="font-semibold mb-2">Current Selection:</h3>
        {selectedIndustry && <p>Industry: {selectedIndustry}</p>}
        {selectedCollection && <p>Collection: {selectedCollection}</p>}
        {selectedApplication && <p>Application: {selectedApplication}</p>}
        {!selectedIndustry && !selectedCollection && !selectedApplication && <p>None selected</p>}
      </div>

      {/* Click Log */}
      <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm">
        <h3 className="font-semibold mb-2">Click Event Log:</h3>
        {clickLog.length === 0 ? (
          <p>No clicks yet. Click any card above to test.</p>
        ) : (
          clickLog.map((log, index) => (
            <div key={index}>{log}</div>
          ))
        )}
      </div>

      <Button 
        className="mt-4" 
        onClick={() => {
          setClickLog([]);
          setSelectedIndustry(null);
          setSelectedCollection(null);
          setSelectedApplication(null);
        }}
      >
        Reset Test
      </Button>
    </div>
  );
}