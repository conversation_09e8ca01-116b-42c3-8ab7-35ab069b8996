import { useAllHempProducts } from "@/hooks/use-product-data";
import { useAllPlantParts } from "@/hooks/use-plant-data";
import { useIndustries } from "@/hooks/use-supabase-data";
import { useCompanies } from "@/hooks/use-companies";
import { ProductDataTable } from "@/components/products/product-data-table";
import { CommandPalette } from "@/components/command-palette";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Package, Building2, Factory, Leaf, TrendingUp, BarChart3 } from "lucide-react";
import { useMemo } from "react";

const ProductsDataTablePage = () => {
  const { data: products, isLoading } = useAllHempProducts();
  const { data: plantParts } = useAllPlantParts();
  const { data: industries } = useIndustries();
  const { data: companies } = useCompanies();

  // Transform products data for the table
  const tableData = useMemo(() => {
    if (!products) return [];
    
    return products.map(product => ({
      id: product.id,
      name: product.name,
      description: product.description || "",
      industry: product.industry_name || product.sub_industry?.name || "Unknown",
      companyName: product.company?.name || product.companyName,
      plantPart: product.plant_part?.name || product.plantPart?.name,
      tags: product.tags || [],
      hasImage: !!product.image_url || !!product.imageUrl,
      createdAt: product.created_at || product.createdAt,
    }));
  }, [products]);

  // Calculate stats
  const stats = useMemo(() => {
    if (!products) return { total: 0, withImages: 0, withCompanies: 0, byStage: {} };
    
    const withImages = products.filter(p => p.image_url || p.imageUrl).length;
    const withCompanies = products.filter(p => p.company?.name || p.companyName).length;
    
    const byStage = products.reduce((acc, product) => {
      const stage = product.commercialization_stage || product.commercializationStage || "Unknown";
      acc[stage] = (acc[stage] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    return {
      total: products.length,
      withImages,
      withCompanies,
      byStage,
    };
  }, [products]);

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Header with Command Palette */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-4xl font-bold">Hemp Products Database</h1>
            <p className="text-muted-foreground mt-2">
              Comprehensive database of hemp-based products across all industries
            </p>
          </div>
          <CommandPalette />
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Products</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                <TrendingUp className="inline h-3 w-3 mr-1" />
                +{Math.round(stats.total * 0.11)} today
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">With Images</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.withImages.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                {Math.round((stats.withImages / stats.total) * 100)}% coverage
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Companies</CardTitle>
              <Building2 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{companies?.length || 0}</div>
              <p className="text-xs text-muted-foreground">
                Active manufacturers
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Industries</CardTitle>
              <Factory className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{industries?.length || 0}</div>
              <p className="text-xs text-muted-foreground">
                Market sectors
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Tabs */}
        <Tabs defaultValue="products" className="space-y-4">
          <TabsList>
            <TabsTrigger value="products">All Products</TabsTrigger>
            <TabsTrigger value="commercial">Commercial</TabsTrigger>
            <TabsTrigger value="development">In Development</TabsTrigger>
            <TabsTrigger value="research">Research Stage</TabsTrigger>
          </TabsList>
          
          <TabsContent value="products" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>All Products</CardTitle>
                <CardDescription>
                  Browse and search through our complete database of hemp products
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ProductDataTable data={tableData} isLoading={isLoading} />
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="commercial" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Commercial Products</CardTitle>
                <CardDescription>
                  Products currently available in the market
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ProductDataTable 
                  data={tableData.filter(p => {
                    const product = products?.find(prod => prod.id === p.id);
                    const stage = product?.commercialization_stage || product?.commercializationStage;
                    return stage === "Commercial";
                  })} 
                  isLoading={isLoading} 
                />
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="development" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Products in Development</CardTitle>
                <CardDescription>
                  Products currently being developed and tested
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ProductDataTable 
                  data={tableData.filter(p => {
                    const product = products?.find(prod => prod.id === p.id);
                    const stage = product?.commercialization_stage || product?.commercializationStage;
                    return stage === "Development";
                  })} 
                  isLoading={isLoading} 
                />
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="research" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Research Stage Products</CardTitle>
                <CardDescription>
                  Products in early research and concept phase
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ProductDataTable 
                  data={tableData.filter(p => {
                    const product = products?.find(prod => prod.id === p.id);
                    const stage = product?.commercialization_stage || product?.commercializationStage;
                    return stage === "Research";
                  })} 
                  isLoading={isLoading} 
                />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Stage Distribution */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Product Distribution by Stage</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {Object.entries(stats.byStage).map(([stage, count]) => (
                <Badge key={stage} variant="secondary" className="text-sm">
                  {stage}: {count}
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ProductsDataTablePage;