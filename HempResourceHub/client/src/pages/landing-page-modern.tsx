import { useState } from "react";
import { motion } from "framer-motion";
import { 
  Search, 
  ChevronRight, 
  Sparkles,
  Package,
  Building2,
  FlaskConical,
  Users,
  TrendingUp,
  Award,
  Globe,
  Leaf,
  ArrowRight,
  Star,
  CheckCircle
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useLocation } from "wouter";
import { cn } from "@/lib/utils";

// Category cards data
const CATEGORIES = [
  {
    id: "products",
    title: "Hemp Products",
    subtitle: "Discover innovative hemp solutions",
    image: "/images/fiber-hemp.png",
    count: "2,562+",
    trending: true,
    path: "/products"
  },
  {
    id: "companies",
    title: "Hemp Companies",
    subtitle: "Connect with industry leaders",
    image: "/images/dual-use-hemp.png",
    count: "204+",
    verified: true,
    path: "/hemp-companies"
  },
  {
    id: "research",
    title: "Research Hub",
    subtitle: "Scientific papers & studies",
    image: "/images/cannabinoid-hemp.png",
    count: "1,000+",
    new: true,
    path: "/research"
  },
  {
    id: "industries",
    title: "Industry Explorer",
    subtitle: "16 major sectors covered",
    image: "/images/grain-hemp.png",
    count: "16",
    path: "/industries"
  },
  {
    id: "plant-parts",
    title: "Plant Science",
    subtitle: "From seed to fiber",
    image: "/images/unknown-hemp-image.png",
    count: "7 Parts",
    path: "/plant-parts"
  },
  {
    id: "discover",
    title: "Discover More",
    subtitle: "Explore the hemp ecosystem",
    image: "/images/fallbacks/hemp-generic-neutral.png",
    special: true,
    path: "/products-discovery"
  }
];

// Feature highlights
const FEATURES = [
  {
    icon: Package,
    title: "2,500+ Products",
    description: "Comprehensive database of hemp products across all industries"
  },
  {
    icon: Building2,
    title: "200+ Companies",
    description: "Verified hemp businesses and suppliers worldwide"
  },
  {
    icon: FlaskConical,
    title: "1,000+ Research Papers",
    description: "Scientific studies and industry research"
  },
  {
    icon: Globe,
    title: "Global Coverage",
    description: "Hemp innovations from around the world"
  }
];

// Stats section
const STATS = [
  { value: "25.6%", label: "Database Growth", trend: "+132%" },
  { value: "42", label: "Industries Covered", trend: "All Sectors" },
  { value: "90%", label: "Image Coverage", trend: "Visual Data" },
  { value: "24/7", label: "AI Updates", trend: "Real-time" }
];

export default function ModernLandingPage() {
  const [, setLocation] = useLocation();
  const [searchQuery, setSearchQuery] = useState("");

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      setLocation(`/products?search=${encodeURIComponent(searchQuery)}`);
    }
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section with Gradient Background */}
      <section className="relative overflow-hidden">
        {/* Gradient Background - Airbnb inspired */}
        <div className="absolute inset-0 bg-gradient-to-br from-purple-600 via-purple-500 to-purple-400 opacity-90" />
        <div className="absolute inset-0 bg-gradient-to-tr from-black/20 via-transparent to-transparent" />
        
        {/* Content */}
        <div className="relative z-10 container mx-auto px-4 py-20">
          <div className="max-w-4xl mx-auto text-center text-white">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <Badge className="mb-4 bg-white/20 text-white border-white/30 backdrop-blur">
                <Sparkles className="w-3 h-3 mr-1" />
                AI-Powered Hemp Intelligence
              </Badge>
              
              <h1 className="text-5xl md:text-6xl font-bold mb-6">
                Discover the Future of
                <span className="block text-green-300">Industrial Hemp</span>
              </h1>
              
              <p className="text-xl mb-8 text-white/90 max-w-2xl mx-auto">
                Explore the world's most comprehensive database of hemp products, 
                companies, and research. Your gateway to sustainable innovation.
              </p>
            </motion.div>

            {/* Search Bar */}
            <motion.form
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              onSubmit={handleSearch}
              className="max-w-2xl mx-auto"
            >
              <div className="relative bg-white rounded-full shadow-2xl p-2 flex items-center">
                <Search className="absolute left-6 text-gray-400 w-5 h-5" />
                <Input
                  type="text"
                  placeholder="Search products, companies, or research..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="flex-1 pl-14 pr-4 py-3 text-gray-900 bg-transparent border-0 focus:ring-0 text-lg"
                />
                <Button 
                  type="submit"
                  className="bg-purple-600 hover:bg-purple-700 text-white rounded-full px-8 py-3"
                >
                  Search
                </Button>
              </div>
            </motion.form>

            {/* Quick Links */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="mt-8 flex flex-wrap justify-center gap-4"
            >
              <Button
                variant="ghost"
                className="text-white hover:bg-white/20"
                onClick={() => setLocation("/products")}
              >
                <Package className="w-4 h-4 mr-2" />
                Browse Products
              </Button>
              <Button
                variant="ghost"
                className="text-white hover:bg-white/20"
                onClick={() => setLocation("/hemp-companies")}
              >
                <Building2 className="w-4 h-4 mr-2" />
                Find Companies
              </Button>
              <Button
                variant="ghost"
                className="text-white hover:bg-white/20"
                onClick={() => setLocation("/research")}
              >
                <FlaskConical className="w-4 h-4 mr-2" />
                Research Papers
              </Button>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Category Cards Grid - Airbnb Style */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Explore Hemp Categories
            </h2>
            <p className="text-lg text-gray-600">
              Dive into specific areas of the hemp industry
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {CATEGORIES.map((category, index) => (
              <motion.div
                key={category.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <Card 
                  className="group cursor-pointer overflow-hidden hover:shadow-xl transition-all duration-300"
                  onClick={() => setLocation(category.path)}
                >
                  <div className="relative h-48 overflow-hidden">
                    <img
                      src={category.image}
                      alt={category.title}
                      className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                    
                    {/* Badges */}
                    <div className="absolute top-4 left-4 flex gap-2">
                      {category.trending && (
                        <Badge className="bg-red-500 text-white">
                          <TrendingUp className="w-3 h-3 mr-1" />
                          Trending
                        </Badge>
                      )}
                      {category.verified && (
                        <Badge className="bg-blue-500 text-white">
                          <CheckCircle className="w-3 h-3 mr-1" />
                          Verified
                        </Badge>
                      )}
                      {category.new && (
                        <Badge className="bg-green-500 text-white">
                          <Sparkles className="w-3 h-3 mr-1" />
                          New
                        </Badge>
                      )}
                      {category.special && (
                        <Badge className="bg-purple-500 text-white">
                          <Star className="w-3 h-3 mr-1" />
                          Featured
                        </Badge>
                      )}
                    </div>

                    {/* Count Badge */}
                    <div className="absolute bottom-4 left-4">
                      <span className="text-white font-bold text-2xl">{category.count}</span>
                    </div>
                  </div>
                  
                  <CardContent className="p-6">
                    <h3 className="text-xl font-semibold text-gray-900 mb-2 group-hover:text-purple-600 transition-colors">
                      {category.title}
                    </h3>
                    <p className="text-gray-600 mb-4">{category.subtitle}</p>
                    <div className="flex items-center text-purple-600 font-medium">
                      Explore
                      <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-2 transition-transform" />
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Why Choose HempQuarterz Database?
            </h2>
            <p className="text-lg text-gray-600">
              The most comprehensive hemp intelligence platform
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {FEATURES.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="text-center"
              >
                <div className="inline-flex items-center justify-center w-16 h-16 bg-purple-100 text-purple-600 rounded-2xl mb-4">
                  <feature.icon className="w-8 h-8" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {feature.title}
                </h3>
                <p className="text-gray-600">
                  {feature.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-gray-900 text-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {STATS.map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="text-center"
              >
                <div className="text-4xl font-bold text-green-400 mb-2">
                  {stat.value}
                </div>
                <div className="text-lg font-medium mb-1">
                  {stat.label}
                </div>
                <div className="text-sm text-gray-400">
                  {stat.trend}
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-br from-purple-600 to-purple-500 text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-4xl font-bold mb-6">
              Ready to Explore the Hemp Revolution?
            </h2>
            <p className="text-xl mb-8 max-w-2xl mx-auto text-white/90">
              Join thousands of researchers, entrepreneurs, and innovators discovering 
              the potential of industrial hemp.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                size="lg"
                className="bg-white text-purple-600 hover:bg-gray-100"
                onClick={() => setLocation("/products")}
              >
                Start Exploring
                <ChevronRight className="w-5 h-5 ml-2" />
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="border-white text-white hover:bg-white/20"
                onClick={() => setLocation("/research")}
              >
                <FlaskConical className="w-5 h-5 mr-2" />
                Browse Research
              </Button>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-gray-400 py-12">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
            <div>
              <h3 className="text-white font-semibold mb-4 flex items-center">
                <Leaf className="w-5 h-5 mr-2 text-green-400" />
                HempQuarterz®
              </h3>
              <p className="text-sm">
                The world's most comprehensive industrial hemp database and intelligence platform.
              </p>
            </div>
            
            <div>
              <h4 className="text-white font-semibold mb-4">Explore</h4>
              <ul className="space-y-2 text-sm">
                <li><a href="/products" className="hover:text-white transition-colors">Products</a></li>
                <li><a href="/hemp-companies" className="hover:text-white transition-colors">Companies</a></li>
                <li><a href="/research" className="hover:text-white transition-colors">Research</a></li>
                <li><a href="/industries" className="hover:text-white transition-colors">Industries</a></li>
              </ul>
            </div>
            
            <div>
              <h4 className="text-white font-semibold mb-4">Resources</h4>
              <ul className="space-y-2 text-sm">
                <li><a href="/plant-parts" className="hover:text-white transition-colors">Plant Science</a></li>
                <li><a href="/analytics" className="hover:text-white transition-colors">Analytics</a></li>
                <li><a href="/about" className="hover:text-white transition-colors">About Us</a></li>
                <li><a href="/contact" className="hover:text-white transition-colors">Contact</a></li>
              </ul>
            </div>
            
            <div>
              <h4 className="text-white font-semibold mb-4">Connect</h4>
              <p className="text-sm mb-4">
                Stay updated with the latest hemp innovations and research.
              </p>
              <div className="flex space-x-4">
                <Button
                  size="sm"
                  className="bg-green-600 hover:bg-green-700"
                  onClick={() => setLocation("/contact")}
                >
                  Get in Touch
                </Button>
              </div>
            </div>
          </div>
          
          <div className="border-t border-gray-800 pt-8 text-center text-sm">
            <p>&copy; 2025 HempQuarterz®. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}