import React, { useState, useEffect, useMemo } from "react";
import { Helmet } from "react-helmet";
import { useLocation } from "wouter";
import { GlobalSearchAdvanced } from "@/components/ui/global-search-advanced";
import { CategoryNavigation } from "@/components/ui/category-navigation";
import { ResponsiveModernGrid } from "@/components/product/responsive-modern-grid";
import { AppLayout } from "@/components/layout/AppLayout";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { 
  TrendingUp, 
  Sparkles, 
  Database, 
  Filter,
  X,
  BarChart3,
  Leaf,
  Factory,
  Globe
} from "lucide-react";
import { useAllHempProducts } from "@/hooks/use-product-data";
import { usePlantParts, useIndustries } from "@/hooks/use-plant-data";
import { cn } from "@/lib/utils";
import { componentStyles } from "@/lib/design-system";

interface CategoryItem {
  id: number;
  name: string;
  type: 'plantPart' | 'industry';
}

interface SearchFilters {
  query: string;
  selectedCategories: number[];
  commercializationStages: string[];
  sustainabilityFilter: boolean;
  trlRange: [number, number];
}

const HempDiscoveryHub = () => {
  const [location, setLocation] = useLocation();
  
  // State management
  const [filters, setFilters] = useState<SearchFilters>({
    query: '',
    selectedCategories: [],
    commercializationStages: [],
    sustainabilityFilter: false,
    trlRange: [1, 9]
  });
  
  const [bookmarkedProducts, setBookmarkedProducts] = useState<number[]>(() => {
    const saved = localStorage.getItem('hemp-bookmarks');
    return saved ? JSON.parse(saved) : [];
  });

  // Data hooks
  const { data: products, isLoading: productsLoading, error: productsError } = useAllHempProducts();
  const { data: plantParts } = usePlantParts();
  const { data: industries } = useIndustries();

  // Parse URL parameters on mount
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const searchParam = urlParams.get('search');
    const categoriesParam = urlParams.get('categories');
    
    if (searchParam) {
      setFilters(prev => ({ ...prev, query: searchParam }));
    }
    
    if (categoriesParam) {
      const categoryIds = categoriesParam.split(',').map(Number).filter(Boolean);
      setFilters(prev => ({ ...prev, selectedCategories: categoryIds }));
    }
  }, []);

  // Enhanced product data with metadata
  const enhancedProducts = useMemo(() => {
    if (!products || !plantParts || !industries) return [];

    return products.map(product => {
      const plantPart = plantParts.find(p => p.id === product.plantPartId);
      const industry = industries.find(i => i.id === product.industryId);
      
      return {
        ...product,
        plantPartName: plantPart?.name,
        industryName: industry?.name,
        // Add mock data for enhanced features
        rating: Math.random() * 2 + 3, // 3-5 rating
        sustainability_score: Math.floor(Math.random() * 4) + 6, // 6-10 score
        trl_level: Math.floor(Math.random() * 9) + 1, // 1-9 TRL
        company_name: `Hemp Co. ${Math.floor(Math.random() * 100)}`
      };
    });
  }, [products, plantParts, industries]);

  // Filtered products based on current filters
  const filteredProducts = useMemo(() => {
    if (!enhancedProducts) return [];

    return enhancedProducts.filter(product => {
      // Text search
      if (filters.query) {
        const searchTerm = filters.query.toLowerCase();
        const matchesName = product.name.toLowerCase().includes(searchTerm);
        const matchesDescription = product.description?.toLowerCase().includes(searchTerm);
        const matchesKeywords = product.keywords?.some(keyword => 
          keyword.toLowerCase().includes(searchTerm)
        );
        
        if (!matchesName && !matchesDescription && !matchesKeywords) {
          return false;
        }
      }

      // Category filters
      if (filters.selectedCategories.length > 0) {
        const matchesPlantPart = filters.selectedCategories.includes(product.plantPartId);
        const matchesIndustry = product.industryId && filters.selectedCategories.includes(product.industryId);
        
        if (!matchesPlantPart && !matchesIndustry) {
          return false;
        }
      }

      // Commercialization stage filter
      if (filters.commercializationStages.length > 0) {
        if (!product.commercializationStage || 
            !filters.commercializationStages.includes(product.commercializationStage.toLowerCase())) {
          return false;
        }
      }

      // Sustainability filter
      if (filters.sustainabilityFilter) {
        if (!product.sustainability_score || product.sustainability_score < 7) {
          return false;
        }
      }

      // TRL range filter
      if (product.trl_level) {
        if (product.trl_level < filters.trlRange[0] || product.trl_level > filters.trlRange[1]) {
          return false;
        }
      }

      return true;
    });
  }, [enhancedProducts, filters]);

  // Statistics
  const stats = useMemo(() => {
    const total = enhancedProducts.length;
    const commercial = enhancedProducts.filter(p => p.commercializationStage?.toLowerCase() === 'commercial').length;
    const sustainable = enhancedProducts.filter(p => p.sustainability_score && p.sustainability_score >= 7).length;
    const industries = new Set(enhancedProducts.map(p => p.industryName).filter(Boolean)).size;

    return { total, commercial, sustainable, industries };
  }, [enhancedProducts]);

  // Event handlers
  const handleSearch = (query: string) => {
    setFilters(prev => ({ ...prev, query }));
    // Update URL
    const params = new URLSearchParams(window.location.search);
    if (query) {
      params.set('search', query);
    } else {
      params.delete('search');
    }
    window.history.replaceState({}, '', `${window.location.pathname}?${params}`);
  };

  const handleCategorySelect = (category: CategoryItem) => {
    setFilters(prev => {
      const isSelected = prev.selectedCategories.includes(category.id);
      const newCategories = isSelected
        ? prev.selectedCategories.filter(id => id !== category.id)
        : [...prev.selectedCategories, category.id];
      
      return { ...prev, selectedCategories: newCategories };
    });
  };

  const handleProductBookmark = (productId: number) => {
    setBookmarkedProducts(prev => {
      const newBookmarks = prev.includes(productId)
        ? prev.filter(id => id !== productId)
        : [...prev, productId];
      
      localStorage.setItem('hemp-bookmarks', JSON.stringify(newBookmarks));
      return newBookmarks;
    });
  };

  const handleProductShare = (product: any) => {
    if (navigator.share) {
      navigator.share({
        title: product.name,
        text: product.description,
        url: `${window.location.origin}/product/${product.id}`
      });
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(`${window.location.origin}/product/${product.id}`);
    }
  };

  const clearAllFilters = () => {
    setFilters({
      query: '',
      selectedCategories: [],
      commercializationStages: [],
      sustainabilityFilter: false,
      trlRange: [1, 9]
    });
    window.history.replaceState({}, '', window.location.pathname);
  };

  const hasActiveFilters = filters.query || filters.selectedCategories.length > 0 || 
    filters.commercializationStages.length > 0 || filters.sustainabilityFilter;

  return (
    <AppLayout>
      <Helmet>
        <title>Hemp Discovery Hub - HempQuarterz® Industrial Hemp Database</title>
        <meta name="description" content="Discover and explore thousands of hemp products, applications, and innovations across industries. Advanced search and filtering for hemp professionals." />
      </Helmet>

      <div className="min-h-screen bg-black text-white">
        {/* Hero Section */}
        <section className="relative py-12 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            {/* Header */}
            <div className="text-center mb-8">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-4">
                <span className="bg-gradient-to-r from-green-400 to-purple-400 bg-clip-text text-transparent">
                  Hemp Discovery Hub
                </span>
              </h1>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                Explore the world's most comprehensive database of industrial hemp products, 
                applications, and innovations across {stats.industries}+ industries.
              </p>
            </div>

            {/* Statistics Cards */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
              <Card className={cn(componentStyles.interface.card, "p-4 text-center")}>
                <Database className="h-8 w-8 text-green-400 mx-auto mb-2" />
                <div className="text-2xl font-bold text-white">{stats.total.toLocaleString()}</div>
                <div className="text-sm text-gray-400">Total Products</div>
              </Card>
              
              <Card className={cn(componentStyles.interface.card, "p-4 text-center")}>
                <TrendingUp className="h-8 w-8 text-purple-400 mx-auto mb-2" />
                <div className="text-2xl font-bold text-white">{stats.commercial}</div>
                <div className="text-sm text-gray-400">Commercial Ready</div>
              </Card>
              
              <Card className={cn(componentStyles.interface.card, "p-4 text-center")}>
                <Leaf className="h-8 w-8 text-green-400 mx-auto mb-2" />
                <div className="text-2xl font-bold text-white">{stats.sustainable}</div>
                <div className="text-sm text-gray-400">Eco-Friendly</div>
              </Card>
              
              <Card className={cn(componentStyles.interface.card, "p-4 text-center")}>
                <Factory className="h-8 w-8 text-purple-400 mx-auto mb-2" />
                <div className="text-2xl font-bold text-white">{stats.industries}</div>
                <div className="text-sm text-gray-400">Industries</div>
              </Card>
            </div>

            {/* Global Search */}
            <div className="mb-8">
              <GlobalSearchAdvanced
                onSearch={handleSearch}
                placeholder="Search hemp products, applications, industries, or ask a question..."
              />
            </div>

            {/* Category Navigation */}
            <div className="mb-8">
              <CategoryNavigation
                onCategorySelect={handleCategorySelect}
                selectedCategories={filters.selectedCategories}
                showCounts={true}
                allowMultiSelect={true}
              />
            </div>

            {/* Active Filters */}
            {hasActiveFilters && (
              <div className="mb-6 flex flex-wrap items-center gap-3">
                <span className="text-sm text-gray-400">Active filters:</span>
                
                {filters.query && (
                  <Badge variant="secondary" className="bg-purple-500/20 text-purple-400 border-purple-500/30">
                    Search: "{filters.query}"
                    <button
                      onClick={() => handleSearch('')}
                      className="ml-2 hover:text-purple-300"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </Badge>
                )}

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearAllFilters}
                  className="text-gray-400 hover:text-white"
                >
                  <X className="h-4 w-4 mr-1" />
                  Clear All
                </Button>
              </div>
            )}
          </div>
        </section>

        {/* Products Grid */}
        <section className="px-4 sm:px-6 lg:px-8 pb-12">
          <div className="max-w-7xl mx-auto">
            <ResponsiveModernGrid
              products={filteredProducts}
              isLoading={productsLoading}
              error={productsError?.message}
              onProductBookmark={handleProductBookmark}
              onProductShare={handleProductShare}
              bookmarkedProducts={bookmarkedProducts}
              showFilters={true}
              showSorting={true}
              enableInfiniteScroll={true}
            />
          </div>
        </section>
      </div>
    </AppLayout>
  );
};

export default HempDiscoveryHub;
