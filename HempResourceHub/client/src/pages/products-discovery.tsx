import { useState } from "react";
import { motion } from "framer-motion";
import { 
  Package, 
  Heart, 
  Home, 
  Stethoscope, 
  Shirt, 
  Zap,
  Leaf,
  Building,
  FlaskConical,
  Sparkles,
  ChevronRight,
  Search,
  Filter,
  Grid3x3,
  List
} from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useAllHempProducts } from "@/hooks/use-product-data";
import UnifiedProductCard from "@/components/product/unified-product-card";
import ProductListItem from "@/components/product/product-list-item";
import { cn } from "@/lib/utils";
import { useLocation } from "wouter";

// Industry configuration with icons and colors
const INDUSTRIES = [
  { 
    id: "food", 
    name: "Food Industry", 
    alternateNames: ["Food & Beverages", "Food and Beverages"],
    displayName: "Food & Nutrition",
    icon: Package, 
    color: "bg-green-500",
    gradient: "from-green-400 to-green-600",
    count: 1490,
    description: "Nutritious hemp foods and supplements"
  },
  { 
    id: "cosmetics", 
    name: "Cosmetics & Personal Care", 
    displayName: "Cosmetics & Beauty",
    icon: Heart, 
    color: "bg-pink-500",
    gradient: "from-pink-400 to-pink-600",
    count: 1244,
    description: "Natural hemp-based skincare and beauty"
  },
  { 
    id: "construction", 
    name: "Construction & Building Materials", 
    displayName: "Construction Materials",
    icon: Building, 
    color: "bg-gray-600",
    gradient: "from-gray-500 to-gray-700",
    count: 964,
    description: "Sustainable building with hemp"
  },
  { 
    id: "medical", 
    name: "Wellness & Pharmaceutical Industries", 
    displayName: "Medical & Healthcare",
    icon: Stethoscope, 
    color: "bg-blue-500",
    gradient: "from-blue-400 to-blue-600",
    count: 957,
    description: "Medical-grade hemp products"
  },
  { 
    id: "textiles", 
    name: "Textiles & Fashion Industry", 
    displayName: "Textiles & Fashion",
    icon: Shirt, 
    color: "bg-purple-500",
    gradient: "from-purple-400 to-purple-600",
    count: 787,
    description: "Hemp fabrics and fashion"
  },
  { 
    id: "energy", 
    name: "Energy Storage", 
    displayName: "Energy & Biofuels",
    icon: Zap, 
    color: "bg-yellow-500",
    gradient: "from-yellow-400 to-yellow-600",
    count: 133,
    description: "Renewable energy from hemp"
  },
];

// Featured collections
const COLLECTIONS = [
  {
    id: "construction-essentials",
    name: "Construction Essentials",
    description: "Top hemp building materials",
    productCount: 20,
    image: "/images/collections/construction.jpg"
  },
  {
    id: "beauty-bestsellers",
    name: "Beauty Bestsellers", 
    description: "Most popular hemp cosmetics",
    productCount: 15,
    image: "/images/collections/beauty.jpg"
  },
  {
    id: "hemp-kitchen",
    name: "Hemp Kitchen",
    description: "Essential hemp ingredients",
    productCount: 25,
    image: "/images/collections/kitchen.jpg"
  },
  {
    id: "eco-home",
    name: "Eco Home Bundle",
    description: "Sustainable household items",
    productCount: 18,
    image: "/images/collections/home.jpg"
  }
];

export default function ProductsDiscoveryPage() {
  const [selectedIndustry, setSelectedIndustry] = useState<string | null>(null);
  const [selectedCollection, setSelectedCollection] = useState<string | null>(null);
  const [selectedApplication, setSelectedApplication] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [searchQuery, setSearchQuery] = useState("");
  const { data: products, isLoading } = useAllHempProducts();
  const [, navigate] = useLocation();

  // Debug logging
  console.log('Current filters:', {
    selectedIndustry,
    selectedCollection,
    selectedApplication,
    searchQuery,
    totalProducts: products?.length || 0
  });

  // Filter products by selected industry and search query
  const filteredProducts = products?.filter(p => {
    // First apply search filter if there's a query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      const matchesSearch = 
        p.name?.toLowerCase().includes(query) ||
        p.description?.toLowerCase().includes(query) ||
        p.plant_part_name?.toLowerCase().includes(query) ||
        p.sub_industry_name?.toLowerCase().includes(query) ||
        p.industry_name?.toLowerCase().includes(query);
      
      if (!matchesSearch) return false;
    }
    
    // Then apply industry filter if selected
    if (selectedIndustry) {
      const industryMatch = INDUSTRIES.find(i => i.id === selectedIndustry);
      if (!industryMatch) return false;
      
      // Check if product's industry matches the selected industry
      const productIndustry = p.industry_name || p.industry_sub_categories?.industries?.name;
      
      if (productIndustry) {
        // Check main name
        if (productIndustry === industryMatch.name) return true;
        
        // Check alternate names
        if (industryMatch.alternateNames?.includes(productIndustry)) return true;
      }
      
      return false;
    }
    
    // Apply collection filter if selected
    if (selectedCollection) {
      // Filter based on collection logic
      if (selectedCollection === "construction-essentials") {
        return p.industry_name === "Construction & Building Materials" && 
               p.commercialization_stage === "Commercial";
      }
      if (selectedCollection === "beauty-bestsellers") {
        return p.industry_name === "Cosmetics & Personal Care" &&
               (p.rating > 4 || p.sub_industry_name === "Body Care");
      }
      if (selectedCollection === "hemp-kitchen") {
        return (p.industry_name === "Food Industry" || p.industry_name === "Food & Beverages") &&
               (p.sub_industry_name === "Hemp Seeds" || p.sub_industry_name === "Hemp Seed Oil Products");
      }
      if (selectedCollection === "eco-home") {
        return p.sub_industry_name === "Home Textiles" || 
               p.name?.toLowerCase().includes("household");
      }
    }
    
    // Apply application filter if selected
    if (selectedApplication) {
      const appName = selectedApplication.toLowerCase();
      if (appName.includes("builders")) {
        return p.industry_name === "Construction & Building Materials";
      }
      if (appName.includes("chefs")) {
        return p.industry_name === "Food Industry" || p.industry_name === "Food & Beverages";
      }
      if (appName.includes("wellness")) {
        return p.industry_name === "Wellness & Pharmaceutical Industries" ||
               p.industry_name === "Cosmetics & Personal Care";
      }
      if (appName.includes("designers")) {
        return p.industry_name === "Textiles & Fashion Industry";
      }
      if (appName.includes("scientists")) {
        return p.industry_name === "Wellness & Pharmaceutical Industries";
      }
      if (appName.includes("engineers")) {
        return p.industry_name === "Energy Storage";
      }
      if (appName.includes("farmers")) {
        return p.sub_industry_name === "Hemp Seeds" || 
               p.name?.toLowerCase().includes("cultivation");
      }
      if (appName.includes("innovators")) {
        return p.commercialization_stage === "Prototype" || 
               p.commercialization_stage === "Research";
      }
    }
    
    return true;
  });

  // Log filtered results
  console.log('Filtered products:', filteredProducts?.length || 0, 'from', products?.length || 0);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
      {/* Header */}
      <div className="bg-white dark:bg-gray-900 shadow-sm border-b">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center gap-2">
                <Leaf className="w-8 h-8 text-green-500" />
                Hemp Product Discovery
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                Explore {products?.length || "6,000+"} innovative hemp products across all industries
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="icon"
                onClick={() => setViewMode("grid")}
                className={cn(viewMode === "grid" && "bg-gray-100")}
              >
                <Grid3x3 className="w-4 h-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={() => setViewMode("list")}
                className={cn(viewMode === "list" && "bg-gray-100")}
              >
                <List className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Search Bar */}
          <div className="relative max-w-2xl mx-auto">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <Input
              type="search"
              placeholder="Search products, companies, or applications..."
              className="pl-10 pr-4 py-6 text-lg"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <Button className="absolute right-2 top-1/2 transform -translate-y-1/2">
              <Filter className="w-4 h-4 mr-2" />
              Filters
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        <Tabs defaultValue="industries" className="space-y-6">
          <TabsList className="grid w-full max-w-md mx-auto grid-cols-3">
            <TabsTrigger value="industries">Industries</TabsTrigger>
            <TabsTrigger value="collections">Collections</TabsTrigger>
            <TabsTrigger value="applications">Applications</TabsTrigger>
          </TabsList>

          {/* Industries Tab */}
          <TabsContent value="industries" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {INDUSTRIES.map((industry) => (
                <motion.div
                  key={industry.id}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Card 
                    className={cn(
                      "relative overflow-hidden cursor-pointer transition-all duration-300",
                      "hover:shadow-xl border-2",
                      selectedIndustry === industry.id && "ring-2 ring-offset-2 ring-blue-500"
                    )}
                    onClick={() => {
                      console.log('Industry clicked:', industry.id);
                      setSelectedIndustry(
                        selectedIndustry === industry.id ? null : industry.id
                      );
                    }}
                  >
                    <div className={cn(
                      "absolute inset-0 opacity-10 bg-gradient-to-br",
                      industry.gradient
                    )} />
                    <CardContent className="relative p-6">
                      <div className="flex items-start justify-between mb-4">
                        <div className={cn(
                          "p-3 rounded-lg bg-gradient-to-br text-white",
                          industry.gradient
                        )}>
                          <industry.icon className="w-6 h-6" />
                        </div>
                        <Badge variant="secondary" className="text-lg px-3 py-1">
                          {industry.count.toLocaleString()}
                        </Badge>
                      </div>
                      <h3 className="text-xl font-semibold mb-2">{industry.displayName}</h3>
                      <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
                        {industry.description}
                      </p>
                      <div className="flex items-center text-sm text-blue-600 dark:text-blue-400">
                        <span>Explore products</span>
                        <ChevronRight className="w-4 h-4 ml-1" />
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>

            {/* Quick Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-8">
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-green-600">6,149</div>
                  <div className="text-sm text-gray-600">Total Products</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-blue-600">236</div>
                  <div className="text-sm text-gray-600">Companies</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-purple-600">42</div>
                  <div className="text-sm text-gray-600">Sub-Categories</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-orange-600">16</div>
                  <div className="text-sm text-gray-600">Active Industries</div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Collections Tab */}
          <TabsContent value="collections" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {COLLECTIONS.map((collection) => (
                <motion.div
                  key={collection.id}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => {
                    console.log('Collection clicked:', collection.id);
                    setSelectedCollection(collection.id);
                    setSelectedIndustry(null);
                    setSelectedApplication(null);
                  }}
                >
                  <Card className="overflow-hidden cursor-pointer hover:shadow-xl transition-all">
                    <div className="aspect-video bg-gradient-to-br from-gray-200 to-gray-300 relative">
                      <div className="absolute inset-0 flex items-center justify-center">
                        <Sparkles className="w-12 h-12 text-gray-400" />
                      </div>
                    </div>
                    <CardContent className="p-4">
                      <h3 className="text-lg font-semibold mb-1">{collection.name}</h3>
                      <p className="text-sm text-gray-600 mb-2">{collection.description}</p>
                      <div className="flex items-center justify-between">
                        <Badge variant="outline">{collection.productCount} products</Badge>
                        <Button variant="ghost" size="sm">
                          View Collection
                          <ChevronRight className="w-4 h-4 ml-1" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </TabsContent>

          {/* Applications Tab */}
          <TabsContent value="applications" className="space-y-6">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {[
                { name: "For Builders", icon: Building, count: 964 },
                { name: "For Chefs", icon: Package, count: 1490 },
                { name: "For Wellness", icon: Heart, count: 1631 },
                { name: "For Designers", icon: Shirt, count: 317 },
                { name: "For Scientists", icon: FlaskConical, count: 387 },
                { name: "For Engineers", icon: Zap, count: 220 },
                { name: "For Farmers", icon: Leaf, count: 145 },
                { name: "For Innovators", icon: Sparkles, count: 995 }
              ].map((app) => (
                <Card 
                  key={app.name} 
                  className="hover:shadow-lg transition-all cursor-pointer"
                  onClick={() => {
                    console.log('Application clicked:', app.name);
                    setSelectedApplication(app.name);
                    setSelectedIndustry(null);
                    setSelectedCollection(null);
                  }}
                >
                  <CardContent className="p-4 text-center">
                    <app.icon className="w-8 h-8 mx-auto mb-2 text-gray-600" />
                    <h3 className="font-medium mb-1">{app.name}</h3>
                    <p className="text-sm text-gray-500">{app.count} products</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>

        {/* Products Section - Always show when we have products */}
        {products && products.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-8"
          >
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-2xl font-bold flex items-center gap-2">
                {selectedIndustry 
                  ? `${INDUSTRIES.find(i => i.id === selectedIndustry)?.displayName} Products`
                  : selectedCollection
                    ? `${COLLECTIONS.find(c => c.id === selectedCollection)?.name} Collection`
                  : selectedApplication
                    ? `Products ${selectedApplication}`
                  : searchQuery 
                    ? `Search Results for "${searchQuery}"`
                    : "Select a category above to filter products"
                }
                {(selectedIndustry || selectedCollection || selectedApplication || searchQuery) && 
                  filteredProducts && (
                    <Badge variant="secondary" className="ml-2">
                      {filteredProducts.length} products
                    </Badge>
                  )
                }
              </h2>
              {(selectedIndustry || selectedCollection || selectedApplication || searchQuery) && (
                <Button 
                  variant="ghost" 
                  onClick={() => {
                    setSelectedIndustry(null);
                    setSelectedCollection(null);
                    setSelectedApplication(null);
                    setSearchQuery("");
                  }}
                >
                  Clear Filters
                </Button>
              )}
            </div>
            {/* Product Grid */}
            {isLoading ? (
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
              </div>
            ) : (selectedIndustry || selectedCollection || selectedApplication || searchQuery) && filteredProducts && filteredProducts.length > 0 ? (
              <div className={cn(
                viewMode === "grid"
                  ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8"
                  : "space-y-4"
              )}>
                {filteredProducts.slice(0, 20).map((product) => {
                  const productProps = {
                    product: {
                      ...product,
                      image_url: product.image_url,
                      ai_generated_image_url: product.ai_generated_image_url,
                      company_name: product.hemp_company_products?.[0]?.hemp_companies?.name || product.company_name,
                      commercialization_stage: product.commercialization_stage,
                      rating: product.rating || 4 + Math.random(),
                      views: Math.floor(Math.random() * 1000 + 100),
                      sustainability_score: Math.floor(Math.random() * 30 + 70),
                      is_featured: Math.random() > 0.9
                    },
                    industryName: product.industry_sub_categories?.industries?.name || product.industry_name,
                    subIndustryName: product.sub_industry_name,
                    plantPartName: product.plant_parts?.name || product.plant_part_name,
                    onFavorite: (id: number) => console.log('Favorited:', id),
                    onShare: (p: any) => console.log('Share:', p)
                  };

                  return viewMode === "list" ? (
                    <ProductListItem key={product.id} {...productProps} />
                  ) : (
                    <UnifiedProductCard
                      key={product.id}
                      product={product}
                      viewMode="grid"
                      showDescription={true}
                      showBenefits={true}
                      showCompany={true}
                    />
                  );
                })}
              </div>
            ) : (selectedIndustry || selectedCollection || selectedApplication || searchQuery) ? (
              <div className="text-center py-12">
                <p className="text-gray-500">No products found in this category.</p>
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="max-w-md mx-auto">
                  <Package className="w-16 h-16 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-semibold text-gray-700 mb-2">Select a Category to Browse Products</h3>
                  <p className="text-gray-500">
                    Choose an industry, collection, or application from the tabs above to explore our {products?.length || "6,000+"} hemp products.
                  </p>
                </div>
              </div>
            )}
          </motion.div>
        )}
      </div>
    </div>
  );
}