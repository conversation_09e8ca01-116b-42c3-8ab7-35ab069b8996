import { useState } from "react";
import { motion } from "framer-motion";
import { 
  Search, 
  ChevronRight, 
  Package,
  Building2,
  FlaskConical,
  Leaf,
  ArrowRight,
  TrendingUp,
  Users,
  FileText
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { useLocation } from "wouter";
import { cn } from "@/lib/utils";

// Simplified categories - reduced from 6 to 4
const MAIN_CATEGORIES = [
  {
    id: "products",
    title: "2,562+ Hemp Products",
    subtitle: "Discover innovative solutions",
    icon: Package,
    path: "/products",
    stats: "25% Monthly Growth",
    color: "border-green-500"
  },
  {
    id: "companies",
    title: "204 Verified Companies",
    subtitle: "Connect with suppliers",
    icon: Building2,
    path: "/hemp-companies",
    stats: "Global Network",
    color: "border-purple-500"
  },
  {
    id: "research",
    title: "1,000+ Research Papers",
    subtitle: "Scientific insights",
    icon: FlaskConical,
    path: "/research",
    stats: "Peer Reviewed",
    color: "border-blue-500"
  },
  {
    id: "industries",
    title: "16 Industry Sectors",
    subtitle: "Explore applications",
    icon: Users,
    path: "/industries",
    stats: "All Markets",
    color: "border-amber-500"
  }
];

// Popular searches for quick access
const POPULAR_SEARCHES = [
  "CBD Products",
  "Hemp Fiber",
  "Building Materials", 
  "Hemp Seeds",
  "Textiles",
  "Bioplastics"
];

// Single stat for hero
const HERO_STAT = {
  products: "2,562",
  companies: "204",
  growth: "132%",
  coverage: "Global"
};

export default function ModernLandingPageV2() {
  const [, setLocation] = useLocation();
  const [searchQuery, setSearchQuery] = useState("");
  const [isSearchFocused, setIsSearchFocused] = useState(false);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      setLocation(`/products?search=${encodeURIComponent(searchQuery)}`);
    }
  };

  const handlePopularSearch = (term: string) => {
    setSearchQuery(term);
    setLocation(`/products?search=${encodeURIComponent(term)}`);
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section - Black/Dark theme as specified */}
      <section className="relative bg-black text-white overflow-hidden">
        {/* Subtle gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-gray-900 to-black opacity-90" />
        
        {/* Content */}
        <div className="relative z-10 container mx-auto px-4 py-16 md:py-24">
          <div className="max-w-4xl mx-auto text-center">
            {/* Logo/Brand */}
            <div className="flex items-center justify-center mb-8">
              <Leaf className="w-8 h-8 text-green-500 mr-2" />
              <span className="text-2xl font-bold">HempQuarterz®</span>
            </div>

            {/* Main headline */}
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
              The World's Largest
              <span className="block text-green-500">Hemp Intelligence Platform</span>
            </h1>
            
            <p className="text-lg md:text-xl mb-8 text-gray-300 max-w-2xl mx-auto">
              Access {HERO_STAT.products} products from {HERO_STAT.companies} companies across the global hemp industry.
            </p>

            {/* Search Bar with suggestions */}
            <form
              onSubmit={handleSearch}
              className="max-w-2xl mx-auto mb-6"
            >
              <div className="relative">
                <div className="relative bg-white rounded-lg shadow-xl">
                  <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <Input
                    type="text"
                    placeholder="Search products, companies, or research..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onFocus={() => setIsSearchFocused(true)}
                    onBlur={() => setTimeout(() => setIsSearchFocused(false), 200)}
                    className="w-full pl-12 pr-32 py-4 text-gray-900 bg-transparent border-0 focus:ring-2 focus:ring-purple-500 rounded-lg text-base md:text-lg"
                  />
                  <Button 
                    type="submit"
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-purple-600 hover:bg-purple-700 text-white rounded-md px-6"
                  >
                    Search
                  </Button>
                </div>

                {/* Search suggestions */}
                {isSearchFocused && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="absolute top-full left-0 right-0 mt-2 bg-white rounded-lg shadow-xl p-4 z-20"
                  >
                    <p className="text-sm text-gray-500 mb-2">Popular searches:</p>
                    <div className="flex flex-wrap gap-2">
                      {POPULAR_SEARCHES.map((term) => (
                        <button
                          key={term}
                          onClick={() => handlePopularSearch(term)}
                          className="px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded-full text-sm text-gray-700 transition-colors"
                        >
                          {term}
                        </button>
                      ))}
                    </div>
                  </motion.div>
                )}
              </div>
            </form>

            {/* Quick stats */}
            <div className="flex items-center justify-center gap-8 text-sm">
              <div className="flex items-center gap-2">
                <TrendingUp className="w-4 h-4 text-green-500" />
                <span className="text-gray-300">{HERO_STAT.growth} Growth</span>
              </div>
              <div className="flex items-center gap-2">
                <Users className="w-4 h-4 text-purple-500" />
                <span className="text-gray-300">{HERO_STAT.coverage} Coverage</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Main Categories - Simplified grid */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Explore the Hemp Ecosystem
            </h2>
            <p className="text-lg text-gray-600">
              Navigate through our comprehensive database
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-6xl mx-auto">
            {MAIN_CATEGORIES.map((category) => (
              <motion.div
                key={category.id}
                whileHover={{ y: -4 }}
                transition={{ duration: 0.2 }}
              >
                <Card 
                  className={cn(
                    "cursor-pointer border-2 hover:shadow-lg transition-all duration-200",
                    category.color
                  )}
                  onClick={() => setLocation(category.path)}
                >
                  <CardContent className="p-6">
                    <category.icon className="w-12 h-12 mb-4 text-gray-700" />
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">
                      {category.title}
                    </h3>
                    <p className="text-gray-600 mb-4">{category.subtitle}</p>
                    <div className="flex items-center justify-between">
                      <Badge variant="secondary" className="text-xs">
                        {category.stats}
                      </Badge>
                      <ArrowRight className="w-4 h-4 text-gray-400" />
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Single CTA Section */}
      <section className="py-16 bg-black text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6">
            Ready to Explore?
          </h2>
          <p className="text-lg mb-8 text-gray-300 max-w-2xl mx-auto">
            Join researchers, entrepreneurs, and innovators using our platform to discover hemp opportunities.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              size="lg"
              className="bg-green-600 hover:bg-green-700 text-white"
              onClick={() => setLocation("/products")}
            >
              Browse Products
              <ChevronRight className="w-5 h-5 ml-2" />
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="border-gray-600 text-white hover:bg-gray-800"
              onClick={() => setLocation("/hemp-companies")}
            >
              Find Companies
            </Button>
          </div>
        </div>
      </section>

      {/* Simplified Footer */}
      <footer className="bg-gray-900 text-gray-400 py-12">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center mb-4 md:mb-0">
              <Leaf className="w-6 h-6 mr-2 text-green-400" />
              <span className="text-white font-semibold">HempQuarterz®</span>
            </div>
            
            <nav className="flex flex-wrap gap-6 text-sm">
              <a href="/products" className="hover:text-white transition-colors">Products</a>
              <a href="/hemp-companies" className="hover:text-white transition-colors">Companies</a>
              <a href="/research" className="hover:text-white transition-colors">Research</a>
              <a href="/about" className="hover:text-white transition-colors">About</a>
              <a href="/contact" className="hover:text-white transition-colors">Contact</a>
            </nav>
          </div>
          
          <div className="mt-8 pt-8 border-t border-gray-800 text-center text-sm">
            <p>&copy; 2025 HempQuarterz®. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}