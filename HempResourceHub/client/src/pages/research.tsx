import { useState, useMemo } from "react";
import { Helmet } from "react-helmet";
import { useResearchPapers } from "../hooks/use-research-papers";
import ResearchPaperList from "@/components/research/research-paper-list";
import { KnowledgeBaseExplorer } from "@/components/research/knowledge-base-explorer";
import { Separator } from "@/components/ui/separator";
import { createBreadcrumb } from "@/components/ui/breadcrumb";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { BookOpen, TrendingUp, Globe, Users, Search, Filter, Package, ChevronLeft, ChevronRight, Database } from "lucide-react";
import { ResearchEntrySkeleton } from "@/components/ui/skeleton-loader";

export default function ResearchPage() {
  // Tab state
  const [activeTab, setActiveTab] = useState<'knowledge-base' | 'research-papers'>('knowledge-base');

  // Fetch research papers
  const { data: papers, isLoading } = useResearchPapers();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedType, setSelectedType] = useState<string | null>(null);
  const [selectedYear, setSelectedYear] = useState<string | null>(null);
  const [selectedLetter, setSelectedLetter] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(12);

  // Generate alphabet array for filtering
  const alphabet = Array.from({ length: 26 }, (_, i) => String.fromCharCode(65 + i));

  // Filter and paginate research papers
  const { filteredPapers, totalPages, paginatedPapers, paperTypes, availableYears } = useMemo(() => {
    if (!papers) return {
      filteredPapers: [],
      totalPages: 0,
      paginatedPapers: [],
      paperTypes: {},
      availableYears: []
    };

    // Get available paper types and years
    const paperTypes = papers.reduce((acc, paper) => {
      const type = paper.entryType || 'Unknown';
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const availableYears = [...new Set(papers.map(paper => {
      if (paper.publicationDate) {
        return new Date(paper.publicationDate).getFullYear().toString();
      }
      return null;
    }).filter(Boolean))].sort((a, b) => parseInt(b!) - parseInt(a!)) as string[];

    let filtered = papers.filter(paper => {
      const matchesSearch = paper.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           paper.abstractSummary?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           paper.authorsOrAssignees?.some(author =>
                             author.toLowerCase().includes(searchTerm.toLowerCase())
                           );
      const matchesType = !selectedType || paper.entryType === selectedType;
      const matchesYear = !selectedYear || (paper.publicationDate &&
                         new Date(paper.publicationDate).getFullYear().toString() === selectedYear);
      const matchesLetter = !selectedLetter || paper.title.charAt(0).toUpperCase() === selectedLetter;
      return matchesSearch && matchesType && matchesYear && matchesLetter;
    });

    // Sort by publication date (newest first) or title
    filtered.sort((a, b) => {
      if (a.publicationDate && b.publicationDate) {
        return new Date(b.publicationDate).getTime() - new Date(a.publicationDate).getTime();
      }
      return a.title.localeCompare(b.title);
    });

    const totalPages = Math.ceil(filtered.length / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const paginatedPapers = filtered.slice(startIndex, startIndex + itemsPerPage);

    return { filteredPapers: filtered, totalPages, paginatedPapers, paperTypes, availableYears };
  }, [papers, searchTerm, selectedType, selectedYear, selectedLetter, currentPage, itemsPerPage]);

  // Calculate stats
  const totalPapers = papers?.length || 0;
  const totalCitations = papers?.reduce((sum, paper) => sum + (paper.citations || 0), 0) || 0;

  // Reset page when filters change
  const handleFilterChange = (filterType: string, value: any) => {
    setCurrentPage(1);
    if (filterType === 'search') setSearchTerm(value);
    if (filterType === 'type') setSelectedType(value);
    if (filterType === 'year') setSelectedYear(value);
    if (filterType === 'letter') setSelectedLetter(value);
  };

  // Pagination handlers
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleItemsPerPageChange = (value: string) => {
    setItemsPerPage(parseInt(value));
    setCurrentPage(1);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-green-900/20">
      <Helmet>
        <title>Research Papers | HempQuarterz</title>
        <meta
          name="description"
          content="Explore peer-reviewed research papers on industrial hemp applications, cultivation methods, and potential benefits across various industries."
        />
      </Helmet>

      <div className="container mx-auto px-4 py-12">
        <div className="max-w-7xl mx-auto">
          {/* Breadcrumb */}
          <div className="mb-6">
            {createBreadcrumb([
              { href: "/", label: "Home" },
              { href: "/research", label: "Research", isCurrent: true },
            ])}
          </div>

          {/* Hero Section */}
          <div className="text-center mb-12">
            <div className="inline-flex items-center gap-2 bg-green-500/10 border border-green-500/20 rounded-full px-4 py-2 mb-6">
              <BookOpen className="h-4 w-4 text-green-400" />
              <span className="text-sm text-green-400 font-medium">Research Repository</span>
            </div>

            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6">
              <span className="hemp-brand-secondary">Hemp</span> Research
              <span className="block text-green-400 drop-shadow-[0_0_8px_rgba(74,222,128,0.3)]">
                Repository
              </span>
            </h1>

            <p className="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
              Discover cutting-edge research on industrial hemp applications, cultivation methods,
              and breakthrough innovations shaping the future of sustainable industries.
            </p>
          </div>

          {/* Tab Navigation */}
          <div className="flex justify-center mb-8">
            <div className="bg-gray-900/40 backdrop-blur-sm rounded-xl p-2 border border-gray-700/50">
              <div className="flex gap-2">
                <button
                  onClick={() => setActiveTab('knowledge-base')}
                  className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 flex items-center gap-2 ${
                    activeTab === 'knowledge-base'
                      ? 'bg-purple-500 text-white shadow-lg'
                      : 'text-gray-400 hover:text-white hover:bg-gray-800/50'
                  }`}
                >
                  <BookOpen className="h-4 w-4" />
                  Knowledge Base
                </button>
                <button
                  onClick={() => setActiveTab('research-papers')}
                  className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 flex items-center gap-2 ${
                    activeTab === 'research-papers'
                      ? 'bg-purple-500 text-white shadow-lg'
                      : 'text-gray-400 hover:text-white hover:bg-gray-800/50'
                  }`}
                >
                  <Database className="h-4 w-4" />
                  Research Papers
                </button>
              </div>
            </div>
          </div>

          {/* Tab Content */}
          {activeTab === 'knowledge-base' ? (
            <KnowledgeBaseExplorer />
          ) : (
            <>
              {/* Stats Cards */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
            <Card className="bg-gray-900/40 backdrop-blur-sm border-gray-800/50 hover:border-green-500/30 transition-colors">
              <CardContent className="p-6 text-center">
                <div className="text-3xl font-bold text-green-400 mb-2">{totalPapers}</div>
                <div className="text-sm text-gray-400">Research Papers</div>
              </CardContent>
            </Card>

            <Card className="bg-gray-900/40 backdrop-blur-sm border-gray-800/50 hover:border-blue-500/30 transition-colors">
              <CardContent className="p-6 text-center">
                <div className="text-3xl font-bold text-blue-400 mb-2">{Object.keys(paperTypes).length}</div>
                <div className="text-sm text-gray-400">Research Types</div>
              </CardContent>
            </Card>

            <Card className="bg-gray-900/40 backdrop-blur-sm border-gray-800/50 hover:border-purple-500/30 transition-colors">
              <CardContent className="p-6 text-center">
                <div className="text-3xl font-bold text-purple-400 mb-2">{totalCitations}</div>
                <div className="text-sm text-gray-400">Total Citations</div>
              </CardContent>
            </Card>

            <Card className="bg-gray-900/40 backdrop-blur-sm border-gray-800/50 hover:border-orange-500/30 transition-colors">
              <CardContent className="p-6 text-center">
                <div className="text-3xl font-bold text-orange-400 mb-2">
                  {papers?.filter(p => p.fullTextUrl).length || 0}
                </div>
                <div className="text-sm text-gray-400">Open Access</div>
              </CardContent>
            </Card>
          </div>

          {/* Research Types */}
          {Object.keys(paperTypes).length > 0 && (
            <div className="mb-8">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-green-400" />
                Research Categories
              </h3>
              <div className="flex flex-wrap gap-2">
                {Object.entries(paperTypes).map(([type, count]) => (
                  <Badge
                    key={type}
                    variant="outline"
                    className="bg-gray-900/40 text-gray-300 border-gray-700/50 hover:border-green-500/50 transition-colors"
                  >
                    {type} ({count})
                  </Badge>
                ))}
              </div>
            </div>
          )}

          <Separator className="my-8 bg-gray-800/50" />

          {/* Search Bar - Full Width */}
          <div className="bg-gray-900/40 backdrop-blur-sm rounded-xl p-6 mb-6 border border-green-500/30">
            <div className="space-y-4">
              {/* Search Input - Full Width */}
              <div className="relative">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <Input
                  type="text"
                  placeholder="Search research papers by title, abstract, or authors..."
                  value={searchTerm}
                  onChange={(e) => handleFilterChange('search', e.target.value)}
                  className="w-full pl-12 pr-4 py-3 bg-gray-800/60 backdrop-blur-sm border border-gray-700/50 rounded-lg text-gray-100 placeholder-gray-400 focus:outline-none focus:border-green-500/50 focus:ring-2 focus:ring-green-500/20 text-base"
                />
                {searchTerm && (
                  <button
                    onClick={() => handleFilterChange('search', '')}
                    className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300"
                  >
                    ×
                  </button>
                )}
              </div>

              {/* Filters - Separate Row */}
              <div className="flex flex-col sm:flex-row sm:items-center gap-3">
                <div className="flex items-center gap-2">
                  <Filter className="h-5 w-5 text-gray-400" />
                  <span className="text-gray-300 text-sm font-medium">Filters:</span>
                </div>
                <div className="flex flex-wrap gap-2">
                  <Select value={selectedType || "all"} onValueChange={(value) => handleFilterChange('type', value === "all" ? null : value)}>
                    <SelectTrigger className="w-40 bg-gray-800/60 border-gray-700/50 text-gray-100">
                      <SelectValue placeholder="All Types" />
                    </SelectTrigger>
                    <SelectContent className="bg-gray-800 border-gray-700">
                      <SelectItem value="all">All Types</SelectItem>
                      {Object.keys(paperTypes).map(type => (
                        <SelectItem key={type} value={type}>{type}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Select value={selectedYear || "all"} onValueChange={(value) => handleFilterChange('year', value === "all" ? null : value)}>
                    <SelectTrigger className="w-32 bg-gray-800/60 border-gray-700/50 text-gray-100">
                      <SelectValue placeholder="All Years" />
                    </SelectTrigger>
                    <SelectContent className="bg-gray-800 border-gray-700">
                      <SelectItem value="all">All Years</SelectItem>
                      {availableYears.map(year => (
                        <SelectItem key={year} value={year}>{year}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </div>

          {/* Alphabetical Filter - Modern & Compact */}
          <div className="bg-gray-900/30 backdrop-blur-md rounded-2xl p-4 mb-6 border border-gray-700/30">
            <div className="flex items-center gap-6">
              {/* Label */}
              <div className="flex items-center gap-2 flex-shrink-0">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span className="text-gray-300 font-medium text-sm">A-Z</span>
              </div>

              {/* Centered Letter Pills Container */}
              <div className="flex-1 flex items-center justify-center">
                <div className="flex items-center gap-1 flex-wrap justify-center max-w-4xl">
                  {/* All Button */}
                  <button
                    onClick={() => handleFilterChange('letter', null)}
                    className={`px-3 py-1.5 rounded-full text-xs font-medium transition-all duration-200 ${
                      selectedLetter === null
                        ? 'bg-green-500 text-white shadow-lg shadow-green-500/25'
                        : 'bg-gray-800/50 text-gray-400 hover:bg-gray-700/50 hover:text-gray-300'
                    }`}
                  >
                    All
                  </button>

                  {/* Letter Pills */}
                  {alphabet.map(letter => (
                    <button
                      key={letter}
                      onClick={() => handleFilterChange('letter', letter)}
                      className={`w-7 h-7 rounded-full text-xs font-medium transition-all duration-200 flex items-center justify-center ${
                        selectedLetter === letter
                          ? 'bg-green-500 text-white shadow-lg shadow-green-500/25 scale-110'
                          : 'bg-gray-800/40 text-gray-400 hover:bg-gray-700/50 hover:text-gray-300 hover:scale-105'
                      }`}
                    >
                      {letter}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Results Summary and View Controls */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
            <div className="text-gray-400 text-sm">
              Showing {paginatedPapers.length} of {filteredPapers.length} research papers
            </div>

            <div className="flex items-center gap-2">
              <span className="text-gray-400 text-sm">Items per page:</span>
              <Select value={itemsPerPage.toString()} onValueChange={handleItemsPerPageChange}>
                <SelectTrigger className="w-20 bg-gray-800/60 border-gray-700/50 text-gray-100">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-700">
                  <SelectItem value="6">6</SelectItem>
                  <SelectItem value="12">12</SelectItem>
                  <SelectItem value="24">24</SelectItem>
                  <SelectItem value="48">48</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Research Papers List */}
          <div className="mb-10">
            <div className="flex items-center gap-2 mb-8">
              <Globe className="h-6 w-6 text-green-400" />
              <h2 className="text-2xl font-heading font-bold text-white">
                Research Papers
              </h2>
            </div>

            {paginatedPapers && paginatedPapers.length > 0 ? (
              <>
                <ResearchPaperList
                  papers={paginatedPapers}
                  isLoading={isLoading}
                  emptyMessage="No research papers found matching your search criteria."
                />

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="flex flex-col sm:flex-row justify-between items-center gap-4 mt-8 pt-6 border-t border-gray-800/50">
                    <div className="text-gray-400 text-sm">
                      Page {currentPage} of {totalPages}
                    </div>

                    <div className="flex items-center gap-2">
                      {/* Previous Button */}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={currentPage === 1}
                        className="bg-gray-800/60 border-gray-700/50 text-gray-300 hover:bg-gray-700/60 disabled:opacity-50"
                      >
                        <ChevronLeft className="h-4 w-4" />
                      </Button>

                      {/* Page Numbers */}
                      <div className="flex gap-1">
                        {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                          let pageNum;
                          if (totalPages <= 5) {
                            pageNum = i + 1;
                          } else if (currentPage <= 3) {
                            pageNum = i + 1;
                          } else if (currentPage >= totalPages - 2) {
                            pageNum = totalPages - 4 + i;
                          } else {
                            pageNum = currentPage - 2 + i;
                          }

                          return (
                            <Button
                              key={pageNum}
                              variant={currentPage === pageNum ? "default" : "outline"}
                              size="sm"
                              onClick={() => handlePageChange(pageNum)}
                              className={`w-10 ${
                                currentPage === pageNum
                                  ? 'bg-green-500 text-white'
                                  : 'bg-gray-800/60 border-gray-700/50 text-gray-300 hover:bg-gray-700/60'
                              }`}
                            >
                              {pageNum}
                            </Button>
                          );
                        })}
                      </div>

                      {/* Next Button */}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={currentPage === totalPages}
                        className="bg-gray-800/60 border-gray-700/50 text-gray-300 hover:bg-gray-700/60 disabled:opacity-50"
                      >
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                )}
              </>
            ) : (
              <div className="text-center py-12 border border-green-800/30 rounded-md bg-black/50">
                <p className="text-green-400 text-xl">
                  No research papers found matching your search criteria.
                </p>
              </div>
            )}
          </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
}
