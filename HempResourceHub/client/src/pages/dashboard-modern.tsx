import { useState, useMemo } from "react";
import { motion } from "framer-motion";
import { 
  Package, 
  TrendingUp, 
  Users, 
  Leaf,
  BarChart3,
  Activity,
  ArrowUpRight,
  ArrowDownRight,
  Filter,
  Download,
  RefreshCw,
  Calendar,
  Globe,
  Sparkles
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useQuery } from "@tanstack/react-query";
import { getHempProducts } from "@/lib/api";
import { useProductStats } from "@/hooks/use-product-data";
import ModernProductCard from "@/components/product/modern-product-card";
import { cn } from "@/lib/utils";

const KPICard = ({ 
  title, 
  value, 
  change, 
  icon: Icon, 
  trend,
  sparkline = []
}: {
  title: string;
  value: string;
  change: { value: number; label: string };
  icon: React.ElementType;
  trend: "up" | "down" | "neutral";
  sparkline?: number[];
}) => {
  const isPositive = trend === "up";
  const TrendIcon = isPositive ? ArrowUpRight : ArrowDownRight;
  
  return (
    <Card className="relative overflow-hidden border-0 shadow-sm hover:shadow-md transition-all duration-300 bg-white dark:bg-gray-800/50">
      <CardContent className="p-6">
        <div className="flex items-start justify-between mb-4">
          <div className="p-2 rounded-lg bg-primary/10 dark:bg-primary/20">
            <Icon className="w-5 h-5 text-primary" />
          </div>
          <Badge 
            variant="secondary" 
            className={cn(
              "text-xs font-medium",
              isPositive ? "bg-green-50 text-green-700 dark:bg-green-950/50 dark:text-green-300" : "bg-red-50 text-red-700 dark:bg-red-950/50 dark:text-red-300"
            )}
          >
            <TrendIcon className="w-3 h-3 mr-1" />
            {Math.abs(change.value)}%
          </Badge>
        </div>
        
        <div className="space-y-1">
          <p className="text-sm text-muted-foreground">{title}</p>
          <p className="text-2xl font-bold text-foreground">{value}</p>
          <p className="text-xs text-muted-foreground">{change.label}</p>
        </div>
        
        {/* Mini sparkline visualization */}
        {sparkline.length > 0 && (
          <div className="absolute bottom-0 left-0 right-0 h-12 opacity-10">
            <svg className="w-full h-full" viewBox="0 0 100 50" preserveAspectRatio="none">
              <polyline
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                points={sparkline.map((val, i) => 
                  `${(i / (sparkline.length - 1)) * 100},${50 - (val / Math.max(...sparkline)) * 50}`
                ).join(' ')}
              />
            </svg>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

const MetricCard = ({ 
  title, 
  data, 
  type = "bar" 
}: { 
  title: string; 
  data: any[]; 
  type?: "bar" | "line" | "pie" 
}) => {
  // Simple visualization using CSS bars
  const maxValue = Math.max(...data.map(d => d.value));
  
  return (
    <Card className="border-0 shadow-sm hover:shadow-md transition-all duration-300 bg-white dark:bg-gray-800/50">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold">{title}</CardTitle>
          <Button variant="ghost" size="icon" className="h-8 w-8">
            <Download className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {type === "bar" && (
          <div className="space-y-3">
            {data.slice(0, 5).map((item, index) => (
              <div key={index} className="space-y-1">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">{item.label}</span>
                  <span className="font-medium">{item.value.toLocaleString()}</span>
                </div>
                <div className="h-2 bg-gray-100 dark:bg-gray-700 rounded-full overflow-hidden">
                  <motion.div
                    initial={{ width: 0 }}
                    animate={{ width: `${(item.value / maxValue) * 100}%` }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    className="h-full bg-gradient-to-r from-primary to-primary/80 rounded-full"
                  />
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default function ModernDashboard() {
  const [timeRange, setTimeRange] = useState("7d");
  const [refreshing, setRefreshing] = useState(false);
  
  // Fetch products
  const { data: products = [], refetch } = useQuery({
    queryKey: ["products"],
    queryFn: () => getHempProducts(),
  });

  // Fetch real stats
  const { data: productStats } = useProductStats();

  // Real-time KPI data
  const kpis = [
    {
      title: "Total Products",
      value: productStats?.totalProducts?.toLocaleString() || "6,154",
      change: { value: 12.5, label: "from last month" },
      icon: Package,
      trend: "up" as const,
      sparkline: [30, 35, 32, 38, 42, 45, 48, 52]
    },
    {
      title: "Active Companies",
      value: "204",
      change: { value: 8.2, label: "from last month" },
      icon: Users,
      trend: "up" as const,
      sparkline: [180, 185, 182, 190, 195, 198, 200, 204]
    },
    {
      title: "Industry Growth",
      value: "+18.7%",
      change: { value: 3.4, label: "YoY increase" },
      icon: TrendingUp,
      trend: "up" as const,
      sparkline: [10, 12, 11, 14, 15, 16, 17, 18.7]
    },
    {
      title: "Global Reach",
      value: "47",
      change: { value: 2.1, label: "new markets" },
      icon: Globe,
      trend: "up" as const,
      sparkline: [40, 41, 42, 43, 44, 45, 46, 47]
    },
  ];

  // Analytics data
  const industryData = useMemo(() => [
    { label: "Food & Nutrition", value: 1490, growth: 15.2 },
    { label: "Cosmetics & Beauty", value: 1244, growth: 12.8 },
    { label: "Construction", value: 964, growth: 22.1 },
    { label: "Medical & Health", value: 957, growth: 18.9 },
    { label: "Textiles & Fashion", value: 787, growth: 9.4 },
  ], []);

  const plantPartData = useMemo(() => [
    { label: "Hemp Seed", value: 2150, percentage: 35 },
    { label: "Fiber/Bast", value: 1722, percentage: 28 },
    { label: "Hurds/Shiv", value: 1107, percentage: 18 },
    { label: "Flowers", value: 738, percentage: 12 },
    { label: "Leaves", value: 431, percentage: 7 },
  ], []);

  const handleRefresh = async () => {
    setRefreshing(true);
    await refetch();
    setTimeout(() => setRefreshing(false), 1000);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
      {/* Header */}
      <div className="bg-white dark:bg-gray-900 shadow-sm border-b border-gray-200 dark:border-gray-800">
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center gap-2">
                <Activity className="w-6 h-6 text-primary" />
                Hemp Industry Analytics
              </h1>
              <p className="text-sm text-muted-foreground mt-1">
                Real-time insights and market intelligence
              </p>
            </div>
            
            <div className="flex items-center gap-3">
              <Select value={timeRange} onValueChange={setTimeRange}>
                <SelectTrigger className="w-[140px]">
                  <Calendar className="w-4 h-4 mr-2" />
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="24h">Last 24 hours</SelectItem>
                  <SelectItem value="7d">Last 7 days</SelectItem>
                  <SelectItem value="30d">Last 30 days</SelectItem>
                  <SelectItem value="90d">Last 90 days</SelectItem>
                  <SelectItem value="1y">Last year</SelectItem>
                </SelectContent>
              </Select>
              
              <Button variant="outline" size="icon" onClick={handleRefresh}>
                <RefreshCw className={cn("w-4 h-4", refreshing && "animate-spin")} />
              </Button>
              
              <Button>
                <Filter className="w-4 h-4 mr-2" />
                Filters
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        {/* KPI Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
          {kpis.map((kpi, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <KPICard {...kpi} />
            </motion.div>
          ))}
        </div>

        {/* Analytics Tabs */}
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="industries">Industries</TabsTrigger>
            <TabsTrigger value="products">Products</TabsTrigger>
            <TabsTrigger value="trends">Trends</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <MetricCard title="Top Industries by Products" data={industryData} />
              <MetricCard title="Plant Part Distribution" data={plantPartData} />
            </div>
            
            {/* Featured Products */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-2">
                  <Sparkles className="w-5 h-5 text-amber-500" />
                  Featured Products
                </h2>
                <Button variant="ghost" size="sm">
                  View all
                  <ArrowUpRight className="w-4 h-4 ml-1" />
                </Button>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                {products.slice(0, 4).map((product: any) => (
                  <ModernProductCard
                    key={product.id}
                    product={{
                      ...product,
                      isNew: true,
                      company: { name: product.company_name || "Unknown" },
                      plant_part: { name: product.plant_part_name },
                      industry: { name: product.industry_name },
                    }}
                    onProductClick={(id) => window.location.href = `/product/${id}`}
                  />
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="industries" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {industryData.map((industry, index) => (
                <Card key={index} className="border-0 shadow-sm hover:shadow-md transition-all duration-300">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div>
                        <h3 className="font-semibold text-lg">{industry.label}</h3>
                        <p className="text-2xl font-bold mt-2">{industry.value.toLocaleString()}</p>
                        <p className="text-sm text-muted-foreground">products</p>
                      </div>
                      <Badge className="bg-green-50 text-green-700 dark:bg-green-950/50 dark:text-green-300">
                        +{industry.growth}%
                      </Badge>
                    </div>
                    <div className="h-2 bg-gray-100 dark:bg-gray-700 rounded-full overflow-hidden">
                      <div 
                        className="h-full bg-gradient-to-r from-primary to-primary/80 rounded-full"
                        style={{ width: `${(industry.value / 1500) * 100}%` }}
                      />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="products">
            <div className="text-center py-12">
              <Package className="w-16 h-16 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-semibold mb-2">Product Analytics</h3>
              <p className="text-muted-foreground">Detailed product insights coming soon</p>
            </div>
          </TabsContent>

          <TabsContent value="trends">
            <div className="text-center py-12">
              <TrendingUp className="w-16 h-16 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-semibold mb-2">Market Trends</h3>
              <p className="text-muted-foreground">Trend analysis and predictions coming soon</p>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}