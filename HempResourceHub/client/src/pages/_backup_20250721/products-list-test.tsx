import { Helmet } from "react-helmet";
import { useAllHempProducts } from "@/hooks/use-product-data";
import { ArrowLeft } from "lucide-react";
import { Link } from "wouter";

const ProductsListTestPage = () => {
  const { data: products, isLoading } = useAllHempProducts();

  return (
    <>
      <Helmet>
        <title>Products List Test - HempQuarterz® Database</title>
      </Helmet>

      <div className="min-h-screen bg-black">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Breadcrumb */}
          <div className="mb-6">
            <Link href="/products">
              <a className="inline-flex items-center text-gray-400 hover:text-white transition-colors">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Products
              </a>
            </Link>
          </div>

          <h1 className="text-3xl font-bold text-white mb-6">Products List Test</h1>

          {isLoading ? (
            <div className="text-white">Loading products...</div>
          ) : (
            <div className="space-y-2">
              <p className="text-gray-400 mb-4">Found {products?.length || 0} products</p>
              {products?.slice(0, 10).map((product, index) => (
                <div key={product.id} className="bg-gray-800 p-4 rounded-lg text-white">
                  <div className="flex items-center gap-4">
                    <span className="text-gray-500">#{String(index + 1).padStart(4, '0')}</span>
                    <span className="font-medium">{product.name}</span>
                    <span className="text-sm text-gray-400">ID: {product.id}</span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default ProductsListTestPage;