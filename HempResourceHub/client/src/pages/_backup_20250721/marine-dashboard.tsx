import { useState } from "react";
import { DashboardLayout } from "@/components/layout/DashboardLayout";
import { MarineKPICard } from "@/components/ui/marine-kpi-card";
import { DetectionCard } from "@/components/ui/detection-card";
import { ComplexityChart } from "@/components/charts/ComplexityChart";
import { ProcessingTimeChart } from "@/components/charts/ProcessingTimeChart";
import { InsightSummary } from "@/components/ui/insight-summary";
import { FilterState } from "@/components/layout/FiltersDrawer";

export default function MarineDashboard() {
  const [filters, setFilters] = useState<FilterState>();

  // KPI data matching screenshot
  const kpis = [
    {
      algorithm: "Hough Transform Detections",
      avgTime: "0.277s",
      efficiency: "125.4 det/s",
      detections: "55",
    },
    {
      algorithm: "Contour Analysis Detections", 
      avgTime: "0.271s",
      efficiency: "65.3 det/s",
      detections: "43",
    },
    {
      algorithm: "Symmetry Detection",
      avgTime: "0.272s",
      efficiency: "13.5 det/s",
      detections: "11",
    },
  ];

  // Detection data for grid
  const detections = [
    {
      algorithm: "Hough Transform",
      detections: "55",
      confidence: "50.0%",
      coordinates: "41.681, -50.133",
      depth: "-3247.6m",
      dataset: "EMONET",
    },
    {
      algorithm: "Contour Analysis",
      detections: "43", 
      confidence: "87.1%",
      coordinates: "41.862, -58.929",
      depth: "-2765.5m",
      dataset: "GEBCO",
    },
    {
      algorithm: "Symmetry Detection",
      detections: "11",
      confidence: "100.0%",
      coordinates: "37.495, -53.393",
      depth: "-2437.1m", 
      dataset: "GEBCO",
    },
    {
      algorithm: "Hough Transform",
      detections: "55",
      confidence: "50.0%",
      coordinates: "41.681, -50.116",
      depth: "-3247.6m",
      dataset: "EMONET",
    },
    {
      algorithm: "Contour Analysis",
      detections: "43",
      confidence: "87.1%", 
      coordinates: "43.733, -58.929",
      depth: "-2765.5m",
      dataset: "GEBCO",
    },
    {
      algorithm: "Symmetry Detection",
      detections: "11",
      confidence: "100.0%",
      coordinates: "37.495, -47.857",
      depth: "-2437.1m",
      dataset: "GEBCO",
    },
  ];

  const insights = {
    keyFindings: [
      "Contour analysis shows the best balance of speed and detection count",
      "Symmetry detection has highest confidence but lowest detection rate",
      "GEBCO dataset yields more consistent results than EMONET",
      "Detection density increases at depths between -2000m and -3000m",
    ],
    recommendations: [
      "Use contour analysis for initial broad-area scanning",
      "Apply symmetry detection for high-confidence validation",
      "Focus processing resources on mid-depth ranges",
      "Consider ensemble approach combining multiple algorithms",
    ],
  };

  return (
    <DashboardLayout showFilters onFiltersChange={setFilters}>
      <div className="min-h-screen bg-marine-bg">
        {/* KPI Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          {kpis.map((kpi, index) => (
            <MarineKPICard key={index} {...kpi} />
          ))}
        </div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          <div className="h-[400px] rounded-xl bg-marine-card p-6">
            <ComplexityChart />
          </div>
          <div className="h-[400px] rounded-xl bg-marine-card p-6">
            <ProcessingTimeChart />
          </div>
        </div>

        {/* Performance Analysis Summary */}
        <div className="mb-6">
          <InsightSummary
            keyFindings={insights.keyFindings}
            recommendations={insights.recommendations}
            className="bg-marine-card border-marine-border"
          />
        </div>

        {/* Detection Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {detections.map((detection, index) => (
            <DetectionCard
              key={index}
              {...detection}
              onView={() => console.log("View", index)}
              onDownload={() => console.log("Download", index)}
            />
          ))}
        </div>
      </div>
    </DashboardLayout>
  );
}