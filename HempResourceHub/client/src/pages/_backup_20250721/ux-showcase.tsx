import React, { useState } from "react";
import { He<PERSON>et } from "react-helmet";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  Sparkles, 
  Search, 
  Filter, 
  BarChart3, 
  Navigation,
  Image as ImageIcon,
  Heart,
  Bookmark,
  Share2,
  Leaf,
  Factory,
  Package
} from "lucide-react";

// Import our new components
import { AdvancedFilterPanel } from "@/components/ui/advanced-filter-panel";
import { SmartSearch } from "@/components/ui/smart-search";
import { DataVisualizationDashboard } from "@/components/ui/data-visualization-dashboard";
import { EnhancedBreadcrumbs } from "@/components/ui/enhanced-breadcrumbs";
import { InteractiveProductCard } from "@/components/product/interactive-product-card";
import { OptimizedImage } from "@/components/ui/optimized-image";
import { TestStyling } from "@/components/test-styling";

// Mock data for demonstrations
const mockProduct = {
  id: 1,
  name: "Hemp Fiber Insulation",
  description: "Sustainable building insulation made from hemp fibers, providing excellent thermal properties while being environmentally friendly.",
  image_url: "/images/hemp-insulation.jpg",
  industryId: 1,
  subIndustryId: 2,
  plantPartId: 1,
  commercialization_stage: "Commercial",
  benefits_advantages: [
    "Carbon negative material",
    "Excellent thermal insulation",
    "Naturally pest resistant",
    "Breathable and moisture regulating"
  ],
  environmental_impact: "Positive - sequesters carbon during growth"
};

const mockFilterSections = [
  {
    id: 'search',
    title: 'Quick Search',
    icon: <Search className="h-4 w-4" />,
    type: 'search' as const,
    placeholder: 'Search products...'
  },
  {
    id: 'plantParts',
    title: 'Plant Parts',
    icon: <Leaf className="h-4 w-4" />,
    type: 'checkbox' as const,
    options: [
      { id: 1, label: 'Fiber/Bast', count: 45 },
      { id: 2, label: 'Seeds', count: 32 },
      { id: 3, label: 'Leaves', count: 18 },
      { id: 4, label: 'Hurds/Shiv', count: 28 },
      { id: 5, label: 'Flowers', count: 15 }
    ]
  },
  {
    id: 'industries',
    title: 'Industries',
    icon: <Factory className="h-4 w-4" />,
    type: 'checkbox' as const,
    options: [
      { id: 1, label: 'Construction', count: 38 },
      { id: 2, label: 'Textiles', count: 42 },
      { id: 3, label: 'Food & Nutrition', count: 29 },
      { id: 4, label: 'Automotive', count: 16 },
      { id: 5, label: 'Cosmetics', count: 21 }
    ]
  },
  {
    id: 'sustainability',
    title: 'Sustainability Score',
    icon: <Sparkles className="h-4 w-4" />,
    type: 'range' as const,
    min: 1,
    max: 5,
    step: 0.5
  },
  {
    id: 'commercial',
    title: 'Commercial Ready',
    icon: <Package className="h-4 w-4" />,
    type: 'toggle' as const
  }
];

const mockBreadcrumbs = [
  {
    label: "Products",
    href: "/products",
    icon: <Package className="h-4 w-4" />,
    metadata: { count: 219 }
  },
  {
    label: "Construction",
    href: "/products/construction",
    icon: <Factory className="h-4 w-4" />,
    metadata: { count: 38, type: "Industry" }
  },
  {
    label: "Insulation Materials",
    icon: <Package className="h-4 w-4" />,
    metadata: { count: 12, type: "Category" }
  }
];

export default function UXShowcase() {
  const [filters, setFilters] = useState({});
  const [favorites, setFavorites] = useState<number[]>([]);
  const [bookmarks, setBookmarks] = useState<number[]>([]);

  const handleFiltersChange = (newFilters: Record<string, any>) => {
    setFilters(newFilters);
    console.log('Filters updated:', newFilters);
  };

  const handleFilterReset = () => {
    setFilters({});
    console.log('Filters reset');
  };

  const handleSearch = (query: string) => {
    console.log('Search query:', query);
  };

  const handleFavorite = (productId: number) => {
    setFavorites(prev => 
      prev.includes(productId) 
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    );
  };

  const handleBookmark = (productId: number) => {
    setBookmarks(prev => 
      prev.includes(productId) 
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    );
  };

  const handleShare = (product: any) => {
    if (navigator.share) {
      navigator.share({
        title: product.name,
        text: product.description,
        url: window.location.href
      });
    } else {
      // Fallback to clipboard
      navigator.clipboard.writeText(window.location.href);
      alert('Link copied to clipboard!');
    }
  };

  return (
    <>
      <Helmet>
        <title>UX Showcase - Enhanced Hemp Database Features</title>
        <meta
          name="description"
          content="Explore the enhanced user experience features of the Hemp Database including advanced filtering, smart search, and interactive components."
        />
      </Helmet>

      <div className="container mx-auto px-4 py-8 space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold text-white">
            <span className="bg-gradient-to-r from-green-400 to-emerald-400 bg-clip-text text-transparent">
              Enhanced UX Features
            </span>
          </h1>
          <p className="text-lg text-gray-300 max-w-2xl mx-auto">
            Discover the new user experience enhancements that make exploring hemp data more intuitive, 
            interactive, and insightful.
          </p>
        </div>

        {/* Enhanced Breadcrumbs Demo */}
        <Card className="bg-gray-900/40 backdrop-blur-sm border-gray-800">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-white">
              <Navigation className="h-5 w-5 text-blue-400" />
              Enhanced Navigation & Breadcrumbs
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-gray-300">
              Context-aware breadcrumbs with metadata, counts, and smart navigation.
            </p>
            <div className="bg-gray-800/50 p-4 rounded-lg">
              <EnhancedBreadcrumbs items={mockBreadcrumbs} />
            </div>
          </CardContent>
        </Card>

        {/* Main Features Tabs */}
        <Tabs defaultValue="styling" className="space-y-6">
          <TabsList className="grid w-full grid-cols-6 bg-gray-900/50">
            <TabsTrigger value="styling" className="flex items-center gap-2">
              <Sparkles className="h-4 w-4" />
              Styling Test
            </TabsTrigger>
            <TabsTrigger value="search" className="flex items-center gap-2">
              <Search className="h-4 w-4" />
              Smart Search
            </TabsTrigger>
            <TabsTrigger value="filters" className="flex items-center gap-2">
              <Filter className="h-4 w-4" />
              Advanced Filters
            </TabsTrigger>
            <TabsTrigger value="cards" className="flex items-center gap-2">
              <Package className="h-4 w-4" />
              Interactive Cards
            </TabsTrigger>
            <TabsTrigger value="analytics" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Data Visualization
            </TabsTrigger>
            <TabsTrigger value="images" className="flex items-center gap-2">
              <ImageIcon className="h-4 w-4" />
              Optimized Images
            </TabsTrigger>
          </TabsList>

          {/* Styling Test Tab */}
          <TabsContent value="styling" className="space-y-6">
            <Card className="bg-gray-900/40 backdrop-blur-sm border-gray-800">
              <CardHeader>
                <CardTitle className="text-white">New Color Scheme Test</CardTitle>
              </CardHeader>
              <CardContent>
                <TestStyling />
              </CardContent>
            </Card>
          </TabsContent>

          {/* Smart Search Tab */}
          <TabsContent value="search" className="space-y-6">
            <Card className="bg-gray-900/40 backdrop-blur-sm border-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-white">
                  <Sparkles className="h-5 w-5 text-purple-400" />
                  AI-Powered Smart Search
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-gray-300">
                  Intelligent search with AI suggestions, voice input, and contextual results.
                </p>
                <SmartSearch 
                  onSearch={handleSearch}
                  showAISuggestions={true}
                  showVoiceSearch={true}
                  showImageSearch={true}
                />
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
                  <div className="bg-gray-800/50 p-4 rounded-lg">
                    <h4 className="font-medium text-green-400 mb-2">AI Intent Detection</h4>
                    <p className="text-sm text-gray-400">
                      Understands user intent and provides contextual suggestions
                    </p>
                  </div>
                  <div className="bg-gray-800/50 p-4 rounded-lg">
                    <h4 className="font-medium text-blue-400 mb-2">Voice Search</h4>
                    <p className="text-sm text-gray-400">
                      Speak your queries naturally with voice recognition
                    </p>
                  </div>
                  <div className="bg-gray-800/50 p-4 rounded-lg">
                    <h4 className="font-medium text-purple-400 mb-2">Smart Suggestions</h4>
                    <p className="text-sm text-gray-400">
                      Real-time suggestions based on your search patterns
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Advanced Filters Tab */}
          <TabsContent value="filters" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-1">
                <AdvancedFilterPanel
                  sections={mockFilterSections}
                  onFiltersChange={handleFiltersChange}
                  onReset={handleFilterReset}
                  showActiveCount={true}
                />
              </div>
              <div className="lg:col-span-2">
                <Card className="bg-gray-900/40 backdrop-blur-sm border-gray-800">
                  <CardHeader>
                    <CardTitle className="text-white">Filter Results</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <p className="text-gray-300">
                        Advanced filtering with collapsible sections, search within filters, 
                        range sliders, and real-time counts.
                      </p>
                      <div className="bg-gray-800/50 p-4 rounded-lg">
                        <h4 className="font-medium text-white mb-2">Current Filters:</h4>
                        <pre className="text-sm text-gray-400 overflow-auto">
                          {JSON.stringify(filters, null, 2)}
                        </pre>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="bg-gray-800/50 p-3 rounded-lg">
                          <div className="text-2xl font-bold text-green-400">127</div>
                          <div className="text-sm text-gray-400">Products Found</div>
                        </div>
                        <div className="bg-gray-800/50 p-3 rounded-lg">
                          <div className="text-2xl font-bold text-blue-400">8</div>
                          <div className="text-sm text-gray-400">Active Filters</div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          {/* Interactive Cards Tab */}
          <TabsContent value="cards" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <InteractiveProductCard
                product={mockProduct}
                industryNames={{ 1: "Construction" }}
                subIndustryNames={{ 2: "Building Materials" }}
                plantPartNames={{ 1: "Fiber/Bast" }}
                variant="default"
                showActions={true}
                showStats={false}
                onFavorite={handleFavorite}
                onShare={handleShare}
                onBookmark={handleBookmark}
                isFavorited={favorites.includes(mockProduct.id)}
                isBookmarked={bookmarks.includes(mockProduct.id)}
              />
              <InteractiveProductCard
                product={{...mockProduct, id: 2, name: "Hemp Seed Oil"}}
                industryNames={{ 1: "Food & Nutrition" }}
                subIndustryNames={{ 2: "Supplements" }}
                plantPartNames={{ 1: "Seeds" }}
                variant="compact"
                showActions={true}
                showStats={false}
                onFavorite={handleFavorite}
                onShare={handleShare}
                onBookmark={handleBookmark}
                isFavorited={favorites.includes(2)}
                isBookmarked={bookmarks.includes(2)}
              />
              <InteractiveProductCard
                product={{...mockProduct, id: 3, name: "Hemp Concrete"}}
                industryNames={{ 1: "Construction" }}
                subIndustryNames={{ 2: "Structural Materials" }}
                plantPartNames={{ 1: "Hurds/Shiv" }}
                variant="featured"
                showActions={true}
                showStats={false}
                onFavorite={handleFavorite}
                onShare={handleShare}
                onBookmark={handleBookmark}
                isFavorited={favorites.includes(3)}
                isBookmarked={bookmarks.includes(3)}
              />
            </div>
            
            <Card className="bg-gray-900/40 backdrop-blur-sm border-gray-800">
              <CardContent className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="bg-gray-800/50 p-4 rounded-lg">
                    <Heart className="h-8 w-8 text-red-400 mb-2" />
                    <h4 className="font-medium text-white">Favorites</h4>
                    <p className="text-sm text-gray-400">Save products you love</p>
                  </div>
                  <div className="bg-gray-800/50 p-4 rounded-lg">
                    <Bookmark className="h-8 w-8 text-blue-400 mb-2" />
                    <h4 className="font-medium text-white">Bookmarks</h4>
                    <p className="text-sm text-gray-400">Quick access to important items</p>
                  </div>
                  <div className="bg-gray-800/50 p-4 rounded-lg">
                    <Share2 className="h-8 w-8 text-green-400 mb-2" />
                    <h4 className="font-medium text-white">Share</h4>
                    <p className="text-sm text-gray-400">Share with colleagues</p>
                  </div>
                  <div className="bg-gray-800/50 p-4 rounded-lg">
                    <Sparkles className="h-8 w-8 text-purple-400 mb-2" />
                    <h4 className="font-medium text-white">Sustainability</h4>
                    <p className="text-sm text-gray-400">Environmental impact scores</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Data Visualization Tab */}
          <TabsContent value="analytics">
            <DataVisualizationDashboard />
          </TabsContent>

          {/* Optimized Images Tab */}
          <TabsContent value="images" className="space-y-6">
            <Card className="bg-gray-900/40 backdrop-blur-sm border-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-white">
                  <ImageIcon className="h-5 w-5 text-green-400" />
                  Optimized Image Loading
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-gray-300">
                  Advanced image optimization with lazy loading, fallbacks, and progressive enhancement.
                </p>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="space-y-2">
                    <h4 className="font-medium text-white">Standard Loading</h4>
                    <OptimizedImage
                      src="/images/hemp-fiber.jpg"
                      alt="Hemp fiber close-up"
                      className="w-full h-48 rounded-lg"
                      priority={false}
                    />
                  </div>
                  <div className="space-y-2">
                    <h4 className="font-medium text-white">With Fallback</h4>
                    <OptimizedImage
                      src="/images/nonexistent.jpg"
                      alt="This will show fallback"
                      className="w-full h-48 rounded-lg"
                      fallbackSrc="/images/hemp-placeholder.jpg"
                    />
                  </div>
                  <div className="space-y-2">
                    <h4 className="font-medium text-white">Priority Loading</h4>
                    <OptimizedImage
                      src="/images/hemp-products.jpg"
                      alt="Hemp products showcase"
                      className="w-full h-48 rounded-lg"
                      priority={true}
                    />
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
                  <div className="bg-gray-800/50 p-4 rounded-lg">
                    <h4 className="font-medium text-green-400 mb-2">Lazy Loading</h4>
                    <p className="text-sm text-gray-400">
                      Images load only when they enter the viewport
                    </p>
                  </div>
                  <div className="bg-gray-800/50 p-4 rounded-lg">
                    <h4 className="font-medium text-blue-400 mb-2">Progressive Enhancement</h4>
                    <p className="text-sm text-gray-400">
                      Blur placeholders and smooth transitions
                    </p>
                  </div>
                  <div className="bg-gray-800/50 p-4 rounded-lg">
                    <h4 className="font-medium text-purple-400 mb-2">Error Handling</h4>
                    <p className="text-sm text-gray-400">
                      Graceful fallbacks when images fail to load
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Implementation Notes */}
        <Card className="bg-gray-900/40 backdrop-blur-sm border-gray-800">
          <CardHeader>
            <CardTitle className="text-white">Implementation Benefits</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h4 className="font-medium text-green-400">User Experience</h4>
                <ul className="space-y-2 text-sm text-gray-300">
                  <li>• Faster perceived loading times</li>
                  <li>• Intuitive search and filtering</li>
                  <li>• Interactive product exploration</li>
                  <li>• Real-time data visualization</li>
                  <li>• Accessible navigation patterns</li>
                </ul>
              </div>
              <div className="space-y-4">
                <h4 className="font-medium text-blue-400">Technical Benefits</h4>
                <ul className="space-y-2 text-sm text-gray-300">
                  <li>• Reduced bandwidth usage</li>
                  <li>• Better Core Web Vitals scores</li>
                  <li>• Improved SEO performance</li>
                  <li>• Enhanced accessibility</li>
                  <li>• Mobile-optimized interactions</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  );
}
