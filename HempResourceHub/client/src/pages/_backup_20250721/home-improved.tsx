import { Helmet } from "react-helmet";
import { <PERSON> } from "wouter";
import { Search, TrendingUp, Package, Leaf, ArrowRight, Globe, Building2, FlaskConical, Database, Users, BarChart3 } from "lucide-react";
import { useState } from "react";
import { useProductStats } from "@/hooks/use-product-data";
import PlantTypeCards from "@/components/home/<USER>";
import FeaturedCompany from "@/components/home/<USER>";

const ImprovedHomePage = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const { data: productStats } = useProductStats();

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      window.location.href = `/products?search=${encodeURIComponent(searchQuery)}`;
    }
  };

  const keyFeatures = [
    {
      icon: Database,
      title: "Comprehensive Database",
      description: "5,196+ hemp products across 42 industries",
      link: "/products",
      color: "from-green-600 to-emerald-600"
    },
    {
      icon: BarChart3,
      title: "Real-Time Analytics",
      description: "Track market trends and industry insights",
      link: "/dashboard",
      color: "from-blue-600 to-cyan-600"
    },
    {
      icon: Users,
      title: "Industry Network",
      description: "Connect with 204+ leading companies",
      link: "/companies",
      color: "from-purple-600 to-pink-600"
    }
  ];

  return (
    <>
      <Helmet>
        <title>HempQuarterz® - The World's Largest Hemp Database</title>
        <meta
          name="description"
          content="Explore 5,196+ industrial hemp products, connect with 204+ companies, and access cutting-edge research. Your gateway to hemp innovation."
        />
      </Helmet>

      {/* Streamlined Hero Section */}
      <section className="relative min-h-[60vh] flex items-center py-16">
        <div className="absolute inset-0 bg-gradient-to-br from-marine-bg via-marine-bg to-green-900/10" />
        <div className="absolute inset-0 bg-[url('/grid.svg')] opacity-5" />
        
        <div className="relative z-10 w-full max-w-6xl mx-auto px-6 text-center">
          {/* Main Heading */}
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight">
            The World's Largest
            <span className="block text-transparent bg-clip-text bg-gradient-to-r from-green-400 to-green-600 mt-2">
              Hemp Database
            </span>
          </h1>
          
          <p className="text-lg md:text-xl text-marine-text-secondary mb-8 max-w-3xl mx-auto">
            Discover innovative hemp applications, connect with industry leaders, and access real-time market insights.
          </p>

          {/* Single Search Bar */}
          <form onSubmit={handleSearch} className="max-w-2xl mx-auto mb-8">
            <div className="relative">
              <Search className="absolute left-4 top-1/2 h-5 w-5 -translate-y-1/2 text-marine-text-muted" />
              <input
                type="search"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search products, companies, or industries..."
                className="w-full rounded-xl bg-marine-card/80 backdrop-blur-sm pl-12 pr-32 py-4 text-base text-white placeholder-marine-text-muted border border-marine-border focus:border-green-500 focus:outline-none focus:ring-2 focus:ring-green-500/20 transition-all"
              />
              <button
                type="submit"
                className="absolute right-2 top-1/2 -translate-y-1/2 px-6 py-2 rounded-lg bg-green-600 text-white font-medium hover:bg-green-700 transition-colors"
              >
                Search
              </button>
            </div>
          </form>

          {/* Quick Stats */}
          <div className="flex flex-wrap justify-center gap-8 text-center">
            <div>
              <p className="text-3xl font-bold text-white">{productStats?.totalProducts || 0}+</p>
              <p className="text-sm text-marine-text-secondary">Products</p>
            </div>
            <div>
              <p className="text-3xl font-bold text-white">204+</p>
              <p className="text-sm text-marine-text-secondary">Companies</p>
            </div>
            <div>
              <p className="text-3xl font-bold text-white">42</p>
              <p className="text-sm text-marine-text-secondary">Industries</p>
            </div>
            <div>
              <p className="text-3xl font-bold text-white">156</p>
              <p className="text-sm text-marine-text-secondary">Research Papers</p>
            </div>
          </div>
        </div>
      </section>

      {/* Key Features Section */}
      <section className="py-16 px-6">
        <div className="max-w-6xl mx-auto">
          <div className="grid md:grid-cols-3 gap-6">
            {keyFeatures.map((feature) => (
              <Link key={feature.title} href={feature.link}>
                <a className="group relative overflow-hidden rounded-2xl bg-marine-card border border-marine-border hover:border-marine-border/80 transition-all hover:scale-[1.02] p-6">
                  <div className={`absolute inset-0 bg-gradient-to-br ${feature.color} opacity-0 group-hover:opacity-10 transition-opacity`} />
                  <feature.icon className="h-10 w-10 text-green-500 mb-4" />
                  <h3 className="text-xl font-semibold text-white mb-2">{feature.title}</h3>
                  <p className="text-marine-text-secondary mb-4">{feature.description}</p>
                  <span className="inline-flex items-center gap-2 text-green-500 font-medium">
                    Explore
                    <ArrowRight className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
                  </span>
                </a>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Plant Types Section - Simplified */}
      <section className="py-16 px-6 bg-marine-card/30">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-10">
            <h2 className="text-3xl font-bold text-white mb-3">
              Explore by Hemp Type
            </h2>
            <p className="text-lg text-marine-text-secondary">
              Each hemp variety offers unique industrial applications
            </p>
          </div>
          <PlantTypeCards />
        </div>
      </section>

      {/* Featured Company - Cleaner */}
      <section className="py-16 px-6">
        <div className="max-w-4xl mx-auto">
          <FeaturedCompany />
        </div>
      </section>

      {/* Single CTA Section */}
      <section className="py-20 px-6">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl font-bold text-white mb-4">
            Ready to Discover Hemp Innovation?
          </h2>
          <p className="text-lg text-marine-text-secondary mb-8">
            Join thousands using HempQuarterz® to explore sustainable solutions
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/products">
              <a className="inline-flex items-center justify-center px-8 py-3 rounded-lg bg-green-600 text-white font-medium hover:bg-green-700 transition-colors">
                Browse All Products
              </a>
            </Link>
            <Link href="/register">
              <a className="inline-flex items-center justify-center px-8 py-3 rounded-lg bg-marine-card text-white font-medium hover:bg-marine-cardHover transition-colors border border-marine-border">
                Create Free Account
              </a>
            </Link>
          </div>
        </div>
      </section>
    </>
  );
};

export default ImprovedHomePage;