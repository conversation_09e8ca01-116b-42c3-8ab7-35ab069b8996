import { Helmet } from "react-helmet";
import DesktopHero from "@/components/home/<USER>";
import ModernHero from "@/components/home/<USER>";
import BentoGridV2 from "@/components/home/<USER>";
import PlantTypeCards from "@/components/home/<USER>";
import FeaturedProducts from "@/components/home/<USER>";
import FeaturedProductSpotlight from "@/components/home/<USER>";
import FeaturedCompany from "@/components/home/<USER>";
import StatsCounter from "@/components/home/<USER>";
import { DataVisualizationDashboard } from "@/components/ui/data-visualization-dashboard";
import { useDeviceType } from "@/hooks/useDeviceType";

const HomePage = () => {
  const { isDesktop, isTablet } = useDeviceType();
  const showDesktopLayout = isDesktop || isTablet;

  return (
    <>
      <Helmet>
        <title>HempQuarterz® - Interactive Industrial Hemp Applications Database</title>
        <meta
          name="description"
          content="Explore the versatile applications of industrial hemp across industries, plant parts, and product categories with our comprehensive interactive database."
        />
        <meta name="keywords" content="hemp database, industrial hemp, hemp products, hemp applications, sustainable materials, hemp industry, hemp research, eco-friendly products, green technology, hemp innovation" />
        <meta property="og:title" content="HempQuarterz® - Interactive Industrial Hemp Applications Database" />
        <meta property="og:description" content="Explore the versatile applications of industrial hemp across industries, plant parts, and product categories with our comprehensive interactive database." />
        <meta property="og:type" content="website" />
        <meta property="og:site_name" content="HempQuarterz Database" />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="HempQuarterz® - Interactive Industrial Hemp Applications Database" />
        <meta name="twitter:description" content="Explore the versatile applications of industrial hemp across industries, plant parts, and product categories." />
        <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover" />
        <meta name="theme-color" content="#22c55e" />
      </Helmet>

      {/* Hero section - Device specific */}
      {showDesktopLayout ? <DesktopHero /> : <ModernHero />}

      {/* Content Sections - Responsive layouts */}
      <div className={showDesktopLayout ? "space-y-16 py-12" : "space-y-10 py-8"}>
        {/* Bento Grid Section */}
        <section className="max-w-7xl mx-auto px-4 md:px-6">
          <div className={showDesktopLayout ? "mb-8" : "mb-6 text-center"}>
            <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-white mb-3">
              Explore Hemp Applications
            </h2>
            <p className="text-base md:text-lg text-gray-400 max-w-2xl">
              Discover how industrial hemp is revolutionizing industries worldwide
            </p>
          </div>
          <BentoGridV2 />
        </section>

        {/* Plant Types Section */}
        <section className="max-w-7xl mx-auto px-4 md:px-6">
          <div className={showDesktopLayout ? "mb-8" : "mb-6 text-center"}>
            <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-white mb-3">
              Hemp Plant Categories
            </h2>
            <p className="text-base md:text-lg text-gray-400 max-w-2xl">
              Each part of the hemp plant offers unique applications and benefits
            </p>
          </div>
          <PlantTypeCards />
        </section>

        {/* Featured Products Grid */}
        <section className="max-w-7xl mx-auto px-4 md:px-6">
          <div className={showDesktopLayout ? "mb-8 flex justify-between items-end" : "mb-6 text-center"}>
            <div>
              <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-white mb-2">
                Featured Products
              </h2>
              <p className="text-base md:text-lg text-gray-400 max-w-2xl">
                Latest innovations in hemp-based products
              </p>
            </div>
            {showDesktopLayout && (
              <a href="/products" className="text-hemp-500 hover:text-hemp-400 font-medium flex items-center gap-2">
                View All Products
                <span>→</span>
              </a>
            )}
          </div>
          <FeaturedProducts />
        </section>

        {/* Desktop-only sections */}
        {showDesktopLayout && (
          <>
            {/* Stats & Analytics */}
            <section className="bg-dark-card py-12 lg:py-16">
              <div className="max-w-7xl mx-auto px-6">
                <div className="grid lg:grid-cols-2 gap-12 items-center">
                  <div>
                    <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-white mb-3">
                      Real-Time Analytics
                    </h2>
                    <p className="text-base md:text-lg text-gray-400 mb-6">
                      Track market trends, growth patterns, and industry insights with our comprehensive analytics dashboard.
                    </p>
                    <StatsCounter />
                  </div>
                  <div className="bg-marine-card rounded-2xl p-6 border border-marine-border">
                    <DataVisualizationDashboard />
                  </div>
                </div>
              </div>
            </section>

            {/* Featured Company */}
            <section className="max-w-7xl mx-auto px-4 md:px-6">
              <FeaturedCompany />
            </section>
          </>
        )}

        {/* Mobile-optimized sections */}
        {!showDesktopLayout && (
          <>
            {/* Simplified Stats */}
            <section className="px-4">
              <StatsCounter />
            </section>

            {/* Product Spotlight for mobile */}
            <section className="px-4">
              <FeaturedProductSpotlight />
            </section>
          </>
        )}

        {/* CTA Section */}
        <section className="max-w-4xl mx-auto px-4 md:px-6 text-center py-8">
          <div className="bg-gradient-to-r from-green-600/10 to-green-600/5 rounded-2xl p-8 md:p-12 border border-green-600/20">
            <h2 className="text-2xl md:text-3xl font-bold text-white mb-3">
              Ready to explore the hemp industry?
            </h2>
            <p className="text-base md:text-lg text-gray-400 mb-6 max-w-2xl mx-auto">
              Join thousands of researchers, businesses, and innovators using HempQuarterz® to discover sustainable solutions.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="/products"
                className="inline-flex items-center justify-center px-6 py-3 rounded-lg bg-green-600 text-white font-medium hover:bg-green-700 transition-colors"
              >
                Browse Products
              </a>
              <a
                href="/register"
                className="inline-flex items-center justify-center px-6 py-3 rounded-lg bg-marine-card text-white font-medium hover:bg-marine-cardHover transition-colors border border-marine-border"
              >
                Create Free Account
              </a>
            </div>
          </div>
        </section>
      </div>
    </>
  );
};

export default HomePage;