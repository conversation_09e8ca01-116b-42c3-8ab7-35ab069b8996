import { useParams } from "wouter";
import { useHempProduct } from "@/hooks/use-product-data";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Building2, Leaf, ExternalLink, Check } from "lucide-react";
import { useLocation } from "wouter";
import { Skeleton } from "@/components/ui/skeleton";
import SmartProductImage from "@/components/ui/smart-product-image";

const SimplifiedProductDetailPage = () => {
  const params = useParams();
  const productId = parseInt(params.id as string, 10);
  const { data: product, isLoading } = useHempProduct(productId);
  const [, setLocation] = useLocation();

  if (isLoading) {
    return <ProductDetailSkeleton />;
  }

  if (!product) {
    return (
      <div className="min-h-screen bg-neutral-900 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-white mb-4">Product not found</h1>
          <Button onClick={() => setLocation("/products")}>
            Back to Products
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-neutral-900">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Back Button */}
        <Button
          variant="ghost"
          onClick={() => setLocation("/products")}
          className="mb-6 text-neutral-400 hover:text-white"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Products
        </Button>

        {/* Product Hero */}
        <div className="bg-neutral-800 rounded-lg overflow-hidden mb-8">
          <div className="w-full h-64">
            <SmartProductImage 
              product={{
                name: product.name,
                image_url: product.image_url,
                plantPartId: product.plant_part_id
              }}
              plantPartName={product.plant_part_name}
              className="w-full h-full object-cover"
              showAIBadge={true}
            />
          </div>
          <div className="p-6">
            <h1 className="text-3xl font-bold text-white mb-4">{product.name}</h1>
            
            {/* Key Info Badges */}
            <div className="flex flex-wrap gap-2 mb-6">
              {product.company_name && (
                <Badge variant="outline" className="text-neutral-300 border-neutral-600">
                  <Building2 className="h-3 w-3 mr-1" />
                  {product.company_name}
                </Badge>
              )}
              {product.plant_part_name && (
                <Badge variant="outline" className="text-neutral-300 border-neutral-600">
                  <Leaf className="h-3 w-3 mr-1" />
                  {product.plant_part_name}
                </Badge>
              )}
              {product.commercialization_stage && (
                <Badge className="bg-hemp-400 text-white">
                  {product.commercialization_stage}
                </Badge>
              )}
            </div>

            {/* Description */}
            <p className="text-neutral-300 text-lg leading-relaxed">
              {product.description}
            </p>
          </div>
        </div>

        {/* Key Benefits */}
        {product.benefits && product.benefits.length > 0 && (
          <div className="bg-neutral-800 rounded-lg p-6 mb-8">
            <h2 className="text-xl font-bold text-white mb-4">Key Benefits</h2>
            <ul className="space-y-3">
              {product.benefits.map((benefit, index) => (
                <li key={index} className="flex items-start gap-3">
                  <Check className="h-5 w-5 text-hemp-400 mt-0.5 flex-shrink-0" />
                  <span className="text-neutral-300">{benefit}</span>
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Technical Specs */}
        <div className="bg-neutral-800 rounded-lg p-6 mb-8">
          <h2 className="text-xl font-bold text-white mb-4">Technical Information</h2>
          <dl className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {product.industry_category && (
              <div>
                <dt className="text-sm font-medium text-neutral-400">Industry</dt>
                <dd className="mt-1 text-neutral-200">{product.industry_category}</dd>
              </div>
            )}
            {product.sub_category && (
              <div>
                <dt className="text-sm font-medium text-neutral-400">Sub-category</dt>
                <dd className="mt-1 text-neutral-200">{product.sub_category}</dd>
              </div>
            )}
            {product.thc_content && (
              <div>
                <dt className="text-sm font-medium text-neutral-400">THC Content</dt>
                <dd className="mt-1 text-neutral-200">{product.thc_content}</dd>
              </div>
            )}
            {product.cbd_content && (
              <div>
                <dt className="text-sm font-medium text-neutral-400">CBD Content</dt>
                <dd className="mt-1 text-neutral-200">{product.cbd_content}</dd>
              </div>
            )}
          </dl>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-4">
          {product.external_url && (
            <Button
              asChild
              className="bg-hemp-400 hover:bg-hemp-500 text-white"
            >
              <a href={product.external_url} target="_blank" rel="noopener noreferrer">
                Learn More
                <ExternalLink className="h-4 w-4 ml-2" />
              </a>
            </Button>
          )}
          <Button
            variant="outline"
            className="border-neutral-600 text-neutral-300 hover:text-white"
          >
            Contact Supplier
          </Button>
        </div>
      </div>
    </div>
  );
};

function ProductDetailSkeleton() {
  return (
    <div className="min-h-screen bg-neutral-900">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Skeleton className="h-10 w-32 mb-6" />
        <div className="bg-neutral-800 rounded-lg overflow-hidden mb-8">
          <Skeleton className="w-full h-64" />
          <div className="p-6">
            <Skeleton className="h-10 w-3/4 mb-4" />
            <div className="flex gap-2 mb-6">
              <Skeleton className="h-6 w-24" />
              <Skeleton className="h-6 w-20" />
              <Skeleton className="h-6 w-28" />
            </div>
            <Skeleton className="h-24 w-full" />
          </div>
        </div>
        <div className="bg-neutral-800 rounded-lg p-6">
          <Skeleton className="h-8 w-32 mb-4" />
          <div className="space-y-3">
            <Skeleton className="h-6 w-full" />
            <Skeleton className="h-6 w-5/6" />
            <Skeleton className="h-6 w-4/5" />
          </div>
        </div>
      </div>
    </div>
  );
}

export default SimplifiedProductDetailPage;