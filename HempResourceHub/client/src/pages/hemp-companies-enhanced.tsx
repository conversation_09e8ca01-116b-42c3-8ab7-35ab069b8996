import React from "react";
import { DirectoryStyleCompanies } from "@/components/companies/directory-style-companies";
import { Helmet } from "react-helmet";
import { createBreadcrumb } from "@/components/ui/breadcrumb";

export default function HempCompaniesEnhanced() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-green-900/20">
      <Helmet>
        <title>Hemp Companies Directory | HempQuarterz</title>
        <meta
          name="description"
          content="Discover leading companies in the hemp industry. Connect with manufacturers, suppliers, and innovators driving the hemp economy forward."
        />
      </Helmet>

      <div className="container mx-auto px-4 py-12">
        <div className="max-w-7xl mx-auto">
          {/* Breadcrumb */}
          <div className="mb-6">
            {createBreadcrumb([
              { href: "/", label: "Home" },
              { href: "/hemp-companies-enhanced", label: "Companies", isCurrent: true },
            ])}
          </div>

          <DirectoryStyleCompanies />
        </div>
      </div>
    </div>
  );
}




