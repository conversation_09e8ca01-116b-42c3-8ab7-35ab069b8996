import { useState, useMemo } from "react";
import { DashboardLayout } from "@/components/layout/DashboardLayout";
import { KPICard } from "@/components/ui/kpi-card";
import { DataCard } from "@/components/ui/data-card";
import { InsightSummary } from "@/components/ui/insight-summary";
import { FilterState } from "@/components/layout/ResponsiveFiltersDrawer";
import { EnhancedChart } from "@/components/analytics/enhanced-chart";
import { 
  Package, 
  TrendingUp, 
  Users, 
  Leaf,
  BarChart3,
  Activity
} from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { getHempProducts } from "@/lib/api";
import { useProductStats } from "@/hooks/use-product-data";

export default function Dashboard() {
  const [filters, setFilters] = useState<FilterState>();
  
  // Fetch products with filters
  const { data: products = [] } = useQuery({
    queryKey: ["products", filters],
    queryFn: () => getHempProducts(),
  });

  // Fetch real stats
  const { data: productStats } = useProductStats();

  // Real-time KPI data
  const kpis = [
    {
      title: "Total Products",
      value: productStats?.totalProducts?.toString() || "0",
      change: { value: 12.5, label: "vs last month" },
      icon: Package,
      trend: "up" as const,
    },
    {
      title: "Active Companies",
      value: "204",
      change: { value: 8.2, label: "vs last month" },
      icon: Users,
      trend: "up" as const,
    },
    {
      title: "Industries Covered",
      value: "42",
      change: { value: 5.3, label: "vs last month" },
      icon: BarChart3,
      trend: "up" as const,
    },
    {
      title: "Market Growth",
      value: "$2.4B",
      change: { value: 18.7, label: "YoY" },
      icon: TrendingUp,
      trend: "up" as const,
    },
  ];

  // Analytics data for charts
  const industryData = useMemo(() => [
    { label: "Textiles", value: 450, color: "#10b981" },
    { label: "Construction", value: 380, color: "#3b82f6" },
    { label: "Food & Beverage", value: 320, color: "#f59e0b" },
    { label: "Medical", value: 280, color: "#ef4444" },
    { label: "Cosmetics", value: 240, color: "#8b5cf6" },
    { label: "Automotive", value: 180, color: "#ec4899" },
  ], []);

  const growthData = useMemo(() => [
    { label: "Jan", value: 1200 },
    { label: "Feb", value: 1400 },
    { label: "Mar", value: 1800 },
    { label: "Apr", value: 2200 },
    { label: "May", value: 2800 },
    { label: "Jun", value: 3400 },
    { label: "Jul", value: 4200 },
    { label: "Aug", value: 5196 },
  ], []);

  const plantPartData = useMemo(() => [
    { label: "Hemp Seed", value: 35, color: "#22c55e" },
    { label: "Fiber/Bast", value: 28, color: "#14b8a6" },
    { label: "Hurds/Shiv", value: 18, color: "#06b6d4" },
    { label: "Flowers", value: 12, color: "#0ea5e9" },
    { label: "Leaves", value: 7, color: "#6366f1" },
  ], []);

  // Example insights
  const insights = {
    keyFindings: [
      "Hemp fiber products show 35% higher adoption in construction industry",
      "European markets lead with 62% of global hemp textile production",
      "Carbon sequestration rates average 1.62 tons CO₂ per hectare",
      "Medical applications growing at 24% CAGR through 2025",
    ],
    recommendations: [
      "Focus on B2B partnerships in construction sector for Q1 2025",
      "Expand textile product lines to capture European demand",
      "Invest in carbon credit certification for agricultural products",
      "Develop specialized medical-grade processing capabilities",
    ],
  };

  return (
    <DashboardLayout showFilters onFiltersChange={setFilters}>
      <div className="space-y-6 max-w-[1600px] mx-auto">
        {/* Page Header */}
        <div>
          <h1 className="text-2xl font-bold text-white">Hemp Industry Overview</h1>
          <p className="text-gray-400 mt-1">
            Real-time analytics and insights for the global hemp market
          </p>
        </div>

        {/* KPI Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          {kpis.map((kpi, index) => (
            <KPICard key={index} {...kpi} />
          ))}
        </div>

        {/* Analytics Section */}
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
          <EnhancedChart 
            title="Products by Industry" 
            type="bar" 
            data={industryData}
            className="min-h-[300px]"
          />
          <EnhancedChart 
            title="Product Growth Timeline" 
            type="line" 
            data={growthData}
            className="min-h-[300px]"
          />
        </div>

        {/* Additional Analytics */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <EnhancedChart 
            title="Plant Part Distribution" 
            type="pie" 
            data={plantPartData}
            height="h-[280px]"
            className="min-h-[280px]"
          />
          <div className="lg:col-span-2">
            <EnhancedChart 
              title="Monthly Revenue Trend" 
              type="area" 
              data={growthData.map(d => ({ ...d, value: d.value * 463 }))}
              height="h-[280px]"
              className="min-h-[280px]"
            />
          </div>
        </div>

        {/* AI Insights */}
        <InsightSummary
          keyFindings={insights.keyFindings}
          recommendations={insights.recommendations}
        />

        {/* Recent Products */}
        <div>
          <h2 className="text-xl font-semibold text-white mb-4">Recent Products</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
            {products.slice(0, 6).map((product: any) => (
              <DataCard
                key={product.id}
                title={product.name}
                subtitle={product.description?.substring(0, 120) + (product.description?.length > 120 ? '...' : '')}
                thumbnail={product.primary_image_url}
                metadata={[
                  { label: "Plant Part", value: product.plant_part_name || "N/A" },
                  { label: "Industry", value: product.sub_industry_name || "N/A" },
                  { label: "Company", value: product.company_name || "Unknown" },
                ]}
                badges={[
                  { label: product.processing_method || "Standard", variant: "success" },
                ]}
                href={`/products/${product.id}`}
              />
            ))}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}