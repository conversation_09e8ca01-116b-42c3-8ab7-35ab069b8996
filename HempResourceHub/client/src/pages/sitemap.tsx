import { Link } from "wouter";
import { PageLayout } from "@/components/layout/page-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { FileText, Package, Building2, FlaskConical, Search, LayoutDashboard, Users, Leaf } from "lucide-react";

export default function SitemapPage() {
  const sections = [
    {
      title: "Main Pages",
      icon: <LayoutDashboard className="h-5 w-5" />,
      links: [
        { label: "Home", href: "/", description: "Explore the world's largest hemp database" },
        { label: "Dashboard", href: "/dashboard", description: "View analytics and insights" },
        { label: "About", href: "/about", description: "Learn about HempQuarterz®" },
      ],
    },
    {
      title: "Products & Data",
      icon: <Package className="h-5 w-5" />,
      links: [
        { label: "Browse Products", href: "/products", description: "Discover hemp products" },
        { label: "Product Explorer", href: "/products-explorer", description: "Advanced product search" },
        { label: "Enhanced Explorer", href: "/hemp-dex-enhanced", description: "Interactive product discovery" },
        { label: "Product Table", href: "/products-table", description: "Data table view" },
      ],
    },
    {
      title: "Plant Information",
      icon: <Leaf className="h-5 w-5" />,
      links: [
        { label: "Plant Parts", href: "/plant-parts", description: "Explore hemp plant components" },
        { label: "Plant Types", href: "/plant-types", description: "Different hemp varieties" },
        { label: "Industries", href: "/industries", description: "Hemp industry categories" },
      ],
    },
    {
      title: "Companies & Research",
      icon: <Building2 className="h-5 w-5" />,
      links: [
        { label: "Companies", href: "/hemp-companies", description: "Hemp industry companies" },
        { label: "Research Papers", href: "/research", description: "Scientific research database" },
      ],
    },
    {
      title: "Search & Tools",
      icon: <Search className="h-5 w-5" />,
      links: [
        { label: "Advanced Search", href: "/search", description: "Powerful search tools" },
        { label: "Product Showcase", href: "/product-showcase", description: "Featured products" },
      ],
    },
    {
      title: "Legal & Support",
      icon: <FileText className="h-5 w-5" />,
      links: [
        { label: "Privacy Policy", href: "/privacy", description: "How we protect your data" },
        { label: "Terms of Service", href: "/terms", description: "Terms and conditions" },
        { label: "Contact", href: "/contact", description: "Get in touch with us" },
      ],
    },
  ];

  return (
    <PageLayout title="Sitemap">
      <div className="container mx-auto py-8 px-4">
        <div className="mb-8 text-center">
          <h1 className="text-4xl font-bold mb-4">Site Map</h1>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Navigate through all pages and sections of the HempQuarterz® database
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {sections.map((section) => (
            <Card key={section.title} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  {section.icon}
                  {section.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3">
                  {section.links.map((link) => (
                    <li key={link.href}>
                      <Link href={link.href}>
                        <a className="block hover:bg-muted p-2 rounded-md transition-colors">
                          <div className="font-medium text-primary hover:underline">
                            {link.label}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {link.description}
                          </div>
                        </a>
                      </Link>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </PageLayout>
  );
}