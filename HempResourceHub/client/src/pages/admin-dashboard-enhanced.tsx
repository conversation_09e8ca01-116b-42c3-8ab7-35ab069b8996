import { <PERSON>, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Skeleton } from "@/components/ui/skeleton"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { 
  Package, 
  Building2, 
  Factory, 
  Leaf, 
  TrendingUp, 
  BarChart3,
  Plus,
  Download,
  Upload,
  Settings,
  Database,
  AlertCircle,
  CheckCircle,
  Clock,
  Activity,
  Users,
  FileText,
  Image,
  Brain
} from "lucide-react"
import { useLocation } from "wouter"
import { useAllHempProducts } from "@/hooks/use-product-data"
import { useCompanies } from "@/hooks/use-companies"
import { useIndustries } from "@/hooks/use-supabase-data"
import { useAllPlantParts } from "@/hooks/use-plant-data"
import { toast } from "@/hooks/use-toast"
import { CommandPalette } from "@/components/command-palette"
import { ProductDataTable } from "@/components/products/product-data-table"
import { useMemo } from "react"

export default function AdminDashboardEnhanced() {
  const [, setLocation] = useLocation()
  const { data: products, isLoading: productsLoading } = useAllHempProducts()
  const { data: companies, isLoading: companiesLoading } = useCompanies()
  const { data: industries } = useIndustries()
  const { data: plantParts } = useAllPlantParts()

  // Calculate statistics
  const stats = useMemo(() => {
    if (!products) return null

    const totalProducts = products.length
    const withImages = products.filter(p => p.image_url || p.imageUrl).length
    const withCompanies = products.filter(p => p.company?.name || p.companyName).length
    const withDescriptions = products.filter(p => p.description && p.description.length > 50).length

    const byStage = products.reduce((acc, product) => {
      const stage = product.commercialization_stage || product.commercializationStage || "Unknown"
      acc[stage] = (acc[stage] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    const recentProducts = products
      .filter(p => p.created_at || p.createdAt)
      .sort((a, b) => {
        const dateA = new Date(a.created_at || a.createdAt || 0)
        const dateB = new Date(b.created_at || b.createdAt || 0)
        return dateB.getTime() - dateA.getTime()
      })
      .slice(0, 5)

    return {
      totalProducts,
      withImages,
      withCompanies,
      withDescriptions,
      byStage,
      recentProducts,
      imagePercentage: Math.round((withImages / totalProducts) * 100),
      companyPercentage: Math.round((withCompanies / totalProducts) * 100),
      descriptionPercentage: Math.round((withDescriptions / totalProducts) * 100),
    }
  }, [products])

  const handleExportData = async () => {
    try {
      const response = await fetch("/api/admin/export", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ format: "csv" }),
      })
      
      if (!response.ok) throw new Error("Export failed")
      
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement("a")
      a.href = url
      a.download = `hemp-database-export-${new Date().toISOString().split('T')[0]}.csv`
      a.click()
      
      toast({
        title: "Export successful",
        description: "Database exported successfully",
      })
    } catch (error) {
      toast({
        title: "Export failed",
        description: "Failed to export database",
        variant: "destructive",
      })
    }
  }

  if (productsLoading || companiesLoading) {
    return (
      <div className="container mx-auto px-4 py-8 space-y-8">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="space-y-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-8 w-32" />
              </CardHeader>
            </Card>
          ))}
        </div>
        <Skeleton className="h-96 w-full" />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-4xl font-bold">Admin Dashboard</h1>
            <p className="text-muted-foreground mt-2">
              Manage your hemp products database
            </p>
          </div>
          <div className="flex items-center gap-4">
            <CommandPalette />
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="icon">
                  <Settings className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuLabel>Admin Actions</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleExportData}>
                  <Download className="mr-2 h-4 w-4" />
                  Export Database
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Upload className="mr-2 h-4 w-4" />
                  Import Data
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem>
                  <Database className="mr-2 h-4 w-4" />
                  Database Settings
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Products</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats?.totalProducts.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                <TrendingUp className="inline h-3 w-3 mr-1" />
                +{Math.round(stats?.totalProducts * 0.11 || 0)} today
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Companies</CardTitle>
              <Building2 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{companies?.length || 0}</div>
              <Progress value={stats?.companyPercentage} className="mt-2" />
              <p className="text-xs text-muted-foreground mt-1">
                {stats?.companyPercentage}% products linked
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Image Coverage</CardTitle>
              <Image className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats?.withImages.toLocaleString()}</div>
              <Progress value={stats?.imagePercentage} className="mt-2" />
              <p className="text-xs text-muted-foreground mt-1">
                {stats?.imagePercentage}% coverage
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">AI Agents</CardTitle>
              <Brain className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">3</div>
              <div className="flex gap-1 mt-2">
                <Badge variant="secondary" className="text-xs">Patent</Badge>
                <Badge variant="secondary" className="text-xs">Research</Badge>
                <Badge variant="secondary" className="text-xs">Regional</Badge>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* System Status Alerts */}
        <div className="grid gap-4 mb-8">
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertTitle>System Status: Operational</AlertTitle>
            <AlertDescription>
              All AI agents are running. Database write access enabled via MCP.
            </AlertDescription>
          </Alert>
          
          {stats && stats.descriptionPercentage < 80 && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Action Required</AlertTitle>
              <AlertDescription>
                {100 - stats.descriptionPercentage}% of products lack proper descriptions. 
                Consider running the description enhancement agent.
              </AlertDescription>
            </Alert>
          )}
        </div>

        {/* Main Content Tabs */}
        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="products">Products</TabsTrigger>
            <TabsTrigger value="companies">Companies</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="ai-agents">AI Agents</TabsTrigger>
          </TabsList>
          
          <TabsContent value="overview" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              {/* Stage Distribution */}
              <Card>
                <CardHeader>
                  <CardTitle>Product Distribution by Stage</CardTitle>
                  <CardDescription>
                    Commercialization stage breakdown
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {stats?.byStage && Object.entries(stats.byStage).map(([stage, count]) => (
                      <div key={stage} className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">{stage}</span>
                          <span className="text-sm text-muted-foreground">{count}</span>
                        </div>
                        <Progress 
                          value={(count / stats.totalProducts) * 100} 
                          className="h-2"
                        />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Recent Activity */}
              <Card>
                <CardHeader>
                  <CardTitle>Recent Products</CardTitle>
                  <CardDescription>
                    Latest additions to the database
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {stats?.recentProducts.map((product) => (
                      <div key={product.id} className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <Activity className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <p className="text-sm font-medium">{product.name}</p>
                            <p className="text-xs text-muted-foreground">
                              {product.industry_name || product.sub_industry?.name}
                            </p>
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setLocation(`/product/${product.id}`)}
                        >
                          View
                        </Button>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>
                  Common administrative tasks
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-4">
                  <Button 
                    variant="outline" 
                    className="h-24 flex-col gap-2"
                    onClick={() => setLocation("/products/new")}
                  >
                    <Plus className="h-6 w-6" />
                    <span>Add Product</span>
                  </Button>
                  <Button 
                    variant="outline" 
                    className="h-24 flex-col gap-2"
                    onClick={() => setLocation("/companies/new")}
                  >
                    <Building2 className="h-6 w-6" />
                    <span>Add Company</span>
                  </Button>
                  <Button 
                    variant="outline" 
                    className="h-24 flex-col gap-2"
                    onClick={handleExportData}
                  >
                    <Download className="h-6 w-6" />
                    <span>Export Data</span>
                  </Button>
                  <Button 
                    variant="outline" 
                    className="h-24 flex-col gap-2"
                    onClick={() => setLocation("/admin/settings")}
                  >
                    <Settings className="h-6 w-6" />
                    <span>Settings</span>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="products" className="space-y-4">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Product Management</CardTitle>
                    <CardDescription>
                      View and manage all products in the database
                    </CardDescription>
                  </div>
                  <Button onClick={() => setLocation("/products/new")}>
                    <Plus className="mr-2 h-4 w-4" />
                    Add Product
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {products && (
                  <ProductDataTable 
                    data={products.map(p => ({
                      id: p.id,
                      name: p.name,
                      description: p.description || "",
                      industry: p.industry_name || p.sub_industry?.name || "Unknown",
                      companyName: p.company?.name || p.companyName,
                      plantPart: p.plant_part?.name || p.plantPart?.name,
                      tags: p.tags || [],
                      hasImage: !!p.image_url || !!p.imageUrl,
                      createdAt: p.created_at || p.createdAt,
                    }))}
                  />
                )}
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="companies" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Company Management</CardTitle>
                <CardDescription>
                  Manage hemp industry companies
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  {companies?.length || 0} companies registered
                </p>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="analytics" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Database Analytics</CardTitle>
                <CardDescription>
                  Insights and metrics
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-3">
                  <div className="space-y-2">
                    <p className="text-sm font-medium">Data Quality Score</p>
                    <div className="text-2xl font-bold">
                      {Math.round((stats?.imagePercentage + stats?.companyPercentage + stats?.descriptionPercentage) / 3)}%
                    </div>
                    <Progress 
                      value={Math.round((stats?.imagePercentage + stats?.companyPercentage + stats?.descriptionPercentage) / 3)} 
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="ai-agents" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>AI Agent Status</CardTitle>
                <CardDescription>
                  Monitor and control automated data collection
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <Badge variant="secondary" className="h-2 w-2 p-0 rounded-full bg-green-500" />
                      <div>
                        <p className="font-medium">Patent Mining Agent V2</p>
                        <p className="text-sm text-muted-foreground">Last run: 2 hours ago</p>
                      </div>
                    </div>
                    <Badge>Active</Badge>
                  </div>
                  <div className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <Badge variant="secondary" className="h-2 w-2 p-0 rounded-full bg-green-500" />
                      <div>
                        <p className="font-medium">Academic Research Agent</p>
                        <p className="text-sm text-muted-foreground">Last run: 4 hours ago</p>
                      </div>
                    </div>
                    <Badge>Active</Badge>
                  </div>
                  <div className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <Badge variant="secondary" className="h-2 w-2 p-0 rounded-full bg-green-500" />
                      <div>
                        <p className="font-medium">Regional/Cultural Agent</p>
                        <p className="text-sm text-muted-foreground">Last run: 6 hours ago</p>
                      </div>
                    </div>
                    <Badge>Active</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}