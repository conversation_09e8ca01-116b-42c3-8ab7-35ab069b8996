import { useState } from "react";
import { ResponsiveProductGrid } from "@/components/product/responsive-product-grid";
import { UltimateProductCard } from "@/components/product/ultimate-product-card";
import { useAllHempProducts } from "@/hooks/use-product-data";
import { useIndustries, usePlantParts } from "@/hooks/use-supabase-data";
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";
import { 
  Sparkles, 
  Palette, 
  Smartphone, 
  Zap, 
  Eye,
  Heart,
  Share2,
  Grid3x3,
  List,
  LayoutGrid,
  Check
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";
import { HempProduct } from "@shared/schema";

const ProductShowcase = () => {
  const { data: products, isLoading } = useAllHempProducts();
  const { data: industries } = useIndustries();
  const { data: plantParts } = usePlantParts();
  const { toast } = useToast();
  
  const [selectedVariant, setSelectedVariant] = useState<"default" | "compact" | "detailed">("default");

  // Create lookup maps
  const industryNames = industries?.reduce((acc, ind) => {
    acc[ind.id] = ind.name;
    return acc;
  }, {} as Record<number, string>) || {};

  const plantPartNames = plantParts?.reduce((acc, part) => {
    acc[part.id] = part.name;
    return acc;
  }, {} as Record<number, string>) || {};

  // Sample product for demonstration
  const sampleProduct = products?.[0] || {
    id: 1,
    name: "Premium Hemp Protein Powder",
    description: "High-quality organic hemp protein powder with complete amino acid profile. Perfect for smoothies, baking, and post-workout recovery.",
    industrySubCategoryId: 1,
    plantPartId: 1,
    created_at: new Date().toISOString(),
    image_url: "/images/hemp-protein.jpg",
    rating: 4.8,
    company_name: "Hemp Health Co.",
    views: 1250,
    is_featured: true,
    sustainability_score: 95,
    commercialization_stage: "Market Ready"
  };

  const handleFavorite = (productId: number) => {
    toast({
      title: "Added to favorites",
      description: `Product ${productId} has been added to your favorites.`,
    });
  };

  const handleShare = (product: HempProduct) => {
    toast({
      title: "Share link copied",
      description: `Link for "${product.name}" copied to clipboard.`,
    });
  };

  const features = [
    {
      icon: Sparkles,
      title: "Modern Design",
      description: "Clean, contemporary aesthetic with smooth animations"
    },
    {
      icon: Smartphone,
      title: "Fully Responsive",
      description: "Optimized for all devices from mobile to desktop"
    },
    {
      icon: Zap,
      title: "Interactive Features",
      description: "Quick actions, favorites, and sharing capabilities"
    },
    {
      icon: Palette,
      title: "Multiple Variants",
      description: "Default, compact, and detailed view options"
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Hero Section */}
      <div className="bg-gradient-to-br from-primary/10 via-primary/5 to-transparent">
        <div className="container mx-auto px-4 py-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center max-w-3xl mx-auto"
          >
            <Badge className="mb-4" variant="secondary">
              <Sparkles className="w-3 h-3 mr-1" />
              New Component
            </Badge>
            <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
              Ultimate Product Card
            </h1>
            <p className="text-lg text-gray-600 dark:text-gray-400 mb-8">
              A modern, feature-rich product card component with multiple variants, 
              responsive design, and enhanced user interactions.
            </p>
          </motion.div>
        </div>
      </div>

      {/* Features Grid */}
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card className="h-full hover:shadow-lg transition-shadow">
                <CardHeader>
                  <feature.icon className="w-10 h-10 text-primary mb-2" />
                  <CardTitle className="text-lg">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>{feature.description}</CardDescription>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Component Demo */}
        <Tabs defaultValue="demo" className="space-y-8">
          <TabsList className="grid w-full max-w-md mx-auto grid-cols-3">
            <TabsTrigger value="demo">Demo</TabsTrigger>
            <TabsTrigger value="variants">Variants</TabsTrigger>
            <TabsTrigger value="grid">Grid View</TabsTrigger>
          </TabsList>

          <TabsContent value="demo" className="space-y-8">
            <Card>
              <CardHeader>
                <CardTitle>Interactive Demo</CardTitle>
                <CardDescription>
                  Try hovering over the card to see quick actions. Click the heart to favorite,
                  or the eye icon for quick preview.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="max-w-sm mx-auto">
                  <UltimateProductCard
                    product={sampleProduct}
                    industryName={sampleProduct.industry_name || "Hemp Industry"}
                    subIndustryName={sampleProduct.sub_industry_name || "General"}
                    plantPartName={plantPartNames?.[sampleProduct.plantPartId]}
                    onFavorite={handleFavorite}
                    onShare={handleShare}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="variants" className="space-y-8">
            <Card>
              <CardHeader>
                <CardTitle>Card Variants</CardTitle>
                <CardDescription>
                  Choose between different card variants based on your layout needs
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex gap-4 mb-6 justify-center">
                  <Button
                    variant={selectedVariant === "default" ? "default" : "outline"}
                    onClick={() => setSelectedVariant("default")}
                  >
                    Default
                  </Button>
                  <Button
                    variant={selectedVariant === "compact" ? "default" : "outline"}
                    onClick={() => setSelectedVariant("compact")}
                  >
                    Compact
                  </Button>
                  <Button
                    variant={selectedVariant === "detailed" ? "default" : "outline"}
                    onClick={() => setSelectedVariant("detailed")}
                  >
                    Detailed
                  </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className={cn(selectedVariant === "detailed" && "md:col-span-3")}>
                    <UltimateProductCard
                      product={sampleProduct}
                      industryName={sampleProduct.industry_name || "Hemp Industry"}
                      subIndustryName={sampleProduct.sub_industry_name || "General"}
                      plantPartName={plantPartNames?.[sampleProduct.plantPartId]}
                      variant={selectedVariant}
                      onFavorite={handleFavorite}
                      onShare={handleShare}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="grid" className="space-y-8">
            <Card>
              <CardHeader>
                <CardTitle>Responsive Grid</CardTitle>
                <CardDescription>
                  The grid automatically adjusts based on screen size and selected view mode
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveProductGrid
                  products={products?.slice(0, 8) || []}
                  plantPartNames={plantPartNames}
                  loading={isLoading}
                  onFavorite={handleFavorite}
                  onShare={handleShare}
                />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Key Features */}
        <Card className="mt-12">
          <CardHeader>
            <CardTitle>Key Features</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {[
                "Smooth hover animations and transitions",
                "Quick action buttons (preview, favorite, share)",
                "Multiple card variants (default, compact, detailed)",
                "Responsive grid with view mode controls",
                "Accessibility-first design with ARIA labels",
                "Performance optimized with memo and lazy loading",
                "Dark mode support with proper contrast",
                "Loading states and error handling",
                "Touch-friendly on mobile devices",
                "Semantic HTML structure"
              ].map((feature) => (
                <div key={feature} className="flex items-start gap-2">
                  <Check className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                  <span className="text-sm text-gray-600 dark:text-gray-400">{feature}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ProductShowcase;