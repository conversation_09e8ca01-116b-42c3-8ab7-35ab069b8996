import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export function TestStyling() {
  return (
    <div className="p-8 space-y-6">
      <Card className="max-w-md">
        <CardHeader>
          <CardTitle className="text-white">UI Test Component - New Color Scheme</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <h3 className="text-lg font-semibold text-white">Basic Color Tests</h3>
            <div className="flex gap-2 flex-wrap">
              <div className="px-3 py-1 bg-black text-white rounded">Black Primary</div>
              <div className="px-3 py-1 bg-purple-500 text-white rounded">Purple Secondary</div>
              <div className="px-3 py-1 bg-green-500 text-white rounded">Green Hemp</div>
            </div>
          </div>

          <div className="space-y-2">
            <h3 className="text-lg font-semibold text-white">Badge Variants</h3>
            <div className="flex gap-2 flex-wrap">
              <Badge variant="default">Default</Badge>
              <Badge variant="hemp">Hemp</Badge>
              <Badge variant="plantPart">Plant Part</Badge>
              <Badge variant="industry">Industry</Badge>
              <Badge variant="category">Category</Badge>
            </div>
          </div>

          <div className="space-y-2">
            <h3 className="text-lg font-semibold text-white">Button Variants</h3>
            <div className="flex gap-2 flex-wrap">
              <Button variant="default">Default</Button>
              <Button variant="hemp">Hemp</Button>
              <Button variant="category">Category</Button>
              <Button variant="secondary">Secondary</Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
