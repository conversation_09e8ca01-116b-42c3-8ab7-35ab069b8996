import { cn } from "@/lib/utils";

interface SkeletonProps {
  className?: string;
  variant?: 'text' | 'circular' | 'rectangular' | 'rounded';
  width?: string | number;
  height?: string | number;
  animation?: 'pulse' | 'wave' | 'none';
}

export function Skeleton({
  className,
  variant = 'text',
  width,
  height,
  animation = 'pulse'
}: SkeletonProps) {
  const baseClasses = "bg-marine-card/50";
  
  const animationClasses = {
    pulse: "animate-pulse",
    wave: "animate-shimmer",
    none: ""
  };

  const variantClasses = {
    text: "h-4 rounded",
    circular: "rounded-full",
    rectangular: "rounded-none",
    rounded: "rounded-lg"
  };

  const style: React.CSSProperties = {
    width: width || (variant === 'text' ? '100%' : undefined),
    height: height || (variant === 'text' ? '1rem' : undefined)
  };

  return (
    <div
      className={cn(
        baseClasses,
        animationClasses[animation],
        variantClasses[variant],
        className
      )}
      style={style}
      role="status"
      aria-live="polite"
      aria-busy="true"
    >
      <span className="sr-only">Loading...</span>
    </div>
  );
}

// Product Card Skeleton
export function ProductCardSkeleton() {
  return (
    <div className="bg-marine-card rounded-xl overflow-hidden">
      <Skeleton variant="rectangular" height={200} className="w-full" />
      <div className="p-5 space-y-3">
        <Skeleton variant="text" className="w-3/4" />
        <Skeleton variant="text" className="w-full" />
        <Skeleton variant="text" className="w-full" />
        <div className="flex gap-2">
          <Skeleton variant="rounded" width={80} height={24} />
          <Skeleton variant="rounded" width={100} height={24} />
        </div>
      </div>
    </div>
  );
}

// Company Card Skeleton
export function CompanyCardSkeleton() {
  return (
    <div className="bg-marine-card rounded-xl p-6 space-y-4">
      <div className="flex items-start gap-4">
        <Skeleton variant="rounded" width={80} height={80} />
        <div className="flex-1 space-y-2">
          <Skeleton variant="text" className="w-1/2" />
          <Skeleton variant="text" className="w-1/3" />
        </div>
      </div>
      <Skeleton variant="text" className="w-full" />
      <Skeleton variant="text" className="w-full" />
      <Skeleton variant="text" className="w-3/4" />
      <div className="flex gap-2">
        <Skeleton variant="rounded" width={100} height={32} />
        <Skeleton variant="rounded" width={100} height={32} />
      </div>
    </div>
  );
}

// Research Entry Skeleton
export function ResearchEntrySkeleton() {
  return (
    <div className="bg-marine-card rounded-lg p-4 space-y-3">
      <Skeleton variant="text" className="w-3/4 h-6" />
      <div className="flex items-center gap-4 text-sm">
        <Skeleton variant="text" width={100} />
        <Skeleton variant="text" width={80} />
        <Skeleton variant="text" width={120} />
      </div>
      <Skeleton variant="text" className="w-full" />
      <Skeleton variant="text" className="w-full" />
      <Skeleton variant="text" className="w-2/3" />
    </div>
  );
}

// Table Row Skeleton
export function TableRowSkeleton({ columns = 5 }: { columns?: number }) {
  return (
    <tr className="border-b border-marine-border">
      {Array.from({ length: columns }).map((_, i) => (
        <td key={i} className="px-4 py-3">
          <Skeleton variant="text" />
        </td>
      ))}
    </tr>
  );
}

// Stats Card Skeleton
export function StatsCardSkeleton() {
  return (
    <div className="bg-marine-card rounded-xl p-6 space-y-4">
      <div className="flex items-center justify-between">
        <Skeleton variant="text" width={120} />
        <Skeleton variant="circular" width={40} height={40} />
      </div>
      <Skeleton variant="text" className="w-1/3 h-8" />
      <Skeleton variant="text" className="w-2/3" />
    </div>
  );
}

// Chart Skeleton
export function ChartSkeleton({ height = 300 }: { height?: number }) {
  return (
    <div className="bg-marine-card rounded-xl p-6">
      <div className="mb-4">
        <Skeleton variant="text" className="w-1/4 h-6" />
      </div>
      <Skeleton variant="rectangular" height={height} className="w-full rounded-lg" />
    </div>
  );
}

// List Item Skeleton
export function ListItemSkeleton() {
  return (
    <div className="flex items-center gap-3 p-3">
      <Skeleton variant="circular" width={40} height={40} />
      <div className="flex-1 space-y-2">
        <Skeleton variant="text" className="w-1/2" />
        <Skeleton variant="text" className="w-1/3 h-3" />
      </div>
    </div>
  );
}

// Add shimmer animation to globals.css
const shimmerKeyframes = `
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.animate-shimmer {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.1) 20%,
    rgba(255, 255, 255, 0.2) 60%,
    rgba(255, 255, 255, 0);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}
`;