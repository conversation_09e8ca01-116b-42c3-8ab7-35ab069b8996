import { useState, useMemo } from "react";
import { useAllHempProducts } from "@/hooks/use-product-data";
import { 
  ChevronUp, 
  ChevronDown, 
  Search,
  Filter,
  ArrowUpDown,
  ChevronLeft,
  ChevronRight
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

type SortDirection = 'asc' | 'desc' | null;
type SortField = 'name' | 'industry' | 'plantPart' | 'stage' | 'created';

export function AnalyticsDataTable() {
  const { data: products } = useAllHempProducts();
  const [searchTerm, setSearchTerm] = useState("");
  const [sortField, setSortField] = useState<SortField>('name');
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  // Filter and sort data
  const processedData = useMemo(() => {
    if (!products) return [];

    // Filter
    let filtered = products.filter(product => {
      const search = searchTerm.toLowerCase();
      return product.name.toLowerCase().includes(search) ||
             product.description?.toLowerCase().includes(search) ||
             product.industry_name?.toLowerCase().includes(search) ||
             product.plant_part_name?.toLowerCase().includes(search);
    });

    // Sort
    if (sortField && sortDirection) {
      filtered.sort((a, b) => {
        let aVal: any, bVal: any;
        
        switch (sortField) {
          case 'name':
            aVal = a.name;
            bVal = b.name;
            break;
          case 'industry':
            aVal = a.industry_name || '';
            bVal = b.industry_name || '';
            break;
          case 'plantPart':
            aVal = a.plant_part_name || '';
            bVal = b.plant_part_name || '';
            break;
          case 'stage':
            aVal = a.commercialization_stage || '';
            bVal = b.commercialization_stage || '';
            break;
          case 'created':
            aVal = new Date(a.created_at || 0).getTime();
            bVal = new Date(b.created_at || 0).getTime();
            break;
        }

        if (sortDirection === 'asc') {
          return aVal > bVal ? 1 : -1;
        } else {
          return aVal < bVal ? 1 : -1;
        }
      });
    }

    return filtered;
  }, [products, searchTerm, sortField, sortDirection]);

  // Pagination
  const totalPages = Math.ceil(processedData.length / itemsPerPage);
  const paginatedData = processedData.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(
        sortDirection === 'asc' ? 'desc' : sortDirection === 'desc' ? null : 'asc'
      );
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const SortIcon = ({ field }: { field: SortField }) => {
    if (sortField !== field) {
      return <ArrowUpDown className="h-4 w-4 text-marine-text-muted" />;
    }
    if (sortDirection === 'asc') {
      return <ChevronUp className="h-4 w-4 text-green-400" />;
    }
    return <ChevronDown className="h-4 w-4 text-green-400" />;
  };

  return (
    <div className="bg-marine-card rounded-xl border border-marine-border">
      {/* Header */}
      <div className="p-4 border-b border-marine-border">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-white">Product Analytics</h3>
          <div className="flex items-center gap-2">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-marine-text-muted" />
              <Input
                type="text"
                placeholder="Search products..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-9 w-64 bg-marine-sidebar border-marine-border text-white"
              />
            </div>
          </div>
        </div>
        
        <div className="flex items-center justify-between text-sm text-marine-text-secondary">
          <span>Showing {paginatedData.length} of {processedData.length} products</span>
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b border-marine-border">
              <th className="px-4 py-3 text-left">
                <button
                  onClick={() => handleSort('name')}
                  className="flex items-center gap-2 text-sm font-medium text-marine-text-secondary hover:text-white transition-colors"
                >
                  Product Name
                  <SortIcon field="name" />
                </button>
              </th>
              <th className="px-4 py-3 text-left">
                <button
                  onClick={() => handleSort('industry')}
                  className="flex items-center gap-2 text-sm font-medium text-marine-text-secondary hover:text-white transition-colors"
                >
                  Industry
                  <SortIcon field="industry" />
                </button>
              </th>
              <th className="px-4 py-3 text-left">
                <button
                  onClick={() => handleSort('plantPart')}
                  className="flex items-center gap-2 text-sm font-medium text-marine-text-secondary hover:text-white transition-colors"
                >
                  Plant Part
                  <SortIcon field="plantPart" />
                </button>
              </th>
              <th className="px-4 py-3 text-left">
                <button
                  onClick={() => handleSort('stage')}
                  className="flex items-center gap-2 text-sm font-medium text-marine-text-secondary hover:text-white transition-colors"
                >
                  Stage
                  <SortIcon field="stage" />
                </button>
              </th>
              <th className="px-4 py-3 text-left">
                <button
                  onClick={() => handleSort('created')}
                  className="flex items-center gap-2 text-sm font-medium text-marine-text-secondary hover:text-white transition-colors"
                >
                  Added
                  <SortIcon field="created" />
                </button>
              </th>
            </tr>
          </thead>
          <tbody>
            {paginatedData.map((product) => (
              <tr
                key={product.id}
                className="border-b border-marine-border/50 hover:bg-marine-sidebar/30 transition-colors"
              >
                <td className="px-4 py-3">
                  <div>
                    <p className="text-sm font-medium text-white">{product.name}</p>
                    <p className="text-xs text-marine-text-secondary line-clamp-1">
                      {product.description}
                    </p>
                  </div>
                </td>
                <td className="px-4 py-3">
                  <Badge variant="outline" className="text-xs">
                    {product.industry_name || 'Unknown'}
                  </Badge>
                </td>
                <td className="px-4 py-3">
                  <Badge variant="secondary" className="text-xs">
                    {product.plant_part_name || 'Unknown'}
                  </Badge>
                </td>
                <td className="px-4 py-3">
                  <span className="text-sm text-marine-text-secondary">
                    {product.commercialization_stage || 'N/A'}
                  </span>
                </td>
                <td className="px-4 py-3">
                  <span className="text-sm text-marine-text-secondary">
                    {product.created_at 
                      ? new Date(product.created_at).toLocaleDateString()
                      : 'N/A'
                    }
                  </span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="p-4 border-t border-marine-border flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              className="gap-1"
            >
              <ChevronLeft className="h-4 w-4" />
              Previous
            </Button>
            
            <div className="flex items-center gap-1">
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                let pageNum;
                if (totalPages <= 5) {
                  pageNum = i + 1;
                } else if (currentPage <= 3) {
                  pageNum = i + 1;
                } else if (currentPage >= totalPages - 2) {
                  pageNum = totalPages - 4 + i;
                } else {
                  pageNum = currentPage - 2 + i;
                }
                
                return (
                  <Button
                    key={i}
                    variant={pageNum === currentPage ? "default" : "outline"}
                    size="sm"
                    onClick={() => setCurrentPage(pageNum)}
                    className="w-8 h-8 p-0"
                  >
                    {pageNum}
                  </Button>
                );
              })}
            </div>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
              className="gap-1"
            >
              Next
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
          
          <span className="text-sm text-marine-text-secondary">
            Page {currentPage} of {totalPages}
          </span>
        </div>
      )}
    </div>
  );
}