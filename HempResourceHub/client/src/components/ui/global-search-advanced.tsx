import React, { useState, useEffect, useMemo, useRef } from "react";
import { useLocation } from "wouter";
import { Search, Filter, X, Sparkles, Mic, Camera, ArrowRight, Clock, TrendingUp } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { <PERSON><PERSON>, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet";
import { Slider } from "@/components/ui/slider";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { useAllHempProducts } from "@/hooks/use-product-data";
import { usePlantParts, useIndustries } from "@/hooks/use-plant-data";
import { cn } from "@/lib/utils";
import { componentStyles } from "@/lib/design-system";

interface SearchSuggestion {
  id: string;
  type: 'product' | 'industry' | 'plantPart' | 'recent' | 'trending';
  text: string;
  description?: string;
  icon: React.ReactNode;
  metadata?: any;
}

interface AdvancedFilters {
  trlRange: [number, number];
  sustainabilityImpact: string[];
  marketSize: string[];
  geographicRelevance: string[];
  regulatoryStatus: string[];
  dateRange: string;
  marketEntryYear: string;
}

interface GlobalSearchAdvancedProps {
  placeholder?: string;
  onSearch?: (query: string, filters?: AdvancedFilters) => void;
  onSuggestionSelect?: (suggestion: SearchSuggestion) => void;
  className?: string;
  showVoiceSearch?: boolean;
  showImageSearch?: boolean;
  showAISuggestions?: boolean;
}

export function GlobalSearchAdvanced({
  placeholder = "Search hemp products, applications, industries, or ask a question...",
  onSearch,
  onSuggestionSelect,
  className,
  showVoiceSearch = true,
  showImageSearch = true,
  showAISuggestions = true
}: GlobalSearchAdvancedProps) {
  const [location, setLocation] = useLocation();
  const [query, setQuery] = useState("");
  const [isFocused, setIsFocused] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [showFilters, setShowFilters] = useState(false);
  
  const searchRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Advanced filters state
  const [filters, setFilters] = useState<AdvancedFilters>({
    trlRange: [1, 9],
    sustainabilityImpact: [],
    marketSize: [],
    geographicRelevance: [],
    regulatoryStatus: [],
    dateRange: 'all',
    marketEntryYear: 'all'
  });

  // Data hooks
  const { data: products } = useAllHempProducts();
  const { data: plantParts } = usePlantParts();
  const { data: industries } = useIndustries();

  // Load recent searches from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('hemp-recent-searches');
    if (saved) {
      setRecentSearches(JSON.parse(saved));
    }
  }, []);

  // Trending searches (could be dynamic from analytics)
  const trendingSearches = [
    "Hemp fiber textiles",
    "CBD products", 
    "Hemp building materials",
    "Sustainable packaging",
    "Hemp bioplastics"
  ];

  // Product suggestions based on search query
  const productSuggestions = useMemo<SearchSuggestion[]>(() => {
    if (!products || !query.trim()) return [];

    return products
      .filter(product => 
        product.name.toLowerCase().includes(query.toLowerCase()) ||
        product.description?.toLowerCase().includes(query.toLowerCase())
      )
      .slice(0, 4)
      .map(product => ({
        id: `product-${product.id}`,
        type: 'product' as const,
        text: product.name,
        description: product.description?.slice(0, 80) + '...',
        icon: <Search className="h-4 w-4 text-gray-400" />,
        metadata: { productId: product.id }
      }));
  }, [products, query]);

  // Industry suggestions
  const industrySuggestions = useMemo<SearchSuggestion[]>(() => {
    if (!industries || !query.trim()) return [];

    return industries
      .filter(industry => 
        industry.name.toLowerCase().includes(query.toLowerCase())
      )
      .slice(0, 3)
      .map(industry => ({
        id: `industry-${industry.id}`,
        type: 'industry' as const,
        text: industry.name,
        description: `Explore ${industry.name} applications`,
        icon: <TrendingUp className="h-4 w-4 text-purple-400" />,
        metadata: { industryId: industry.id }
      }));
  }, [industries, query]);

  // Plant part suggestions
  const plantPartSuggestions = useMemo<SearchSuggestion[]>(() => {
    if (!plantParts || !query.trim()) return [];

    return plantParts
      .filter(part => 
        part.name.toLowerCase().includes(query.toLowerCase())
      )
      .slice(0, 3)
      .map(part => ({
        id: `plantpart-${part.id}`,
        type: 'plantPart' as const,
        text: part.name,
        description: `Products using ${part.name}`,
        icon: <Sparkles className="h-4 w-4 text-green-400" />,
        metadata: { plantPartId: part.id }
      }));
  }, [plantParts, query]);

  // Recent search suggestions
  const recentSuggestions = useMemo<SearchSuggestion[]>(() => {
    if (query.trim()) return [];
    
    return recentSearches.slice(0, 5).map((search, index) => ({
      id: `recent-${index}`,
      type: 'recent' as const,
      text: search,
      icon: <Clock className="h-4 w-4 text-gray-400" />,
    }));
  }, [recentSearches, query]);

  // Trending suggestions
  const trendingSuggestions = useMemo<SearchSuggestion[]>(() => {
    if (query.trim() || recentSearches.length > 0) return [];
    
    return trendingSearches.map((search, index) => ({
      id: `trending-${index}`,
      type: 'trending' as const,
      text: search,
      icon: <TrendingUp className="h-4 w-4 text-purple-400" />,
    }));
  }, [query, recentSearches]);

  // Combined suggestions
  const allSuggestions = useMemo(() => {
    return [
      ...productSuggestions,
      ...industrySuggestions,
      ...plantPartSuggestions,
      ...recentSuggestions,
      ...trendingSuggestions
    ];
  }, [productSuggestions, industrySuggestions, plantPartSuggestions, recentSuggestions, trendingSuggestions]);

  const handleSearch = (searchQuery: string = query) => {
    if (!searchQuery.trim()) return;

    // Save to recent searches
    const newRecent = [searchQuery, ...recentSearches.filter(s => s !== searchQuery)].slice(0, 10);
    setRecentSearches(newRecent);
    localStorage.setItem('hemp-recent-searches', JSON.stringify(newRecent));

    // Perform search
    onSearch?.(searchQuery, filters);
    setLocation(`/hemp-dex?search=${encodeURIComponent(searchQuery)}`);
    setQuery("");
    setIsFocused(false);
  };

  const handleSuggestionClick = (suggestion: SearchSuggestion) => {
    if (suggestion.type === 'product') {
      setLocation(`/product/${suggestion.metadata.productId}`);
    } else {
      handleSearch(suggestion.text);
    }
    onSuggestionSelect?.(suggestion);
  };

  return (
    <div className={cn("relative w-full max-w-4xl mx-auto", className)} ref={searchRef}>
      {/* Main Search Input */}
      <div className={cn(
        "relative flex items-center",
        componentStyles.interface.card,
        "rounded-2xl transition-all duration-300",
        isFocused && "ring-2 ring-purple-500/50 border-purple-500/50"
      )}>
        <Search className="absolute left-4 h-5 w-5 text-gray-400" />
        
        <Input
          ref={inputRef}
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setTimeout(() => setIsFocused(false), 200)}
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              e.preventDefault();
              handleSearch();
            }
          }}
          placeholder={placeholder}
          className="pl-12 pr-32 py-4 text-lg bg-transparent border-none focus:ring-0 text-white placeholder:text-gray-400"
        />

        {/* Action Buttons */}
        <div className="absolute right-2 flex items-center gap-2">
          {showVoiceSearch && (
            <Button
              size="sm"
              variant="ghost"
              className={cn(
                "h-8 w-8 p-0 rounded-full",
                isListening && "bg-red-500/20 text-red-400"
              )}
              onClick={() => setIsListening(!isListening)}
            >
              <Mic className="h-4 w-4" />
            </Button>
          )}
          
          {showImageSearch && (
            <Button
              size="sm"
              variant="ghost"
              className="h-8 w-8 p-0 rounded-full"
            >
              <Camera className="h-4 w-4" />
            </Button>
          )}

          <Sheet open={showFilters} onOpenChange={setShowFilters}>
            <SheetTrigger asChild>
              <Button
                size="sm"
                variant="ghost"
                className={cn(
                  "h-8 w-8 p-0 rounded-full",
                  Object.entries(filters).some(([key, value]) => {
                    if (Array.isArray(value)) return value.length > 0;
                    if (key === 'trlRange') return JSON.stringify(value) !== JSON.stringify([1, 9]);
                    return value !== 'all';
                  }) && "bg-purple-500/20 text-purple-400"
                )}
              >
                <Filter className="h-4 w-4" />
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-96 bg-black/95 border-gray-800">
              <SheetHeader>
                <SheetTitle className="text-white">Advanced Filters</SheetTitle>
              </SheetHeader>

              <div className="space-y-6 mt-6">
                {/* Technology Readiness Level */}
                <div className="space-y-3">
                  <label className="text-sm font-medium text-white">
                    Technology Readiness Level (TRL)
                  </label>
                  <div className="px-3">
                    <Slider
                      value={filters.trlRange}
                      onValueChange={(value) => setFilters(prev => ({ ...prev, trlRange: value as [number, number] }))}
                      max={9}
                      min={1}
                      step={1}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-gray-400 mt-1">
                      <span>TRL {filters.trlRange[0]}</span>
                      <span>TRL {filters.trlRange[1]}</span>
                    </div>
                  </div>
                </div>

                {/* Sustainability Impact */}
                <div className="space-y-3">
                  <label className="text-sm font-medium text-white">
                    Sustainability Impact
                  </label>
                  <div className="space-y-2">
                    {['High Environmental Benefit', 'Carbon Negative', 'Biodegradable', 'Renewable Resource'].map((impact) => (
                      <div key={impact} className="flex items-center space-x-2">
                        <Checkbox
                          id={impact}
                          checked={filters.sustainabilityImpact.includes(impact)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setFilters(prev => ({
                                ...prev,
                                sustainabilityImpact: [...prev.sustainabilityImpact, impact]
                              }));
                            } else {
                              setFilters(prev => ({
                                ...prev,
                                sustainabilityImpact: prev.sustainabilityImpact.filter(i => i !== impact)
                              }));
                            }
                          }}
                          className="border-gray-600 data-[state=checked]:bg-green-500"
                        />
                        <label htmlFor={impact} className="text-sm text-gray-300">
                          {impact}
                        </label>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Market Size */}
                <div className="space-y-3">
                  <label className="text-sm font-medium text-white">
                    Market Size
                  </label>
                  <Select
                    value={filters.marketSize[0] || ''}
                    onValueChange={(value) => setFilters(prev => ({ ...prev, marketSize: value ? [value] : [] }))}
                  >
                    <SelectTrigger className="bg-black/50 border-gray-700 text-white">
                      <SelectValue placeholder="Select market size" />
                    </SelectTrigger>
                    <SelectContent className="bg-black border-gray-700">
                      <SelectItem value="emerging">Emerging (&lt;$100M)</SelectItem>
                      <SelectItem value="growing">Growing ($100M-$1B)</SelectItem>
                      <SelectItem value="established">Established ($1B+)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Geographic Relevance */}
                <div className="space-y-3">
                  <label className="text-sm font-medium text-white">
                    Geographic Relevance
                  </label>
                  <div className="space-y-2">
                    {['North America', 'Europe', 'Asia-Pacific', 'Global'].map((region) => (
                      <div key={region} className="flex items-center space-x-2">
                        <Checkbox
                          id={region}
                          checked={filters.geographicRelevance.includes(region)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setFilters(prev => ({
                                ...prev,
                                geographicRelevance: [...prev.geographicRelevance, region]
                              }));
                            } else {
                              setFilters(prev => ({
                                ...prev,
                                geographicRelevance: prev.geographicRelevance.filter(r => r !== region)
                              }));
                            }
                          }}
                          className="border-gray-600 data-[state=checked]:bg-purple-500"
                        />
                        <label htmlFor={region} className="text-sm text-gray-300">
                          {region}
                        </label>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Regulatory Status */}
                <div className="space-y-3">
                  <label className="text-sm font-medium text-white">
                    Regulatory Status
                  </label>
                  <div className="space-y-2">
                    {['FDA Approved', 'USDA Certified', 'EU Compliant', 'Pending Approval'].map((status) => (
                      <div key={status} className="flex items-center space-x-2">
                        <Checkbox
                          id={status}
                          checked={filters.regulatoryStatus.includes(status)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setFilters(prev => ({
                                ...prev,
                                regulatoryStatus: [...prev.regulatoryStatus, status]
                              }));
                            } else {
                              setFilters(prev => ({
                                ...prev,
                                regulatoryStatus: prev.regulatoryStatus.filter(s => s !== status)
                              }));
                            }
                          }}
                          className="border-gray-600 data-[state=checked]:bg-purple-500"
                        />
                        <label htmlFor={status} className="text-sm text-gray-300">
                          {status}
                        </label>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Date Range */}
                <div className="space-y-3">
                  <label className="text-sm font-medium text-white">
                    Date Range
                  </label>
                  <Select
                    value={filters.dateRange}
                    onValueChange={(value) => setFilters(prev => ({ ...prev, dateRange: value }))}
                  >
                    <SelectTrigger className="bg-black/50 border-gray-700 text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-black border-gray-700">
                      <SelectItem value="all">All Time</SelectItem>
                      <SelectItem value="last-month">Last Month</SelectItem>
                      <SelectItem value="last-3-months">Last 3 Months</SelectItem>
                      <SelectItem value="last-year">Last Year</SelectItem>
                      <SelectItem value="last-2-years">Last 2 Years</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Filter Actions */}
                <div className="flex gap-3 pt-4 border-t border-gray-800">
                  <Button
                    onClick={() => setFilters({
                      trlRange: [1, 9],
                      sustainabilityImpact: [],
                      marketSize: [],
                      geographicRelevance: [],
                      regulatoryStatus: [],
                      dateRange: 'all',
                      marketEntryYear: 'all'
                    })}
                    variant="ghost"
                    className="flex-1 border border-gray-700"
                  >
                    Reset
                  </Button>
                  <Button
                    onClick={() => {
                      handleSearch();
                      setShowFilters(false);
                    }}
                    className="flex-1 bg-purple-500 hover:bg-purple-600"
                  >
                    Apply Filters
                  </Button>
                </div>
              </div>
            </SheetContent>
          </Sheet>

          <Button
            size="sm"
            className="h-8 px-4 bg-purple-500 hover:bg-purple-600"
            onClick={() => handleSearch()}
          >
            Search
          </Button>
        </div>
      </div>

      {/* Search Suggestions Dropdown */}
      {isFocused && allSuggestions.length > 0 && (
        <div className={cn(
          "absolute top-full left-0 right-0 mt-2 z-50",
          componentStyles.interface.card,
          "rounded-xl shadow-2xl max-h-96 overflow-y-auto"
        )}>
          <div className="p-2">
            {allSuggestions.map((suggestion, index) => (
              <button
                key={suggestion.id}
                onClick={() => handleSuggestionClick(suggestion)}
                className={cn(
                  "w-full flex items-center gap-3 p-3 rounded-lg text-left transition-colors",
                  "hover:bg-gray-800/50",
                  selectedIndex === index && "bg-gray-800/50"
                )}
              >
                {suggestion.icon}
                <div className="flex-1 min-w-0">
                  <div className="text-white font-medium truncate">
                    {suggestion.text}
                  </div>
                  {suggestion.description && (
                    <div className="text-gray-400 text-sm truncate">
                      {suggestion.description}
                    </div>
                  )}
                </div>
                <ArrowRight className="h-4 w-4 text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity" />
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
