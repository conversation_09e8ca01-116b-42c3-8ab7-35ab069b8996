import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  Clock, 
  X, 
  Search,
  Star,
  StarOff,
  Trash2
} from "lucide-react";
import { cn } from "@/lib/utils";

interface SearchHistoryItem {
  id: string;
  query: string;
  timestamp: Date;
  resultCount?: number;
  filters?: Record<string, any>;
  isSaved?: boolean;
}

interface SearchHistoryProps {
  onSelectSearch: (item: SearchHistoryItem) => void;
  maxItems?: number;
  className?: string;
}

export function SearchHistory({ 
  onSelectSearch, 
  maxItems = 20,
  className 
}: SearchHistoryProps) {
  const [history, setHistory] = useState<SearchHistoryItem[]>([]);
  const [savedSearches, setSavedSearches] = useState<SearchHistoryItem[]>([]);

  // Load from localStorage on mount
  useEffect(() => {
    const savedHistory = localStorage.getItem('hemp-search-history');
    const saved = localStorage.getItem('hemp-saved-searches');
    
    if (savedHistory) {
      try {
        const parsed = JSON.parse(savedHistory);
        setHistory(parsed.map((item: any) => ({
          ...item,
          timestamp: new Date(item.timestamp)
        })));
      } catch (e) {
        console.error('Failed to parse search history:', e);
      }
    }

    if (saved) {
      try {
        const parsed = JSON.parse(saved);
        setSavedSearches(parsed.map((item: any) => ({
          ...item,
          timestamp: new Date(item.timestamp)
        })));
      } catch (e) {
        console.error('Failed to parse saved searches:', e);
      }
    }
  }, []);

  // Save to localStorage whenever history changes
  useEffect(() => {
    localStorage.setItem('hemp-search-history', JSON.stringify(history));
  }, [history]);

  useEffect(() => {
    localStorage.setItem('hemp-saved-searches', JSON.stringify(savedSearches));
  }, [savedSearches]);

  const addToHistory = (item: Omit<SearchHistoryItem, 'id'>) => {
    const newItem: SearchHistoryItem = {
      ...item,
      id: `search-${Date.now()}`,
      timestamp: new Date()
    };

    setHistory(prev => {
      // Remove duplicates
      const filtered = prev.filter(h => h.query !== item.query);
      // Add new item at the beginning
      const updated = [newItem, ...filtered];
      // Limit to maxItems
      return updated.slice(0, maxItems);
    });
  };

  const removeFromHistory = (id: string) => {
    setHistory(prev => prev.filter(item => item.id !== id));
  };

  const toggleSaved = (item: SearchHistoryItem) => {
    if (item.isSaved) {
      // Remove from saved
      setSavedSearches(prev => prev.filter(s => s.id !== item.id));
      setHistory(prev => 
        prev.map(h => h.id === item.id ? { ...h, isSaved: false } : h)
      );
    } else {
      // Add to saved
      const savedItem = { ...item, isSaved: true };
      setSavedSearches(prev => [...prev, savedItem]);
      setHistory(prev => 
        prev.map(h => h.id === item.id ? savedItem : h)
      );
    }
  };

  const clearHistory = () => {
    setHistory([]);
    localStorage.removeItem('hemp-search-history');
  };

  const formatDate = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    if (days < 7) return `${days}d ago`;
    return date.toLocaleDateString();
  };

  const formatFilters = (filters?: Record<string, any>) => {
    if (!filters) return null;
    
    const activeFilters = [];
    if (filters.plantParts?.length > 0) {
      activeFilters.push(`${filters.plantParts.length} plant parts`);
    }
    if (filters.industries?.length > 0) {
      activeFilters.push(`${filters.industries.length} industries`);
    }
    if (filters.stage && filters.stage !== 'all') {
      activeFilters.push(filters.stage);
    }
    
    return activeFilters.length > 0 ? activeFilters.join(', ') : null;
  };

  // Expose addToHistory method via window for external use
  useEffect(() => {
    (window as any).addSearchToHistory = addToHistory;
    return () => {
      delete (window as any).addSearchToHistory;
    };
  }, []);

  return (
    <div className={cn("bg-marine-card rounded-xl p-4", className)}>
      <div className="space-y-4">
        {/* Header */}
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-white flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Search History
          </h3>
          {history.length > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearHistory}
              className="text-red-400 hover:text-red-300"
            >
              <Trash2 className="h-4 w-4 mr-1" />
              Clear
            </Button>
          )}
        </div>

        {/* Saved Searches */}
        {savedSearches.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-marine-text-secondary flex items-center gap-2">
              <Star className="h-4 w-4" />
              Saved Searches
            </h4>
            <div className="space-y-1">
              {savedSearches.map(item => (
                <SearchHistoryItemComponent
                  key={item.id}
                  item={item}
                  onSelect={() => onSelectSearch(item)}
                  onToggleSave={() => toggleSaved(item)}
                  onRemove={() => removeFromHistory(item.id)}
                  formatDate={formatDate}
                  formatFilters={formatFilters}
                />
              ))}
            </div>
          </div>
        )}

        {/* Recent Searches */}
        {history.length > 0 ? (
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-marine-text-secondary">
              Recent Searches
            </h4>
            <ScrollArea className="h-64">
              <div className="space-y-1">
                {history.filter(item => !item.isSaved).map(item => (
                  <SearchHistoryItemComponent
                    key={item.id}
                    item={item}
                    onSelect={() => onSelectSearch(item)}
                    onToggleSave={() => toggleSaved(item)}
                    onRemove={() => removeFromHistory(item.id)}
                    formatDate={formatDate}
                    formatFilters={formatFilters}
                  />
                ))}
              </div>
            </ScrollArea>
          </div>
        ) : (
          <div className="text-center py-8 text-marine-text-secondary">
            <Search className="h-12 w-12 mx-auto mb-2 opacity-50" />
            <p>No search history yet</p>
            <p className="text-sm mt-1">Your searches will appear here</p>
          </div>
        )}
      </div>
    </div>
  );
}

interface SearchHistoryItemComponentProps {
  item: SearchHistoryItem;
  onSelect: () => void;
  onToggleSave: () => void;
  onRemove: () => void;
  formatDate: (date: Date) => string;
  formatFilters: (filters?: Record<string, any>) => string | null;
}

function SearchHistoryItemComponent({
  item,
  onSelect,
  onToggleSave,
  onRemove,
  formatDate,
  formatFilters
}: SearchHistoryItemComponentProps) {
  return (
    <div className="group flex items-center gap-2 p-2 rounded-lg hover:bg-marine-card/50 transition-colors">
      <button
        onClick={onSelect}
        className="flex-1 text-left"
      >
        <div className="flex items-center gap-2">
          <Search className="h-4 w-4 text-gray-400 flex-shrink-0" />
          <div className="flex-1 min-w-0">
            <p className="text-white truncate">{item.query}</p>
            <div className="flex items-center gap-2 text-xs text-marine-text-secondary">
              <span>{formatDate(item.timestamp)}</span>
              {item.resultCount !== undefined && (
                <>
                  <span>•</span>
                  <span>{item.resultCount} results</span>
                </>
              )}
              {formatFilters(item.filters) && (
                <>
                  <span>•</span>
                  <span>{formatFilters(item.filters)}</span>
                </>
              )}
            </div>
          </div>
        </div>
      </button>
      
      <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
        <Button
          variant="ghost"
          size="sm"
          onClick={onToggleSave}
          className="h-7 w-7 p-0"
        >
          {item.isSaved ? (
            <Star className="h-4 w-4 text-yellow-400 fill-yellow-400" />
          ) : (
            <StarOff className="h-4 w-4" />
          )}
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={onRemove}
          className="h-7 w-7 p-0 text-red-400 hover:text-red-300"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}