import { cn } from "@/lib/utils";
import { Download, Eye, ExternalLink } from "lucide-react";

interface DataCardProps {
  thumbnail?: string;
  title: string;
  subtitle?: string;
  metadata: Array<{
    label: string;
    value: string | number;
  }>;
  badges?: Array<{
    label: string;
    variant?: "default" | "success" | "warning" | "error";
  }>;
  onView?: () => void;
  onDownload?: () => void;
  href?: string;
  className?: string;
}

export function DataCard({
  thumbnail,
  title,
  subtitle,
  metadata,
  badges,
  onView,
  onDownload,
  href,
  className
}: DataCardProps) {
  const badgeVariants = {
    default: "bg-gray-500/10 text-gray-400 border-gray-700",
    success: "bg-green-500/10 text-green-500 border-green-500/20",
    warning: "bg-yellow-500/10 text-yellow-500 border-yellow-500/20",
    error: "bg-red-500/10 text-red-500 border-red-500/20"
  };

  return (
    <div className={cn(
      "group rounded-xl bg-black/50 backdrop-blur-sm border border-gray-800 overflow-hidden hover:border-gray-700 transition-all",
      className
    )}>
      {/* Thumbnail */}
      {thumbnail && (
        <div className="aspect-video relative overflow-hidden bg-gray-900">
          <img 
            src={thumbnail} 
            alt={title}
            className="h-full w-full object-cover group-hover:scale-105 transition-transform duration-300"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity" />
        </div>
      )}

      {/* Content */}
      <div className="p-6">
        {/* Header */}
        <div className="mb-4">
          <h3 className="text-lg font-semibold text-white line-clamp-1">{title}</h3>
          {subtitle && (
            <p className="mt-1 text-sm text-gray-400 line-clamp-2">{subtitle}</p>
          )}
        </div>

        {/* Badges */}
        {badges && badges.length > 0 && (
          <div className="mb-4 flex flex-wrap gap-2">
            {badges.map((badge, index) => (
              <span
                key={index}
                className={cn(
                  "rounded-full border px-2.5 py-0.5 text-xs font-medium",
                  badgeVariants[badge.variant || "default"]
                )}
              >
                {badge.label}
              </span>
            ))}
          </div>
        )}

        {/* Metadata */}
        <div className="space-y-2">
          {metadata.map((item, index) => (
            <div key={index} className="flex justify-between text-sm">
              <span className="text-gray-500">{item.label}</span>
              <span className="text-gray-300 font-medium">{item.value}</span>
            </div>
          ))}
        </div>

        {/* Actions */}
        <div className="mt-6 flex gap-2">
          {onView && (
            <button
              onClick={onView}
              className="flex-1 flex items-center justify-center gap-2 rounded-lg bg-green-500/10 px-3 py-2 text-sm font-medium text-green-500 hover:bg-green-500/20 transition-colors"
            >
              <Eye className="h-4 w-4" />
              View
            </button>
          )}
          
          {href && (
            <a
              href={href}
              target="_blank"
              rel="noopener noreferrer"
              className="flex-1 flex items-center justify-center gap-2 rounded-lg bg-green-500/10 px-3 py-2 text-sm font-medium text-green-500 hover:bg-green-500/20 transition-colors"
            >
              <ExternalLink className="h-4 w-4" />
              View
            </a>
          )}
          
          {onDownload && (
            <button
              onClick={onDownload}
              className="flex items-center justify-center rounded-lg bg-gray-800 p-2 text-gray-400 hover:bg-gray-700 hover:text-white transition-colors"
            >
              <Download className="h-4 w-4" />
            </button>
          )}
        </div>
      </div>
    </div>
  );
}