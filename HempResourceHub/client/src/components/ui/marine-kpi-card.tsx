import { cn } from "@/lib/utils";

interface MarineKPICardProps {
  algorithm: string;
  avgTime: string;
  efficiency: string;
  detections: string;
  className?: string;
}

export function MarineKPICard({ 
  algorithm, 
  avgTime, 
  efficiency,
  detections,
  className 
}: MarineKPICardProps) {
  return (
    <div className={cn(
      "rounded-xl bg-marine-card p-6 space-y-4",
      className
    )}>
      <div>
        <h3 className="text-lg font-medium text-white mb-1">{algorithm}</h3>
        <p className="text-sm text-marine-text-muted">{detections} detections</p>
      </div>
      
      <div className="space-y-3">
        <div>
          <p className="text-xs text-marine-text-secondary mb-1">Avg Time:</p>
          <p className="text-2xl font-bold text-white">{avgTime}</p>
        </div>
        
        <div>
          <p className="text-xs text-marine-text-secondary mb-1">Efficiency:</p>
          <p className="text-lg font-semibold text-marine-accent">{efficiency}</p>
        </div>
      </div>
    </div>
  );
}