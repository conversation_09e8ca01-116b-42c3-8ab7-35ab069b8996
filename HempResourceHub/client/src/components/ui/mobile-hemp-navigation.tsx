import React, { useState } from "react";
import { <PERSON>, useLocation } from "wouter";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>et, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, SheetTrigger } from "@/components/ui/sheet";
import { Badge } from "@/components/ui/badge";
import { 
  Menu,
  X,
  Home,
  Search,
  Database,
  Building2,
  Leaf,
  Factory,
  BarChart3,
  Users,
  Settings,
  Bookmark,
  TrendingUp,
  Globe,
  ChevronRight
} from "lucide-react";
import { cn } from "@/lib/utils";
import { componentStyles } from "@/lib/design-system";

interface NavigationItem {
  label: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  badge?: string;
  description?: string;
  isNew?: boolean;
}

interface NavigationSection {
  title: string;
  items: NavigationItem[];
}

const navigationSections: NavigationSection[] = [
  {
    title: "Discover",
    items: [
      {
        label: "Home",
        href: "/",
        icon: Home,
        description: "Welcome to HempQuarterz®"
      },
      {
        label: "Hemp Discovery Hub",
        href: "/hemp-discovery",
        icon: Search,
        description: "Advanced product search & discovery",
        isNew: true
      },
      {
        label: "All Products",
        href: "/hemp-dex-unified",
        icon: Database,
        description: "Browse all hemp products"
      },
      {
        label: "Enhanced Search",
        href: "/search",
        icon: TrendingUp,
        description: "Smart search with filters"
      }
    ]
  },
  {
    title: "Categories",
    items: [
      {
        label: "Plant Parts",
        href: "/plant-parts",
        icon: Leaf,
        description: "Seeds, Fiber, Hurds, Leaves & more"
      },
      {
        label: "Industries",
        href: "/industries",
        icon: Factory,
        description: "Construction, Textiles, Food & more"
      },
      {
        label: "Companies",
        href: "/companies",
        icon: Building2,
        description: "Hemp industry companies"
      }
    ]
  },
  {
    title: "Analytics",
    items: [
      {
        label: "Dashboard",
        href: "/dashboard",
        icon: BarChart3,
        description: "Market insights & trends"
      },
      {
        label: "Research",
        href: "/research",
        icon: Users,
        description: "Scientific papers & studies"
      }
    ]
  }
];

interface MobileHempNavigationProps {
  className?: string;
}

export function MobileHempNavigation({ className }: MobileHempNavigationProps) {
  const [location] = useLocation();
  const [isOpen, setIsOpen] = useState(false);

  const isActive = (href: string) => {
    if (href === "/") {
      return location === "/";
    }
    return location.startsWith(href);
  };

  const handleNavigation = (href: string) => {
    setIsOpen(false);
    // Small delay to allow sheet to close before navigation
    setTimeout(() => {
      window.location.href = href;
    }, 150);
  };

  return (
    <div className={cn("lg:hidden", className)}>
      <Sheet open={isOpen} onOpenChange={setIsOpen}>
        <SheetTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className="h-10 w-10 p-0 text-white hover:bg-gray-800"
          >
            <Menu className="h-5 w-5" />
            <span className="sr-only">Open navigation menu</span>
          </Button>
        </SheetTrigger>
        
        <SheetContent 
          side="left" 
          className="w-80 bg-black/95 backdrop-blur-md border-gray-800 p-0"
        >
          <SheetHeader className="p-6 border-b border-gray-800">
            <div className="flex items-center justify-between">
              <SheetTitle className="text-white text-lg font-bold">
                <span className="bg-gradient-to-r from-green-400 to-purple-400 bg-clip-text text-transparent">
                  HempQuarterz®
                </span>
              </SheetTitle>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsOpen(false)}
                className="h-8 w-8 p-0 text-gray-400 hover:text-white"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            <p className="text-sm text-gray-400 text-left">
              Industrial Hemp Database & Knowledge Directory
            </p>
          </SheetHeader>

          <div className="flex-1 overflow-y-auto">
            <nav className="p-4 space-y-6">
              {navigationSections.map((section) => (
                <div key={section.title}>
                  <h3 className="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3">
                    {section.title}
                  </h3>
                  
                  <div className="space-y-1">
                    {section.items.map((item) => {
                      const Icon = item.icon;
                      const active = isActive(item.href);
                      
                      return (
                        <button
                          key={item.href}
                          onClick={() => handleNavigation(item.href)}
                          className={cn(
                            "w-full flex items-center gap-3 px-3 py-3 rounded-lg text-left transition-all duration-200",
                            "hover:bg-gray-800/50",
                            active 
                              ? "bg-purple-500/20 text-purple-300 border border-purple-500/30" 
                              : "text-gray-300 hover:text-white"
                          )}
                        >
                          <Icon className={cn(
                            "h-5 w-5 flex-shrink-0",
                            active ? "text-purple-400" : "text-gray-400"
                          )} />
                          
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2">
                              <span className="font-medium truncate">
                                {item.label}
                              </span>
                              
                              {item.isNew && (
                                <Badge className="bg-green-500/20 text-green-400 border-green-500/30 text-xs px-1.5 py-0.5">
                                  New
                                </Badge>
                              )}
                              
                              {item.badge && (
                                <Badge variant="secondary" className="text-xs px-1.5 py-0.5">
                                  {item.badge}
                                </Badge>
                              )}
                            </div>
                            
                            {item.description && (
                              <p className="text-xs text-gray-500 truncate mt-0.5">
                                {item.description}
                              </p>
                            )}
                          </div>
                          
                          <ChevronRight className="h-4 w-4 text-gray-500 flex-shrink-0" />
                        </button>
                      );
                    })}
                  </div>
                </div>
              ))}
            </nav>

            {/* Quick Stats */}
            <div className="p-4 border-t border-gray-800 mt-6">
              <h3 className="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3">
                Quick Stats
              </h3>
              
              <div className="grid grid-cols-2 gap-3">
                <div className={cn(componentStyles.interface.card, "p-3 text-center")}>
                  <Database className="h-5 w-5 text-green-400 mx-auto mb-1" />
                  <div className="text-lg font-bold text-white">5,196+</div>
                  <div className="text-xs text-gray-400">Products</div>
                </div>
                
                <div className={cn(componentStyles.interface.card, "p-3 text-center")}>
                  <Factory className="h-5 w-5 text-purple-400 mx-auto mb-1" />
                  <div className="text-lg font-bold text-white">42+</div>
                  <div className="text-xs text-gray-400">Industries</div>
                </div>
                
                <div className={cn(componentStyles.interface.card, "p-3 text-center")}>
                  <Building2 className="h-5 w-5 text-blue-400 mx-auto mb-1" />
                  <div className="text-lg font-bold text-white">204+</div>
                  <div className="text-xs text-gray-400">Companies</div>
                </div>
                
                <div className={cn(componentStyles.interface.card, "p-3 text-center")}>
                  <Globe className="h-5 w-5 text-green-400 mx-auto mb-1" />
                  <div className="text-lg font-bold text-white">24/7</div>
                  <div className="text-xs text-gray-400">Live Data</div>
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="p-4 border-t border-gray-800">
              <div className="flex items-center justify-between text-xs text-gray-500">
                <span>© 2024 HempQuarterz®</span>
                <Link href="/about" className="hover:text-gray-300">
                  About
                </Link>
              </div>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    </div>
  );
}

// Quick access floating action button for mobile
export function MobileQuickAccess() {
  const [location] = useLocation();
  
  // Don't show on home page
  if (location === "/") return null;

  return (
    <div className="fixed bottom-6 right-6 lg:hidden z-50">
      <Link href="/hemp-discovery">
        <Button
          size="lg"
          className={cn(
            "h-14 w-14 rounded-full shadow-2xl",
            "bg-gradient-to-r from-green-500 to-purple-500",
            "hover:from-green-600 hover:to-purple-600",
            "border-2 border-white/20"
          )}
        >
          <Search className="h-6 w-6" />
          <span className="sr-only">Quick Search</span>
        </Button>
      </Link>
    </div>
  );
}

// Mobile bottom navigation bar
export function MobileBottomNavigation() {
  const [location] = useLocation();

  const bottomNavItems = [
    { label: "Home", href: "/", icon: Home },
    { label: "Discover", href: "/hemp-discovery", icon: Search },
    { label: "Products", href: "/hemp-dex-unified", icon: Database },
    { label: "Companies", href: "/companies", icon: Building2 },
    { label: "Dashboard", href: "/dashboard", icon: BarChart3 }
  ];

  const isActive = (href: string) => {
    if (href === "/") {
      return location === "/";
    }
    return location.startsWith(href);
  };

  return (
    <div className="fixed bottom-0 left-0 right-0 lg:hidden z-40 bg-black/95 backdrop-blur-md border-t border-gray-800">
      <div className="flex items-center justify-around px-2 py-2">
        {bottomNavItems.map((item) => {
          const Icon = item.icon;
          const active = isActive(item.href);
          
          return (
            <Link key={item.href} href={item.href}>
              <button
                className={cn(
                  "flex flex-col items-center gap-1 px-3 py-2 rounded-lg transition-colors",
                  active 
                    ? "text-purple-400" 
                    : "text-gray-400 hover:text-white"
                )}
              >
                <Icon className="h-5 w-5" />
                <span className="text-xs font-medium">
                  {item.label}
                </span>
              </button>
            </Link>
          );
        })}
      </div>
    </div>
  );
}
