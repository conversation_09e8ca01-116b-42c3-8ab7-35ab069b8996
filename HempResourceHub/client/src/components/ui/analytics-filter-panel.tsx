import { X, RotateCcw } from "lucide-react";
import { cn } from "@/lib/utils";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle } from "@/components/ui/sheet";

export interface FilterState {
  plantParts: string[];
  industries: string[];
  processingMethods: string[];
  productStatus: string;
  priceRange: [number, number];
  sustainabilityScore: [number, number];
}

interface AnalyticsFilterPanelProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  filters: FilterState;
  onFiltersChange: (filters: FilterState) => void;
}

const defaultFilters: FilterState = {
  plantParts: [],
  industries: [],
  processingMethods: [],
  productStatus: "all",
  priceRange: [0, 10000],
  sustainabilityScore: [0, 100],
};

const plantPartOptions = ["Seed", "Fiber", "Flower", "Stalk", "Root", "Leaf"];
const industryOptions = ["Textiles", "Construction", "Food & Beverage", "Medical", "Cosmetics", "Automotive"];
const processingOptions = ["Cold-Pressed", "Decortication", "Retting", "Extraction", "Milling"];
const statusOptions = [
  { value: "all", label: "All Products" },
  { value: "active", label: "Active" },
  { value: "prototype", label: "Prototype" },
  { value: "discontinued", label: "Discontinued" },
];

export function AnalyticsFilterPanel({ open, onOpenChange, filters, onFiltersChange }: AnalyticsFilterPanelProps) {
  const handleReset = () => {
    onFiltersChange(defaultFilters);
  };

  const handleFilterChange = (updates: Partial<FilterState>) => {
    const newFilters = { ...filters, ...updates };
    onFiltersChange(newFilters);
  };

  const toggleArrayFilter = (key: keyof FilterState, value: string) => {
    const current = (filters[key] as string[]) || [];
    const updated = current.includes(value) 
      ? current.filter(v => v !== value)
      : [...current, value];
    handleFilterChange({ [key]: updated });
  };

  const activeFilterCount = 
    filters.plantParts.length + 
    filters.industries.length + 
    filters.processingMethods.length +
    (filters.productStatus !== "all" ? 1 : 0) +
    (filters.priceRange[0] !== 0 || filters.priceRange[1] !== 10000 ? 1 : 0) +
    (filters.sustainabilityScore[0] !== 0 || filters.sustainabilityScore[1] !== 100 ? 1 : 0);

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent side="right" className="w-[320px] sm:w-[400px] bg-marine-sidebar border-marine-border">
        <SheetHeader>
          <SheetTitle className="flex items-center justify-between text-white">
            Analytics Filters
            {activeFilterCount > 0 && (
              <span className="text-sm font-normal bg-purple-600/20 text-purple-400 px-2 py-1 rounded-full">
                {activeFilterCount} active
              </span>
            )}
          </SheetTitle>
          <SheetDescription className="text-marine-text-secondary">
            Refine your analytics view with filters
          </SheetDescription>
        </SheetHeader>

        <div className="mt-6 space-y-6 max-h-[calc(100vh-200px)] overflow-y-auto">
          {/* Plant Parts */}
          <div>
            <Label className="text-sm font-medium text-marine-text-secondary mb-3 block">Plant Parts</Label>
            <div className="space-y-2">
              {plantPartOptions.map(part => (
                <div key={part} className="flex items-center space-x-2">
                  <Checkbox
                    id={`part-${part}`}
                    checked={(filters.plantParts || []).includes(part)}
                    onCheckedChange={() => toggleArrayFilter("plantParts", part)}
                    className="border-marine-border data-[state=checked]:bg-green-600 data-[state=checked]:border-green-600"
                  />
                  <Label
                    htmlFor={`part-${part}`}
                    className="text-sm text-marine-text-secondary cursor-pointer hover:text-white transition-colors"
                  >
                    {part}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          {/* Industries */}
          <div>
            <Label className="text-sm font-medium text-marine-text-secondary mb-3 block">Industries</Label>
            <div className="space-y-2">
              {industryOptions.map(industry => (
                <div key={industry} className="flex items-center space-x-2">
                  <Checkbox
                    id={`industry-${industry}`}
                    checked={(filters.industries || []).includes(industry)}
                    onCheckedChange={() => toggleArrayFilter("industries", industry)}
                    className="border-marine-border data-[state=checked]:bg-green-600 data-[state=checked]:border-green-600"
                  />
                  <Label
                    htmlFor={`industry-${industry}`}
                    className="text-sm text-marine-text-secondary cursor-pointer hover:text-white transition-colors"
                  >
                    {industry}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          {/* Processing Methods */}
          <div>
            <Label className="text-sm font-medium text-marine-text-secondary mb-3 block">Processing Methods</Label>
            <div className="space-y-2">
              {processingOptions.map(method => (
                <div key={method} className="flex items-center space-x-2">
                  <Checkbox
                    id={`method-${method}`}
                    checked={(filters.processingMethods || []).includes(method)}
                    onCheckedChange={() => toggleArrayFilter("processingMethods", method)}
                    className="border-marine-border data-[state=checked]:bg-green-600 data-[state=checked]:border-green-600"
                  />
                  <Label
                    htmlFor={`method-${method}`}
                    className="text-sm text-marine-text-secondary cursor-pointer hover:text-white transition-colors"
                  >
                    {method}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          {/* Product Status */}
          <div>
            <Label className="text-sm font-medium text-marine-text-secondary mb-3 block">Product Status</Label>
            <Select
              value={filters.productStatus}
              onValueChange={(value) => handleFilterChange({ productStatus: value })}
            >
              <SelectTrigger className="bg-marine-card border-marine-border text-white">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-marine-card border-marine-border">
                {statusOptions.map(option => (
                  <SelectItem key={option.value} value={option.value} className="text-marine-text-secondary hover:text-white">
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Price Range */}
          <div>
            <Label className="text-sm font-medium text-marine-text-secondary mb-3 block">
              Price Range: ${filters.priceRange?.[0] || 0} - ${filters.priceRange?.[1] || 10000}
            </Label>
            <Slider
              value={filters.priceRange || [0, 10000]}
              onValueChange={(value) => handleFilterChange({ priceRange: value as [number, number] })}
              min={0}
              max={10000}
              step={100}
              className="[&_[role=slider]]:bg-green-600"
            />
          </div>

          {/* Sustainability Score */}
          <div>
            <Label className="text-sm font-medium text-marine-text-secondary mb-3 block">
              Sustainability Score: {filters.sustainabilityScore?.[0] || 0} - {filters.sustainabilityScore?.[1] || 100}
            </Label>
            <Slider
              value={filters.sustainabilityScore || [0, 100]}
              onValueChange={(value) => handleFilterChange({ sustainabilityScore: value as [number, number] })}
              min={0}
              max={100}
              step={5}
              className="[&_[role=slider]]:bg-green-600"
            />
          </div>
        </div>

        {/* Actions */}
        <div className="absolute bottom-0 left-0 right-0 p-6 bg-marine-sidebar border-t border-marine-border">
          <div className="flex gap-2">
            <Button
              variant="outline"
              className="flex-1 border-marine-border text-marine-text-secondary hover:text-white hover:bg-marine-card"
              onClick={handleReset}
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset All
            </Button>
            <Button
              className="flex-1 bg-green-600 hover:bg-green-700 text-white"
              onClick={() => onOpenChange(false)}
            >
              Apply Filters
            </Button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}