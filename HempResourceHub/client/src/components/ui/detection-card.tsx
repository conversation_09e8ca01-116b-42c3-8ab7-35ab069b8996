import { cn } from "@/lib/utils";
import { Eye, Download } from "lucide-react";

interface DetectionCardProps {
  algorithm: string;
  detections: string;
  confidence: string;
  coordinates?: string;
  depth?: string;
  dataset?: string;
  thumbnail?: string;
  onView?: () => void;
  onDownload?: () => void;
  className?: string;
}

export function DetectionCard({
  algorithm,
  detections,
  confidence,
  coordinates,
  depth,
  dataset,
  thumbnail,
  onView,
  onDownload,
  className
}: DetectionCardProps) {
  return (
    <div className={cn(
      "rounded-xl bg-marine-card overflow-hidden hover:bg-marine-cardHover transition-colors",
      className
    )}>
      {/* Thumbnail with overlay */}
      <div className="relative h-48 bg-marine-bg">
        {thumbnail ? (
          <img 
            src={thumbnail} 
            alt={algorithm}
            className="h-full w-full object-cover"
          />
        ) : (
          <div className="h-full w-full flex items-center justify-center">
            <div className="text-center">
              <div className="text-5xl font-bold text-white mb-2">{confidence}</div>
              <p className="text-sm text-marine-text-secondary">Click to view details</p>
            </div>
          </div>
        )}
        
        {/* Algorithm badge */}
        <div className="absolute top-3 left-3">
          <span className="px-3 py-1 rounded-full bg-marine-bg/80 backdrop-blur-sm text-xs font-medium text-white">
            {algorithm}
          </span>
        </div>
        
        {/* Detection count */}
        <div className="absolute top-3 right-3">
          <span className="text-sm text-white bg-marine-bg/80 backdrop-blur-sm px-2 py-1 rounded">
            {detections} detections found
          </span>
        </div>
      </div>

      {/* Details */}
      <div className="p-4 space-y-3">
        {coordinates && (
          <div className="flex justify-between text-sm">
            <span className="text-marine-text-secondary">Coordinates:</span>
            <span className="text-white font-mono">{coordinates}</span>
          </div>
        )}
        
        {depth && (
          <div className="flex justify-between text-sm">
            <span className="text-marine-text-secondary">Depth:</span>
            <span className="text-white">{depth}</span>
          </div>
        )}
        
        {dataset && (
          <div className="flex justify-between text-sm">
            <span className="text-marine-text-secondary">Dataset:</span>
            <span className="text-white">{dataset}</span>
          </div>
        )}
        
        {/* Actions */}
        <div className="flex gap-2 pt-2">
          <button
            onClick={onView}
            className="flex-1 flex items-center justify-center gap-2 rounded-lg bg-marine-accent/10 px-3 py-2 text-sm font-medium text-marine-accent hover:bg-marine-accent/20 transition-colors"
          >
            <Eye className="h-4 w-4" />
            View
          </button>
          
          <button
            onClick={onDownload}
            className="flex items-center justify-center rounded-lg bg-marine-border px-3 py-2 text-marine-text-secondary hover:bg-marine-border/80 hover:text-white transition-colors"
          >
            <Download className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  );
}