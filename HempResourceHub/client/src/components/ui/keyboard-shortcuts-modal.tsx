import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON>alogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { X } from "lucide-react";

interface KeyboardShortcut {
  keys: string[];
  description: string;
  category: string;
}

const shortcuts: KeyboardShortcut[] = [
  // Navigation
  { keys: ["Ctrl", "G"], description: "Go to home", category: "Navigation" },
  { keys: ["Ctrl", "P"], description: "Go to products", category: "Navigation" },
  { keys: ["Ctrl", "A"], description: "Go to analytics", category: "Navigation" },
  { keys: ["Tab"], description: "Navigate forward through elements", category: "Navigation" },
  { keys: ["Shift", "Tab"], description: "Navigate backward through elements", category: "Navigation" },
  
  // Search
  { keys: ["/"], description: "Focus search input", category: "Search" },
  { keys: ["Escape"], description: "Clear search / Close modals", category: "Search" },
  { keys: ["Enter"], description: "Submit search", category: "Search" },
  
  // General
  { keys: ["?"], description: "Show keyboard shortcuts", category: "General" },
  { keys: ["Space"], description: "Activate buttons and controls", category: "General" },
  { keys: ["Enter"], description: "Activate links and submit forms", category: "General" },
];

export function KeyboardShortcutsModal() {
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Show modal on ? key
      if (e.key === "?" && e.shiftKey) {
        e.preventDefault();
        setIsOpen(true);
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, []);

  // Group shortcuts by category
  const shortcutsByCategory = shortcuts.reduce((acc, shortcut) => {
    if (!acc[shortcut.category]) {
      acc[shortcut.category] = [];
    }
    acc[shortcut.category].push(shortcut);
    return acc;
  }, {} as Record<string, KeyboardShortcut[]>);

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto bg-marine-card border-marine-border">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold text-white">
            Keyboard Shortcuts
          </DialogTitle>
          <DialogDescription className="text-marine-text-secondary">
            Use these keyboard shortcuts to navigate quickly through the application
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 mt-4">
          {Object.entries(shortcutsByCategory).map(([category, categoryShortcuts]) => (
            <div key={category}>
              <h3 className="text-sm font-semibold text-marine-text-secondary uppercase tracking-wider mb-3">
                {category}
              </h3>
              <div className="space-y-2">
                {categoryShortcuts.map((shortcut, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-3 rounded-lg bg-marine-sidebar/50 hover:bg-marine-sidebar transition-colors"
                  >
                    <span className="text-sm text-white">{shortcut.description}</span>
                    <div className="flex items-center gap-1">
                      {shortcut.keys.map((key, keyIndex) => (
                        <span key={keyIndex} className="flex items-center gap-1">
                          <Badge
                            variant="secondary"
                            className="bg-marine-bg text-marine-text-secondary px-2 py-0.5 text-xs font-mono"
                          >
                            {key}
                          </Badge>
                          {keyIndex < shortcut.keys.length - 1 && (
                            <span className="text-marine-text-muted text-xs">+</span>
                          )}
                        </span>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        <div className="mt-6 p-4 bg-marine-sidebar/30 rounded-lg border border-marine-border">
          <p className="text-xs text-marine-text-secondary">
            <strong>Tip:</strong> Press <Badge variant="secondary" className="ml-1 mr-1 bg-marine-bg text-marine-text-secondary px-1.5 py-0 text-xs font-mono">?</Badge> at any time to show this help
          </p>
        </div>
      </DialogContent>
    </Dialog>
  );
}