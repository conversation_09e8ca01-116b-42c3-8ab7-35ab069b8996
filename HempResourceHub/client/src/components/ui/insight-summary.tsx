import { cn } from "@/lib/utils";
import { Brain, TrendingUp, AlertCircle } from "lucide-react";

interface InsightSummaryProps {
  keyFindings: string[];
  recommendations: string[];
  className?: string;
  loading?: boolean;
}

export function InsightSummary({ 
  keyFindings, 
  recommendations, 
  className,
  loading = false 
}: InsightSummaryProps) {
  if (loading) {
    return (
      <div className={cn(
        "rounded-xl bg-black/50 backdrop-blur-sm border border-gray-800 p-6",
        className
      )}>
        <div className="flex items-center gap-3 mb-4">
          <Brain className="h-5 w-5 text-green-500 animate-pulse" />
          <h3 className="text-lg font-semibold text-white">Generating Insights...</h3>
        </div>
        <div className="space-y-3">
          {[1, 2, 3].map(i => (
            <div key={i} className="h-4 bg-gray-800 rounded animate-pulse" />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className={cn(
      "rounded-xl bg-marine-card border border-marine-border overflow-hidden",
      className
    )}>
      {/* Header */}
      <div className="border-b border-marine-border px-6 py-4">
        <div className="flex items-center gap-3">
          <Brain className="h-5 w-5 text-marine-accent" />
          <h3 className="text-lg font-medium text-white">Performance Analysis Summary</h3>
        </div>
      </div>

      {/* Content */}
      <div className="grid md:grid-cols-2 divide-x divide-marine-border">
        {/* Key Findings */}
        <div className="p-6">
          <div className="mb-4">
            <h4 className="text-sm font-medium text-marine-text-secondary">Key Findings:</h4>
          </div>
          <ul className="space-y-3">
            {keyFindings.map((finding, index) => (
              <li key={index} className="flex items-start gap-2">
                <span className="mt-1.5 h-1.5 w-1.5 rounded-full bg-marine-accent flex-shrink-0" />
                <span className="text-sm text-marine-text-secondary leading-relaxed">{finding}</span>
              </li>
            ))}
          </ul>
        </div>

        {/* Recommendations */}
        <div className="p-6">
          <div className="mb-4">
            <h4 className="text-sm font-medium text-marine-text-secondary">Recommendations:</h4>
          </div>
          <ul className="space-y-3">
            {recommendations.map((recommendation, index) => (
              <li key={index} className="flex items-start gap-2">
                <span className="mt-1.5 h-1.5 w-1.5 rounded-full bg-marine-accent flex-shrink-0" />
                <span className="text-sm text-marine-text-secondary leading-relaxed">{recommendation}</span>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
}