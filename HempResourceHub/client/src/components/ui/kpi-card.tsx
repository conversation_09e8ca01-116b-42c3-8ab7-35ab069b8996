import { cn } from "@/lib/utils";
import { LucideIcon } from "lucide-react";

interface KPICardProps {
  title: string;
  value: string | number;
  change?: {
    value: number;
    label: string;
  };
  icon?: LucideIcon;
  trend?: "up" | "down" | "neutral";
  className?: string;
}

export function KPICard({ 
  title, 
  value, 
  change, 
  icon: Icon, 
  trend = "neutral",
  className 
}: KPICardProps) {
  const trendColors = {
    up: "text-green-500",
    down: "text-red-500",
    neutral: "text-gray-400"
  };

  const trendBgColors = {
    up: "bg-green-500/10",
    down: "bg-red-500/10",
    neutral: "bg-gray-500/10"
  };

  return (
    <div className={cn(
      "rounded-xl bg-black/50 backdrop-blur-sm border border-gray-800 p-6 hover:border-gray-700 transition-colors",
      className
    )}>
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-gray-400">{title}</p>
          <p className="mt-2 text-3xl font-bold text-white">{value}</p>
          
          {change && (
            <div className="mt-3 flex items-center gap-2">
              <span className={cn(
                "rounded-full px-2 py-0.5 text-xs font-medium",
                trendBgColors[trend],
                trendColors[trend]
              )}>
                {trend === "up" && "↑"}
                {trend === "down" && "↓"}
                {change.value > 0 ? "+" : ""}{change.value}%
              </span>
              <span className="text-xs text-gray-500">{change.label}</span>
            </div>
          )}
        </div>
        
        {Icon && (
          <div className="rounded-lg bg-green-500/10 p-3">
            <Icon className="h-6 w-6 text-green-500" />
          </div>
        )}
      </div>
    </div>
  );
}