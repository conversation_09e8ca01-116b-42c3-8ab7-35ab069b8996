import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { 
  Filter, 
  X, 
  ChevronDown, 
  ChevronUp,
  Calendar,
  DollarSign,
  Factory,
  Leaf
} from "lucide-react";
import { cn } from "@/lib/utils";
import { usePlantParts, useIndustries } from "@/hooks/use-plant-data";

interface SearchFilter {
  id: string;
  type: 'checkbox' | 'radio' | 'range' | 'date';
  label: string;
  value: any;
  options?: { label: string; value: string }[];
}

interface SearchFiltersProps {
  onFiltersChange: (filters: Record<string, any>) => void;
  className?: string;
  showAsPopover?: boolean;
}

export function SearchFilters({ onFiltersChange, className, showAsPopover = false }: SearchFiltersProps) {
  const { data: plantParts } = usePlantParts();
  const { data: industries } = useIndustries();
  
  const [isOpen, setIsOpen] = useState(!showAsPopover);
  const [activeFilters, setActiveFilters] = useState<Record<string, any>>({
    plantParts: [],
    industries: [],
    stage: 'all',
    dateRange: 'all',
    priceRange: [0, 1000]
  });

  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    plantParts: true,
    industries: false,
    stage: false,
    advanced: false
  });

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const updateFilter = (key: string, value: any) => {
    const newFilters = { ...activeFilters, [key]: value };
    setActiveFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const clearAllFilters = () => {
    const clearedFilters = {
      plantParts: [],
      industries: [],
      stage: 'all',
      dateRange: 'all',
      priceRange: [0, 1000]
    };
    setActiveFilters(clearedFilters);
    onFiltersChange(clearedFilters);
  };

  const activeFilterCount = 
    activeFilters.plantParts.length + 
    activeFilters.industries.length + 
    (activeFilters.stage !== 'all' ? 1 : 0) +
    (activeFilters.dateRange !== 'all' ? 1 : 0);

  const FilterContent = () => (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-white flex items-center gap-2">
          <Filter className="h-5 w-5" />
          Filters
        </h3>
        {activeFilterCount > 0 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={clearAllFilters}
            className="text-red-400 hover:text-red-300"
          >
            Clear all ({activeFilterCount})
          </Button>
        )}
      </div>

      {/* Plant Parts */}
      <div className="space-y-3">
        <button
          onClick={() => toggleSection('plantParts')}
          className="w-full flex items-center justify-between text-left hover:text-white transition-colors"
        >
          <div className="flex items-center gap-2">
            <Leaf className="h-4 w-4 text-green-400" />
            <span className="font-medium">Plant Parts</span>
            {activeFilters.plantParts.length > 0 && (
              <Badge variant="secondary" className="ml-2">
                {activeFilters.plantParts.length}
              </Badge>
            )}
          </div>
          {expandedSections.plantParts ? (
            <ChevronUp className="h-4 w-4" />
          ) : (
            <ChevronDown className="h-4 w-4" />
          )}
        </button>
        
        {expandedSections.plantParts && (
          <div className="space-y-2 pl-6">
            {plantParts?.map(part => (
              <div key={part.id} className="flex items-center space-x-2">
                <Checkbox
                  id={`part-${part.id}`}
                  checked={activeFilters.plantParts.includes(part.id)}
                  onCheckedChange={(checked) => {
                    const newParts = checked
                      ? [...activeFilters.plantParts, part.id]
                      : activeFilters.plantParts.filter(p => p !== part.id);
                    updateFilter('plantParts', newParts);
                  }}
                />
                <Label
                  htmlFor={`part-${part.id}`}
                  className="text-sm font-normal cursor-pointer"
                >
                  {part.name}
                </Label>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Industries */}
      <div className="space-y-3">
        <button
          onClick={() => toggleSection('industries')}
          className="w-full flex items-center justify-between text-left hover:text-white transition-colors"
        >
          <div className="flex items-center gap-2">
            <Factory className="h-4 w-4 text-blue-400" />
            <span className="font-medium">Industries</span>
            {activeFilters.industries.length > 0 && (
              <Badge variant="secondary" className="ml-2">
                {activeFilters.industries.length}
              </Badge>
            )}
          </div>
          {expandedSections.industries ? (
            <ChevronUp className="h-4 w-4" />
          ) : (
            <ChevronDown className="h-4 w-4" />
          )}
        </button>
        
        {expandedSections.industries && (
          <div className="space-y-2 pl-6 max-h-48 overflow-y-auto">
            {industries?.map(industry => (
              <div key={industry.id} className="flex items-center space-x-2">
                <Checkbox
                  id={`industry-${industry.id}`}
                  checked={activeFilters.industries.includes(industry.id)}
                  onCheckedChange={(checked) => {
                    const newIndustries = checked
                      ? [...activeFilters.industries, industry.id]
                      : activeFilters.industries.filter(i => i !== industry.id);
                    updateFilter('industries', newIndustries);
                  }}
                />
                <Label
                  htmlFor={`industry-${industry.id}`}
                  className="text-sm font-normal cursor-pointer"
                >
                  {industry.name}
                </Label>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Development Stage */}
      <div className="space-y-3">
        <button
          onClick={() => toggleSection('stage')}
          className="w-full flex items-center justify-between text-left hover:text-white transition-colors"
        >
          <span className="font-medium">Development Stage</span>
          {expandedSections.stage ? (
            <ChevronUp className="h-4 w-4" />
          ) : (
            <ChevronDown className="h-4 w-4" />
          )}
        </button>
        
        {expandedSections.stage && (
          <RadioGroup
            value={activeFilters.stage}
            onValueChange={(value) => updateFilter('stage', value)}
            className="pl-6"
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="all" id="stage-all" />
              <Label htmlFor="stage-all" className="font-normal cursor-pointer">
                All Stages
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="commercial" id="stage-commercial" />
              <Label htmlFor="stage-commercial" className="font-normal cursor-pointer">
                Commercial
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="pilot" id="stage-pilot" />
              <Label htmlFor="stage-pilot" className="font-normal cursor-pointer">
                Pilot
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="research" id="stage-research" />
              <Label htmlFor="stage-research" className="font-normal cursor-pointer">
                Research
              </Label>
            </div>
          </RadioGroup>
        )}
      </div>

      {/* Advanced Filters */}
      <div className="space-y-3">
        <button
          onClick={() => toggleSection('advanced')}
          className="w-full flex items-center justify-between text-left hover:text-white transition-colors"
        >
          <span className="font-medium">Advanced Filters</span>
          {expandedSections.advanced ? (
            <ChevronUp className="h-4 w-4" />
          ) : (
            <ChevronDown className="h-4 w-4" />
          )}
        </button>
        
        {expandedSections.advanced && (
          <div className="space-y-4 pl-6">
            {/* Date Range */}
            <div className="space-y-2">
              <Label className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Date Added
              </Label>
              <RadioGroup
                value={activeFilters.dateRange}
                onValueChange={(value) => updateFilter('dateRange', value)}
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="all" id="date-all" />
                  <Label htmlFor="date-all" className="font-normal cursor-pointer">
                    All Time
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="week" id="date-week" />
                  <Label htmlFor="date-week" className="font-normal cursor-pointer">
                    Past Week
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="month" id="date-month" />
                  <Label htmlFor="date-month" className="font-normal cursor-pointer">
                    Past Month
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="year" id="date-year" />
                  <Label htmlFor="date-year" className="font-normal cursor-pointer">
                    Past Year
                  </Label>
                </div>
              </RadioGroup>
            </div>

            {/* Price Range (placeholder for future) */}
            <div className="space-y-2">
              <Label className="flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                Price Range
              </Label>
              <div className="px-2">
                <Slider
                  min={0}
                  max={1000}
                  step={10}
                  value={activeFilters.priceRange}
                  onValueChange={(value) => updateFilter('priceRange', value)}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-gray-400 mt-1">
                  <span>${activeFilters.priceRange[0]}</span>
                  <span>${activeFilters.priceRange[1]}+</span>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );

  if (showAsPopover) {
    return (
      <div className={cn("relative", className)}>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsOpen(!isOpen)}
          className={cn(
            "gap-2",
            activeFilterCount > 0 && "border-green-500"
          )}
        >
          <Filter className="h-4 w-4" />
          Filters
          {activeFilterCount > 0 && (
            <Badge variant="secondary" className="ml-1">
              {activeFilterCount}
            </Badge>
          )}
        </Button>

        {isOpen && (
          <div className="absolute top-full mt-2 right-0 w-80 bg-marine-card border border-marine-border rounded-xl shadow-2xl z-50 p-4">
            <FilterContent />
          </div>
        )}
      </div>
    );
  }

  return (
    <div className={cn("bg-marine-card rounded-xl p-4", className)}>
      <FilterContent />
    </div>
  );
}