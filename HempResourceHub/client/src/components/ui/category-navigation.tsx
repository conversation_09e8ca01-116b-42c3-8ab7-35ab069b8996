import React, { useState, useRef, useEffect } from "react";
import { ChevronLeft, ChevronRight, Leaf, Factory, Building2, Wheat, Flower, TreePine, Package, Sparkles } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { usePlantParts, useIndustries } from "@/hooks/use-plant-data";
import { cn } from "@/lib/utils";
import { componentStyles } from "@/lib/design-system";

// Hemp Plant Parts Icons
const plantPartIcons = {
  "Seeds": Wheat,
  "Fiber": Package,
  "Hurds": TreePine,
  "Leaves": Leaf,
  "Flowers": Flower,
  "Roots": TreePine,
  "Multi-part": Sparkles,
  "Byproducts": Factory,
  // Fallback for any other plant parts
  "default": Leaf
};

// Industry Sector Icons
const industryIcons = {
  "Construction": Building2,
  "Textiles": Package,
  "Food & Beverage": Wheat,
  "Bioplastics": Factory,
  "Paper": Package,
  "Health & Wellness": Sparkles,
  "Automotive": Factory,
  "Energy": Factory,
  // Fallback for any other industries
  "default": Factory
};

interface CategoryItem {
  id: number;
  name: string;
  description?: string;
  count?: number;
  type: 'plantPart' | 'industry';
}

interface CategoryNavigationProps {
  onCategorySelect?: (category: CategoryItem) => void;
  selectedCategories?: number[];
  className?: string;
  showCounts?: boolean;
  allowMultiSelect?: boolean;
}

export function CategoryNavigation({
  onCategorySelect,
  selectedCategories = [],
  className,
  showCounts = true,
  allowMultiSelect = true
}: CategoryNavigationProps) {
  const [activeTab, setActiveTab] = useState<'plantParts' | 'industries'>('plantParts');
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);
  const scrollRef = useRef<HTMLDivElement>(null);

  // Data hooks
  const { data: plantParts, isLoading: plantPartsLoading } = usePlantParts();
  const { data: industries, isLoading: industriesLoading } = useIndustries();

  // Transform data to CategoryItem format
  const plantPartCategories: CategoryItem[] = plantParts?.map(part => ({
    id: part.id,
    name: part.name,
    description: part.description,
    type: 'plantPart' as const,
    count: 0 // TODO: Add actual count from products
  })) || [];

  const industryCategories: CategoryItem[] = industries?.map(industry => ({
    id: industry.id,
    name: industry.name,
    description: industry.description,
    type: 'industry' as const,
    count: 0 // TODO: Add actual count from products
  })) || [];

  const currentCategories = activeTab === 'plantParts' ? plantPartCategories : industryCategories;
  const currentIcons = activeTab === 'plantParts' ? plantPartIcons : industryIcons;

  // Check scroll position
  const checkScrollPosition = () => {
    if (scrollRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollRef.current;
      setCanScrollLeft(scrollLeft > 0);
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1);
    }
  };

  useEffect(() => {
    checkScrollPosition();
    const scrollElement = scrollRef.current;
    if (scrollElement) {
      scrollElement.addEventListener('scroll', checkScrollPosition);
      return () => scrollElement.removeEventListener('scroll', checkScrollPosition);
    }
  }, [currentCategories]);

  const scrollLeft = () => {
    if (scrollRef.current) {
      scrollRef.current.scrollBy({ left: -200, behavior: 'smooth' });
    }
  };

  const scrollRight = () => {
    if (scrollRef.current) {
      scrollRef.current.scrollBy({ left: 200, behavior: 'smooth' });
    }
  };

  const handleCategoryClick = (category: CategoryItem) => {
    onCategorySelect?.(category);
  };

  const getIcon = (categoryName: string) => {
    const IconComponent = currentIcons[categoryName as keyof typeof currentIcons] || currentIcons.default;
    return <IconComponent className="h-5 w-5" />;
  };

  const isSelected = (categoryId: number) => selectedCategories.includes(categoryId);

  if (plantPartsLoading || industriesLoading) {
    return (
      <div className={cn("w-full", className)}>
        <div className="flex items-center gap-4 mb-4">
          <div className="h-8 w-24 bg-gray-800 rounded animate-pulse" />
          <div className="h-8 w-24 bg-gray-800 rounded animate-pulse" />
        </div>
        <div className="flex gap-3 overflow-hidden">
          {Array.from({ length: 6 }).map((_, i) => (
            <div key={i} className="flex-shrink-0 h-20 w-20 bg-gray-800 rounded-xl animate-pulse" />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className={cn("w-full", className)}>
      {/* Tab Navigation */}
      <div className="flex items-center gap-4 mb-6">
        <Button
          variant={activeTab === 'plantParts' ? 'default' : 'ghost'}
          onClick={() => setActiveTab('plantParts')}
          className={cn(
            "px-6 py-2 rounded-full transition-all duration-200",
            activeTab === 'plantParts' 
              ? "bg-green-500 hover:bg-green-600 text-white" 
              : "text-gray-400 hover:text-white hover:bg-gray-800"
          )}
        >
          <Leaf className="h-4 w-4 mr-2" />
          Hemp Plant Parts
          <Badge variant="secondary" className="ml-2 bg-black/20">
            {plantPartCategories.length}
          </Badge>
        </Button>
        
        <Button
          variant={activeTab === 'industries' ? 'default' : 'ghost'}
          onClick={() => setActiveTab('industries')}
          className={cn(
            "px-6 py-2 rounded-full transition-all duration-200",
            activeTab === 'industries' 
              ? "bg-purple-500 hover:bg-purple-600 text-white" 
              : "text-gray-400 hover:text-white hover:bg-gray-800"
          )}
        >
          <Factory className="h-4 w-4 mr-2" />
          Industry Sectors
          <Badge variant="secondary" className="ml-2 bg-black/20">
            {industryCategories.length}
          </Badge>
        </Button>
      </div>

      {/* Scrollable Category Icons */}
      <div className="relative">
        {/* Left Scroll Button */}
        {canScrollLeft && (
          <Button
            variant="ghost"
            size="sm"
            onClick={scrollLeft}
            className="absolute left-0 top-1/2 -translate-y-1/2 z-10 h-10 w-10 rounded-full bg-black/80 backdrop-blur-sm border border-gray-700 hover:bg-gray-800"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
        )}

        {/* Right Scroll Button */}
        {canScrollRight && (
          <Button
            variant="ghost"
            size="sm"
            onClick={scrollRight}
            className="absolute right-0 top-1/2 -translate-y-1/2 z-10 h-10 w-10 rounded-full bg-black/80 backdrop-blur-sm border border-gray-700 hover:bg-gray-800"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        )}

        {/* Category Icons Container */}
        <div
          ref={scrollRef}
          className="flex gap-4 overflow-x-auto scrollbar-hide pb-2"
          style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
        >
          {currentCategories.map((category) => (
            <button
              key={category.id}
              onClick={() => handleCategoryClick(category)}
              className={cn(
                "flex-shrink-0 group relative",
                "flex flex-col items-center justify-center",
                "w-24 h-24 rounded-xl transition-all duration-300",
                "border-2 hover:scale-105",
                isSelected(category.id)
                  ? activeTab === 'plantParts'
                    ? "bg-green-500/20 border-green-500/50 text-green-400"
                    : "bg-purple-500/20 border-purple-500/50 text-purple-400"
                  : componentStyles.interface.card + " border-gray-700 text-gray-300 hover:border-gray-600 hover:text-white"
              )}
            >
              {/* Icon */}
              <div className={cn(
                "mb-1 transition-colors duration-200",
                isSelected(category.id)
                  ? activeTab === 'plantParts' ? "text-green-400" : "text-purple-400"
                  : "text-gray-400 group-hover:text-white"
              )}>
                {getIcon(category.name)}
              </div>

              {/* Category Name */}
              <span className="text-xs font-medium text-center leading-tight px-1">
                {category.name}
              </span>

              {/* Count Badge */}
              {showCounts && category.count !== undefined && category.count > 0 && (
                <Badge
                  variant="secondary"
                  className={cn(
                    "absolute -top-2 -right-2 h-5 w-5 p-0 text-xs rounded-full",
                    isSelected(category.id)
                      ? activeTab === 'plantParts'
                        ? "bg-green-500 text-white"
                        : "bg-purple-500 text-white"
                      : "bg-gray-700 text-gray-300"
                  )}
                >
                  {category.count > 99 ? '99+' : category.count}
                </Badge>
              )}

              {/* Selection Indicator */}
              {isSelected(category.id) && (
                <div className={cn(
                  "absolute inset-0 rounded-xl ring-2 ring-offset-2 ring-offset-black",
                  activeTab === 'plantParts' ? "ring-green-500/50" : "ring-purple-500/50"
                )} />
              )}
            </button>
          ))}
        </div>
      </div>

      {/* Selected Categories Summary */}
      {selectedCategories.length > 0 && (
        <div className="mt-4 flex flex-wrap gap-2">
          <span className="text-sm text-gray-400">Active filters:</span>
          {selectedCategories.map((categoryId) => {
            const category = currentCategories.find(c => c.id === categoryId);
            if (!category) return null;
            
            return (
              <Badge
                key={categoryId}
                variant="secondary"
                className={cn(
                  "px-3 py-1",
                  activeTab === 'plantParts'
                    ? "bg-green-500/20 text-green-400 border-green-500/30"
                    : "bg-purple-500/20 text-purple-400 border-purple-500/30"
                )}
              >
                {category.name}
              </Badge>
            );
          })}
        </div>
      )}
    </div>
  );
}
