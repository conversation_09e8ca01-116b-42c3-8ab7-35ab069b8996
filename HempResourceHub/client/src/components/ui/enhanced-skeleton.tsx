import { cn } from "@/lib/utils";
import { Skeleton } from "@/components/ui/skeleton";

interface EnhancedSkeletonProps {
  className?: string;
  animate?: boolean;
}

export function ProductCardSkeleton({ className, animate = true }: EnhancedSkeletonProps) {
  return (
    <div className={cn("space-y-3", className)}>
      <Skeleton className={cn("h-48 w-full rounded-lg", animate && "animate-pulse")} />
      <div className="space-y-2">
        <Skeleton className={cn("h-5 w-3/4", animate && "animate-pulse")} />
        <Skeleton className={cn("h-4 w-full", animate && "animate-pulse")} />
        <Skeleton className={cn("h-4 w-5/6", animate && "animate-pulse")} />
      </div>
      <div className="flex gap-2">
        <Skeleton className={cn("h-6 w-20 rounded-full", animate && "animate-pulse")} />
        <Skeleton className={cn("h-6 w-24 rounded-full", animate && "animate-pulse")} />
      </div>
    </div>
  );
}

export function TableRowSkeleton({ columns = 6, animate = true }: { columns?: number; animate?: boolean }) {
  return (
    <tr>
      {Array.from({ length: columns }).map((_, i) => (
        <td key={i} className="p-4">
          <Skeleton className={cn("h-6 w-full", animate && "animate-pulse")} />
        </td>
      ))}
    </tr>
  );
}

export function DashboardStatSkeleton({ animate = true }: EnhancedSkeletonProps) {
  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <Skeleton className={cn("h-4 w-24", animate && "animate-pulse")} />
        <Skeleton className={cn("h-8 w-8 rounded", animate && "animate-pulse")} />
      </div>
      <Skeleton className={cn("h-8 w-32", animate && "animate-pulse")} />
      <Skeleton className={cn("h-3 w-20", animate && "animate-pulse")} />
    </div>
  );
}

export function ChartSkeleton({ height = 300, animate = true }: { height?: number; animate?: boolean }) {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Skeleton className={cn("h-6 w-32", animate && "animate-pulse")} />
        <div className="flex gap-2">
          <Skeleton className={cn("h-8 w-20 rounded", animate && "animate-pulse")} />
          <Skeleton className={cn("h-8 w-20 rounded", animate && "animate-pulse")} />
        </div>
      </div>
      <Skeleton 
        className={cn("w-full rounded-lg", animate && "animate-pulse")} 
        style={{ height: `${height}px` }}
      />
    </div>
  );
}

export function FormSkeleton({ fields = 4, animate = true }: { fields?: number; animate?: boolean }) {
  return (
    <div className="space-y-4">
      {Array.from({ length: fields }).map((_, i) => (
        <div key={i} className="space-y-2">
          <Skeleton className={cn("h-4 w-24", animate && "animate-pulse")} />
          <Skeleton className={cn("h-10 w-full rounded-md", animate && "animate-pulse")} />
        </div>
      ))}
      <div className="flex gap-2 pt-4">
        <Skeleton className={cn("h-10 w-24 rounded-md", animate && "animate-pulse")} />
        <Skeleton className={cn("h-10 w-24 rounded-md", animate && "animate-pulse")} />
      </div>
    </div>
  );
}

export function ListItemSkeleton({ animate = true }: EnhancedSkeletonProps) {
  return (
    <div className="flex items-center space-x-4 p-4">
      <Skeleton className={cn("h-12 w-12 rounded-full", animate && "animate-pulse")} />
      <div className="flex-1 space-y-2">
        <Skeleton className={cn("h-4 w-1/3", animate && "animate-pulse")} />
        <Skeleton className={cn("h-3 w-1/2", animate && "animate-pulse")} />
      </div>
      <Skeleton className={cn("h-8 w-16 rounded", animate && "animate-pulse")} />
    </div>
  );
}