import { cn } from "@/lib/utils";
import { TrendingUp, Package, Leaf } from "lucide-react";

interface HempKPICardProps {
  category: string;
  productCount: number;
  growth: string;
  revenue?: string;
  icon?: "trending" | "package" | "leaf";
  className?: string;
}

export function HempKPICard({ 
  category, 
  productCount, 
  growth,
  revenue,
  icon = "package",
  className 
}: HempKPICardProps) {
  const icons = {
    trending: TrendingUp,
    package: Package,
    leaf: Leaf,
  };
  
  const Icon = icons[icon];
  
  return (
    <div className={cn(
      "rounded-xl bg-marine-card p-6 space-y-4 hover:bg-marine-cardHover transition-colors",
      className
    )}>
      <div className="flex items-start justify-between">
        <div>
          <h3 className="text-lg font-medium text-white mb-1">{category}</h3>
          <p className="text-sm text-marine-text-muted">Hemp Products</p>
        </div>
        <div className="p-2 rounded-lg bg-green-600/10">
          <Icon className="h-5 w-5 text-green-500" />
        </div>
      </div>
      
      <div className="space-y-3">
        <div>
          <p className="text-xs text-marine-text-secondary mb-1">Active Products:</p>
          <p className="text-2xl font-bold text-white">{productCount}</p>
        </div>
        
        <div className="flex justify-between items-end">
          <div>
            <p className="text-xs text-marine-text-secondary mb-1">Growth Rate:</p>
            <p className="text-lg font-semibold text-green-500">{growth}</p>
          </div>
          {revenue && (
            <div className="text-right">
              <p className="text-xs text-marine-text-secondary mb-1">Revenue:</p>
              <p className="text-sm font-medium text-white">{revenue}</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}