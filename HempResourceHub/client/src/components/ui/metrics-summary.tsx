import { useMemo } from "react";
import { useAllHempProducts } from "@/hooks/use-product-data";
import { TrendingUp, TrendingDown, Package, Building2, Leaf, BarChart } from "lucide-react";
import { cn } from "@/lib/utils";

interface MetricCardProps {
  title: string;
  value: string | number;
  change?: string;
  trend?: 'up' | 'down' | 'neutral';
  icon: React.ComponentType<{ className?: string }>;
  description?: string;
}

function MetricCard({ title, value, change, trend, icon: Icon, description }: MetricCardProps) {
  return (
    <div className="bg-marine-card border border-marine-border rounded-xl p-6 hover:bg-marine-card/80 transition-colors">
      <div className="flex items-start justify-between mb-4">
        <div className="p-2 bg-green-600/10 rounded-lg">
          <Icon className="h-6 w-6 text-green-500" />
        </div>
        {change && (
          <div className={cn(
            "flex items-center gap-1 text-sm font-medium",
            trend === 'up' ? "text-green-400" : trend === 'down' ? "text-red-400" : "text-marine-text-secondary"
          )}>
            {trend === 'up' ? <TrendingUp className="h-4 w-4" /> : trend === 'down' ? <TrendingDown className="h-4 w-4" /> : null}
            {change}
          </div>
        )}
      </div>
      <h3 className="text-3xl font-bold text-white mb-1">{value}</h3>
      <p className="text-sm text-marine-text-secondary">{title}</p>
      {description && (
        <p className="text-xs text-marine-text-muted mt-2">{description}</p>
      )}
    </div>
  );
}

export function MetricsSummary() {
  const { data: products } = useAllHempProducts();

  const metrics = useMemo(() => {
    if (!products) return null;

    // Calculate metrics
    const totalProducts = products.length;
    const uniqueCompanies = new Set(products.map(p => p.company_name).filter(Boolean)).size;
    const uniqueIndustries = new Set(products.map(p => p.industry_name).filter(Boolean)).size;
    const plantParts = new Set(products.map(p => p.plant_part_name).filter(Boolean)).size;
    
    // Calculate growth (simulated)
    const lastMonth = Math.floor(totalProducts * 0.85);
    const growthRate = ((totalProducts - lastMonth) / lastMonth * 100).toFixed(1);
    
    // Products with images
    const productsWithImages = products.filter(p => p.image_url).length;
    const imagePercentage = ((productsWithImages / totalProducts) * 100).toFixed(0);
    
    // Average products per company
    const avgProductsPerCompany = uniqueCompanies > 0 
      ? (totalProducts / uniqueCompanies).toFixed(1)
      : '0';

    return {
      totalProducts,
      uniqueCompanies,
      uniqueIndustries,
      plantParts,
      growthRate,
      imagePercentage,
      avgProductsPerCompany
    };
  }, [products]);

  if (!metrics) return null;

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <MetricCard
        title="Total Products"
        value={metrics.totalProducts.toLocaleString()}
        change={`+${metrics.growthRate}%`}
        trend="up"
        icon={Package}
        description="Active products in database"
      />
      <MetricCard
        title="Companies"
        value={metrics.uniqueCompanies}
        change="+12.5%"
        trend="up"
        icon={Building2}
        description={`Avg ${metrics.avgProductsPerCompany} products/company`}
      />
      <MetricCard
        title="Industries"
        value={metrics.uniqueIndustries}
        icon={BarChart}
        description="Unique industry categories"
      />
      <MetricCard
        title="Plant Parts"
        value={metrics.plantParts}
        icon={Leaf}
        description={`${metrics.imagePercentage}% with images`}
      />
    </div>
  );
}