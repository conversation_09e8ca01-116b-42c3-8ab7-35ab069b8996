import { Link, useLocation } from "wouter";
import { cn } from "@/lib/utils";
import { 
  Home,
  Globe,
  BarChart3,
  Layers3,
  Database,
  Leaf,
  Building2,
  FileSearch,
  Settings,
  User,
  Menu,
  X,
  ChevronDown,
  Package,
  FlaskConical
} from "lucide-react";
import { useState, useRef, useEffect } from "react";
import { useAuth } from "@/components/auth/enhanced-auth-provider";
import { useRovingTabIndex } from "@/hooks/useFocusManagement";
import { useProductStats } from "@/hooks/use-product-data";
import logoCircle from "@/assets/circle-logo.png";

interface NavItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  children?: NavItem[];
}

const navigation: NavItem[] = [
  { 
    name: "Dashboard", 
    href: "/dashboard", 
    icon: BarChart3,
  },
  { 
    name: "Products", 
    href: "/products", 
    icon: Package,
    children: [
      { name: "Browse All", href: "/products", icon: Globe },
      { name: "By Plant Part", href: "/plant-parts", icon: Leaf },
      { name: "Industries", href: "/industries", icon: Building2 },
    ]
  },
  { 
    name: "Companies", 
    href: "/companies", 
    icon: Building2,
  },
  { 
    name: "Research", 
    href: "/research", 
    icon: FlaskConical,
  },
];

interface AppSidebarProps {
  className?: string;
  onClose?: () => void;
}

export function AppSidebar({ className, onClose }: AppSidebarProps) {
  const [location] = useLocation();
  const { user } = useAuth();
  const [expandedItems, setExpandedItems] = useState<string[]>([]);
  const { data: stats } = useProductStats();

  const toggleExpanded = (name: string) => {
    setExpandedItems(prev => 
      prev.includes(name) 
        ? prev.filter(item => item !== name)
        : [...prev, name]
    );
  };

  const isActive = (href: string) => {
    if (href === "/") return location === href;
    return location.startsWith(href);
  };

  const NavContent = () => (
    <>
      {/* Logo & Brand */}
      <div className="px-4 py-6 border-b border-gray-200 dark:border-gray-800">
        <Link href="/" className="flex items-center gap-3 group">
          <div className="relative">
            <img 
              src={logoCircle} 
              alt="HempQuarterz Logo" 
              className="h-10 w-10 object-contain flex-shrink-0 transition-transform group-hover:scale-110"
            />
            <div className="absolute inset-0 rounded-full bg-primary/20 blur-xl opacity-0 group-hover:opacity-100 transition-opacity" />
          </div>
          <div className="min-w-0 flex-1">
            <h1 className="text-sm font-semibold text-white truncate">HempQuarterz®</h1>
            <p className="text-xs text-gray-500">Database</p>
          </div>
        </Link>
      </div>

      {/* Navigation */}
      <nav 
        className="flex-1 px-3 py-3 overflow-y-auto"
        role="navigation"
        aria-label="Main navigation"
      >
        {navigation.map((item) => {
          const active = isActive(item.href);
          const expanded = expandedItems.includes(item.name);
          
          return (
            <div key={item.name} className="mb-1">
              {item.children ? (
                <>
                  <button
                    onClick={() => toggleExpanded(item.name)}
                    className={cn(
                      "w-full group relative flex items-center gap-3 rounded-lg px-3 py-2.5 transition-all",
                      active
                        ? "bg-dark-card text-white"
                        : "text-gray-400 hover:bg-dark-card/50 hover:text-white"
                    )}
                    aria-expanded={expanded}
                    aria-controls={`submenu-${item.name.toLowerCase().replace(/\s+/g, '-')}`}
                    aria-label={`${item.name} menu`}
                  >
                    {active && (
                      <div className="absolute left-0 top-1/2 h-8 w-1 -translate-y-1/2 rounded-r-full bg-hemp-500" />
                    )}
                    <item.icon className="h-4 w-4 flex-shrink-0" />
                    <span className="flex-1 text-left text-sm font-medium">{item.name}</span>
                    <ChevronDown className={cn(
                      "h-4 w-4 transition-transform",
                      expanded && "rotate-180"
                    )} />
                  </button>
                  
                  {expanded && (
                    <div 
                      className="ml-11 mt-1 space-y-1"
                      id={`submenu-${item.name.toLowerCase().replace(/\s+/g, '-')}`}
                      role="group"
                      aria-label={`${item.name} submenu`}
                    >
                      {item.children.map((child) => {
                        const childActive = isActive(child.href);
                        return (
                          <Link 
                            key={child.href} 
                            href={child.href}
                            className={cn(
                              "flex items-center gap-2 rounded-lg px-3 py-1.5 text-sm transition-all",
                              childActive
                                ? "bg-hemp-500/10 text-green-500"
                                : "text-gray-400 hover:bg-dark-card/30 hover:text-white"
                            )}
                            aria-current={childActive ? "page" : undefined}
                          >
                            <child.icon className="h-4 w-4" />
                            {child.name}
                          </Link>
                        );
                      })}
                    </div>
                  )}
                </>
              ) : (
                <Link 
                  href={item.href}
                  className={cn(
                    "group relative flex items-center gap-3 rounded-lg px-3 py-2.5 transition-all",
                    active
                      ? "bg-dark-card text-white"
                      : "text-gray-400 hover:bg-dark-card/50 hover:text-white"
                  )}
                  aria-current={active ? "page" : undefined}
                >
                  {active && (
                    <div className="absolute left-0 top-1/2 h-8 w-1 -translate-y-1/2 rounded-r-full bg-hemp-500" />
                  )}
                  <item.icon className="h-4 w-4 flex-shrink-0" />
                  <span className="text-sm font-medium">{item.name}</span>
                </Link>
              )}
            </div>
          );
        })}
      </nav>

      {/* Bottom Section */}
      <div className="border-t border-dark-border p-4">
        {user ? (
          <div className="space-y-2">
            <Link 
              href="/admin"
              className="flex items-center gap-2 rounded-lg px-3 py-2 text-sm font-medium text-gray-400 hover:bg-dark-card/50 hover:text-white transition-all"
            >
              <Settings className="h-4 w-4" />
              <span>Admin Panel</span>
            </Link>
            <div className="flex items-center gap-2 px-3 py-2">
              <User className="h-4 w-4 text-gray-400" />
              <div className="flex-1">
                <p className="text-sm text-white">{user.email}</p>
                <p className="text-xs text-gray-500">Administrator</p>
              </div>
            </div>
          </div>
        ) : (
          <Link 
            href="/login"
            className="flex items-center justify-center gap-2 rounded-lg bg-hemp-500 px-4 py-2 text-sm font-medium text-white hover:bg-green-700 transition-colors"
          >
            <User className="h-4 w-4" />
            Sign In
          </Link>
        )}
        
        {/* Status */}
        <div className="mt-4 flex items-center gap-2 px-3">
          <div className="h-2 w-2 rounded-full bg-hemp-500 animate-pulse" />
          <span className="text-xs text-gray-400">
            {stats?.totalProducts || 0} Products
          </span>
        </div>
      </div>
    </>
  );

  return (
    <>
      {/* Desktop Sidebar */}
      <div className={cn(
        "hidden lg:flex h-screen flex-col bg-black w-[280px] fixed left-0 top-0",
        className
      )}>
        <NavContent />
      </div>

      {/* Mobile Sidebar */}
      <div className={cn(
        "lg:hidden fixed left-0 top-0 h-screen flex-col bg-black w-[280px] z-40 transform transition-transform duration-300",
        className?.includes("translate-x-0") ? "translate-x-0" : "-translate-x-full"
      )}>
        <NavContent />
      </div>

      {/* Mobile Overlay */}
      {className?.includes("translate-x-0") && (
        <div
          className="fixed inset-0 bg-black/50 z-30 lg:hidden"
          onClick={onClose}
        />
      )}
    </>
  );
}