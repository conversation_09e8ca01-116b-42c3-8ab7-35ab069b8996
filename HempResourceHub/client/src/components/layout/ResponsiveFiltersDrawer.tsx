import { useState } from "react";
import { X, Filter, <PERSON>ota<PERSON><PERSON>c<PERSON>, ChevronLeft } from "lucide-react";
import { cn } from "@/lib/utils";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useMediaQuery } from "@/hooks/use-media-query";

export interface FilterState {
  plantParts: string[];
  industries: string[];
  processingMethods: string[];
  productStatus: string;
  priceRange: [number, number];
  sustainabilityScore: [number, number];
}

interface ResponsiveFiltersDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  filters: FilterState;
  onFiltersChange: (filters: FilterState) => void;
}

const defaultFilters: FilterState = {
  plantParts: [],
  industries: [],
  processingMethods: [],
  productStatus: "all",
  priceRange: [0, 10000],
  sustainabilityScore: [0, 100],
};

const plantPartOptions = ["Seed", "Fiber", "Flower", "Stalk", "Root", "Leaf"];
const industryOptions = ["Textiles", "Construction", "Food & Beverage", "Medical", "Cosmetics", "Automotive"];
const processingOptions = ["Cold-Pressed", "Decortication", "Retting", "Extraction", "Milling"];
const statusOptions = [
  { value: "all", label: "All Products" },
  { value: "active", label: "Active" },
  { value: "prototype", label: "Prototype" },
  { value: "discontinued", label: "Discontinued" },
];

export function ResponsiveFiltersDrawer({ open, onOpenChange, filters, onFiltersChange }: ResponsiveFiltersDrawerProps) {
  const [localFilters, setLocalFilters] = useState<FilterState>(filters || defaultFilters);
  const isMobile = useMediaQuery("(max-width: 768px)");
  const isTablet = useMediaQuery("(max-width: 1024px)");

  const handleReset = () => {
    setLocalFilters(defaultFilters);
    onFiltersChange(defaultFilters);
  };

  const handleFilterChange = (updates: Partial<FilterState>) => {
    const newFilters = { ...localFilters, ...updates };
    setLocalFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const toggleArrayFilter = (key: keyof FilterState, value: string) => {
    const current = (localFilters[key] as string[]) || [];
    const updated = current.includes(value) 
      ? current.filter(v => v !== value)
      : [...current, value];
    handleFilterChange({ [key]: updated });
  };

  // Mobile: Full screen overlay
  if (isMobile) {
    return (
      <>
        {/* Mobile Toggle Button */}
        <button
          onClick={() => onOpenChange(!open)}
          className="fixed bottom-6 right-6 z-50 flex h-14 w-14 items-center justify-center rounded-full bg-green-600 text-white shadow-lg hover:bg-green-700 transition-colors md:hidden"
        >
          <Filter className="h-6 w-6" />
        </button>

        {/* Mobile Drawer - Full Screen */}
        <div
          className={cn(
            "fixed inset-0 z-50 bg-marine-bg transform transition-transform duration-300 md:hidden",
            open ? "translate-x-0" : "translate-x-full"
          )}
        >
          <div className="flex h-full flex-col">
            {/* Mobile Header */}
            <div className="flex items-center justify-between border-b border-marine-border px-4 py-4">
              <h3 className="text-lg font-semibold text-white">Filters</h3>
              <button
                onClick={() => onOpenChange(false)}
                className="rounded-lg p-2 text-marine-text-secondary hover:bg-marine-card hover:text-white transition-colors"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            {/* Filter Content */}
            <div className="flex-1 overflow-y-auto px-4 py-4">
              <FilterContent 
                localFilters={localFilters}
                toggleArrayFilter={toggleArrayFilter}
                handleFilterChange={handleFilterChange}
              />
            </div>

            {/* Mobile Footer */}
            <div className="border-t border-marine-border p-4 space-y-3">
              <button
                onClick={handleReset}
                className="w-full rounded-lg bg-marine-card py-3 text-sm font-medium text-marine-text-secondary hover:bg-marine-cardHover hover:text-white transition-colors"
              >
                Reset Filters
              </button>
              <button
                onClick={() => onOpenChange(false)}
                className="w-full rounded-lg bg-green-600 py-3 text-sm font-medium text-white hover:bg-green-700 transition-colors"
              >
                Apply Filters
              </button>
            </div>
          </div>
        </div>
      </>
    );
  }

  // Desktop/Tablet: Sidebar
  return (
    <>
      {/* Desktop Toggle Button - Positioned properly */}
      {!open && (
        <button
          onClick={() => onOpenChange(true)}
          className={cn(
            "fixed z-40 flex items-center gap-2 rounded-r-lg bg-marine-card px-4 py-2 text-marine-text-secondary hover:text-white transition-all border-y border-r border-marine-border",
            "left-0 top-24"
          )}
        >
          <Filter className="h-4 w-4" />
          <span className="text-sm font-medium">Filters</span>
        </button>
      )}

      {/* Desktop Drawer */}
      <div
        className={cn(
          "fixed top-[64px] bottom-0 z-30 bg-marine-sidebar border-r border-marine-border transform transition-all duration-300",
          isTablet ? "left-0 w-[280px]" : "left-0 w-[320px]",
          open ? "translate-x-0" : "-translate-x-full"
        )}
      >
        <div className="flex h-full flex-col">
          {/* Header */}
          <div className="flex items-center justify-between border-b border-marine-border px-6 py-4">
            <h3 className="text-lg font-medium text-white">Filters</h3>
            <button
              onClick={() => onOpenChange(false)}
              className="rounded-lg p-1 text-marine-text-secondary hover:bg-marine-card hover:text-white transition-colors"
            >
              <ChevronLeft className="h-5 w-5" />
            </button>
          </div>

          {/* Filter Sections */}
          <div className="flex-1 overflow-y-auto px-6 py-4">
            <FilterContent 
              localFilters={localFilters}
              toggleArrayFilter={toggleArrayFilter}
              handleFilterChange={handleFilterChange}
            />
          </div>

          {/* Reset Button */}
          <div className="border-t border-marine-border px-6 py-4">
            <button
              onClick={handleReset}
              className="w-full rounded-lg bg-marine-card py-2 text-sm font-medium text-marine-text-secondary hover:bg-marine-cardHover hover:text-white transition-colors flex items-center justify-center gap-2"
            >
              <RotateCcw className="h-4 w-4" />
              Reset Filters
            </button>
          </div>
        </div>
      </div>
    </>
  );
}

// Shared filter content component
function FilterContent({ 
  localFilters, 
  toggleArrayFilter, 
  handleFilterChange 
}: {
  localFilters: FilterState;
  toggleArrayFilter: (key: keyof FilterState, value: string) => void;
  handleFilterChange: (updates: Partial<FilterState>) => void;
}) {
  return (
    <div className="space-y-6">
      {/* Plant Parts */}
      <div>
        <Label className="text-sm font-medium text-marine-text-secondary mb-3 block">Plant Parts</Label>
        <div className="space-y-2">
          {plantPartOptions.map(part => (
            <div key={part} className="flex items-center space-x-2">
              <Checkbox
                id={`part-${part}`}
                checked={(localFilters.plantParts || []).includes(part)}
                onCheckedChange={() => toggleArrayFilter("plantParts", part)}
                className="border-marine-border data-[state=checked]:bg-green-600 data-[state=checked]:border-green-600"
              />
              <Label
                htmlFor={`part-${part}`}
                className="text-sm text-marine-text-secondary cursor-pointer hover:text-white transition-colors"
              >
                {part}
              </Label>
            </div>
          ))}
        </div>
      </div>

      {/* Industries */}
      <div>
        <Label className="text-sm font-medium text-marine-text-secondary mb-3 block">Industries</Label>
        <div className="space-y-2">
          {industryOptions.map(industry => (
            <div key={industry} className="flex items-center space-x-2">
              <Checkbox
                id={`industry-${industry}`}
                checked={(localFilters.industries || []).includes(industry)}
                onCheckedChange={() => toggleArrayFilter("industries", industry)}
                className="border-marine-border data-[state=checked]:bg-green-600 data-[state=checked]:border-green-600"
              />
              <Label
                htmlFor={`industry-${industry}`}
                className="text-sm text-marine-text-secondary cursor-pointer hover:text-white transition-colors"
              >
                {industry}
              </Label>
            </div>
          ))}
        </div>
      </div>

      {/* Processing Methods */}
      <div>
        <Label className="text-sm font-medium text-marine-text-secondary mb-3 block">Processing Methods</Label>
        <div className="space-y-2">
          {processingOptions.map(method => (
            <div key={method} className="flex items-center space-x-2">
              <Checkbox
                id={`method-${method}`}
                checked={(localFilters.processingMethods || []).includes(method)}
                onCheckedChange={() => toggleArrayFilter("processingMethods", method)}
                className="border-marine-border data-[state=checked]:bg-green-600 data-[state=checked]:border-green-600"
              />
              <Label
                htmlFor={`method-${method}`}
                className="text-sm text-marine-text-secondary cursor-pointer hover:text-white transition-colors"
              >
                {method}
              </Label>
            </div>
          ))}
        </div>
      </div>

      {/* Product Status */}
      <div>
        <Label className="text-sm font-medium text-marine-text-secondary mb-3 block">Product Status</Label>
        <Select
          value={localFilters.productStatus}
          onValueChange={(value) => handleFilterChange({ productStatus: value })}
        >
          <SelectTrigger className="bg-marine-card border-marine-border text-white">
            <SelectValue />
          </SelectTrigger>
          <SelectContent className="bg-marine-card border-marine-border">
            {statusOptions.map(option => (
              <SelectItem key={option.value} value={option.value} className="text-marine-text-secondary hover:text-white">
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Price Range */}
      <div>
        <Label className="text-sm font-medium text-marine-text-secondary mb-3 block">
          Price Range: ${localFilters.priceRange?.[0] || 0} - ${localFilters.priceRange?.[1] || 10000}
        </Label>
        <Slider
          value={localFilters.priceRange || [0, 10000]}
          onValueChange={(value) => handleFilterChange({ priceRange: value as [number, number] })}
          min={0}
          max={10000}
          step={100}
          className="[&_[role=slider]]:bg-green-600"
        />
      </div>

      {/* Sustainability Score */}
      <div>
        <Label className="text-sm font-medium text-marine-text-secondary mb-3 block">
          Sustainability Score: {localFilters.sustainabilityScore?.[0] || 0} - {localFilters.sustainabilityScore?.[1] || 100}
        </Label>
        <Slider
          value={localFilters.sustainabilityScore || [0, 100]}
          onValueChange={(value) => handleFilterChange({ sustainabilityScore: value as [number, number] })}
          min={0}
          max={100}
          step={5}
          className="[&_[role=slider]]:bg-green-600"
        />
      </div>
    </div>
  );
}