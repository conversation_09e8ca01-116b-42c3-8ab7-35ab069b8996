import { ReactNode } from "react";
import { useLocation } from "wouter";
import { AppLayout } from "./AppLayout";
import { DashboardLayout } from "./DashboardLayout";
import { useFocusManagement } from "@/hooks/useFocusManagement";

interface LayoutWrapperProps {
  children: ReactNode;
}

// Pages that use the specialized dashboard layout
const dashboardPages = ["/dashboard", "/analytics", "/data"];

// Pages that should have no layout (full screen)
const noLayoutPages = ["/login", "/register", "/enhanced-login", "/enhanced-register"];

export function LayoutWrapper({ children }: LayoutWrapperProps) {
  const [location] = useLocation();
  
  // Enable focus management for accessibility
  useFocusManagement();
  
  // Check if current page should use dashboard layout
  if (dashboardPages.some(page => location.startsWith(page))) {
    return children; // These pages handle their own layout
  }
  
  // Check if current page should have no layout
  if (noLayoutPages.some(page => location.startsWith(page))) {
    return <>{children}</>;
  }
  
  // Default to AppLayout for all other pages
  return <AppLayout>{children}</AppLayout>;
}