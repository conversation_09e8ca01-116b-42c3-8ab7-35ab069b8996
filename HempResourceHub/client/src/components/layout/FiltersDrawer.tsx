import { useState } from "react";
import { X, Filter, RotateCcw } from "lucide-react";
import { cn } from "@/lib/utils";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

export interface FilterState {
  plantParts: string[];
  industries: string[];
  processingMethods: string[];
  productStatus: string;
  priceRange: [number, number];
  sustainabilityScore: [number, number];
}

interface FiltersDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  filters: FilterState;
  onFiltersChange: (filters: FilterState) => void;
}

const defaultFilters: FilterState = {
  plantParts: [],
  industries: [],
  processingMethods: [],
  productStatus: "all",
  priceRange: [0, 10000],
  sustainabilityScore: [0, 100],
};

const plantPartOptions = ["Seed", "Fiber", "Flower", "Stalk", "Root", "Leaf"];
const industryOptions = ["Textiles", "Construction", "Food & Beverage", "Medical", "Cosmetics", "Automotive"];
const processingOptions = ["Cold-Pressed", "Decortication", "Retting", "Extraction", "Milling"];
const statusOptions = [
  { value: "all", label: "All Products" },
  { value: "active", label: "Active" },
  { value: "prototype", label: "Prototype" },
  { value: "discontinued", label: "Discontinued" },
];

export function FiltersDrawer({ open, onOpenChange, filters, onFiltersChange }: FiltersDrawerProps) {
  const [localFilters, setLocalFilters] = useState<FilterState>(filters || defaultFilters);

  const handleReset = () => {
    setLocalFilters(defaultFilters);
    onFiltersChange(defaultFilters);
  };

  const handleFilterChange = (updates: Partial<FilterState>) => {
    const newFilters = { ...localFilters, ...updates };
    setLocalFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const toggleArrayFilter = (key: keyof FilterState, value: string) => {
    const current = (localFilters[key] as string[]) || [];
    const updated = current.includes(value) 
      ? current.filter(v => v !== value)
      : [...current, value];
    handleFilterChange({ [key]: updated });
  };

  return (
    <>
      {/* Toggle Button */}
      <button
        onClick={() => onOpenChange(!open)}
        className={cn(
          "fixed left-[280px] top-24 z-40 flex items-center gap-2 rounded-lg bg-marine-card px-4 py-2 text-marine-text-secondary hover:text-white transition-all",
          open && "left-[560px]"
        )}
      >
        <Filter className="h-4 w-4" />
        <span className="text-sm font-medium">Filters</span>
      </button>

      {/* Drawer */}
      <div
        className={cn(
          "fixed left-[280px] top-20 bottom-0 z-30 w-[280px] bg-marine-sidebar border-r border-marine-border transform transition-transform duration-300",
          open ? "translate-x-0" : "-translate-x-full"
        )}
      >
        <div className="flex h-full flex-col">
          {/* Header */}
          <div className="border-b border-marine-border px-6 py-4">
            <h3 className="text-lg font-medium text-white">Filters</h3>
          </div>

          {/* Filter Sections */}
          <div className="flex-1 overflow-y-auto px-6 py-4 space-y-6">
            {/* Plant Parts */}
            <div>
              <Label className="text-sm font-medium text-marine-text-secondary mb-3 block">Plant Parts</Label>
              <div className="space-y-2">
                {plantPartOptions.map(part => (
                  <div key={part} className="flex items-center space-x-2">
                    <Checkbox
                      id={`part-${part}`}
                      checked={(localFilters.plantParts || []).includes(part)}
                      onCheckedChange={() => toggleArrayFilter("plantParts", part)}
                      className="border-marine-border data-[state=checked]:bg-green-600 data-[state=checked]:border-green-600"
                    />
                    <Label
                      htmlFor={`part-${part}`}
                      className="text-sm text-marine-text-secondary cursor-pointer hover:text-white"
                    >
                      {part}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            {/* Industries */}
            <div>
              <Label className="text-sm font-medium text-marine-text-secondary mb-3 block">Industries</Label>
              <div className="space-y-2">
                {industryOptions.map(industry => (
                  <div key={industry} className="flex items-center space-x-2">
                    <Checkbox
                      id={`industry-${industry}`}
                      checked={(localFilters.industries || []).includes(industry)}
                      onCheckedChange={() => toggleArrayFilter("industries", industry)}
                      className="border-marine-border data-[state=checked]:bg-green-600 data-[state=checked]:border-green-600"
                    />
                    <Label
                      htmlFor={`industry-${industry}`}
                      className="text-sm text-marine-text-secondary cursor-pointer hover:text-white"
                    >
                      {industry}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            {/* Processing Methods */}
            <div>
              <Label className="text-sm font-medium text-marine-text-secondary mb-3 block">Processing Methods</Label>
              <div className="space-y-2">
                {processingOptions.map(method => (
                  <div key={method} className="flex items-center space-x-2">
                    <Checkbox
                      id={`method-${method}`}
                      checked={(localFilters.processingMethods || []).includes(method)}
                      onCheckedChange={() => toggleArrayFilter("processingMethods", method)}
                      className="border-marine-border data-[state=checked]:bg-green-600 data-[state=checked]:border-green-600"
                    />
                    <Label
                      htmlFor={`method-${method}`}
                      className="text-sm text-marine-text-secondary cursor-pointer hover:text-white"
                    >
                      {method}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            {/* Product Status */}
            <div>
              <Label className="text-sm font-medium text-marine-text-secondary mb-3 block">Product Status</Label>
              <Select
                value={localFilters.productStatus}
                onValueChange={(value) => handleFilterChange({ productStatus: value })}
              >
                <SelectTrigger className="bg-marine-card border-marine-border text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-marine-card border-marine-border">
                  {statusOptions.map(option => (
                    <SelectItem key={option.value} value={option.value} className="text-marine-text-secondary hover:text-white">
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Price Range */}
            <div>
              <Label className="text-sm font-medium text-marine-text-secondary mb-3 block">
                Price Range: ${localFilters.priceRange?.[0] || 0} - ${localFilters.priceRange?.[1] || 10000}
              </Label>
              <Slider
                value={localFilters.priceRange || [0, 10000]}
                onValueChange={(value) => handleFilterChange({ priceRange: value as [number, number] })}
                min={0}
                max={10000}
                step={100}
                className="[&_[role=slider]]:bg-green-600"
              />
            </div>

            {/* Sustainability Score */}
            <div>
              <Label className="text-sm font-medium text-marine-text-secondary mb-3 block">
                Sustainability Score: {localFilters.sustainabilityScore?.[0] || 0} - {localFilters.sustainabilityScore?.[1] || 100}
              </Label>
              <Slider
                value={localFilters.sustainabilityScore || [0, 100]}
                onValueChange={(value) => handleFilterChange({ sustainabilityScore: value as [number, number] })}
                min={0}
                max={100}
                step={5}
                className="[&_[role=slider]]:bg-green-600"
              />
            </div>
          </div>

          {/* Reset Button */}
          <div className="border-t border-marine-border px-6 py-4">
            <button
              onClick={handleReset}
              className="w-full rounded-lg bg-marine-card py-2 text-sm font-medium text-marine-text-secondary hover:bg-marine-cardHover hover:text-white transition-colors"
            >
              Reset Filters
            </button>
          </div>
        </div>
      </div>
    </>
  );
}