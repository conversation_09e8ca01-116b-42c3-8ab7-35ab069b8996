import { useState } from "react";
import { Link, useLocation } from "wouter";
import { But<PERSON> } from "@/components/ui/button";
import { Menu } from "lucide-react";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { cn } from "@/lib/utils";
import HempQuarterzLogo from "@/assets/circle-logo.png?url";

// Navigation links with proper structure
const mainNavLinks = [
  { href: "/", label: "Home" },
  { href: "/about", label: "About" },
  { href: "/plant-parts", label: "Plant Parts" },
  { href: "/industries", label: "Industries" },
  { href: "/hemp-companies", label: "Companies" },
  { href: "/research", label: "Research" },
  { href: "/products", label: "Products" },
];

const NavbarEnhanced = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [location] = useLocation();
  const isHomePage = location === "/";

  const isActive = (href: string) => {
    if (href === "/") return location === href;
    return location.startsWith(href);
  };

  return (
    <nav className="bg-black/80 backdrop-blur-md shadow-md relative z-20">
      {/* Container with consistent padding: 16px mobile, 24px tablet, 32px desktop */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Navbar height: 80px for proper spacing */}
        <div className="flex justify-between items-center h-20">
          {/* Logo Section */}
          <div className="flex-shrink-0 flex items-center">
            <Link href="/">
              <img
                src={HempQuarterzLogo}
                alt="HempQuarterz Logo"
                className="h-14 w-14 cursor-pointer rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
              />
            </Link>
          </div>

          {/* Desktop Navigation - Hidden on mobile */}
          <div className="hidden lg:flex lg:items-center lg:gap-2">
            {/* Main Navigation Links */}
            <nav className="flex items-center">
              {mainNavLinks.map((link) => (
                <Link key={link.href} href={link.href}>
                  <div
                    className={cn(
                      // Base styles with proper padding and line height
                      "px-4 py-2 text-sm font-medium rounded-md transition-all duration-200",
                      "leading-normal", // Ensures proper text spacing
                      "hover:bg-gray-800 hover:text-primary",
                      // Active state
                      isActive(link.href)
                        ? "text-primary bg-gray-800/50 border-b-2 border-primary"
                        : "text-white border-b-2 border-transparent"
                    )}
                  >
                    {link.label}
                  </div>
                </Link>
              ))}
            </nav>

            {/* Auth Buttons with proper spacing */}
            <div className="flex items-center gap-3 ml-8">
              <Link href="/login">
                <Button 
                  variant="ghost" 
                  size="sm"
                  className="h-9 px-4 text-white hover:text-primary hover:bg-gray-800"
                >
                  Sign In
                </Button>
              </Link>
              <Link href="/register">
                <Button 
                  size="sm"
                  className="h-9 px-4 bg-primary hover:bg-primary/90 text-black font-medium"
                >
                  Sign Up
                </Button>
              </Link>
            </div>
          </div>

          {/* Mobile Menu Button */}
          <div className="flex lg:hidden">
            <Sheet open={isMenuOpen} onOpenChange={setIsMenuOpen}>
              <SheetTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="text-gray-300 hover:text-white hover:bg-gray-800 h-10 w-10"
                >
                  <span className="sr-only">Open menu</span>
                  <Menu className="h-6 w-6" />
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="bg-black w-80 p-0">
                <div className="flex flex-col h-full">
                  {/* Mobile Header with Logo */}
                  <div className="flex items-center justify-between p-6 border-b border-gray-800">
                    <Link href="/" onClick={() => setIsMenuOpen(false)}>
                      <img
                        src={HempQuarterzLogo}
                        alt="HempQuarterz Logo"
                        className="h-14 w-14 cursor-pointer rounded-full shadow-lg"
                      />
                    </Link>
                  </div>

                  {/* Mobile Navigation Links */}
                  <nav className="flex-1 overflow-y-auto py-6">
                    <div className="space-y-1 px-4">
                      {mainNavLinks.map((link) => (
                        <Link key={link.href} href={link.href}>
                          <div
                            className={cn(
                              // Mobile nav item with proper spacing and touch target
                              "block px-4 py-3 text-base font-medium rounded-lg transition-colors duration-200",
                              "min-h-[44px]", // Minimum touch target height
                              "leading-normal",
                              isActive(link.href)
                                ? "text-primary bg-gray-800/50 font-semibold"
                                : "text-white hover:text-primary hover:bg-gray-800/30"
                            )}
                            onClick={() => setIsMenuOpen(false)}
                          >
                            {link.label}
                          </div>
                        </Link>
                      ))}
                    </div>

                    {/* Mobile Auth Buttons */}
                    <div className="mt-8 px-4 space-y-3">
                      <Link href="/login" onClick={() => setIsMenuOpen(false)}>
                        <Button 
                          variant="outline" 
                          className="w-full h-11 text-white border-gray-700 hover:bg-gray-800"
                        >
                          Sign In
                        </Button>
                      </Link>
                      <Link href="/register" onClick={() => setIsMenuOpen(false)}>
                        <Button 
                          className="w-full h-11 bg-primary hover:bg-primary/90 text-black font-medium"
                        >
                          Sign Up
                        </Button>
                      </Link>
                    </div>
                  </nav>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default NavbarEnhanced;