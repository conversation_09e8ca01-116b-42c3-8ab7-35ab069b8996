import { ReactNode, useState } from "react";
import { Sidebar } from "./Sidebar";
import { HeaderBar } from "./HeaderBar";
import { ResponsiveFiltersDrawer, FilterState } from "./ResponsiveFiltersDrawer";
import { MainPane } from "./MainPane";
import { cn } from "@/lib/utils";

interface DashboardLayoutProps {
  children: ReactNode;
  showFilters?: boolean;
  onFiltersChange?: (filters: FilterState) => void;
}

const defaultFilters: FilterState = {
  plantParts: [],
  industries: [],
  processingMethods: [],
  productStatus: "all",
  priceRange: [0, 10000],
  sustainabilityScore: [0, 100],
};

export function DashboardLayout({ 
  children, 
  showFilters = false,
  onFiltersChange 
}: DashboardLayoutProps) {
  const [filtersOpen, setFiltersOpen] = useState(false);
  const [filters, setFilters] = useState<FilterState>(defaultFilters);

  const handleFiltersChange = (newFilters: FilterState) => {
    setFilters(newFilters);
    onFiltersChange?.(newFilters);
  };

  return (
    <div className="flex h-screen bg-marine-bg">
      <Sidebar />
      
      <div className="flex flex-1 flex-col">
        <HeaderBar />
        
        <div className="relative flex flex-1 overflow-hidden">
          {showFilters && (
            <ResponsiveFiltersDrawer
              open={filtersOpen}
              onOpenChange={setFiltersOpen}
              filters={filters}
              onFiltersChange={handleFiltersChange}
            />
          )}
          
          <MainPane 
            className={cn(
              "bg-marine-bg transition-all duration-300",
              showFilters && filtersOpen && "lg:ml-[320px] md:ml-[280px]"
            )}
          >
            {children}
          </MainPane>
        </div>
      </div>
    </div>
  );
}