import { useState } from "react";
import { Link, useLocation } from "wouter";
import { cn } from "@/lib/utils";
import { SmartSearch } from "@/components/ui/smart-search";
import { But<PERSON> } from "@/components/ui/button";
import { 
  Search,
  Menu,
  X,
  User,
  Settings,
  LogOut,
  ChevronDown,
  Moon,
  Sun,
  HelpCircle
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useAuth } from "@/components/auth/enhanced-auth-provider";
import { useTheme } from "@/hooks/useTheme";

interface AppHeaderProps {
  onMenuToggle?: () => void;
  className?: string;
}

export function AppHeader({ onMenuToggle, className }: AppHeaderProps) {
  const [location, setLocation] = useLocation();
  const { user, logout } = useAuth();
  const { theme, toggleTheme } = useTheme();
  const [searchOpen, setSearchOpen] = useState(false);

  const handleSearch = (query: string) => {
    setLocation(`/search?q=${encodeURIComponent(query)}`);
    setSearchOpen(false);
  };

  return (
    <>
      {/* Skip Navigation Link for Accessibility */}
      <a href="#main-content" className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-green-600 text-white px-4 py-2 rounded-lg z-50">
        Skip to main content
      </a>

      <header className={cn(
        "fixed top-0 right-0 left-0 lg:left-[280px] h-16 bg-marine-sidebar border-b border-marine-border z-40",
        "flex items-center justify-between px-4 lg:px-6",
        className
      )}>
        {/* Mobile Menu Button */}
        <button
          onClick={onMenuToggle}
          className="lg:hidden p-2 rounded-lg hover:bg-marine-card transition-colors"
          aria-label="Toggle navigation menu"
        >
          <Menu className="h-5 w-5 text-white" />
        </button>

        {/* Search Bar - Desktop */}
        <div className="hidden lg:flex items-center flex-1 max-w-2xl mx-auto">
          <SmartSearch
            placeholder="Search products, companies, research..."
            onSearch={handleSearch}
            className="w-full"
            showVoiceSearch={true}
            showImageSearch={false}
            showAISuggestions={true}
          />
        </div>

        {/* Mobile Search Button */}
        <button
          onClick={() => setSearchOpen(true)}
          className="lg:hidden p-2 rounded-lg hover:bg-marine-card transition-colors"
          aria-label="Open search"
        >
          <Search className="h-5 w-5 text-white" />
        </button>

        {/* Right Section */}
        <div className="flex items-center gap-2">
          {/* Theme Toggle */}
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleTheme}
            className="text-marine-text-secondary hover:text-white"
            aria-label={`Switch to ${theme === 'dark' ? 'light' : 'dark'} mode`}
          >
            {theme === 'dark' ? (
              <Sun className="h-5 w-5" />
            ) : (
              <Moon className="h-5 w-5" />
            )}
          </Button>


          {/* Help Button */}
          <Button
            variant="ghost"
            size="icon"
            className="text-marine-text-secondary hover:text-white"
            aria-label="Help"
          >
            <HelpCircle className="h-5 w-5" />
          </Button>

          {/* User Menu */}
          {user ? (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className="flex items-center gap-2 text-marine-text-secondary hover:text-white"
                >
                  <div className="h-8 w-8 rounded-full bg-green-600 flex items-center justify-center">
                    <span className="text-sm font-medium text-white">
                      {user.email?.charAt(0).toUpperCase()}
                    </span>
                  </div>
                  <span className="hidden md:inline-block text-sm">{user.email}</span>
                  <ChevronDown className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56 bg-marine-card border-marine-border">
                <DropdownMenuLabel>My Account</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href="/profile">
                    <a className="flex items-center gap-2 cursor-pointer" role="menuitem">
                      <User className="h-4 w-4" aria-hidden="true" />
                      <span>Profile</span>
                    </a>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/admin">
                    <a className="flex items-center gap-2 cursor-pointer" role="menuitem">
                      <Settings className="h-4 w-4" aria-hidden="true" />
                      <span>Admin Panel</span>
                    </a>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={logout}
                  className="flex items-center gap-2 cursor-pointer text-red-400"
                >
                  <LogOut className="h-4 w-4" />
                  <span>Log out</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            <Button
              variant="default"
              size="sm"
              asChild
              className="bg-green-600 hover:bg-green-700"
            >
              <Link href="/login">
                <a className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  <span className="hidden md:inline">Sign In</span>
                </a>
              </Link>
            </Button>
          )}
        </div>
      </header>

      {/* Mobile Search Overlay */}
      {searchOpen && (
        <div className="fixed inset-0 bg-marine-bg z-50 lg:hidden">
          <div className="flex items-center justify-between p-4 border-b border-marine-border">
            <h2 className="text-lg font-semibold text-white">Search</h2>
            <button
              onClick={() => setSearchOpen(false)}
              className="p-2 rounded-lg hover:bg-marine-card transition-colors"
              aria-label="Close search"
            >
              <X className="h-5 w-5 text-white" />
            </button>
          </div>
          <div className="p-4">
            <SmartSearch
              placeholder="Search products, companies, research..."
              onSearch={handleSearch}
              className="w-full"
              showVoiceSearch={true}
              showImageSearch={false}
              showAISuggestions={true}
            />
          </div>
        </div>
      )}
    </>
  );
}