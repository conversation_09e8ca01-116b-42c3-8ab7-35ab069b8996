import { ReactNode } from "react";
import { cn } from "@/lib/utils";

interface MainPaneProps {
  children: ReactNode;
  className?: string;
  withPadding?: boolean;
}

export function MainPane({ children, className, withPadding = true }: MainPaneProps) {
  return (
    <main 
      className={cn(
        "flex-1 overflow-auto bg-gray-900/50",
        withPadding && "p-6",
        className
      )}
    >
      {children}
    </main>
  );
}