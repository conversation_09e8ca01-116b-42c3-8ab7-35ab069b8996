import { ReactNode, useState } from "react";
import { AppSidebar } from "./AppSidebar";
import { AppHeader } from "./AppHeader";
import { EnhancedBreadcrumbs } from "@/components/ui/enhanced-breadcrumbs";
import Footer from "./footer";
import { cn } from "@/lib/utils";
import { useDeviceType } from "@/hooks/useDeviceType";

interface AppLayoutProps {
  children: ReactNode;
  className?: string;
  noPadding?: boolean;
}

export function AppLayout({ children, className, noPadding = false }: AppLayoutProps) {
  const { isDesktop } = useDeviceType();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  
  return (
    <div className="min-h-screen bg-marine-bg flex flex-col">
      <AppSidebar 
        className={sidebarOpen ? "translate-x-0" : ""} 
        onClose={() => setSidebarOpen(false)}
      />
      <AppHeader onMenuToggle={() => setSidebarOpen(!sidebarOpen)} />
      
      {/* Main Content Wrapper */}
      <div className="flex-1 lg:pl-[280px] flex flex-col">
        {/* Header spacer */}
        <div className="h-16" />
        
        {/* Breadcrumbs */}
        <div className="border-b border-marine-border bg-marine-sidebar/50">
          <div className={cn(
            "py-2",
            isDesktop ? "px-6" : "px-4"
          )}>
            <EnhancedBreadcrumbs />
          </div>
        </div>
        
        {/* Main content container */}
        <div className="flex-1 flex flex-col">
          <main 
            id="main-content"
            role="main"
            aria-label="Main content"
            className={cn(
              "flex-1",
              !noPadding && (isDesktop ? "p-6" : "p-4"),
              className
            )}
          >
            {children}
          </main>
          
          {/* Footer */}
          <Footer />
        </div>
      </div>
    </div>
  );
}