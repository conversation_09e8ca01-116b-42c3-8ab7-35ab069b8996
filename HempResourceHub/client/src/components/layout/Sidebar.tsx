import { Link, useLocation } from "wouter";
import { cn } from "@/lib/utils";
import { 
  Home,
  Globe,
  BarChart3,
  Layers3,
  Database,
  Leaf
} from "lucide-react";
import { useProductStats } from "@/hooks/use-product-data";
import logoCircle from "@/assets/circle-logo.png";

interface NavItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
}

const navigation: NavItem[] = [
  { 
    name: "Overview", 
    href: "/dashboard", 
    icon: Home,
  },
  { 
    name: "Global Hemp Map", 
    href: "/map", 
    icon: Globe,
  },
  { 
    name: "Industry\nAnalytics", 
    href: "/analytics", 
    icon: BarChart3,
  },
  { 
    name: "Product Categories", 
    href: "/products", 
    icon: Layers3,
  },
  { 
    name: "Hemp Data", 
    href: "/data", 
    icon: Database,
  },
];

export function Sidebar() {
  const [location] = useLocation();
  const { data: stats } = useProductStats();

  return (
    <div className="flex h-screen flex-col bg-black w-[280px] border-r border-gray-800">
      {/* Logo & Brand */}
      <div className="px-6 py-8">
        <div className="flex items-center gap-3">
          <img 
            src={logoCircle} 
            alt="HempQuarterz Logo" 
            className="h-10 w-10 object-contain flex-shrink-0"
          />
          <div className="min-w-0 flex-1">
            <h1 className="text-base font-semibold text-white truncate">HempQuarterz®</h1>
            <p className="text-xs text-gray-400">Analytics Suite</p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-3">
        {navigation.map((item) => {
          const isActive = location === item.href;
          
          return (
            <Link key={item.name} href={item.href}>
              <a
                className={cn(
                  "group relative flex items-center gap-4 rounded-xl px-4 py-4 mb-2 transition-all",
                  isActive
                    ? "bg-gray-900 text-white"
                    : "text-gray-400 hover:bg-gray-900/50 hover:text-white"
                )}
              >
                {isActive && (
                  <div className="absolute left-0 top-1/2 h-8 w-1 -translate-y-1/2 rounded-r-full bg-purple-500" />
                )}
                <item.icon className="h-5 w-5 flex-shrink-0" />
                <span className="text-sm font-medium whitespace-pre-line">{item.name}</span>
              </a>
            </Link>
          );
        })}
      </nav>

      {/* Bottom Status */}
      <div className="px-6 py-6 border-t border-marine-border">
        <div className="flex items-center gap-2">
          <div className="h-2 w-2 rounded-full bg-green-500 animate-pulse" />
          <span className="text-xs text-marine-text-secondary">
            {stats?.totalProducts || 0} Products
          </span>
        </div>
      </div>
    </div>
  );
}