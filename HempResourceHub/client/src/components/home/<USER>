import { Link, useLocation } from "wouter";
import { Search, TrendingUp, Package, Leaf, ArrowRight, Globe, Building2, FlaskConical } from "lucide-react";
import { useState } from "react";
import { useProductStats } from "@/hooks/use-product-data";

export default function DesktopHero() {
  const [searchQuery, setSearchQuery] = useState("");
  const [, setLocation] = useLocation();
  const { data: productStats } = useProductStats();

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      setLocation(`/products?search=${encodeURIComponent(searchQuery)}`);
    }
  };

  const stats = [
    { label: "Products", value: `${productStats?.totalProducts || 0}+`, icon: Package, color: "text-green-500" },
    { label: "Industries", value: "42", icon: Building2, color: "text-blue-500" },
    { label: "Companies", value: "482", icon: Globe, color: "text-purple-500" },
    { label: "Research Papers", value: "156", icon: FlaskConical, color: "text-orange-500" },
  ];

  const features = [
    "Comprehensive product database",
    "Industry analytics & insights",
    "Company profiles & connections",
    "Research paper repository"
  ];

  return (
    <section className="relative min-h-[70vh] flex items-center py-12 lg:py-16">
      {/* Background pattern */}
      <div className="absolute inset-0 bg-gradient-to-br from-marine-bg via-marine-bg to-marine-card/10" />
      <div className="absolute inset-0 bg-[url('/grid.svg')] opacity-5" />
      
      {/* Content */}
      <div className="relative z-10 w-full max-w-7xl mx-auto px-6">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Column - Content */}
          <div>
            {/* Badge */}
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-green-600/10 border border-green-600/20 mb-6">
              <Leaf className="h-4 w-4 text-green-500" />
              <span className="text-sm font-medium text-green-500">Industrial Hemp Database v2.0</span>
            </div>
            
            {/* Heading */}
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4 leading-tight">
              The World's Largest
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-green-400 to-green-600 mt-1">
                Hemp Resource Hub
              </span>
            </h1>
            
            <p className="text-base md:text-lg text-marine-text-secondary mb-6 leading-relaxed">
              Access comprehensive data on industrial hemp products, companies, and research. 
              Make informed decisions with real-time analytics and market insights.
            </p>

            {/* Feature list */}
            <ul className="space-y-2 mb-6">
              {features.map((feature) => (
                <li key={feature} className="flex items-center gap-2 text-sm md:text-base text-marine-text-secondary">
                  <ArrowRight className="h-4 w-4 text-green-500 flex-shrink-0" />
                  {feature}
                </li>
              ))}
            </ul>

            {/* CTA Buttons */}
            <div className="flex flex-wrap gap-4">
              <Link href="/products">
                <a className="inline-flex items-center gap-2 px-6 py-3 rounded-lg bg-green-600 text-white font-medium hover:bg-green-700 transition-colors shadow-lg shadow-green-600/20">
                  Explore Database
                  <Package className="h-5 w-5" />
                </a>
              </Link>
              <Link href="/dashboard">
                <a className="inline-flex items-center gap-2 px-6 py-3 rounded-lg bg-marine-card text-white font-medium hover:bg-marine-cardHover transition-colors border border-marine-border">
                  View Analytics
                  <TrendingUp className="h-5 w-5" />
                </a>
              </Link>
            </div>
          </div>

          {/* Right Column - Stats & Search */}
          <div className="space-y-6">
            {/* Search Bar */}
            <div className="bg-marine-card/50 backdrop-blur-sm rounded-2xl p-6 border border-marine-border">
              <h3 className="text-base font-semibold text-white mb-3">Quick Search</h3>
              <form onSubmit={handleSearch}>
                <div className="relative">
                  <Search className="absolute left-4 top-1/2 h-5 w-5 -translate-y-1/2 text-marine-text-muted" />
                  <input
                    type="search"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="Search products, companies, or industries..."
                    className="w-full rounded-xl bg-marine-bg pl-12 pr-4 py-3 text-sm text-white placeholder-marine-text-muted border border-marine-border focus:border-green-500 focus:outline-none focus:ring-2 focus:ring-green-500/20 transition-all"
                  />
                  <button
                    type="submit"
                    className="absolute right-2 top-1/2 -translate-y-1/2 px-4 py-2 rounded-lg bg-green-600 text-white font-medium hover:bg-green-700 transition-colors"
                  >
                    Search
                  </button>
                </div>
              </form>
            </div>

            {/* Stats Grid */}
            <div className="grid grid-cols-2 gap-4">
              {stats.map((stat) => (
                <div key={stat.label} className="bg-marine-card/50 backdrop-blur-sm rounded-xl p-4 border border-marine-border hover:border-marine-border/80 transition-colors group">
                  <div className="flex items-center justify-between mb-2">
                    <stat.icon className={`h-6 w-6 ${stat.color} opacity-80 group-hover:opacity-100 transition-opacity`} />
                    <span className="text-xs text-marine-text-muted uppercase tracking-wider">{stat.label}</span>
                  </div>
                  <p className="text-2xl font-bold text-white">{stat.value}</p>
                </div>
              ))}
            </div>

            {/* Live Updates */}
            <div className="bg-marine-card/30 rounded-xl p-4 border border-marine-border/50">
              <div className="flex items-center gap-2 mb-2">
                <div className="h-2 w-2 rounded-full bg-green-500 animate-pulse" />
                <span className="text-sm font-medium text-green-500">Live Database</span>
              </div>
              <p className="text-sm text-marine-text-secondary">
                Last updated 2 minutes ago • 47 new products today
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}