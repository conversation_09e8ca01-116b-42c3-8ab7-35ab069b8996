import { Link, useLocation } from "wouter";
import { Search, TrendingUp, Package, Leaf } from "lucide-react";
import { useState } from "react";
import { useProductStats } from "@/hooks/use-product-data";

export default function ModernHero() {
  const [searchQuery, setSearchQuery] = useState("");
  const [, setLocation] = useLocation();
  const { data: productStats } = useProductStats();

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      setLocation(`/products?search=${encodeURIComponent(searchQuery)}`);
    }
  };

  const stats = [
    { label: "Products", value: `${productStats?.totalProducts || 0}+`, icon: Package },
    { label: "Industries", value: "42", icon: TrendingUp },
    { label: "Plant Parts", value: "6", icon: Leaf },
  ];

  return (
    <section className="relative min-h-[500px] flex items-center py-8">
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-marine-bg via-marine-card/20 to-marine-bg" />
      
      {/* Content */}
      <div className="relative z-10 w-full max-w-7xl mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center">
          {/* Badge */}
          <div className="inline-flex items-center gap-2 px-3 py-1.5 rounded-full bg-green-600/10 border border-green-600/20 mb-4">
            <Leaf className="h-4 w-4 text-green-500" />
            <span className="text-xs font-medium text-green-500">Industrial Hemp Database</span>
          </div>
          
          {/* Heading */}
          <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4 leading-tight">
            Discover the Future of
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-green-400 to-green-600"> Sustainable Materials</span>
          </h1>
          
          <p className="text-base md:text-lg text-marine-text-secondary mb-6 leading-relaxed px-4">
            Explore over 700 industrial hemp products across 42 industries. 
            From textiles to construction, find innovative solutions for a sustainable future.
          </p>

          {/* Search Bar */}
          <form onSubmit={handleSearch} className="mb-6 px-4">
            <div className="relative max-w-xl mx-auto">
              <Search className="absolute left-4 top-1/2 h-5 w-5 -translate-y-1/2 text-marine-text-muted" />
              <input
                type="search"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search products, companies, or industries..."
                className="w-full rounded-xl bg-marine-card pl-12 pr-4 py-3 text-sm text-white placeholder-marine-text-muted border border-marine-border focus:border-green-500 focus:outline-none focus:ring-2 focus:ring-green-500/20 transition-all"
              />
              <button
                type="submit"
                className="absolute right-2 top-1/2 -translate-y-1/2 px-4 py-2 rounded-lg bg-green-600 text-white font-medium hover:bg-green-700 transition-colors"
              >
                Search
              </button>
            </div>
          </form>

          {/* CTA Buttons */}
          <div className="flex flex-wrap gap-3 mb-8 justify-center px-4">
            <Link href="/products">
              <a className="inline-flex items-center gap-2 px-4 py-2.5 rounded-lg bg-green-600 text-white text-sm font-medium hover:bg-green-700 transition-colors">
                Explore Products
                <Package className="h-5 w-5" />
              </a>
            </Link>
            <Link href="/dashboard">
              <a className="inline-flex items-center gap-2 px-4 py-2.5 rounded-lg bg-marine-card text-white text-sm font-medium hover:bg-marine-cardHover transition-colors border border-marine-border">
                View Analytics
                <TrendingUp className="h-5 w-5" />
              </a>
            </Link>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-3 gap-4 max-w-lg mx-auto px-4">
            {stats.map((stat) => (
              <div key={stat.label} className="text-center">
                <div className="flex items-center justify-center gap-1 mb-1">
                  <stat.icon className="h-4 w-4 text-green-500" />
                  <p className="text-xl font-bold text-white">{stat.value}</p>
                </div>
                <p className="text-xs text-marine-text-muted">{stat.label}</p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Decorative elements */}
      <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-marine-border to-transparent" />
    </section>
  );
}