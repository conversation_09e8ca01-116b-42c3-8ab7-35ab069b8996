import { useStats } from "@/hooks/use-plant-data";
import { Button } from "@/components/ui/button";
import { ArrowRight, Layers } from "lucide-react";
import { useLocation } from "wouter";

const SimplifiedHero = () => {
  const { data: stats } = useStats();
  const [, setLocation] = useLocation();

  return (
    <div className="relative py-16 sm:py-24">
      {/* Simple gradient background */}
      <div className="absolute inset-0 bg-gradient-to-b from-neutral-900 via-neutral-900/95 to-neutral-900" />
      
      {/* Content */}
      <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        {/* Brand Logo */}
        <div className="mb-8">
          <h1 className="text-5xl sm:text-6xl lg:text-7xl font-black text-hemp-400 font-hemp">
            HempQuarterz®
          </h1>
          <p className="mt-2 text-lg text-neutral-400">
            Industrial Hemp Database & Analytics
          </p>
        </div>

        {/* Primary CTAs */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
          <Button
            size="lg"
            className="bg-hemp-400 hover:bg-hemp-500 text-white font-semibold text-lg px-8 py-6"
            onClick={() => setLocation("/products")}
          >
            Explore Products
            <ArrowRight className="ml-2 h-5 w-5" />
          </Button>
          <Button
            size="lg"
            variant="outline"
            className="border-2 border-hemp-400 text-hemp-400 hover:bg-hemp-400/10 font-semibold text-lg px-8 py-6"
            onClick={() => setLocation("/industries")}
          >
            <Layers className="mr-2 h-5 w-5" />
            Browse by Industry
          </Button>
        </div>

        {/* Quick Stats - Simple and Clean */}
        <div className="grid grid-cols-3 gap-8 max-w-2xl mx-auto">
          <div className="text-center">
            <div className="text-3xl font-bold text-white">
              {stats?.totalProducts || 708}
            </div>
            <div className="text-sm text-neutral-400 mt-1">Products</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-white">
              {stats?.totalIndustries || 49}
            </div>
            <div className="text-sm text-neutral-400 mt-1">Industries</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-white">
              {stats?.totalCompanies || 136}
            </div>
            <div className="text-sm text-neutral-400 mt-1">Companies</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimplifiedHero;