import { useMemo } from "react";
import { useAllHempProducts } from "@/hooks/use-product-data";
import { useCompanies } from "@/hooks/use-companies";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Building2, Package, Globe, ArrowRight, Award } from "lucide-react";
import { useLocation } from "wouter";

const FeaturedCompany = () => {
  const [, setLocation] = useLocation();
  const { data: products, isLoading: productsLoading } = useAllHempProducts();
  const { data: companies, isLoading: companiesLoading } = useCompanies();

  // Select a featured company with the most products
  const featuredCompanyData = useMemo(() => {
    if (!companies || !products) {
      console.log('Featured Company: No companies or products data', { companies: companies?.length, products: products?.length });
      return null;
    }

    console.log('Featured Company: Data available', { companies: companies.length, products: products.length });

    // Count products per company
    const companyProductCounts = companies.map(company => {
      const companyProducts = products.filter(p => {
        // Check primary_company_id first
        if (p.primary_company_id === company.id) return true;
        
        // Then check hemp_company_products relationships
        if (p.hemp_company_products) {
          return p.hemp_company_products.some(rel => 
            rel.hemp_companies?.id === company.id && rel.is_primary
          );
        }
        
        return false;
      });
      
      return {
        company,
        productCount: companyProducts.length,
        products: companyProducts.slice(0, 3) // Get top 3 products
      };
    }).filter(item => item.productCount > 0);

    console.log('Featured Company: Companies with products', companyProductCounts.length);

    // Sort by product count and get top company
    companyProductCounts.sort((a, b) => b.productCount - a.productCount);
    
    // Return a random company from top 5 for variety
    const topCompanies = companyProductCounts.slice(0, 5);
    if (topCompanies.length > 0) {
      const selected = topCompanies[Math.floor(Math.random() * topCompanies.length)];
      console.log('Featured Company: Selected', selected.company.name, 'with', selected.productCount, 'products');
      return selected;
    }

    // Fallback: if no companies have products, just show any company
    console.log('Featured Company: No companies with products, showing any company');
    if (companies.length > 0) {
      const randomCompany = companies[Math.floor(Math.random() * Math.min(5, companies.length))];
      return {
        company: randomCompany,
        productCount: 0,
        products: []
      };
    }

    console.log('Featured Company: No companies found at all');
    return null;
  }, [companies, products]);

  // Show loading state
  if (productsLoading || companiesLoading) {
    return (
      <section className="py-16 bg-neutral-900/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="animate-pulse">
            <div className="h-64 bg-neutral-800 rounded-lg"></div>
          </div>
        </div>
      </section>
    );
  }

  if (!featuredCompanyData) {
    console.log('No featured company found');
    return null;
  }

  const { company, productCount, products: companyProducts } = featuredCompanyData;

  return (
    <section className="py-16 bg-neutral-900/50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-blue-400/10 border border-blue-400/20 mb-4">
            <Award className="w-4 h-4 text-blue-400" />
            <span className="text-sm font-medium text-blue-400">Company Spotlight</span>
          </div>
          
          <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
            Leading Hemp 
            <span className="bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent"> Innovators</span>
          </h2>
        </div>

        {/* Featured Company Card */}
        <Card className="bg-neutral-800/50 backdrop-blur border-neutral-700 overflow-hidden">
          <CardHeader className="pb-4">
            <div className="flex items-start justify-between">
              <div className="flex items-center gap-4">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center">
                  <Building2 className="w-8 h-8 text-white" />
                </div>
                <div>
                  <CardTitle className="text-2xl text-white mb-1">{company.name}</CardTitle>
                  {(company.city || company.country) && (
                    <div className="flex items-center gap-2 text-neutral-400">
                      <Globe className="w-4 h-4" />
                      <span className="text-sm">
                        {[company.city, company.state_province, company.country]
                          .filter(Boolean)
                          .join(', ')}
                      </span>
                    </div>
                  )}
                </div>
              </div>
              {productCount > 0 && (
                <Badge className="bg-blue-500/20 text-blue-400 border border-blue-500/30">
                  <Package className="w-3 h-3 mr-1" />
                  {productCount} Products
                </Badge>
              )}
            </div>
          </CardHeader>
          
          <CardContent>
            {company.description && (
              <p className="text-neutral-300 mb-6">
                {company.description}
              </p>
            )}

            {/* Featured Products from this Company */}
            {companyProducts && companyProducts.length > 0 && (
              <div className="mb-6">
                <h4 className="text-sm font-semibold text-neutral-400 uppercase tracking-wider mb-4">
                  Featured Products
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {companyProducts.map((product) => (
                    <Card 
                      key={product.id} 
                      className="bg-neutral-900/50 border-neutral-700 cursor-pointer hover:border-neutral-600 transition-colors"
                      onClick={() => setLocation(`/product/${product.id}`)}
                    >
                      <CardContent className="p-4">
                        <h5 className="font-semibold text-white mb-2 line-clamp-1">
                          {product.name}
                        </h5>
                        <p className="text-sm text-neutral-400 line-clamp-2">
                          {product.description}
                        </p>
                        {product.commercialization_stage && (
                          <Badge className="mt-3 text-xs" variant="outline">
                            {product.commercialization_stage}
                          </Badge>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            )}

            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                onClick={() => setLocation('/hemp-companies')}
                className="bg-blue-500 hover:bg-blue-600 text-white"
              >
                View All Companies
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
              {company.website && (
                <Button
                  variant="outline"
                  className="border-neutral-600 text-neutral-300 hover:text-white"
                  asChild
                >
                  <a href={company.website} target="_blank" rel="noopener noreferrer">
                    <Globe className="mr-2 h-4 w-4" />
                    Visit Website
                  </a>
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};

export default FeaturedCompany;