import { useEffect, useState } from "react";
import { <PERSON> } from "wouter";
import { motion } from "framer-motion";
import { 
  Leaf, 
  Building2, 
  TrendingUp, 
  Search, 
  Package, 
  Users,
  BarChart3,
  Sparkles,
  FlaskConical,
  Globe
} from "lucide-react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useQuery } from "@tanstack/react-query";
import { cn } from "@/lib/utils";
import supabase from "@/lib/supabase";

interface BentoCardProps {
  className?: string;
  children: React.ReactNode;
}

const BentoCard = ({ className, children }: BentoCardProps) => {
  return (
    <Card className={cn(
      "group relative overflow-hidden bg-gradient-to-br from-gray-900/50 to-gray-800/50 backdrop-blur-sm",
      "border-gray-700/50 hover:border-emerald-500/50 transition-all duration-500",
      "hover:shadow-2xl hover:shadow-emerald-500/10",
      className
    )}>
      {children}
    </Card>
  );
};

export default function BentoGrid() {
  const [hoveredCard, setHoveredCard] = useState<string | null>(null);

  // Fetch statistics
  const { data: stats } = useQuery({
    queryKey: ['homepage-stats'],
    queryFn: async () => {
      // Fetch data using supabase directly
      const [productsRes, companiesRes, plantPartsRes] = await Promise.all([
        supabase.from('uses_products').select('*', { count: 'exact' }),
        supabase.from('hemp_companies').select('*'),
        supabase.from('plant_parts').select('*')
      ]);
      
      const products = productsRes.data || [];
      const companies = companiesRes.data || [];
      const plantParts = plantPartsRes.data || [];
      
      // Get unique industries
      const industries = new Set(products.map(p => p.industry_sub_category_id));
      
      return {
        totalProducts: productsRes.count || products.length,
        totalCompanies: companies.length,
        totalPlantParts: plantParts.length,
        totalIndustries: industries.size,
        featuredProduct: products[Math.floor(Math.random() * Math.min(10, products.length))],
        recentProducts: products.slice(0, 3)
      };
    }
  });

  return (
    <section className="py-16 md:py-24 bg-gray-950">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <motion.h2 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-4xl md:text-5xl font-bold text-white mb-4"
          >
            Explore the Hemp Ecosystem
          </motion.h2>
          <motion.p 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="text-lg text-gray-300 max-w-2xl mx-auto"
          >
            Discover products, companies, and innovations in the industrial hemp industry
          </motion.p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 auto-rows-[200px]">
          
          {/* Large Featured Card - Products Database */}
          <BentoCard className="md:col-span-2 lg:row-span-2">
            <Link to="/products" className="block h-full p-6 lg:p-8">
              <div className="flex flex-col h-full">
                <div className="flex items-center justify-between mb-4">
                  <Package className="w-8 h-8 text-emerald-400" />
                  <span className="text-3xl font-bold text-emerald-400">
                    {stats?.totalProducts || "700+"}
                  </span>
                </div>
                <h3 className="text-2xl font-bold text-white mb-2">Product Database</h3>
                <p className="text-gray-300 mb-6 flex-grow">
                  Comprehensive catalog of hemp-based products across all industries
                </p>
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div className="bg-gray-800/50 rounded-lg p-3">
                    <div className="text-emerald-400 text-sm">Plant Parts</div>
                    <div className="text-white font-semibold">{stats?.totalPlantParts || 8}</div>
                  </div>
                  <div className="bg-gray-800/50 rounded-lg p-3">
                    <div className="text-emerald-400 text-sm">Industries</div>
                    <div className="text-white font-semibold">{stats?.totalIndustries || 15}+</div>
                  </div>
                </div>
                <Button className="w-full bg-emerald-600 hover:bg-emerald-700">
                  Browse Products
                </Button>
              </div>
            </Link>
          </BentoCard>

          {/* Companies Directory */}
          <BentoCard className="lg:row-span-1">
            <Link to="/admin?tab=companies" className="block h-full p-6">
              <div className="flex flex-col h-full">
                <Building2 className="w-8 h-8 text-blue-400 mb-4" />
                <h3 className="text-xl font-bold text-white mb-2">Companies</h3>
                <p className="text-gray-300 text-sm mb-4 flex-grow">
                  {stats?.totalCompanies || 200}+ hemp companies worldwide
                </p>
                <div className="text-blue-400 text-sm font-medium">
                  View Directory →
                </div>
              </div>
            </Link>
          </BentoCard>

          {/* Research Hub */}
          <BentoCard className="lg:row-span-1">
            <Link to="/admin?tab=research" className="block h-full p-6">
              <div className="flex flex-col h-full">
                <FlaskConical className="w-8 h-8 text-purple-400 mb-4" />
                <h3 className="text-xl font-bold text-white mb-2">Research</h3>
                <p className="text-gray-300 text-sm mb-4 flex-grow">
                  Scientific papers and studies
                </p>
                <div className="text-purple-400 text-sm font-medium">
                  Explore Research →
                </div>
              </div>
            </Link>
          </BentoCard>

          {/* Live Stats Ticker */}
          <BentoCard className="md:col-span-2 lg:col-span-1">
            <div className="p-6 h-full flex flex-col">
              <div className="flex items-center justify-between mb-4">
                <TrendingUp className="w-8 h-8 text-green-400" />
                <Sparkles className="w-5 h-5 text-yellow-400 animate-pulse" />
              </div>
              <h3 className="text-xl font-bold text-white mb-4">Live Activity</h3>
              <div className="space-y-3 flex-grow">
                {stats?.recentProducts?.slice(0, 2).map((product, idx) => (
                  <div key={idx} className="text-sm">
                    <div className="text-gray-400">New product added</div>
                    <div className="text-white truncate">{product.name}</div>
                  </div>
                ))}
              </div>
            </div>
          </BentoCard>

          {/* Search Widget */}
          <BentoCard className="lg:col-span-1">
            <Link to="/products" className="block h-full p-6">
              <div className="flex flex-col h-full justify-center items-center text-center">
                <Search className="w-12 h-12 text-emerald-400 mb-4" />
                <h3 className="text-xl font-bold text-white mb-2">Smart Search</h3>
                <p className="text-gray-300 text-sm">
                  Find products by plant part, industry, or keyword
                </p>
              </div>
            </Link>
          </BentoCard>

          {/* Featured Product Spotlight */}
          {stats?.featuredProduct && (
            <BentoCard className="md:col-span-2">
              <Link to={`/products/${stats.featuredProduct.id}`} className="block h-full p-6">
                <div className="flex items-start gap-4">
                  <div className="w-16 h-16 bg-emerald-500/20 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Leaf className="w-8 h-8 text-emerald-400" />
                  </div>
                  <div className="flex-grow">
                    <div className="text-emerald-400 text-sm mb-1">Featured Product</div>
                    <h3 className="text-lg font-bold text-white mb-2">{stats.featuredProduct.name}</h3>
                    <p className="text-gray-300 text-sm line-clamp-2">
                      {stats.featuredProduct.description}
                    </p>
                  </div>
                </div>
              </Link>
            </BentoCard>
          )}

          {/* Global Impact */}
          <BentoCard className="lg:col-span-1">
            <div className="p-6 h-full flex flex-col justify-center items-center text-center">
              <Globe className="w-10 h-10 text-cyan-400 mb-4 animate-spin-slow" />
              <h3 className="text-lg font-bold text-white mb-2">Global Impact</h3>
              <p className="text-gray-300 text-sm">
                Sustainable solutions for a better future
              </p>
            </div>
          </BentoCard>

          {/* Analytics Preview */}
          <BentoCard className="lg:col-span-1">
            <Link to="/admin?tab=analytics" className="block h-full p-6">
              <div className="flex flex-col h-full">
                <BarChart3 className="w-8 h-8 text-orange-400 mb-4" />
                <h3 className="text-xl font-bold text-white mb-2">Analytics</h3>
                <p className="text-gray-300 text-sm mb-4 flex-grow">
                  Database insights & trends
                </p>
                <div className="text-orange-400 text-sm font-medium">
                  View Analytics →
                </div>
              </div>
            </Link>
          </BentoCard>

        </div>
      </div>
    </section>
  );
}