import { useMemo } from "react";
import { useAllHempProducts } from "@/hooks/use-product-data";
import { useCompanies } from "@/hooks/use-companies";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Star, Building2, Leaf, ArrowRight, Sparkles } from "lucide-react";
import { useLocation } from "wouter";
import SmartProductImage from "@/components/ui/smart-product-image";

const FeaturedProductSpotlight = () => {
  const [, setLocation] = useLocation();
  const { data: products, isLoading: productsLoading } = useAllHempProducts();
  const { data: companies } = useCompanies();

  // Select the best product to feature
  const featuredProduct = useMemo(() => {
    if (!products || products.length === 0) return null;

    // First try to find products with complete information
    const candidateProducts = products.filter(product => 
      product.name &&
      product.description &&
      product.description.length > 50 &&
      product.image_url
    );

    // Sort by quality criteria
    const sortedProducts = candidateProducts.sort((a, b) => {
      // Prioritize products with benefits
      const aBenefits = Array.isArray(a.benefits) ? a.benefits.length : 0;
      const bBenefits = Array.isArray(b.benefits) ? b.benefits.length : 0;
      if (aBenefits !== bBenefits) return bBenefits - aBenefits;

      // Then by description length
      return (b.description?.length || 0) - (a.description?.length || 0);
    });

    // Return the best product, or any product as fallback
    return sortedProducts[0] || products[0] || null;
  }, [products]);

  // Find the company for this product
  const company = useMemo(() => {
    if (!featuredProduct || !companies) return null;
    
    // Check primary_company_id first
    if (featuredProduct.primary_company_id) {
      return companies.find(c => c.id === featuredProduct.primary_company_id);
    }
    
    // Then check hemp_company_products relationships
    if (featuredProduct.hemp_company_products && featuredProduct.hemp_company_products.length > 0) {
      const primaryRelation = featuredProduct.hemp_company_products.find(rel => rel.is_primary);
      if (primaryRelation?.hemp_companies) {
        return companies.find(c => c.id === primaryRelation.hemp_companies.id);
      }
    }
    
    return null;
  }, [featuredProduct, companies]);

  // Show loading state
  if (productsLoading) {
    return (
      <section className="py-16 bg-gradient-to-br from-neutral-900 via-neutral-900/95 to-hemp-900/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="animate-pulse">
            <div className="h-96 bg-neutral-800 rounded-lg"></div>
          </div>
        </div>
      </section>
    );
  }

  // Don't render if no product found
  if (!featuredProduct) {
    console.log('No featured product found');
    return null;
  }

  return (
    <section className="py-16 bg-gradient-to-br from-neutral-900 via-neutral-900/95 to-hemp-900/10">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-hemp-400/10 border border-hemp-400/20 mb-4">
            <Sparkles className="w-4 h-4 text-hemp-400" />
            <span className="text-sm font-medium text-hemp-400">Product Spotlight</span>
          </div>
          
          <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
            Featured Hemp 
            <span className="bg-gradient-to-r from-hemp-400 to-emerald-400 bg-clip-text text-transparent"> Innovation</span>
          </h2>
        </div>

        {/* Featured Product Card */}
        <Card className="bg-neutral-800/50 backdrop-blur border-neutral-700 overflow-hidden">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-0">
            {/* Image Section */}
            <div className="relative h-96 lg:h-auto">
              <SmartProductImage
                product={featuredProduct}
                plantPartName={featuredProduct.plant_part_name}
                className="absolute inset-0 w-full h-full object-cover"
              />
              <div className="absolute top-4 left-4">
                <Badge className="bg-hemp-400 text-white font-semibold px-3 py-1">
                  <Star className="w-3 h-3 mr-1 fill-current" />
                  Featured
                </Badge>
              </div>
            </div>

            {/* Content Section */}
            <div className="p-8 lg:p-12">
              <CardHeader className="p-0 mb-6">
                <div className="flex flex-wrap items-center gap-3 mb-4">
                  {company && (
                    <Badge variant="outline" className="text-neutral-300 border-neutral-600">
                      <Building2 className="h-3 w-3 mr-1" />
                      {company.name}
                    </Badge>
                  )}
                  {featuredProduct.plant_part_name && (
                    <Badge variant="outline" className="text-neutral-300 border-neutral-600">
                      <Leaf className="h-3 w-3 mr-1" />
                      {featuredProduct.plant_part_name}
                    </Badge>
                  )}
                  {featuredProduct.commercialization_stage && (
                    <Badge className="bg-emerald-500/20 text-emerald-400 border border-emerald-500/30">
                      {featuredProduct.commercialization_stage}
                    </Badge>
                  )}
                </div>
                <h3 className="text-2xl lg:text-3xl font-bold text-white mb-4">
                  {featuredProduct.name}
                </h3>
              </CardHeader>
              
              <CardContent className="p-0">
                <p className="text-neutral-300 text-lg leading-relaxed mb-6">
                  {featuredProduct.description}
                </p>

                {featuredProduct.benefits && featuredProduct.benefits.length > 0 && (
                  <div className="mb-8">
                    <h4 className="text-sm font-semibold text-neutral-400 uppercase tracking-wider mb-3">
                      Key Benefits
                    </h4>
                    <ul className="space-y-2">
                      {featuredProduct.benefits.slice(0, 3).map((benefit, index) => (
                        <li key={index} className="flex items-start gap-3">
                          <span className="text-hemp-400 mt-1">•</span>
                          <span className="text-neutral-300">{benefit}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                <div className="flex flex-col sm:flex-row gap-4">
                  <Button
                    onClick={() => setLocation(`/product/${featuredProduct.id}`)}
                    className="bg-hemp-400 hover:bg-hemp-500 text-white"
                  >
                    Learn More
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                  {featuredProduct.industry_category && (
                    <Button
                      variant="outline"
                      onClick={() => setLocation('/industries')}
                      className="border-neutral-600 text-neutral-300 hover:text-white"
                    >
                      Explore {featuredProduct.industry_category}
                    </Button>
                  )}
                </div>
              </CardContent>
            </div>
          </div>
        </Card>
      </div>
    </section>
  );
};

export default FeaturedProductSpotlight;