import React, { useState, useMemo } from "react";
import { <PERSON> } from "wouter";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  Search,
  BookOpen,
  FileText,
  Microscope,
  Leaf,
  Factory,
  TrendingUp,
  Users,
  Globe,
  Award,
  Lightbulb,
  BarChart3,
  Calendar,
  ExternalLink,
  ChevronRight,
  Filter,
  Star
} from "lucide-react";
import { cn } from "@/lib/utils";
import { componentStyles } from "@/lib/design-system";

interface KnowledgeCategory {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
  count: number;
  entries: KnowledgeEntry[];
}

interface KnowledgeEntry {
  id: string;
  title: string;
  description: string;
  category: string;
  tags: string[];
  readTime: string;
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced';
  lastUpdated: string;
  views: number;
  rating: number;
  author?: string;
  type: 'Research Paper' | 'Case Study' | 'Guide' | 'Report' | 'Analysis';
}

const knowledgeCategories: KnowledgeCategory[] = [
  {
    id: 'cultivation',
    name: 'Hemp Cultivation',
    description: 'Growing techniques, soil management, and agricultural practices',
    icon: Leaf,
    color: 'text-green-400 bg-green-500/20 border-green-500/30',
    count: 45,
    entries: []
  },
  {
    id: 'processing',
    name: 'Processing & Manufacturing',
    description: 'Industrial processing methods and manufacturing techniques',
    icon: Factory,
    color: 'text-blue-400 bg-blue-500/20 border-blue-500/30',
    count: 38,
    entries: []
  },
  {
    id: 'research',
    name: 'Scientific Research',
    description: 'Peer-reviewed studies and scientific findings',
    icon: Microscope,
    color: 'text-purple-400 bg-purple-500/20 border-purple-500/30',
    count: 67,
    entries: []
  },
  {
    id: 'market',
    name: 'Market Analysis',
    description: 'Industry trends, market data, and economic insights',
    icon: TrendingUp,
    color: 'text-orange-400 bg-orange-500/20 border-orange-500/30',
    count: 29,
    entries: []
  },
  {
    id: 'regulations',
    name: 'Regulations & Policy',
    description: 'Legal frameworks, compliance, and policy updates',
    icon: FileText,
    color: 'text-red-400 bg-red-500/20 border-red-500/30',
    count: 23,
    entries: []
  },
  {
    id: 'sustainability',
    name: 'Sustainability',
    description: 'Environmental impact and sustainable practices',
    icon: Globe,
    color: 'text-emerald-400 bg-emerald-500/20 border-emerald-500/30',
    count: 31,
    entries: []
  },
  {
    id: 'innovation',
    name: 'Innovation & Technology',
    description: 'New technologies and innovative applications',
    icon: Lightbulb,
    color: 'text-yellow-400 bg-yellow-500/20 border-yellow-500/30',
    count: 19,
    entries: []
  },
  {
    id: 'case-studies',
    name: 'Case Studies',
    description: 'Real-world applications and success stories',
    icon: Award,
    color: 'text-pink-400 bg-pink-500/20 border-pink-500/30',
    count: 15,
    entries: []
  }
];

// Sample knowledge entries
const sampleEntries: KnowledgeEntry[] = [
  {
    id: '1',
    title: 'Hemp Fiber Processing: From Plant to Product',
    description: 'Comprehensive guide on industrial hemp fiber processing techniques and quality optimization methods.',
    category: 'processing',
    tags: ['fiber', 'processing', 'quality control'],
    readTime: '12 min',
    difficulty: 'Intermediate',
    lastUpdated: '2024-01-15',
    views: 1247,
    rating: 4.8,
    author: 'Dr. Sarah Chen',
    type: 'Guide'
  },
  {
    id: '2',
    title: 'Sustainable Hemp Cultivation Practices',
    description: 'Research on environmentally friendly hemp growing methods and their impact on yield and quality.',
    category: 'cultivation',
    tags: ['sustainability', 'organic', 'yield optimization'],
    readTime: '8 min',
    difficulty: 'Beginner',
    lastUpdated: '2024-01-10',
    views: 892,
    rating: 4.6,
    author: 'Hemp Research Institute',
    type: 'Research Paper'
  },
  {
    id: '3',
    title: 'Global Hemp Market Trends 2024',
    description: 'Analysis of current market conditions, growth projections, and emerging opportunities in the hemp industry.',
    category: 'market',
    tags: ['market analysis', 'trends', 'forecasting'],
    readTime: '15 min',
    difficulty: 'Advanced',
    lastUpdated: '2024-01-20',
    views: 2156,
    rating: 4.9,
    author: 'Market Research Group',
    type: 'Report'
  }
];

interface KnowledgeBaseExplorerProps {
  className?: string;
}

export function KnowledgeBaseExplorer({ className }: KnowledgeBaseExplorerProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [sortBy, setSortBy] = useState<'recent' | 'popular' | 'rating'>('recent');

  // Filter and sort entries
  const filteredEntries = useMemo(() => {
    let entries = sampleEntries;

    // Filter by search query
    if (searchQuery) {
      entries = entries.filter(entry =>
        entry.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        entry.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        entry.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    // Filter by category
    if (selectedCategory) {
      entries = entries.filter(entry => entry.category === selectedCategory);
    }

    // Sort entries
    entries.sort((a, b) => {
      switch (sortBy) {
        case 'popular':
          return b.views - a.views;
        case 'rating':
          return b.rating - a.rating;
        case 'recent':
        default:
          return new Date(b.lastUpdated).getTime() - new Date(a.lastUpdated).getTime();
      }
    });

    return entries;
  }, [searchQuery, selectedCategory, sortBy]);

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Beginner': return 'text-green-400 bg-green-500/20';
      case 'Intermediate': return 'text-yellow-400 bg-yellow-500/20';
      case 'Advanced': return 'text-red-400 bg-red-500/20';
      default: return 'text-gray-400 bg-gray-500/20';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'Research Paper': return <Microscope className="h-4 w-4" />;
      case 'Case Study': return <Award className="h-4 w-4" />;
      case 'Guide': return <BookOpen className="h-4 w-4" />;
      case 'Report': return <BarChart3 className="h-4 w-4" />;
      case 'Analysis': return <TrendingUp className="h-4 w-4" />;
      default: return <FileText className="h-4 w-4" />;
    }
  };

  return (
    <div className={cn("w-full space-y-8", className)}>
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold text-white">
          <span className="bg-gradient-to-r from-green-400 to-purple-400 bg-clip-text text-transparent">
            Hemp Knowledge Base
          </span>
        </h1>
        <p className="text-xl text-gray-300 max-w-3xl mx-auto">
          Explore our comprehensive collection of research, guides, and insights 
          about industrial hemp applications and innovations.
        </p>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col lg:flex-row gap-4 items-center">
        <div className="relative flex-1 max-w-2xl">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
          <Input
            placeholder="Search knowledge base..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 bg-black/50 border-gray-700 text-white placeholder:text-gray-400"
          />
        </div>
        
        <div className="flex gap-2">
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as any)}
            className="px-4 py-2 bg-black/50 border border-gray-700 rounded-lg text-white"
          >
            <option value="recent">Most Recent</option>
            <option value="popular">Most Popular</option>
            <option value="rating">Highest Rated</option>
          </select>
          
          {selectedCategory && (
            <Button
              variant="ghost"
              onClick={() => setSelectedCategory(null)}
              className="text-gray-400 hover:text-white"
            >
              Clear Filter
            </Button>
          )}
        </div>
      </div>

      {/* Category Grid */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {knowledgeCategories.map((category) => {
          const Icon = category.icon;
          const isSelected = selectedCategory === category.id;
          
          return (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(isSelected ? null : category.id)}
              className={cn(
                "p-6 rounded-xl border-2 transition-all duration-200 text-left",
                componentStyles.interface.card,
                isSelected 
                  ? category.color.replace('bg-', 'bg-').replace('/20', '/30') + ' border-current'
                  : 'border-gray-700 hover:border-gray-600'
              )}
            >
              <div className="flex items-center gap-3 mb-2">
                <Icon className={cn("h-6 w-6", isSelected ? "text-current" : "text-gray-400")} />
                <Badge variant="secondary" className="text-xs">
                  {category.count}
                </Badge>
              </div>
              <h3 className={cn(
                "font-semibold mb-1",
                isSelected ? "text-current" : "text-white"
              )}>
                {category.name}
              </h3>
              <p className="text-sm text-gray-400 leading-relaxed">
                {category.description}
              </p>
            </button>
          );
        })}
      </div>

      {/* Knowledge Entries */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold text-white">
            {selectedCategory 
              ? knowledgeCategories.find(c => c.id === selectedCategory)?.name 
              : 'All Knowledge Entries'
            }
          </h2>
          <span className="text-gray-400">
            {filteredEntries.length} entries found
          </span>
        </div>

        <div className="grid gap-6">
          {filteredEntries.map((entry) => (
            <Card key={entry.id} className={cn(
              componentStyles.interface.card,
              "p-6 hover:border-gray-600 transition-all duration-200 group"
            )}>
              <div className="flex flex-col lg:flex-row gap-6">
                {/* Content */}
                <div className="flex-1 space-y-4">
                  <div className="flex items-start justify-between gap-4">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        {getTypeIcon(entry.type)}
                        <Badge variant="outline" className="text-xs">
                          {entry.type}
                        </Badge>
                        <Badge className={getDifficultyColor(entry.difficulty)}>
                          {entry.difficulty}
                        </Badge>
                      </div>
                      
                      <h3 className="text-xl font-semibold text-white group-hover:text-purple-300 transition-colors">
                        {entry.title}
                      </h3>
                    </div>
                    
                    <div className="flex items-center gap-1 text-yellow-400">
                      <Star className="h-4 w-4 fill-current" />
                      <span className="text-sm">{entry.rating}</span>
                    </div>
                  </div>

                  <p className="text-gray-300 leading-relaxed">
                    {entry.description}
                  </p>

                  <div className="flex flex-wrap gap-2">
                    {entry.tags.map((tag) => (
                      <Badge key={tag} variant="secondary" className="text-xs bg-gray-800 text-gray-300">
                        {tag}
                      </Badge>
                    ))}
                  </div>

                  <div className="flex items-center gap-6 text-sm text-gray-400">
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      {new Date(entry.lastUpdated).toLocaleDateString()}
                    </div>
                    <div className="flex items-center gap-1">
                      <Users className="h-4 w-4" />
                      {entry.views.toLocaleString()} views
                    </div>
                    <span>{entry.readTime} read</span>
                    {entry.author && <span>by {entry.author}</span>}
                  </div>
                </div>

                {/* Action */}
                <div className="flex lg:flex-col items-center lg:items-end gap-3">
                  <Link href={`/research/${entry.id}`}>
                    <Button className="bg-purple-500 hover:bg-purple-600">
                      Read More
                      <ChevronRight className="h-4 w-4 ml-1" />
                    </Button>
                  </Link>
                  
                  <Button variant="ghost" size="sm" className="text-gray-400 hover:text-white">
                    <ExternalLink className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>

        {filteredEntries.length === 0 && (
          <div className="text-center py-12">
            <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-white mb-2">No entries found</h3>
            <p className="text-gray-400">Try adjusting your search or filters</p>
          </div>
        )}
      </div>
    </div>
  );
}
