import React, { useState, useMemo } from "react";
import { <PERSON> } from "wouter";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  Search,
  ShoppingBag,
  Star,
  Heart,
  Share2,
  Filter,
  Grid3X3,
  List,
  Leaf,
  Package,
  Truck,
  Shield,
  Award,
  TrendingUp,
  Eye,
  ExternalLink,
  MapPin,
  DollarSign
} from "lucide-react";
import { cn } from "@/lib/utils";
import { componentStyles } from "@/lib/design-system";

interface Product {
  id: string;
  name: string;
  description: string;
  category: string;
  subcategory: string;
  plantPart: string;
  industry: string;
  benefits: string[];
  images: string[];
  price?: {
    min: number;
    max: number;
    currency: string;
    unit: string;
  };
  supplier: {
    name: string;
    location: string;
    rating: number;
    verified: boolean;
  };
  specifications: {
    [key: string]: string;
  };
  certifications: string[];
  availability: 'In Stock' | 'Limited' | 'Pre-Order' | 'Out of Stock';
  rating: number;
  reviewCount: number;
  views: number;
  isFeatured: boolean;
  isNew: boolean;
  tags: string[];
}

const sampleProducts: Product[] = [
  {
    id: '1',
    name: 'Premium Hemp Fiber Insulation',
    description: 'High-performance thermal insulation made from 100% natural hemp fibers. Excellent for sustainable construction projects.',
    category: 'Building Materials',
    subcategory: 'Insulation',
    plantPart: 'Fiber',
    industry: 'Construction',
    benefits: ['Thermal Insulation', 'Moisture Regulation', 'Fire Resistant', 'Eco-Friendly'],
    images: ['/images/products/hemp-insulation-1.jpg', '/images/products/hemp-insulation-2.jpg'],
    price: {
      min: 45,
      max: 65,
      currency: 'USD',
      unit: 'per m²'
    },
    supplier: {
      name: 'EcoBuild Materials',
      location: 'Portland, OR',
      rating: 4.8,
      verified: true
    },
    specifications: {
      'R-Value': '3.5 per inch',
      'Density': '25-30 kg/m³',
      'Thickness': '50-200mm',
      'Fire Rating': 'Class A'
    },
    certifications: ['GREENGUARD Gold', 'Cradle to Cradle', 'LEED Certified'],
    availability: 'In Stock',
    rating: 4.7,
    reviewCount: 89,
    views: 1247,
    isFeatured: true,
    isNew: false,
    tags: ['sustainable', 'thermal', 'natural', 'construction']
  },
  {
    id: '2',
    name: 'Organic Hemp Seed Oil',
    description: 'Cold-pressed, unrefined hemp seed oil rich in omega fatty acids. Perfect for culinary and cosmetic applications.',
    category: 'Food & Wellness',
    subcategory: 'Oils',
    plantPart: 'Seeds',
    industry: 'Food & Beverage',
    benefits: ['Omega 3 & 6', 'Antioxidants', 'Vitamin E', 'Heart Health'],
    images: ['/images/products/hemp-oil-1.jpg', '/images/products/hemp-oil-2.jpg'],
    price: {
      min: 25,
      max: 45,
      currency: 'USD',
      unit: 'per 500ml'
    },
    supplier: {
      name: 'Pure Hemp Co.',
      location: 'Colorado, USA',
      rating: 4.9,
      verified: true
    },
    specifications: {
      'Volume': '500ml',
      'Extraction': 'Cold-pressed',
      'Omega-6': '55-60%',
      'Omega-3': '15-20%'
    },
    certifications: ['USDA Organic', 'Non-GMO', 'Third-Party Tested'],
    availability: 'In Stock',
    rating: 4.8,
    reviewCount: 156,
    views: 892,
    isFeatured: false,
    isNew: true,
    tags: ['organic', 'cold-pressed', 'omega', 'wellness']
  },
  {
    id: '3',
    name: 'Hemp-Cotton Blend Fabric',
    description: 'Sustainable textile blend combining hemp durability with cotton softness. Ideal for apparel and home textiles.',
    category: 'Textiles',
    subcategory: 'Fabrics',
    plantPart: 'Fiber',
    industry: 'Fashion & Textiles',
    benefits: ['Durable', 'Breathable', 'UV Protection', 'Antimicrobial'],
    images: ['/images/products/hemp-fabric-1.jpg', '/images/products/hemp-fabric-2.jpg'],
    price: {
      min: 18,
      max: 28,
      currency: 'USD',
      unit: 'per yard'
    },
    supplier: {
      name: 'Sustainable Textiles Inc.',
      location: 'North Carolina, USA',
      rating: 4.6,
      verified: true
    },
    specifications: {
      'Composition': '55% Hemp, 45% Cotton',
      'Weight': '200 GSM',
      'Width': '150cm',
      'Weave': 'Plain'
    },
    certifications: ['GOTS Certified', 'OEKO-TEX Standard 100'],
    availability: 'Limited',
    rating: 4.5,
    reviewCount: 73,
    views: 634,
    isFeatured: false,
    isNew: false,
    tags: ['sustainable', 'blend', 'apparel', 'durable']
  }
];

const categories = [
  'All Categories',
  'Building Materials',
  'Food & Wellness',
  'Textiles',
  'Automotive',
  'Paper & Packaging',
  'Bioplastics',
  'Personal Care'
];

const plantParts = [
  'All Parts',
  'Fiber',
  'Seeds',
  'Leaves',
  'Stalks',
  'Flowers',
  'Roots'
];

const industries = [
  'All Industries',
  'Construction',
  'Food & Beverage',
  'Fashion & Textiles',
  'Automotive',
  'Cosmetics',
  'Paper',
  'Energy'
];

interface MarketplaceCatalogProps {
  className?: string;
}

export function MarketplaceCatalog({ className }: MarketplaceCatalogProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All Categories');
  const [selectedPlantPart, setSelectedPlantPart] = useState('All Parts');
  const [selectedIndustry, setSelectedIndustry] = useState('All Industries');
  const [sortBy, setSortBy] = useState<'relevance' | 'price' | 'rating' | 'newest'>('relevance');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFeaturedOnly, setShowFeaturedOnly] = useState(false);

  // Filter and sort products
  const filteredProducts = useMemo(() => {
    let products = sampleProducts;

    // Filter by search query
    if (searchQuery) {
      products = products.filter(product =>
        product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.benefits.some(benefit => 
          benefit.toLowerCase().includes(searchQuery.toLowerCase())
        ) ||
        product.tags.some(tag => 
          tag.toLowerCase().includes(searchQuery.toLowerCase())
        )
      );
    }

    // Filter by category
    if (selectedCategory !== 'All Categories') {
      products = products.filter(product => product.category === selectedCategory);
    }

    // Filter by plant part
    if (selectedPlantPart !== 'All Parts') {
      products = products.filter(product => product.plantPart === selectedPlantPart);
    }

    // Filter by industry
    if (selectedIndustry !== 'All Industries') {
      products = products.filter(product => product.industry === selectedIndustry);
    }

    // Filter featured only
    if (showFeaturedOnly) {
      products = products.filter(product => product.isFeatured);
    }

    // Sort products
    products.sort((a, b) => {
      switch (sortBy) {
        case 'price':
          const aPrice = a.price ? a.price.min : 0;
          const bPrice = b.price ? b.price.min : 0;
          return aPrice - bPrice;
        case 'rating':
          return b.rating - a.rating;
        case 'newest':
          return b.isNew ? 1 : -1;
        case 'relevance':
        default:
          return b.views - a.views;
      }
    });

    return products;
  }, [searchQuery, selectedCategory, selectedPlantPart, selectedIndustry, sortBy, showFeaturedOnly]);

  const getAvailabilityColor = (availability: string) => {
    switch (availability) {
      case 'In Stock': return 'text-green-400 bg-green-500/20';
      case 'Limited': return 'text-yellow-400 bg-yellow-500/20';
      case 'Pre-Order': return 'text-blue-400 bg-blue-500/20';
      case 'Out of Stock': return 'text-red-400 bg-red-500/20';
      default: return 'text-gray-400 bg-gray-500/20';
    }
  };

  const ProductCard = ({ product }: { product: Product }) => (
    <Card className={cn(
      componentStyles.interface.card,
      "overflow-hidden hover:border-gray-600 transition-all duration-200 group",
      product.isFeatured && "ring-2 ring-purple-500/20"
    )}>
      {/* Product Image */}
      <div className="relative aspect-[4/3] bg-gray-800">
        {product.images[0] ? (
          <img 
            src={product.images[0]} 
            alt={product.name}
            className="w-full h-full object-cover"
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.src = '/images/placeholder-product.jpg';
            }}
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <Package className="h-12 w-12 text-gray-400" />
          </div>
        )}
        
        {/* Badges */}
        <div className="absolute top-3 left-3 flex flex-col gap-2">
          {product.isFeatured && (
            <Badge className="bg-purple-500 text-white">Featured</Badge>
          )}
          {product.isNew && (
            <Badge className="bg-green-500 text-white">New</Badge>
          )}
        </div>
        
        {/* Actions */}
        <div className="absolute top-3 right-3 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
          <Button size="sm" variant="ghost" className="bg-black/50 text-white hover:bg-black/70">
            <Heart className="h-4 w-4" />
          </Button>
          <Button size="sm" variant="ghost" className="bg-black/50 text-white hover:bg-black/70">
            <Share2 className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Product Info */}
      <div className="p-4 space-y-3">
        <div className="space-y-2">
          <div className="flex items-start justify-between gap-2">
            <h3 className="font-semibold text-white group-hover:text-purple-300 transition-colors line-clamp-2">
              {product.name}
            </h3>
            <div className="flex items-center gap-1 text-yellow-400 flex-shrink-0">
              <Star className="h-4 w-4 fill-current" />
              <span className="text-sm">{product.rating}</span>
            </div>
          </div>
          
          <p className="text-sm text-gray-400 line-clamp-2">
            {product.description}
          </p>
        </div>

        {/* Plant Part & Industry */}
        <div className="flex items-center gap-2 text-xs">
          <Badge variant="outline" className="border-green-500/30 text-green-400">
            <Leaf className="h-3 w-3 mr-1" />
            {product.plantPart}
          </Badge>
          <Badge variant="outline" className="border-purple-500/30 text-purple-400">
            {product.industry}
          </Badge>
        </div>

        {/* Benefits */}
        <div className="flex flex-wrap gap-1">
          {product.benefits.slice(0, 3).map((benefit) => (
            <Badge key={benefit} variant="secondary" className="text-xs bg-gray-800 text-gray-300">
              {benefit}
            </Badge>
          ))}
          {product.benefits.length > 3 && (
            <Badge variant="secondary" className="text-xs bg-gray-800 text-gray-300">
              +{product.benefits.length - 3}
            </Badge>
          )}
        </div>

        {/* Price & Availability */}
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            {product.price && (
              <div className="text-lg font-bold text-white">
                ${product.price.min}
                {product.price.min !== product.price.max && `-$${product.price.max}`}
                <span className="text-sm text-gray-400 font-normal">
                  {product.price.unit}
                </span>
              </div>
            )}
            <Badge className={getAvailabilityColor(product.availability)}>
              {product.availability}
            </Badge>
          </div>
          
          <div className="text-right">
            <div className="text-sm text-gray-400">
              {product.supplier.name}
            </div>
            <div className="flex items-center gap-1 text-xs text-gray-500">
              <MapPin className="h-3 w-3" />
              {product.supplier.location}
            </div>
          </div>
        </div>

        {/* Action Button */}
        <Link href={`/products/${product.id}`}>
          <Button className="w-full bg-purple-500 hover:bg-purple-600">
            View Details
          </Button>
        </Link>
      </div>
    </Card>
  );

  return (
    <div className={cn("w-full space-y-8", className)}>
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold text-white">
          <span className="bg-gradient-to-r from-green-400 to-purple-400 bg-clip-text text-transparent">
            Hemp Product Marketplace
          </span>
        </h1>
        <p className="text-xl text-gray-300 max-w-3xl mx-auto">
          Discover innovative hemp products across industries. From sustainable materials 
          to wellness products, explore the versatility of industrial hemp.
        </p>
      </div>

      {/* Search and Filters */}
      <Card className={cn(componentStyles.interface.card, "p-6")}>
        <div className="space-y-4">
          {/* Search Bar */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
            <Input
              placeholder="Search products, benefits, or applications..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 bg-black/50 border-gray-700 text-white placeholder:text-gray-400"
            />
          </div>

          {/* Filters Row */}
          <div className="flex flex-col lg:flex-row gap-4 items-center">
            <div className="flex flex-wrap gap-4 flex-1">
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-48 bg-black/50 border-gray-700 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-black border-gray-700">
                  {categories.map(category => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={selectedPlantPart} onValueChange={setSelectedPlantPart}>
                <SelectTrigger className="w-40 bg-black/50 border-gray-700 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-black border-gray-700">
                  {plantParts.map(part => (
                    <SelectItem key={part} value={part}>
                      {part}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={selectedIndustry} onValueChange={setSelectedIndustry}>
                <SelectTrigger className="w-48 bg-black/50 border-gray-700 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-black border-gray-700">
                  {industries.map(industry => (
                    <SelectItem key={industry} value={industry}>
                      {industry}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Button
                variant={showFeaturedOnly ? "default" : "outline"}
                onClick={() => setShowFeaturedOnly(!showFeaturedOnly)}
                className="border-gray-700"
              >
                <Star className="h-4 w-4 mr-2" />
                Featured
              </Button>
            </div>

            {/* Sort and View Controls */}
            <div className="flex items-center gap-2">
              <Select value={sortBy} onValueChange={(value) => setSortBy(value as any)}>
                <SelectTrigger className="w-40 bg-black/50 border-gray-700 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-black border-gray-700">
                  <SelectItem value="relevance">Relevance</SelectItem>
                  <SelectItem value="price">Price</SelectItem>
                  <SelectItem value="rating">Rating</SelectItem>
                  <SelectItem value="newest">Newest</SelectItem>
                </SelectContent>
              </Select>

              <div className="flex border border-gray-700 rounded-lg">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                  className="rounded-r-none"
                >
                  <Grid3X3 className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                  className="rounded-l-none"
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Results Summary */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-white">
          Products
        </h2>
        <span className="text-gray-400">
          {filteredProducts.length} products found
        </span>
      </div>

      {/* Products Grid */}
      <div className={cn(
        "grid gap-6",
        viewMode === 'grid' 
          ? "grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
          : "grid-cols-1"
      )}>
        {filteredProducts.map((product) => (
          <ProductCard key={product.id} product={product} />
        ))}
      </div>

      {filteredProducts.length === 0 && (
        <div className="text-center py-12">
          <ShoppingBag className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-white mb-2">No products found</h3>
          <p className="text-gray-400">Try adjusting your search or filters</p>
        </div>
      )}
    </div>
  );
}
