import { useState, useMemo, useCallback } from "react";
import { useAllHempProducts } from "@/hooks/use-product-data";
import { useAllPlantParts } from "@/hooks/use-plant-data";
import { useIndustrySubCategories } from "@/hooks/use-supabase-data";
import { useCompanies } from "@/hooks/use-companies";
import { 
  Search, 
  Filter, 
  Grid3X3, 
  List, 
  BarChart3, 
  TreePine,
  Building2,
  Package,
  ChevronDown,
  Layers,
  TrendingUp,
  Sparkles,
  Hash
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import UltimateProductCard from "@/components/product/ultimate-product-card";
import PokedexProductList from "./PokedexProductList";
import { Progress } from "@/components/ui/progress";
import { motion, AnimatePresence } from "framer-motion";

interface ProductsExplorerProps {
  initialView?: 'grid' | 'list' | 'industry' | 'plant' | 'company' | 'analytics';
}

export function ProductsExplorer({ initialView = 'grid' }: ProductsExplorerProps) {
  const { data: products, isLoading: productsLoading } = useAllHempProducts();
  const { data: plantParts } = useAllPlantParts();
  const { data: industries } = useIndustrySubCategories();
  const { data: companies } = useCompanies();

  const [searchTerm, setSearchTerm] = useState("");
  const [selectedIndustry, setSelectedIndustry] = useState<number | null>(null);
  const [selectedPlantPart, setSelectedPlantPart] = useState<number | null>(null);
  const [selectedCompany, setSelectedCompany] = useState<number | null>(null);
  const [selectedStage, setSelectedStage] = useState<string | null>(null);
  const [currentView, setCurrentView] = useState(initialView);
  const [itemsPerPage, setItemsPerPage] = useState(24);
  const [currentPage, setCurrentPage] = useState(1);
  const [sortBy, setSortBy] = useState<'name' | 'recent' | 'industry'>('name');

  // Advanced filtering and grouping
  const { filteredProducts, groupedData, stats } = useMemo(() => {
    if (!products) return { filteredProducts: [], groupedData: {}, stats: {} };

    // Apply filters
    let filtered = products.filter(product => {
      const matchesSearch = !searchTerm || 
        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.description?.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesIndustry = !selectedIndustry || 
        product.industry_sub_category_id === selectedIndustry;
      
      const matchesPlantPart = !selectedPlantPart || 
        product.plant_part_id === selectedPlantPart;
      
      const matchesCompany = !selectedCompany || 
        product.primary_company_id === selectedCompany;
      
      const productStage = product.commercialization_stage || product.commercializationStage;
      const matchesStage = !selectedStage || productStage === selectedStage;
      
      return matchesSearch && matchesIndustry && matchesPlantPart && matchesCompany && matchesStage;
    });

    // Sort products
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'recent':
          return new Date(b.created_at || 0).getTime() - new Date(a.created_at || 0).getTime();
        case 'industry':
          return (a.industry_sub_category_id || 0) - (b.industry_sub_category_id || 0);
        default:
          return a.name.localeCompare(b.name);
      }
    });

    // Group data for different views
    const byIndustry = filtered.reduce((acc, product) => {
      const industryId = product.industry_sub_category_id || 0;
      if (!acc[industryId]) acc[industryId] = [];
      acc[industryId].push(product);
      return acc;
    }, {} as Record<number, typeof filtered>);

    const byPlantPart = filtered.reduce((acc, product) => {
      const partId = product.plant_part_id || 0;
      if (!acc[partId]) acc[partId] = [];
      acc[partId].push(product);
      return acc;
    }, {} as Record<number, typeof filtered>);

    const byCompany = filtered.reduce((acc, product) => {
      const companyId = product.primary_company_id || 0;
      if (!acc[companyId]) acc[companyId] = [];
      acc[companyId].push(product);
      return acc;
    }, {} as Record<number, typeof filtered>);

    // Calculate statistics
    const stats = {
      totalProducts: filtered.length,
      byStage: filtered.reduce((acc, p) => {
        const stage = p.commercialization_stage || p.commercializationStage || 'Unknown';
        acc[stage] = (acc[stage] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
      topIndustries: Object.entries(byIndustry)
        .map(([id, products]) => ({
          id: Number(id),
          count: products.length,
          name: industries?.find(i => i.id === Number(id))?.name || 'Unknown'
        }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 10)
    };

    return {
      filteredProducts: filtered,
      groupedData: { byIndustry, byPlantPart, byCompany },
      stats
    };
  }, [products, searchTerm, selectedIndustry, selectedPlantPart, selectedCompany, selectedStage, sortBy, industries]);

  // Pagination
  const paginatedProducts = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return filteredProducts.slice(startIndex, startIndex + itemsPerPage);
  }, [filteredProducts, currentPage, itemsPerPage]);

  const totalPages = Math.ceil(filteredProducts.length / itemsPerPage);

  // Reset page when filters change
  const handleFilterChange = useCallback(() => {
    setCurrentPage(1);
  }, []);

  return (
    <div className="space-y-6">
      {/* Header with Search and View Toggle */}
      <div className="flex flex-col gap-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Package className="h-8 w-8 text-green-500" />
            <div>
              <h1 className="text-3xl font-bold text-white">Hemp Products Explorer</h1>
              <p className="text-gray-400">{products?.length || 0} products across {industries?.length || 0} industries</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Select value={sortBy} onValueChange={(v: any) => setSortBy(v)}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="name">Name (A-Z)</SelectItem>
                <SelectItem value="recent">Most Recent</SelectItem>
                <SelectItem value="industry">By Industry</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Search Bar */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
          <Input
            type="text"
            placeholder="Search products, descriptions, or benefits..."
            value={searchTerm}
            onChange={(e) => {
              setSearchTerm(e.target.value);
              handleFilterChange();
            }}
            className="pl-10 pr-4 py-3 bg-gray-800/50 border-gray-700 text-white"
          />
          {searchTerm && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setSearchTerm("");
                handleFilterChange();
              }}
              className="absolute right-2 top-1/2 -translate-y-1/2"
            >
              Clear
            </Button>
          )}
        </div>

        {/* View Tabs */}
        <Tabs value={currentView} onValueChange={setCurrentView} className="w-full">
          <TabsList className="grid grid-cols-6 w-full max-w-4xl mx-auto">
            <TabsTrigger value="grid" className="flex items-center gap-2">
              <Grid3X3 className="h-4 w-4" />
              <span className="hidden sm:inline">Grid</span>
            </TabsTrigger>
            <TabsTrigger value="list" className="flex items-center gap-2">
              <Hash className="h-4 w-4" />
              <span className="hidden sm:inline">List</span>
            </TabsTrigger>
            <TabsTrigger value="industry" className="flex items-center gap-2">
              <Building2 className="h-4 w-4" />
              <span className="hidden sm:inline">Industry</span>
            </TabsTrigger>
            <TabsTrigger value="plant" className="flex items-center gap-2">
              <TreePine className="h-4 w-4" />
              <span className="hidden sm:inline">Plant</span>
            </TabsTrigger>
            <TabsTrigger value="company" className="flex items-center gap-2">
              <Sparkles className="h-4 w-4" />
              <span className="hidden sm:inline">Company</span>
            </TabsTrigger>
            <TabsTrigger value="analytics" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              <span className="hidden sm:inline">Analytics</span>
            </TabsTrigger>
          </TabsList>

          {/* Active Filters */}
          <div className="flex flex-wrap gap-2 mt-4">
            {selectedIndustry && (
              <Badge variant="secondary" className="flex items-center gap-1">
                Industry: {industries?.find(i => i.id === selectedIndustry)?.name}
                <button onClick={() => setSelectedIndustry(null)} className="ml-1">×</button>
              </Badge>
            )}
            {selectedPlantPart && (
              <Badge variant="secondary" className="flex items-center gap-1">
                Plant Part: {plantParts?.find(p => p.id === selectedPlantPart)?.name}
                <button onClick={() => setSelectedPlantPart(null)} className="ml-1">×</button>
              </Badge>
            )}
            {selectedCompany && (
              <Badge variant="secondary" className="flex items-center gap-1">
                Company: {companies?.find(c => c.id === selectedCompany)?.name}
                <button onClick={() => setSelectedCompany(null)} className="ml-1">×</button>
              </Badge>
            )}
            {selectedStage && (
              <Badge variant="secondary" className="flex items-center gap-1">
                Stage: {selectedStage}
                <button onClick={() => setSelectedStage(null)} className="ml-1">×</button>
              </Badge>
            )}
          </div>

          {/* Grid View - Traditional Product Cards */}
          <TabsContent value="grid" className="mt-6">
            {productsLoading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                {Array.from({ length: 12 }).map((_, i) => (
                  <Skeleton key={i} className="h-96" />
                ))}
              </div>
            ) : (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                  <AnimatePresence mode="popLayout">
                    {paginatedProducts.map((product) => (
                      <motion.div
                        key={product.id}
                        layout
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        transition={{ duration: 0.3 }}
                      >
                        <UltimateProductCard
                          product={{
                            ...product,
                            company_name: companies?.find(c => c.id === product.primary_company_id)?.name,
                            commercialization_stage: product.commercialization_stage || product.commercializationStage,
                            views: Math.floor(Math.random() * 1000 + 100), // Placeholder until we have real data
                            rating: 4 + Math.random() * 1, // Placeholder until we have real data
                            sustainability_score: Math.floor(Math.random() * 30 + 70) // Placeholder until we have real data
                          }}
                          industryName={industries?.find(i => i.id === product.industry_sub_category_id)?.mainCategory}
                          subIndustryName={industries?.find(i => i.id === product.industry_sub_category_id)?.name}
                          plantPartName={plantParts?.find(p => p.id === product.plant_part_id)?.name}
                          variant="default"
                          showQuickActions={true}
                          onFavorite={(id) => console.log('Favorited:', id)}
                          onShare={(p) => console.log('Share:', p)}
                        />
                      </motion.div>
                    ))}
                  </AnimatePresence>
                </div>

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="flex justify-center items-center gap-2 mt-8">
                    <Button
                      variant="outline"
                      onClick={() => setCurrentPage(p => Math.max(1, p - 1))}
                      disabled={currentPage === 1}
                    >
                      Previous
                    </Button>
                    <span className="text-gray-400">
                      Page {currentPage} of {totalPages}
                    </span>
                    <Button
                      variant="outline"
                      onClick={() => setCurrentPage(p => Math.min(totalPages, p + 1))}
                      disabled={currentPage === totalPages}
                    >
                      Next
                    </Button>
                  </div>
                )}
              </>
            )}
          </TabsContent>

          {/* List View - Pokédex Style */}
          <TabsContent value="list" className="mt-6">
            <PokedexProductList
              products={filteredProducts}
              industries={industries}
              plantParts={plantParts}
              companies={companies}
              onProductClick={(product) => {
                // TODO: Navigate to product detail or open modal
                console.log('Product clicked:', product);
              }}
            />
          </TabsContent>

          {/* Industry View - Grouped by Industry */}
          <TabsContent value="industry" className="mt-6">
            <div className="space-y-6">
              {Object.entries(groupedData.byIndustry)
                .sort(([, a], [, b]) => b.length - a.length)
                .map(([industryId, products]) => {
                  const industry = industries?.find(i => i.id === Number(industryId));
                  if (!industry) return null;
                  
                  return (
                    <Card key={industryId} className="bg-gray-800/50 border-gray-700">
                      <CardHeader className="cursor-pointer" onClick={() => setSelectedIndustry(Number(industryId))}>
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-xl text-white flex items-center gap-2">
                            <Building2 className="h-5 w-5 text-green-500" />
                            {industry.name}
                          </CardTitle>
                          <Badge variant="secondary">{products.length} products</Badge>
                        </div>
                        <CardDescription>{industry.description}</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                          {products.slice(0, 6).map(product => (
                            <div key={product.id} className="text-center p-2 rounded-lg bg-gray-700/50 hover:bg-gray-700 transition-colors cursor-pointer">
                              <img 
                                src={product.image_url || '/placeholder.jpg'} 
                                alt={product.name}
                                className="w-full h-20 object-cover rounded mb-2"
                              />
                              <p className="text-xs text-white truncate">{product.name}</p>
                            </div>
                          ))}
                          {products.length > 6 && (
                            <div className="flex items-center justify-center p-2 rounded-lg bg-gray-700/50">
                              <span className="text-gray-400 text-sm">+{products.length - 6} more</span>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
            </div>
          </TabsContent>

          {/* Plant Part View - Visual Tree Structure */}
          <TabsContent value="plant" className="mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {plantParts?.map(part => {
                const products = groupedData.byPlantPart[part.id] || [];
                const percentage = ((products.length / filteredProducts.length) * 100).toFixed(1);
                
                return (
                  <Card key={part.id} className="bg-gray-800/50 border-gray-700 hover:border-green-500 transition-colors cursor-pointer"
                        onClick={() => setSelectedPlantPart(part.id)}>
                    <CardHeader>
                      <CardTitle className="flex items-center justify-between">
                        <span className="text-green-400">{part.name}</span>
                        <Badge>{products.length}</Badge>
                      </CardTitle>
                      <CardDescription>{part.description}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <Progress value={Number(percentage)} className="h-2" />
                        <p className="text-sm text-gray-400">{percentage}% of filtered products</p>
                        <div className="flex flex-wrap gap-1 mt-3">
                          {products.slice(0, 3).map(p => (
                            <Badge key={p.id} variant="outline" className="text-xs">
                              {p.name.slice(0, 20)}...
                            </Badge>
                          ))}
                          {products.length > 3 && (
                            <Badge variant="outline" className="text-xs">
                              +{products.length - 3} more
                            </Badge>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </TabsContent>

          {/* Company View - Brand Showcase */}
          <TabsContent value="company" className="mt-6">
            <div className="space-y-4">
              {companies?.filter(company => {
                const products = groupedData.byCompany[company.id];
                return products && products.length > 0;
              })
              .sort((a, b) => (groupedData.byCompany[b.id]?.length || 0) - (groupedData.byCompany[a.id]?.length || 0))
              .map(company => {
                const products = groupedData.byCompany[company.id] || [];
                
                return (
                  <Card key={company.id} className="bg-gray-800/50 border-gray-700">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div className="flex items-center gap-4">
                          {company.logo_url && (
                            <img src={company.logo_url} alt={company.name} className="h-12 w-12 object-contain" />
                          )}
                          <div>
                            <CardTitle className="text-white">{company.name}</CardTitle>
                            <CardDescription>
                              {company.city}, {company.country} • {products.length} products
                            </CardDescription>
                          </div>
                        </div>
                        <Badge variant={company.verified ? "default" : "secondary"}>
                          {company.verified ? "Verified" : "Unverified"}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="flex gap-2 overflow-x-auto pb-2">
                        {products.slice(0, 10).map(product => (
                          <div key={product.id} className="flex-shrink-0 w-32">
                            <img 
                              src={product.image_url || '/placeholder.jpg'} 
                              alt={product.name}
                              className="w-full h-24 object-cover rounded-lg mb-1"
                            />
                            <p className="text-xs text-gray-300 truncate">{product.name}</p>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </TabsContent>

          {/* Analytics View - Data Visualization */}
          <TabsContent value="analytics" className="mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Products by Stage */}
              <Card className="bg-gray-800/50 border-gray-700">
                <CardHeader>
                  <CardTitle>Products by Development Stage</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {Object.entries(stats.byStage).map(([stage, count]) => {
                      const percentage = ((count / stats.totalProducts) * 100).toFixed(1);
                      return (
                        <div key={stage}>
                          <div className="flex justify-between mb-1">
                            <span className="text-sm text-gray-300">{stage}</span>
                            <span className="text-sm text-gray-400">{count} ({percentage}%)</span>
                          </div>
                          <Progress value={Number(percentage)} className="h-2" />
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>

              {/* Top Industries */}
              <Card className="bg-gray-800/50 border-gray-700">
                <CardHeader>
                  <CardTitle>Top Industries</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {stats.topIndustries.map((industry, index) => (
                      <div key={industry.id} className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <span className="text-2xl font-bold text-gray-500">#{index + 1}</span>
                          <span className="text-sm text-gray-300">{industry.name}</span>
                        </div>
                        <Badge variant="secondary">{industry.count} products</Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Summary Stats */}
              <Card className="bg-gray-800/50 border-gray-700 md:col-span-2">
                <CardHeader>
                  <CardTitle>Database Overview</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center p-4 bg-gray-700/50 rounded-lg">
                      <p className="text-3xl font-bold text-green-500">{stats.totalProducts}</p>
                      <p className="text-sm text-gray-400">Filtered Products</p>
                    </div>
                    <div className="text-center p-4 bg-gray-700/50 rounded-lg">
                      <p className="text-3xl font-bold text-blue-500">{industries?.length || 0}</p>
                      <p className="text-sm text-gray-400">Industries</p>
                    </div>
                    <div className="text-center p-4 bg-gray-700/50 rounded-lg">
                      <p className="text-3xl font-bold text-purple-500">{companies?.length || 0}</p>
                      <p className="text-sm text-gray-400">Companies</p>
                    </div>
                    <div className="text-center p-4 bg-gray-700/50 rounded-lg">
                      <p className="text-3xl font-bold text-orange-500">{plantParts?.length || 0}</p>
                      <p className="text-sm text-gray-400">Plant Parts</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}

export default ProductsExplorer;