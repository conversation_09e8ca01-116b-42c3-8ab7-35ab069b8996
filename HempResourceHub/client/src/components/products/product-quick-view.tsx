"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON>et<PERSON>ontent, SheetDescription, <PERSON>etHeader, <PERSON>et<PERSON><PERSON><PERSON>, Sheet<PERSON>rigger } from "@/components/ui/sheet"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"
import { AspectRatio } from "@/components/ui/aspect-ratio"
import { Skeleton } from "@/components/ui/skeleton"
import { 
  Package, 
  Building2, 
  Leaf, 
  Factory, 
  ExternalLink, 
  TrendingUp,
  Calendar,
  Tag,
  FileText,
  Sparkles,
  TreePine
} from "lucide-react"
import { useLocation } from "wouter"

interface ProductQuickViewProps {
  productId: string
  trigger?: React.ReactNode
  onOpenChange?: (open: boolean) => void
}

export function ProductQuickView({ productId, trigger, onOpenChange }: ProductQuickViewProps) {
  const [open, setOpen] = useState(false)
  const [product, setProduct] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [, setLocation] = useLocation()

  useEffect(() => {
    if (open && productId) {
      fetchProduct()
    }
  }, [open, productId])

  const fetchProduct = async () => {
    setLoading(true)
    try {
      const response = await fetch(`/api/products/${productId}`)
      if (!response.ok) throw new Error("Failed to fetch product")
      const data = await response.json()
      setProduct(data)
    } catch (error) {
      console.error("Error fetching product:", error)
    } finally {
      setLoading(false)
    }
  }

  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen)
    onOpenChange?.(newOpen)
  }

  const getStageColor = (stage: string) => {
    switch (stage?.toLowerCase()) {
      case "commercial": return "bg-green-500/10 text-green-600 border-green-500/20"
      case "development": return "bg-blue-500/10 text-blue-600 border-blue-500/20"
      case "research": return "bg-purple-500/10 text-purple-600 border-purple-500/20"
      default: return "bg-gray-500/10 text-gray-600 border-gray-500/20"
    }
  }

  return (
    <Sheet open={open} onOpenChange={handleOpenChange}>
      {trigger && (
        <SheetTrigger asChild>
          {trigger}
        </SheetTrigger>
      )}
      <SheetContent className="w-full sm:max-w-2xl overflow-hidden">
        <SheetHeader className="pb-4">
          <SheetTitle className="text-2xl">Product Details</SheetTitle>
          <SheetDescription>
            Quick overview of product information
          </SheetDescription>
        </SheetHeader>
        
        <ScrollArea className="h-[calc(100vh-120px)] pr-4">
          {loading ? (
            <div className="space-y-4">
              <Skeleton className="h-48 w-full" />
              <Skeleton className="h-8 w-3/4" />
              <Skeleton className="h-20 w-full" />
              <div className="grid grid-cols-2 gap-4">
                <Skeleton className="h-24" />
                <Skeleton className="h-24" />
              </div>
            </div>
          ) : product ? (
            <div className="space-y-6">
              {/* Product Image */}
              {product.image_url && (
                <AspectRatio ratio={16 / 9} className="overflow-hidden rounded-lg border">
                  <img
                    src={product.image_url}
                    alt={product.name}
                    className="object-cover w-full h-full"
                  />
                </AspectRatio>
              )}

              {/* Product Header */}
              <div className="space-y-3">
                <div className="flex items-start justify-between gap-4">
                  <h3 className="text-xl font-semibold">{product.name}</h3>
                  <Badge className={getStageColor(product.commercialization_stage)}>
                    {product.commercialization_stage}
                  </Badge>
                </div>
                
                <p className="text-muted-foreground leading-relaxed">
                  {product.description}
                </p>

                {/* Quick Stats */}
                <div className="flex flex-wrap gap-4 text-sm">
                  {product.industry_name && (
                    <div className="flex items-center gap-1">
                      <Factory className="h-4 w-4 text-muted-foreground" />
                      <span>{product.industry_name}</span>
                    </div>
                  )}
                  {product.plant_part?.name && (
                    <div className="flex items-center gap-1">
                      <Leaf className="h-4 w-4 text-muted-foreground" />
                      <span>{product.plant_part.name}</span>
                    </div>
                  )}
                  {product.company?.name && (
                    <div className="flex items-center gap-1">
                      <Building2 className="h-4 w-4 text-muted-foreground" />
                      <span>{product.company.name}</span>
                    </div>
                  )}
                </div>
              </div>

              <Separator />

              {/* Detailed Information Tabs */}
              <Tabs defaultValue="details" className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="details">Details</TabsTrigger>
                  <TabsTrigger value="technical">Technical</TabsTrigger>
                  <TabsTrigger value="environmental">Environmental</TabsTrigger>
                </TabsList>
                
                <TabsContent value="details" className="space-y-4 mt-4">
                  {/* Benefits */}
                  {product.benefits_advantages && (
                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-base flex items-center gap-2">
                          <Sparkles className="h-4 w-4" />
                          Benefits & Advantages
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-muted-foreground">
                          {product.benefits_advantages}
                        </p>
                      </CardContent>
                    </Card>
                  )}

                  {/* Tags */}
                  {product.tags && product.tags.length > 0 && (
                    <div className="space-y-2">
                      <h4 className="text-sm font-medium flex items-center gap-2">
                        <Tag className="h-4 w-4" />
                        Tags
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {product.tags.map((tag: string, index: number) => (
                          <Badge key={index} variant="secondary">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Metadata */}
                  <div className="space-y-2 text-sm">
                    {product.created_at && (
                      <div className="flex items-center gap-2 text-muted-foreground">
                        <Calendar className="h-4 w-4" />
                        <span>Added {new Date(product.created_at).toLocaleDateString()}</span>
                      </div>
                    )}
                  </div>
                </TabsContent>
                
                <TabsContent value="technical" className="space-y-4 mt-4">
                  {product.technical_specifications ? (
                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-base flex items-center gap-2">
                          <FileText className="h-4 w-4" />
                          Technical Specifications
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-muted-foreground whitespace-pre-wrap">
                          {product.technical_specifications}
                        </p>
                      </CardContent>
                    </Card>
                  ) : (
                    <p className="text-sm text-muted-foreground text-center py-8">
                      No technical specifications available
                    </p>
                  )}

                  {product.market_applications && (
                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-base flex items-center gap-2">
                          <TrendingUp className="h-4 w-4" />
                          Market Applications
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-muted-foreground">
                          {product.market_applications}
                        </p>
                      </CardContent>
                    </Card>
                  )}
                </TabsContent>
                
                <TabsContent value="environmental" className="space-y-4 mt-4">
                  {product.environmental_benefits ? (
                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-base flex items-center gap-2">
                          <TreePine className="h-4 w-4" />
                          Environmental Benefits
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-muted-foreground">
                          {product.environmental_benefits}
                        </p>
                      </CardContent>
                    </Card>
                  ) : (
                    <p className="text-sm text-muted-foreground text-center py-8">
                      No environmental information available
                    </p>
                  )}

                  {product.sustainability_score && (
                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-base">Sustainability Score</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2">
                          <div className="flex justify-between items-center">
                            <span className="text-2xl font-bold">
                              {product.sustainability_score}
                            </span>
                            <span className="text-sm text-muted-foreground">/ 100</span>
                          </div>
                          <div className="w-full bg-secondary rounded-full h-2">
                            <div
                              className="bg-green-500 h-2 rounded-full transition-all"
                              style={{ width: `${product.sustainability_score}%` }}
                            />
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </TabsContent>
              </Tabs>

              {/* Actions */}
              <div className="flex gap-2 pt-4">
                <Button 
                  variant="default" 
                  className="flex-1"
                  onClick={() => {
                    setLocation(`/product/${productId}`)
                    setOpen(false)
                  }}
                >
                  View Full Details
                  <ExternalLink className="ml-2 h-4 w-4" />
                </Button>
                <Button variant="outline" onClick={() => setOpen(false)}>
                  Close
                </Button>
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-muted-foreground">Product not found</p>
            </div>
          )}
        </ScrollArea>
      </SheetContent>
    </Sheet>
  )
}