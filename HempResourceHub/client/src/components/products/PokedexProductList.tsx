import { useState, useMemo } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { 
  ChevronRight, 
  Hash, 
  Building2, 
  Tree<PERSON><PERSON>,
  Sparkles,
  Search,
  Filter,
  Shuffle,
  X
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { Slider } from "@/components/ui/slider";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { cn } from "@/lib/utils";

interface Product {
  id: number;
  name: string;
  description?: string;
  industry_sub_category_id?: number;
  plant_part_id?: number;
  primary_company_id?: number;
  commercialization_stage?: string;
  commercializationStage?: string;
  image_url?: string;
  created_at?: string;
}

interface PokedexProductListProps {
  products: Product[];
  industries?: any[];
  plantParts?: any[];
  companies?: any[];
  onProductClick?: (product: Product) => void;
}

// Industry color mapping inspired by Pokemon types
const industryColors: Record<string, string> = {
  "Textiles": "bg-purple-500/20 text-purple-300 border-purple-500/50",
  "Construction": "bg-stone-500/20 text-stone-300 border-stone-500/50", 
  "Food & Beverage": "bg-green-500/20 text-green-300 border-green-500/50",
  "Health & Beauty": "bg-pink-500/20 text-pink-300 border-pink-500/50",
  "Automotive": "bg-gray-500/20 text-gray-300 border-gray-500/50",
  "Agriculture": "bg-emerald-500/20 text-emerald-300 border-emerald-500/50",
  "Energy": "bg-yellow-500/20 text-yellow-300 border-yellow-500/50",
  "Technology": "bg-blue-500/20 text-blue-300 border-blue-500/50",
  "Medical": "bg-red-500/20 text-red-300 border-red-500/50",
  "Packaging": "bg-orange-500/20 text-orange-300 border-orange-500/50",
  "Default": "bg-gray-500/20 text-gray-300 border-gray-500/50"
};

// Plant part color mapping
const plantPartColors: Record<string, string> = {
  "Fiber": "bg-amber-500/20 text-amber-300",
  "Seeds": "bg-lime-500/20 text-lime-300",
  "Flowers": "bg-fuchsia-500/20 text-fuchsia-300",
  "Leaves": "bg-teal-500/20 text-teal-300",
  "Stalks": "bg-brown-500/20 text-brown-300",
  "Roots": "bg-orange-500/20 text-orange-300",
  "Oil": "bg-indigo-500/20 text-indigo-300",
  "Default": "bg-gray-500/20 text-gray-300"
};

export function PokedexProductList({
  products,
  industries,
  plantParts,
  companies,
  onProductClick
}: PokedexProductListProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [showAdvancedSearch, setShowAdvancedSearch] = useState(false);
  const [selectedIndustries, setSelectedIndustries] = useState<number[]>([]);
  const [selectedPlantParts, setSelectedPlantParts] = useState<number[]>([]);
  const [selectedStages, setSelectedStages] = useState<string[]>([]);
  const [productRange, setProductRange] = useState([1, products.length]);
  const [hoveredProduct, setHoveredProduct] = useState<number | null>(null);

  // Get unique commercialization stages
  const stages = useMemo(() => {
    const stageSet = new Set<string>();
    products.forEach(p => {
      const stage = p.commercialization_stage || p.commercializationStage;
      if (stage) stageSet.add(stage);
    });
    return Array.from(stageSet).sort();
  }, [products]);

  // Filter products
  const filteredProducts = useMemo(() => {
    return products.filter((product, index) => {
      const productNumber = index + 1;
      
      // Search filter
      const matchesSearch = !searchTerm || 
        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        `#${String(productNumber).padStart(4, '0')}`.includes(searchTerm);
      
      // Number range filter
      const inRange = productNumber >= productRange[0] && productNumber <= productRange[1];
      
      // Industry filter
      const matchesIndustry = selectedIndustries.length === 0 || 
        (product.industry_sub_category_id && selectedIndustries.includes(product.industry_sub_category_id));
      
      // Plant part filter
      const matchesPlantPart = selectedPlantParts.length === 0 || 
        (product.plant_part_id && selectedPlantParts.includes(product.plant_part_id));
      
      // Stage filter
      const productStage = product.commercialization_stage || product.commercializationStage;
      const matchesStage = selectedStages.length === 0 || 
        (productStage && selectedStages.includes(productStage));
      
      return matchesSearch && inRange && matchesIndustry && matchesPlantPart && matchesStage;
    });
  }, [products, searchTerm, productRange, selectedIndustries, selectedPlantParts, selectedStages]);

  // Surprise Me! function
  const handleSurpriseMe = () => {
    if (filteredProducts.length > 0) {
      const randomIndex = Math.floor(Math.random() * filteredProducts.length);
      const randomProduct = filteredProducts[randomIndex];
      if (onProductClick) {
        onProductClick(randomProduct);
      }
    }
  };

  // Get industry color
  const getIndustryColor = (industryId?: number) => {
    if (!industryId || !industries) return industryColors.Default;
    const industry = industries.find(i => i.id === industryId);
    const mainCategory = industry?.main_category || "Default";
    return industryColors[mainCategory] || industryColors.Default;
  };

  // Get plant part color
  const getPlantPartColor = (partId?: number) => {
    if (!partId || !plantParts) return plantPartColors.Default;
    const part = plantParts.find(p => p.id === partId);
    return plantPartColors[part?.name] || plantPartColors.Default;
  };

  return (
    <div className="flex h-[calc(100vh-200px)]">
      {/* Advanced Search Sidebar */}
      <AnimatePresence>
        {showAdvancedSearch && (
          <motion.div
            initial={{ width: 0, opacity: 0 }}
            animate={{ width: 320, opacity: 1 }}
            exit={{ width: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="border-r border-gray-700 bg-gray-900/50 overflow-hidden"
          >
            <div className="p-4 h-full">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-white">Advanced Search</h3>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setShowAdvancedSearch(false)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
              
              <ScrollArea className="h-[calc(100%-60px)]">
                <div className="space-y-6 pr-4">
                  {/* Product Number Range */}
                  <div>
                    <Label className="text-sm text-gray-300 mb-2">Product Range</Label>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-xs text-gray-400">
                        <span>#{String(productRange[0]).padStart(4, '0')}</span>
                        <span>#{String(productRange[1]).padStart(4, '0')}</span>
                      </div>
                      <Slider
                        value={productRange}
                        onValueChange={setProductRange}
                        min={1}
                        max={products.length}
                        step={1}
                        className="w-full"
                      />
                    </div>
                  </div>

                  <Separator />

                  {/* Industries */}
                  <Collapsible defaultOpen>
                    <CollapsibleTrigger className="flex items-center justify-between w-full">
                      <Label className="text-sm text-gray-300">Industries</Label>
                      <ChevronRight className="h-4 w-4 transition-transform duration-200" />
                    </CollapsibleTrigger>
                    <CollapsibleContent className="mt-2 space-y-2">
                      {industries?.map(industry => (
                        <div key={industry.id} className="flex items-center space-x-2">
                          <Checkbox
                            id={`industry-${industry.id}`}
                            checked={selectedIndustries.includes(industry.id)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setSelectedIndustries([...selectedIndustries, industry.id]);
                              } else {
                                setSelectedIndustries(selectedIndustries.filter(id => id !== industry.id));
                              }
                            }}
                          />
                          <Label
                            htmlFor={`industry-${industry.id}`}
                            className="text-xs text-gray-300 cursor-pointer"
                          >
                            {industry.name}
                          </Label>
                        </div>
                      ))}
                    </CollapsibleContent>
                  </Collapsible>

                  <Separator />

                  {/* Plant Parts */}
                  <Collapsible defaultOpen>
                    <CollapsibleTrigger className="flex items-center justify-between w-full">
                      <Label className="text-sm text-gray-300">Plant Parts</Label>
                      <ChevronRight className="h-4 w-4 transition-transform duration-200" />
                    </CollapsibleTrigger>
                    <CollapsibleContent className="mt-2 space-y-2">
                      {plantParts?.map(part => (
                        <div key={part.id} className="flex items-center space-x-2">
                          <Checkbox
                            id={`part-${part.id}`}
                            checked={selectedPlantParts.includes(part.id)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setSelectedPlantParts([...selectedPlantParts, part.id]);
                              } else {
                                setSelectedPlantParts(selectedPlantParts.filter(id => id !== part.id));
                              }
                            }}
                          />
                          <Label
                            htmlFor={`part-${part.id}`}
                            className="text-xs text-gray-300 cursor-pointer"
                          >
                            {part.name}
                          </Label>
                        </div>
                      ))}
                    </CollapsibleContent>
                  </Collapsible>

                  <Separator />

                  {/* Commercialization Stage */}
                  <Collapsible defaultOpen>
                    <CollapsibleTrigger className="flex items-center justify-between w-full">
                      <Label className="text-sm text-gray-300">Development Stage</Label>
                      <ChevronRight className="h-4 w-4 transition-transform duration-200" />
                    </CollapsibleTrigger>
                    <CollapsibleContent className="mt-2 space-y-2">
                      {stages.map(stage => (
                        <div key={stage} className="flex items-center space-x-2">
                          <Checkbox
                            id={`stage-${stage}`}
                            checked={selectedStages.includes(stage)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setSelectedStages([...selectedStages, stage]);
                              } else {
                                setSelectedStages(selectedStages.filter(s => s !== stage));
                              }
                            }}
                          />
                          <Label
                            htmlFor={`stage-${stage}`}
                            className="text-xs text-gray-300 cursor-pointer"
                          >
                            {stage}
                          </Label>
                        </div>
                      ))}
                    </CollapsibleContent>
                  </Collapsible>

                  {/* Clear Filters */}
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full"
                    onClick={() => {
                      setSelectedIndustries([]);
                      setSelectedPlantParts([]);
                      setSelectedStages([]);
                      setProductRange([1, products.length]);
                    }}
                  >
                    Clear All Filters
                  </Button>
                </div>
              </ScrollArea>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Search Bar */}
        <div className="p-4 border-b border-gray-700 bg-gray-900/50">
          <div className="flex gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
              <Input
                type="text"
                placeholder="Search by name, description, or #number..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-gray-800/50 border-gray-700"
              />
            </div>
            <Button
              variant="outline"
              onClick={() => setShowAdvancedSearch(!showAdvancedSearch)}
              className={cn(
                "gap-2",
                showAdvancedSearch && "bg-green-500/20 border-green-500/50"
              )}
            >
              <Filter className="h-4 w-4" />
              {showAdvancedSearch ? "Hide" : "Show"} Advanced Search
            </Button>
            <Button
              variant="outline"
              onClick={handleSurpriseMe}
              className="gap-2 hover:bg-purple-500/20 hover:border-purple-500/50"
            >
              <Shuffle className="h-4 w-4" />
              Surprise Me!
            </Button>
          </div>

          {/* Active Filter Summary */}
          {(selectedIndustries.length > 0 || selectedPlantParts.length > 0 || selectedStages.length > 0) && (
            <div className="mt-2 flex flex-wrap gap-2">
              {selectedIndustries.map(id => {
                const industry = industries?.find(i => i.id === id);
                return industry ? (
                  <Badge key={id} variant="secondary" className="text-xs">
                    {industry.name}
                  </Badge>
                ) : null;
              })}
              {selectedPlantParts.map(id => {
                const part = plantParts?.find(p => p.id === id);
                return part ? (
                  <Badge key={id} variant="secondary" className="text-xs">
                    {part.name}
                  </Badge>
                ) : null;
              })}
              {selectedStages.map(stage => (
                <Badge key={stage} variant="secondary" className="text-xs">
                  {stage}
                </Badge>
              ))}
            </div>
          )}
        </div>

        {/* Product List */}
        <ScrollArea className="flex-1">
          <div className="p-4">
            <div className="text-sm text-gray-400 mb-4">
              Showing {filteredProducts.length} of {products.length} products
            </div>
            
            <div className="space-y-1">
              <AnimatePresence mode="popLayout">
                {filteredProducts.map((product, index) => {
                  const productNumber = products.findIndex(p => p.id === product.id) + 1;
                  const industry = industries?.find(i => i.id === product.industry_sub_category_id);
                  const plantPart = plantParts?.find(p => p.id === product.plant_part_id);
                  const company = companies?.find(c => c.id === product.primary_company_id);
                  
                  return (
                    <motion.div
                      key={product.id}
                      layout
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: 20 }}
                      transition={{ duration: 0.2, delay: index * 0.01 }}
                      onMouseEnter={() => setHoveredProduct(product.id)}
                      onMouseLeave={() => setHoveredProduct(null)}
                      className={cn(
                        "relative flex items-center gap-4 p-3 rounded-lg transition-all cursor-pointer",
                        "hover:bg-gray-800/50 hover:shadow-lg",
                        hoveredProduct === product.id && "bg-gray-800/50 shadow-lg"
                      )}
                      onClick={() => onProductClick?.(product)}
                    >
                      {/* Product Number */}
                      <div className="flex items-center gap-2 min-w-[80px]">
                        <Hash className="h-4 w-4 text-gray-500" />
                        <span className="text-gray-400 font-mono text-sm">
                          {String(productNumber).padStart(4, '0')}
                        </span>
                      </div>

                      {/* Product Image */}
                      {product.image_url && (
                        <div className="w-12 h-12 rounded-md overflow-hidden bg-gray-800 flex-shrink-0">
                          <img 
                            src={product.image_url} 
                            alt={product.name}
                            className="w-full h-full object-cover"
                          />
                        </div>
                      )}

                      {/* Product Info */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <h3 className="text-white font-medium truncate">{product.name}</h3>
                          {company && (
                            <span className="text-xs text-gray-500">by {company.name}</span>
                          )}
                        </div>
                        <div className="flex items-center gap-2">
                          {industry && (
                            <Badge 
                              variant="outline" 
                              className={cn("text-xs", getIndustryColor(product.industry_sub_category_id))}
                            >
                              <Building2 className="h-3 w-3 mr-1" />
                              {industry.name}
                            </Badge>
                          )}
                          {plantPart && (
                            <Badge 
                              variant="outline" 
                              className={cn("text-xs", getPlantPartColor(product.plant_part_id))}
                            >
                              <TreePine className="h-3 w-3 mr-1" />
                              {plantPart.name}
                            </Badge>
                          )}
                          {(product.commercialization_stage || product.commercializationStage) && (
                            <Badge variant="outline" className="text-xs">
                              {product.commercialization_stage || product.commercializationStage}
                            </Badge>
                          )}
                        </div>
                      </div>

                      {/* Hover Indicator */}
                      <ChevronRight className={cn(
                        "h-5 w-5 text-gray-500 transition-transform",
                        hoveredProduct === product.id && "transform translate-x-1"
                      )} />
                    </motion.div>
                  );
                })}
              </AnimatePresence>
            </div>
          </div>
        </ScrollArea>
      </div>
    </div>
  );
}

export default PokedexProductList;