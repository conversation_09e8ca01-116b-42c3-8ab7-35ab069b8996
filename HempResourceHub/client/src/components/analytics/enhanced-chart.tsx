import { useMemo } from "react";
import { Bar<PERSON>hart3, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Activity } from "lucide-react";

interface ChartData {
  label: string;
  value: number;
  color?: string;
}

interface EnhancedChartProps {
  title: string;
  type: "bar" | "line" | "pie" | "area";
  data: ChartData[];
  height?: string;
  showLegend?: boolean;
  className?: string;
}

export function EnhancedChart({ 
  title, 
  type, 
  data, 
  height = "h-[400px]",
  showLegend = true,
  className = "" 
}: EnhancedChartProps) {
  
  const maxValue = useMemo(() => Math.max(...data.map(d => d.value)), [data]);
  
  const getIcon = () => {
    switch (type) {
      case "bar": return <BarChart3 className="h-5 w-5" />;
      case "line": return <TrendingUp className="h-5 w-5" />;
      case "pie": return <PieChart className="h-5 w-5" />;
      case "area": return <Activity className="h-5 w-5" />;
    }
  };

  const renderBar<PERSON>hart = () => (
    <div className="flex items-end justify-between gap-4 h-full px-4">
      {data.map((item, index) => {
        const heightPercent = (item.value / maxValue) * 100;
        const color = item.color || `hsl(${index * 60}, 70%, 50%)`;
        
        return (
          <div key={item.label} className="flex-1 flex flex-col items-center justify-end">
            <div className="relative w-full group">
              {/* Tooltip */}
              <div className="absolute -top-10 left-1/2 -translate-x-1/2 bg-gray-900 text-white px-2 py-1 rounded text-sm opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
                {item.value.toLocaleString()}
              </div>
              
              {/* Bar */}
              <div 
                className="w-full rounded-t-lg transition-all duration-500 ease-out hover:opacity-80"
                style={{ 
                  height: `${heightPercent}%`, 
                  backgroundColor: color,
                  minHeight: '20px'
                }}
              />
            </div>
            
            {/* Label */}
            <p className="text-xs text-gray-400 mt-2 text-center truncate w-full" title={item.label}>
              {item.label}
            </p>
          </div>
        );
      })}
    </div>
  );

  const renderLineChart = () => {
    const points = data.map((item, index) => {
      const x = (index / (data.length - 1)) * 100;
      const y = 100 - ((item.value / maxValue) * 90); // 90% to leave some margin
      return { x, y, value: item.value, label: item.label };
    });

    const pathData = points
      .map((point, index) => `${index === 0 ? 'M' : 'L'} ${point.x}% ${point.y}%`)
      .join(' ');

    return (
      <div className="relative h-full w-full p-4">
        <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
          {/* Grid lines */}
          {[0, 25, 50, 75, 100].map(y => (
            <line
              key={y}
              x1="0%"
              y1={`${y}%`}
              x2="100%"
              y2={`${y}%`}
              stroke="rgba(255,255,255,0.1)"
              strokeWidth="0.5"
            />
          ))}
          
          {/* Line */}
          <path
            d={pathData}
            fill="none"
            stroke="#10b981"
            strokeWidth="2"
            vectorEffect="non-scaling-stroke"
          />
          
          {/* Area under line */}
          <path
            d={`${pathData} L 100% 100% L 0% 100% Z`}
            fill="url(#gradient)"
            opacity="0.2"
          />
          
          {/* Gradient */}
          <defs>
            <linearGradient id="gradient" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor="#10b981" />
              <stop offset="100%" stopColor="#10b981" stopOpacity="0" />
            </linearGradient>
          </defs>
          
          {/* Points */}
          {points.map((point, index) => (
            <g key={index}>
              <circle
                cx={`${point.x}%`}
                cy={`${point.y}%`}
                r="4"
                fill="#10b981"
                className="hover:r-6"
              />
              <title>{`${point.label}: ${point.value}`}</title>
            </g>
          ))}
        </svg>
        
        {/* X-axis labels */}
        <div className="flex justify-between mt-2">
          {data.map((item, index) => (
            <span key={index} className="text-xs text-gray-400 truncate">
              {item.label}
            </span>
          ))}
        </div>
      </div>
    );
  };

  const renderPieChart = () => {
    const total = data.reduce((sum, item) => sum + item.value, 0);
    let currentAngle = -90; // Start from top
    
    const slices = data.map((item, index) => {
      const percentage = (item.value / total) * 100;
      const angle = (percentage / 100) * 360;
      const startAngle = currentAngle;
      currentAngle += angle;
      
      const color = item.color || `hsl(${index * 60}, 70%, 50%)`;
      
      return { ...item, percentage, startAngle, angle, color };
    });

    return (
      <div className="flex items-center justify-between gap-8 h-full p-4">
        {/* Pie Chart */}
        <div className="relative flex-1 h-full">
          <svg className="w-full h-full" viewBox="-50 -50 100 100">
            {slices.map((slice, index) => {
              const startRad = (slice.startAngle * Math.PI) / 180;
              const endRad = ((slice.startAngle + slice.angle) * Math.PI) / 180;
              const largeArc = slice.angle > 180 ? 1 : 0;
              
              const x1 = Math.cos(startRad) * 40;
              const y1 = Math.sin(startRad) * 40;
              const x2 = Math.cos(endRad) * 40;
              const y2 = Math.sin(endRad) * 40;
              
              const pathData = [
                `M 0 0`,
                `L ${x1} ${y1}`,
                `A 40 40 0 ${largeArc} 1 ${x2} ${y2}`,
                `Z`
              ].join(' ');
              
              return (
                <path
                  key={index}
                  d={pathData}
                  fill={slice.color}
                  className="hover:opacity-80 transition-opacity cursor-pointer"
                >
                  <title>{`${slice.label}: ${slice.value} (${slice.percentage.toFixed(1)}%)`}</title>
                </path>
              );
            })}
          </svg>
        </div>
        
        {/* Legend */}
        {showLegend && (
          <div className="flex flex-col gap-2">
            {slices.map((slice, index) => (
              <div key={index} className="flex items-center gap-2">
                <div 
                  className="w-3 h-3 rounded-sm"
                  style={{ backgroundColor: slice.color }}
                />
                <span className="text-sm text-gray-400">
                  {slice.label}: {slice.percentage.toFixed(1)}%
                </span>
              </div>
            ))}
          </div>
        )}
      </div>
    );
  };

  const renderChart = () => {
    switch (type) {
      case "bar": return renderBarChart();
      case "line": 
      case "area": return renderLineChart();
      case "pie": return renderPieChart();
      default: return null;
    }
  };

  return (
    <div className={`${height} ${className} rounded-xl bg-black/50 backdrop-blur-sm border border-gray-800 p-6 overflow-hidden`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-white flex items-center gap-2 truncate">
          {getIcon()}
          <span className="truncate">{title}</span>
        </h3>
      </div>
      <div className="h-[calc(100%-3rem)] overflow-hidden">
        {renderChart()}
      </div>
    </div>
  );
}