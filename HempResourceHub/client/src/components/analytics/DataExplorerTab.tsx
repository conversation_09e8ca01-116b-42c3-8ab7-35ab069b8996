import { useState, useMemo, useCallback } from "react";
import { useAllHempProducts } from "@/hooks/use-product-data";
import { usePlantParts, useIndustries } from "@/hooks/use-plant-data";
import { useCompanies } from "@/hooks/use-companies";
import { 
  Search,
  Filter,
  ArrowUpDown,
  ChevronLeft,
  ChevronRight,
  Database,
  BarChart3,
  Eye,
  Columns,
  Grid3x3,
  List
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { cn } from "@/lib/utils";
import EnhancedProductCard from "@/components/product/enhanced-product-card";

type ViewMode = 'table' | 'cards' | 'list';
type DataSource = 'products' | 'companies' | 'industries' | 'plantparts';

export function DataExplorerTab() {
  // Data sources
  const { data: products } = useAllHempProducts();
  const { data: companies } = useCompanies();
  const { data: plantParts } = usePlantParts();
  const { data: industries } = useIndustries();

  // State
  const [searchTerm, setSearchTerm] = useState("");
  const [viewMode, setViewMode] = useState<ViewMode>('table');
  const [dataSource, setDataSource] = useState<DataSource>('products');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [selectedColumns, setSelectedColumns] = useState<string[]>([
    'name', 'industry', 'plantPart', 'company', 'created'
  ]);

  // Available columns based on data source
  const availableColumns = useMemo(() => {
    switch (dataSource) {
      case 'products':
        return ['name', 'description', 'industry', 'plantPart', 'company', 'benefits', 'created'];
      case 'companies':
        return ['name', 'website', 'city', 'state', 'country', 'productCount', 'created'];
      case 'industries':
        return ['name', 'category', 'productCount', 'description'];
      case 'plantparts':
        return ['name', 'type', 'productCount', 'description'];
      default:
        return [];
    }
  }, [dataSource]);

  // Get current data based on selected source
  const currentData = useMemo(() => {
    switch (dataSource) {
      case 'products':
        return products || [];
      case 'companies':
        return companies || [];
      case 'industries':
        return industries || [];
      case 'plantparts':
        return plantParts || [];
      default:
        return [];
    }
  }, [dataSource, products, companies, industries, plantParts]);

  // Filter data based on search
  const filteredData = useMemo(() => {
    if (!searchTerm) return currentData;
    
    return currentData.filter((item: any) => {
      const searchLower = searchTerm.toLowerCase();
      return Object.values(item).some(value => 
        String(value).toLowerCase().includes(searchLower)
      );
    });
  }, [currentData, searchTerm]);

  // Pagination
  const paginatedData = useMemo(() => {
    const start = (currentPage - 1) * itemsPerPage;
    const end = start + itemsPerPage;
    return filteredData.slice(start, end);
  }, [filteredData, currentPage, itemsPerPage]);

  const totalPages = Math.ceil(filteredData.length / itemsPerPage);

  // Render table view
  const renderTableView = () => (
    <div className="overflow-x-auto">
      <table className="w-full">
        <thead className="border-b border-dark-border">
          <tr>
            {selectedColumns.map(column => (
              <th key={column} className="text-left py-3 px-4 text-sm font-medium text-gray-400">
                <Button
                  variant="ghost"
                  size="sm"
                  className="p-0 h-auto font-medium hover:text-white"
                >
                  {column.charAt(0).toUpperCase() + column.slice(1)}
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
              </th>
            ))}
            <th className="text-left py-3 px-4 text-sm font-medium text-gray-400">
              Actions
            </th>
          </tr>
        </thead>
        <tbody>
          {paginatedData.map((item: any, index) => (
            <tr key={item.id || index} className="border-b border-dark-border/50 hover:bg-dark-hover/50">
              {selectedColumns.map(column => (
                <td key={column} className="py-4 px-4 text-sm text-gray-300">
                  {renderCellValue(item, column)}
                </td>
              ))}
              <td className="py-4 px-4">
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-purple-400 hover:text-purple-300"
                >
                  <Eye className="h-4 w-4" />
                </Button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );

  // Render card view
  const renderCardView = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
      {paginatedData.map((item: any, index) => {
        if (dataSource === 'products') {
          return (
            <EnhancedProductCard
              key={item.id || index}
              product={item}
              viewMode="grid"
            />
          );
        }
        
        // Generic card for other data types
        return (
          <Card key={item.id || index} className="bg-dark-card border-dark-border hover:border-purple-600/50 transition-colors">
            <CardHeader>
              <CardTitle className="text-base text-white">{item.name}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm text-gray-400">
                {selectedColumns.slice(1, 4).map(column => (
                  <div key={column}>
                    <span className="font-medium">{column}: </span>
                    {renderCellValue(item, column)}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );

  // Render list view
  const renderListView = () => (
    <div className="space-y-2">
      {paginatedData.map((item: any, index) => (
        <Card key={item.id || index} className="bg-dark-card border-dark-border hover:border-purple-600/50 transition-colors">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <h3 className="text-white font-medium">{item.name}</h3>
                <div className="flex gap-4 mt-1 text-sm text-gray-400">
                  {selectedColumns.slice(1, 4).map(column => (
                    <div key={column}>
                      <span className="font-medium">{column}: </span>
                      {renderCellValue(item, column)}
                    </div>
                  ))}
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="text-purple-400 hover:text-purple-300"
              >
                <Eye className="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );

  // Helper function to render cell values
  const renderCellValue = (item: any, column: string) => {
    const value = item[column] || item[`${column}_name`] || item[`${column}Name`];
    
    if (!value) return <span className="text-gray-500">—</span>;
    
    if (column === 'created' || column === 'updated') {
      return new Date(value).toLocaleDateString();
    }
    
    if (column === 'productCount' || column === 'product_count') {
      return <Badge variant="secondary">{value}</Badge>;
    }
    
    if (Array.isArray(value)) {
      return value.slice(0, 2).join(', ') + (value.length > 2 ? '...' : '');
    }
    
    if (typeof value === 'object') {
      return JSON.stringify(value);
    }
    
    return String(value).length > 50 ? String(value).slice(0, 50) + '...' : String(value);
  };

  return (
    <div className="space-y-6">
      {/* Data Source Selector */}
      <Card className="bg-dark-card border-dark-border">
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="flex items-center gap-2">
              <Database className="h-5 w-5 text-purple-400" />
              <Select value={dataSource} onValueChange={(value) => setDataSource(value as DataSource)}>
                <SelectTrigger className="w-[200px] bg-dark-sidebar border-dark-border">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="products">Products ({products?.length || 0})</SelectItem>
                  <SelectItem value="companies">Companies ({companies?.length || 0})</SelectItem>
                  <SelectItem value="industries">Industries ({industries?.length || 0})</SelectItem>
                  <SelectItem value="plantparts">Plant Parts ({plantParts?.length || 0})</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-400">View:</span>
              <div className="flex gap-1 bg-dark-sidebar rounded-lg p-1">
                <Button
                  variant={viewMode === 'table' ? 'secondary' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('table')}
                  className="h-8 w-8 p-0"
                >
                  <Grid3x3 className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'cards' ? 'secondary' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('cards')}
                  className="h-8 w-8 p-0"
                >
                  <Columns className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'secondary' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                  className="h-8 w-8 p-0"
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Search and Filters */}
      <div className="flex gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
          <Input
            placeholder={`Search ${dataSource}...`}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 bg-dark-card border-dark-border"
          />
        </div>
        <Select value={String(itemsPerPage)} onValueChange={(value) => setItemsPerPage(Number(value))}>
          <SelectTrigger className="w-[120px] bg-dark-card border-dark-border">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="10">10 per page</SelectItem>
            <SelectItem value="20">20 per page</SelectItem>
            <SelectItem value="50">50 per page</SelectItem>
            <SelectItem value="100">100 per page</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Results Summary */}
      <div className="flex items-center justify-between text-sm text-gray-400">
        <span>
          Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, filteredData.length)} of {filteredData.length} results
        </span>
        <div className="flex items-center gap-4">
          <span>Total: {currentData.length} records</span>
          {searchTerm && (
            <Badge variant="secondary" className="bg-purple-600/20 text-purple-400">
              Filtered: {filteredData.length}
            </Badge>
          )}
        </div>
      </div>

      {/* Data Display */}
      <Card className="bg-dark-card border-dark-border">
        <CardContent className="p-0">
          {viewMode === 'table' && renderTableView()}
          {viewMode === 'cards' && <div className="p-6">{renderCardView()}</div>}
          {viewMode === 'list' && <div className="p-6">{renderListView()}</div>}
        </CardContent>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-400">
            Page {currentPage} of {totalPages}
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
              disabled={currentPage === 1}
              className="border-dark-border"
            >
              <ChevronLeft className="h-4 w-4" />
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
              disabled={currentPage === totalPages}
              className="border-dark-border"
            >
              Next
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}