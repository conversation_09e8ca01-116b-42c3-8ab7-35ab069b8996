"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Drawer, DrawerClose, DrawerContent, DrawerD<PERSON><PERSON>, Drawer<PERSON>ooter, <PERSON>er<PERSON>eader, <PERSON>er<PERSON><PERSON><PERSON>, DrawerTrigger } from "@/components/ui/drawer"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Slider } from "@/components/ui/slider"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { Filter, X } from "lucide-react"
import { useState } from "react"

interface FilterOption {
  value: string
  label: string
  count?: number
}

interface MobileFiltersDrawerProps {
  industries?: FilterOption[]
  plantParts?: FilterOption[]
  companies?: FilterOption[]
  onFiltersChange?: (filters: any) => void
  totalResults?: number
}

export function MobileFiltersDrawer({
  industries = [],
  plantParts = [],
  companies = [],
  onFiltersChange,
  totalResults = 0
}: MobileFiltersDrawerProps) {
  const [open, setOpen] = useState(false)
  const [selectedIndustries, setSelectedIndustries] = useState<string[]>([])
  const [selectedPlantParts, setSelectedPlantParts] = useState<string[]>([])
  const [selectedCompany, setSelectedCompany] = useState<string>("")
  const [commercializationStage, setCommercializationStage] = useState("all")
  const [priceRange, setPriceRange] = useState([0, 1000])

  const handleApplyFilters = () => {
    onFiltersChange?.({
      industries: selectedIndustries,
      plantParts: selectedPlantParts,
      company: selectedCompany,
      commercializationStage,
      priceRange
    })
    setOpen(false)
  }

  const handleClearFilters = () => {
    setSelectedIndustries([])
    setSelectedPlantParts([])
    setSelectedCompany("")
    setCommercializationStage("all")
    setPriceRange([0, 1000])
  }

  const activeFiltersCount = 
    selectedIndustries.length + 
    selectedPlantParts.length + 
    (selectedCompany ? 1 : 0) + 
    (commercializationStage !== "all" ? 1 : 0)

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerTrigger asChild>
        <Button variant="outline" size="sm" className="gap-2">
          <Filter className="h-4 w-4" />
          Filters
          {activeFiltersCount > 0 && (
            <Badge variant="secondary" className="ml-1">
              {activeFiltersCount}
            </Badge>
          )}
        </Button>
      </DrawerTrigger>
      <DrawerContent className="max-h-[85vh]">
        <DrawerHeader>
          <DrawerTitle>Filter Products</DrawerTitle>
          <DrawerDescription>
            {totalResults} products found. Refine your search with filters.
          </DrawerDescription>
        </DrawerHeader>
        
        <ScrollArea className="flex-1 px-4">
          <div className="space-y-6 py-4">
            {/* Commercialization Stage */}
            <div className="space-y-3">
              <h4 className="font-medium leading-none">Commercialization Stage</h4>
              <RadioGroup value={commercializationStage} onValueChange={setCommercializationStage}>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="all" id="stage-all" />
                  <Label htmlFor="stage-all" className="text-sm font-normal">
                    All Stages
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="commercial" id="stage-commercial" />
                  <Label htmlFor="stage-commercial" className="text-sm font-normal">
                    Commercial
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="development" id="stage-development" />
                  <Label htmlFor="stage-development" className="text-sm font-normal">
                    In Development
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="research" id="stage-research" />
                  <Label htmlFor="stage-research" className="text-sm font-normal">
                    Research Stage
                  </Label>
                </div>
              </RadioGroup>
            </div>

            <Separator />

            {/* Industries */}
            {industries.length > 0 && (
              <>
                <div className="space-y-3">
                  <h4 className="font-medium leading-none">Industries</h4>
                  <div className="space-y-2">
                    {industries.slice(0, 5).map((industry) => (
                      <div key={industry.value} className="flex items-center space-x-2">
                        <Checkbox
                          id={`industry-${industry.value}`}
                          checked={selectedIndustries.includes(industry.value)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSelectedIndustries([...selectedIndustries, industry.value])
                            } else {
                              setSelectedIndustries(selectedIndustries.filter(i => i !== industry.value))
                            }
                          }}
                        />
                        <Label
                          htmlFor={`industry-${industry.value}`}
                          className="text-sm font-normal flex-1"
                        >
                          {industry.label}
                          {industry.count && (
                            <span className="text-muted-foreground ml-1">({industry.count})</span>
                          )}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
                <Separator />
              </>
            )}

            {/* Plant Parts */}
            {plantParts.length > 0 && (
              <>
                <div className="space-y-3">
                  <h4 className="font-medium leading-none">Plant Parts</h4>
                  <div className="space-y-2">
                    {plantParts.map((part) => (
                      <div key={part.value} className="flex items-center space-x-2">
                        <Checkbox
                          id={`part-${part.value}`}
                          checked={selectedPlantParts.includes(part.value)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSelectedPlantParts([...selectedPlantParts, part.value])
                            } else {
                              setSelectedPlantParts(selectedPlantParts.filter(p => p !== part.value))
                            }
                          }}
                        />
                        <Label
                          htmlFor={`part-${part.value}`}
                          className="text-sm font-normal flex-1"
                        >
                          {part.label}
                          {part.count && (
                            <span className="text-muted-foreground ml-1">({part.count})</span>
                          )}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
                <Separator />
              </>
            )}

            {/* Price Range */}
            <div className="space-y-3">
              <h4 className="font-medium leading-none">Price Range</h4>
              <div className="px-2">
                <Slider
                  value={priceRange}
                  onValueChange={setPriceRange}
                  max={1000}
                  step={10}
                  className="w-full"
                />
                <div className="flex items-center justify-between mt-2">
                  <span className="text-sm text-muted-foreground">${priceRange[0]}</span>
                  <span className="text-sm text-muted-foreground">${priceRange[1]}</span>
                </div>
              </div>
            </div>
          </div>
        </ScrollArea>

        <DrawerFooter className="pt-2">
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleClearFilters} className="flex-1">
              Clear All
            </Button>
            <Button onClick={handleApplyFilters} className="flex-1">
              Apply Filters
            </Button>
          </div>
          <DrawerClose asChild>
            <Button variant="ghost" size="sm">
              Cancel
            </Button>
          </DrawerClose>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  )
}