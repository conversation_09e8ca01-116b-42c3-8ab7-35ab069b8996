import { useEffect, useRef } from "react";
import * as echarts from "echarts";

const categories = [
  "HEMP FIBER",
  "CBD EXTRACTS", 
  "HEMP SEEDS",
  "HEMP OIL",
  "HEMP PROTEIN",
  "HEMPCRETE",
  "BIOPLASTICS",
  "TEXTILES",
  "COSMETICS",
  "PHARMACEUTICALS",
  "ANIMAL FEED",
  "PAPER PRODUCTS",
  "COMPOSITES",
];

const growth = [82, 94, 67, 78, 85, 89, 75, 91, 88, 92, 71, 68, 86];

export function ProcessingTimeChart() {
  const chartRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!chartRef.current) return;

    const chart = echarts.init(chartRef.current);

    const option: echarts.EChartsOption = {
      backgroundColor: "transparent",
      title: {
        text: "Growth Rate by Product Category",
        left: 20,
        top: 20,
        textStyle: {
          color: "#ffffff",
          fontSize: 16,
          fontWeight: 500,
        },
      },
      grid: {
        left: 140,
        right: 40,
        top: 80,
        bottom: 40,
      },
      xAxis: {
        type: "value",
        name: "Growth Rate (%)",
        nameLocation: "middle",
        nameGap: 30,
        nameTextStyle: {
          color: "#9ca3af",
          fontSize: 12,
        },
        min: 0,
        max: 100,
        axisLine: {
          lineStyle: {
            color: "#1e2447",
          },
        },
        axisLabel: {
          color: "#6b7280",
        },
        splitLine: {
          lineStyle: {
            color: "#1e2447",
            type: "dashed",
          },
        },
      },
      yAxis: {
        type: "category",
        data: categories,
        axisLine: {
          lineStyle: {
            color: "#1e2447",
          },
        },
        axisLabel: {
          color: "#6b7280",
          fontSize: 10,
        },
      },
      series: [
        {
          type: "bar",
          data: growth.map((rate, index) => ({
            value: rate,
            itemStyle: {
              color: rate > 85 ? "#10b981" : rate > 75 ? "#3b82f6" : "#f59e0b",
            },
          })),
          barWidth: 12,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: "rgba(59, 130, 246, 0.5)",
            },
          },
        },
      ],
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "shadow",
        },
        formatter: (params: any) => {
          const data = params[0];
          return `${data.name}<br/>Growth Rate: ${data.value}%`;
        },
        backgroundColor: "#151a3a",
        borderColor: "#1e2447",
        textStyle: {
          color: "#ffffff",
        },
      },
    };

    chart.setOption(option);

    const handleResize = () => chart.resize();
    window.addEventListener("resize", handleResize);

    return () => {
      window.removeEventListener("resize", handleResize);
      chart.dispose();
    };
  }, []);

  return <div ref={chartRef} className="h-full w-full" />;
}