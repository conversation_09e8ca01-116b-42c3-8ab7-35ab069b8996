import { <PERSON><PERSON>ons<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>hart, Line, XAxis, <PERSON>Axis, CartesianGrid, <PERSON>ltip, Legend } from "recharts";
import { useMemo } from "react";
import { useAllHempProducts } from "@/hooks/use-product-data";

export function GrowthTrendChart() {
  const { data: products } = useAllHempProducts();

  const data = useMemo(() => {
    if (!products) return [];

    // Generate sample trend data based on product creation dates
    const monthlyData: Record<string, { products: number; companies: number }> = {};
    
    // Get last 6 months
    const months = [];
    const now = new Date();
    for (let i = 5; i >= 0; i--) {
      const date = new Date(now);
      date.setMonth(date.getMonth() - i);
      const monthKey = date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
      months.push(monthKey);
      monthlyData[monthKey] = { products: 0, companies: 0 };
    }

    // Count products per month (simulated data for demo)
    products.forEach((product, index) => {
      const monthIndex = Math.floor(index / (products.length / 6));
      if (monthIndex < months.length) {
        monthlyData[months[monthIndex]].products++;
        if (product.company_name) {
          monthlyData[months[monthIndex]].companies++;
        }
      }
    });

    // Add cumulative totals
    let cumulativeProducts = 0;
    let cumulativeCompanies = 0;
    
    return months.map(month => {
      cumulativeProducts += monthlyData[month].products;
      cumulativeCompanies += monthlyData[month].companies;
      
      return {
        month,
        products: cumulativeProducts,
        companies: cumulativeCompanies,
        newProducts: monthlyData[month].products,
      };
    });
  }, [products]);

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload[0]) {
      return (
        <div className="bg-marine-card border border-marine-border rounded-lg p-3">
          <p className="text-white font-medium mb-2">{label}</p>
          <p className="text-green-400 text-sm">
            Total Products: {payload[0].value}
          </p>
          <p className="text-blue-400 text-sm">
            Total Companies: {payload[1]?.value || 0}
          </p>
          <p className="text-marine-text-secondary text-xs mt-1">
            New this month: {payload[0].payload.newProducts}
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="h-full">
      <h3 className="text-lg font-semibold text-white mb-4">Growth Trend</h3>
      <ResponsiveContainer width="100%" height="90%">
        <LineChart 
          data={data}
          margin={{ top: 10, right: 30, left: 0, bottom: 20 }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
          <XAxis 
            dataKey="month" 
            stroke="#9CA3AF"
          />
          <YAxis stroke="#9CA3AF" />
          <Tooltip content={<CustomTooltip />} />
          <Legend 
            wrapperStyle={{ color: '#9ca3af' }}
          />
          <Line 
            type="monotone" 
            dataKey="products" 
            stroke="#10b981"
            strokeWidth={2}
            dot={{ fill: '#10b981', r: 4 }}
            name="Products"
          />
          <Line 
            type="monotone" 
            dataKey="companies" 
            stroke="#3b82f6"
            strokeWidth={2}
            dot={{ fill: '#3b82f6', r: 4 }}
            name="Companies"
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
}