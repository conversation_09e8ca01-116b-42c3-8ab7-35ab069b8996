import { useMemo } from "react";
import { useAllHempProducts } from "@/hooks/use-product-data";
import { cn } from "@/lib/utils";

export function HeatmapChart() {
  const { data: products } = useAllHempProducts();

  const data = useMemo(() => {
    if (!products) return { industries: [], plantParts: [], matrix: [] };

    // Get unique industries and plant parts
    const industries = Array.from(new Set(products.map(p => p.industry_name || 'Other')))
      .slice(0, 8); // Top 8 industries
    
    const plantParts = Array.from(new Set(products.map(p => p.plant_part_name || 'Unknown')))
      .filter(part => part !== 'Unknown');

    // Create matrix
    const matrix: number[][] = [];
    const maxCount = { value: 0 };

    industries.forEach((industry, i) => {
      matrix[i] = [];
      plantParts.forEach((plantPart, j) => {
        const count = products.filter(p => 
          (p.industry_name || 'Other') === industry && 
          p.plant_part_name === plantPart
        ).length;
        matrix[i][j] = count;
        if (count > maxCount.value) maxCount.value = count;
      });
    });

    return { industries, plantParts, matrix, maxCount: maxCount.value };
  }, [products]);

  const getColor = (value: number) => {
    if (!data.maxCount || value === 0) return 'bg-marine-sidebar';
    const intensity = value / data.maxCount;
    
    if (intensity > 0.75) return 'bg-green-600';
    if (intensity > 0.5) return 'bg-green-500';
    if (intensity > 0.25) return 'bg-green-400';
    return 'bg-green-300';
  };

  return (
    <div className="h-full">
      <h3 className="text-lg font-semibold text-white mb-4">Industry vs Plant Part Heatmap</h3>
      
      <div className="overflow-x-auto">
        <div className="inline-block">
          {/* Header row with plant parts */}
          <div className="flex mb-2">
            <div className="w-32" /> {/* Empty corner cell */}
            {data.plantParts.map((part, i) => (
              <div 
                key={i}
                className="w-20 px-1 text-xs text-marine-text-secondary text-center transform -rotate-45 origin-bottom-left translate-y-10"
              >
                {part}
              </div>
            ))}
          </div>

          {/* Data rows */}
          {data.industries.map((industry, i) => (
            <div key={i} className="flex items-center mb-1">
              <div className="w-32 pr-2 text-sm text-marine-text-secondary text-right truncate">
                {industry}
              </div>
              {data.matrix[i]?.map((value, j) => (
                <div
                  key={j}
                  className={cn(
                    "w-20 h-12 mx-0.5 rounded flex items-center justify-center text-xs font-medium transition-all hover:scale-105",
                    getColor(value),
                    value > 0 ? 'text-white' : 'text-marine-text-muted'
                  )}
                  title={`${industry} - ${data.plantParts[j]}: ${value} products`}
                >
                  {value > 0 ? value : ''}
                </div>
              ))}
            </div>
          ))}
        </div>
      </div>

      {/* Legend */}
      <div className="mt-6 flex items-center gap-4 text-xs text-marine-text-secondary">
        <span>Low</span>
        <div className="flex gap-1">
          <div className="w-4 h-4 bg-marine-sidebar rounded" />
          <div className="w-4 h-4 bg-green-300 rounded" />
          <div className="w-4 h-4 bg-green-400 rounded" />
          <div className="w-4 h-4 bg-green-500 rounded" />
          <div className="w-4 h-4 bg-green-600 rounded" />
        </div>
        <span>High</span>
      </div>
    </div>
  );
}