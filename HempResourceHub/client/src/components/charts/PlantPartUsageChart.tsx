import { <PERSON><PERSON>ons<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Bar, XAxis, <PERSON>Axi<PERSON>, CartesianGrid, <PERSON>ltip, <PERSON> } from "recharts";
import { useMemo } from "react";
import { useAllHempProducts } from "@/hooks/use-product-data";

export function PlantPartUsageChart() {
  const { data: products } = useAllHempProducts();

  const data = useMemo(() => {
    if (!products) return [];

    // Count products by plant part
    const plantPartCount = products.reduce((acc, product) => {
      const plantPart = product.plant_part_name || 'Unknown';
      acc[plantPart] = (acc[plantPart] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Convert to chart data format
    return Object.entries(plantPartCount)
      .map(([name, products]) => ({ 
        name: name.charAt(0).toUpperCase() + name.slice(1), 
        products 
      }))
      .sort((a, b) => b.products - a.products);
  }, [products]);

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload[0]) {
      return (
        <div className="bg-marine-card border border-marine-border rounded-lg p-3">
          <p className="text-white font-medium">{label}</p>
          <p className="text-green-400">
            {payload[0].value} products
          </p>
          <p className="text-marine-text-secondary text-xs">
            {((payload[0].value / products?.length || 0) * 100).toFixed(1)}% of total
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="h-full">
      <h3 className="text-lg font-semibold text-white mb-4">Plant Part Usage</h3>
      <ResponsiveContainer width="100%" height="90%">
        <BarChart 
          data={data}
          margin={{ top: 10, right: 30, left: 0, bottom: 40 }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
          <XAxis 
            dataKey="name" 
            stroke="#9CA3AF"
            angle={-45}
            textAnchor="end"
            height={80}
          />
          <YAxis stroke="#9CA3AF" />
          <Tooltip content={<CustomTooltip />} />
          <Bar 
            dataKey="products" 
            fill="#10b981"
            radius={[8, 8, 0, 0]}
          />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
}