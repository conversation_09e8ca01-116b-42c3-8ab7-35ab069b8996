import { useEffect, useRef } from "react";
import * as echarts from "echarts";

interface DataPoint {
  name: string;
  complexity: number;
  detections: number;
  color: string;
}

const data: DataPoint[] = [
  { name: "Fiber Products", complexity: 0.3, detections: 234, color: "#10b981" },
  { name: "CBD Products", complexity: 0.8, detections: 187, color: "#3b82f6" },
  { name: "Food & Beverage", complexity: 0.5, detections: 293, color: "#f59e0b" },
  { name: "Construction", complexity: 0.4, detections: 156, color: "#8b5cf6" },
  { name: "Cosmetics", complexity: 0.6, detections: 412, color: "#ec4899" },
];

export function ComplexityChart() {
  const chartRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!chartRef.current) return;

    const chart = echarts.init(chartRef.current);

    const option: echarts.EChartsOption = {
      backgroundColor: "transparent",
      title: {
        text: "Market Complexity vs Product Count",
        left: 20,
        top: 20,
        textStyle: {
          color: "#ffffff",
          fontSize: 16,
          fontWeight: 500,
        },
      },
      grid: {
        left: 60,
        right: 40,
        top: 80,
        bottom: 60,
      },
      xAxis: {
        type: "value",
        name: "Market Entry Complexity",
        nameLocation: "middle",
        nameGap: 40,
        nameTextStyle: {
          color: "#9ca3af",
          fontSize: 12,
        },
        min: 0,
        max: 1,
        axisLine: {
          lineStyle: {
            color: "#1e2447",
          },
        },
        axisLabel: {
          color: "#6b7280",
          formatter: (value: number) => `${(value * 100).toFixed(0)}%`,
        },
        splitLine: {
          lineStyle: {
            color: "#1e2447",
            type: "dashed",
          },
        },
      },
      yAxis: {
        type: "value",
        name: "Number of Products",
        nameLocation: "middle",
        nameGap: 50,
        nameTextStyle: {
          color: "#9ca3af",
          fontSize: 12,
        },
        axisLine: {
          lineStyle: {
            color: "#1e2447",
          },
        },
        axisLabel: {
          color: "#6b7280",
        },
        splitLine: {
          lineStyle: {
            color: "#1e2447",
            type: "dashed",
          },
        },
      },
      legend: {
        data: data.map(d => d.name),
        top: 40,
        right: 20,
        textStyle: {
          color: "#9ca3af",
          fontSize: 12,
        },
        itemWidth: 20,
        itemHeight: 10,
      },
      series: [
        {
          type: "scatter",
          symbolSize: 80,
          data: data.map(d => ({
            value: [d.complexity, d.detections],
            name: d.name,
            itemStyle: {
              color: d.color,
              opacity: 0.8,
            },
          })),
          emphasis: {
            focus: "self",
            itemStyle: {
              opacity: 1,
              shadowBlur: 20,
              shadowColor: "rgba(59, 130, 246, 0.5)",
            },
          },
        },
      ],
      tooltip: {
        trigger: "item",
        formatter: (params: any) => {
          return `${params.name}<br/>Market Complexity: ${(params.value[0] * 100).toFixed(0)}%<br/>Products: ${params.value[1]}`;
        },
        backgroundColor: "#151a3a",
        borderColor: "#1e2447",
        textStyle: {
          color: "#ffffff",
        },
      },
    };

    chart.setOption(option);

    const handleResize = () => chart.resize();
    window.addEventListener("resize", handleResize);

    return () => {
      window.removeEventListener("resize", handleResize);
      chart.dispose();
    };
  }, []);

  return <div ref={chartRef} className="h-full w-full" />;
}