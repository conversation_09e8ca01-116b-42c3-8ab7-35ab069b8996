"use client"

import * as React from "react"
import {
  Calculator,
  Calendar,
  CreditCard,
  Database,
  FileText,
  Hash,
  Home,
  Package,
  Search,
  Settings,
  Building2,
  Leaf,
  BarChart,
  Users,
  FlaskConical,
  Factory,
} from "lucide-react"
import { useLocation } from "wouter"
import {
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
  CommandShortcut,
} from "@/components/ui/command"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useQuery } from "@tanstack/react-query"

interface CommandPaletteProps {
  open?: boolean
  onOpenChange?: (open: boolean) => void
}

export function CommandPalette({ open: controlledOpen, onOpenChange }: CommandPaletteProps) {
  const [internalOpen, setInternalOpen] = React.useState(false)
  const [, setLocation] = useLocation()
  
  const open = controlledOpen !== undefined ? controlledOpen : internalOpen
  const setOpen = onOpenChange || setInternalOpen

  // Fetch recent products for quick access
  const { data: recentProducts } = useQuery({
    queryKey: ["recent-products"],
    queryFn: async () => {
      const response = await fetch("/api/products?limit=5&sort=created_at")
      if (!response.ok) throw new Error("Failed to fetch products")
      return response.json()
    },
    enabled: open,
  })

  React.useEffect(() => {
    const down = (e: KeyboardEvent) => {
      if (e.key === "k" && (e.metaKey || e.ctrlKey)) {
        e.preventDefault()
        setOpen((open) => !open)
      }
    }

    document.addEventListener("keydown", down)
    return () => document.removeEventListener("keydown", down)
  }, [setOpen])

  const runCommand = React.useCallback((command: () => void) => {
    setOpen(false)
    command()
  }, [setOpen])

  return (
    <>
      <Button
        variant="outline"
        className="relative h-9 w-9 p-0 xl:h-10 xl:w-60 xl:justify-start xl:px-3 xl:py-2"
        onClick={() => setOpen(true)}
      >
        <Search className="h-4 w-4 xl:mr-2" />
        <span className="hidden xl:inline-flex">Search...</span>
        <kbd className="pointer-events-none absolute right-1.5 top-2 hidden h-6 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium opacity-100 xl:flex">
          <span className="text-xs">⌘</span>K
        </kbd>
      </Button>
      <CommandDialog open={open} onOpenChange={setOpen}>
        <CommandInput placeholder="Type a command or search..." />
        <CommandList>
          <CommandEmpty>No results found.</CommandEmpty>
          
          <CommandGroup heading="Quick Actions">
            <CommandItem
              onSelect={() => runCommand(() => setLocation("/products"))}
            >
              <Package className="mr-2 h-4 w-4" />
              <span>Browse All Products</span>
            </CommandItem>
            <CommandItem
              onSelect={() => runCommand(() => setLocation("/companies"))}
            >
              <Building2 className="mr-2 h-4 w-4" />
              <span>View Companies</span>
            </CommandItem>
            <CommandItem
              onSelect={() => runCommand(() => setLocation("/dashboard"))}
            >
              <BarChart className="mr-2 h-4 w-4" />
              <span>Analytics Dashboard</span>
            </CommandItem>
            <CommandItem
              onSelect={() => runCommand(() => setLocation("/research"))}
            >
              <FlaskConical className="mr-2 h-4 w-4" />
              <span>Research Papers</span>
            </CommandItem>
          </CommandGroup>

          <CommandSeparator />

          {recentProducts?.data?.length > 0 && (
            <>
              <CommandGroup heading="Recent Products">
                {recentProducts.data.map((product: any) => (
                  <CommandItem
                    key={product.id}
                    onSelect={() => runCommand(() => setLocation(`/product/${product.id}`))}
                  >
                    <Package className="mr-2 h-4 w-4" />
                    <span className="flex-1">{product.name}</span>
                    <Badge variant="secondary" className="ml-2">
                      {product.industry_name}
                    </Badge>
                  </CommandItem>
                ))}
              </CommandGroup>
              <CommandSeparator />
            </>
          )}

          <CommandGroup heading="Navigation">
            <CommandItem
              onSelect={() => runCommand(() => setLocation("/"))}
            >
              <Home className="mr-2 h-4 w-4" />
              <span>Home</span>
              <CommandShortcut>⌘H</CommandShortcut>
            </CommandItem>
            <CommandItem
              onSelect={() => runCommand(() => setLocation("/industries"))}
            >
              <Factory className="mr-2 h-4 w-4" />
              <span>Industries</span>
            </CommandItem>
            <CommandItem
              onSelect={() => runCommand(() => setLocation("/plant-parts"))}
            >
              <Leaf className="mr-2 h-4 w-4" />
              <span>Plant Parts</span>
            </CommandItem>
          </CommandGroup>

          <CommandSeparator />

          <CommandGroup heading="Admin">
            <CommandItem
              onSelect={() => runCommand(() => setLocation("/admin"))}
            >
              <Settings className="mr-2 h-4 w-4" />
              <span>Admin Panel</span>
              <CommandShortcut>⌘A</CommandShortcut>
            </CommandItem>
            <CommandItem
              onSelect={() => runCommand(() => setLocation("/admin/products/new"))}
            >
              <FileText className="mr-2 h-4 w-4" />
              <span>Add New Product</span>
            </CommandItem>
            <CommandItem
              onSelect={() => runCommand(() => setLocation("/admin/companies/new"))}
            >
              <Building2 className="mr-2 h-4 w-4" />
              <span>Add New Company</span>
            </CommandItem>
          </CommandGroup>

          <CommandSeparator />

          <CommandGroup heading="Database Stats">
            <CommandItem disabled>
              <Database className="mr-2 h-4 w-4" />
              <span>Total Products</span>
              <Badge variant="outline" className="ml-auto">
                2,562
              </Badge>
            </CommandItem>
            <CommandItem disabled>
              <Building2 className="mr-2 h-4 w-4" />
              <span>Companies</span>
              <Badge variant="outline" className="ml-auto">
                204
              </Badge>
            </CommandItem>
            <CommandItem disabled>
              <Factory className="mr-2 h-4 w-4" />
              <span>Industries</span>
              <Badge variant="outline" className="ml-auto">
                42
              </Badge>
            </CommandItem>
          </CommandGroup>
        </CommandList>
      </CommandDialog>
    </>
  )
}

export function CommandPaletteDemo() {
  return (
    <div className="flex items-center justify-center min-h-[200px]">
      <div className="text-center space-y-4">
        <p className="text-sm text-muted-foreground">
          Press{" "}
          <kbd className="pointer-events-none inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium opacity-100">
            <span className="text-xs">⌘</span>K
          </kbd>{" "}
          to open the command palette
        </p>
        <CommandPalette />
      </div>
    </div>
  )
}