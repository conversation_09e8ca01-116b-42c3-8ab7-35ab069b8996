import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { 
  X, 
  Filter,
  Search,
  Building2,
  Leaf,
  Package,
  TrendingUp,
  Sparkles,
  SlidersHorizontal,
  ChevronDown
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { cn } from "@/lib/utils";

interface FilterOption {
  value: string;
  label: string;
  count?: number;
  color?: string;
}

interface SmartProductFiltersProps {
  industries: FilterOption[];
  plantParts: FilterOption[];
  companies: FilterOption[];
  onFiltersChange: (filters: any) => void;
  totalProducts: number;
  filteredCount: number;
  className?: string;
}

export function SmartProductFilters({
  industries,
  plantParts,
  companies,
  onFiltersChange,
  totalProducts,
  filteredCount,
  className
}: SmartProductFiltersProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedIndustries, setSelectedIndustries] = useState<string[]>([]);
  const [selectedPlantParts, setSelectedPlantParts] = useState<string[]>([]);
  const [selectedCompanies, setSelectedCompanies] = useState<string[]>([]);
  const [showNewOnly, setShowNewOnly] = useState(false);
  const [showTrendingOnly, setShowTrendingOnly] = useState(false);
  const [priceRange, setPriceRange] = useState([0, 1000]);
  const [sortBy, setSortBy] = useState("relevance");
  const [isExpanded, setIsExpanded] = useState(false);

  // Update filters when any selection changes
  useEffect(() => {
    const filters = {
      search: searchQuery,
      industries: selectedIndustries,
      plantParts: selectedPlantParts,
      companies: selectedCompanies,
      newOnly: showNewOnly,
      trendingOnly: showTrendingOnly,
      priceRange,
      sortBy
    };
    onFiltersChange(filters);
  }, [
    searchQuery,
    selectedIndustries,
    selectedPlantParts,
    selectedCompanies,
    showNewOnly,
    showTrendingOnly,
    priceRange,
    sortBy
  ]);

  const activeFilterCount = 
    selectedIndustries.length + 
    selectedPlantParts.length + 
    selectedCompanies.length +
    (showNewOnly ? 1 : 0) +
    (showTrendingOnly ? 1 : 0) +
    (searchQuery ? 1 : 0);

  const clearAllFilters = () => {
    setSearchQuery("");
    setSelectedIndustries([]);
    setSelectedPlantParts([]);
    setSelectedCompanies([]);
    setShowNewOnly(false);
    setShowTrendingOnly(false);
    setPriceRange([0, 1000]);
    setSortBy("relevance");
  };

  const toggleIndustry = (industry: string) => {
    setSelectedIndustries(prev =>
      prev.includes(industry)
        ? prev.filter(i => i !== industry)
        : [...prev, industry]
    );
  };

  const togglePlantPart = (part: string) => {
    setSelectedPlantParts(prev =>
      prev.includes(part)
        ? prev.filter(p => p !== part)
        : [...prev, part]
    );
  };

  const toggleCompany = (company: string) => {
    setSelectedCompanies(prev =>
      prev.includes(company)
        ? prev.filter(c => c !== company)
        : [...prev, company]
    );
  };

  return (
    <div className={cn("bg-white dark:bg-gray-900 rounded-lg shadow-lg", className)}>
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <Filter className="w-5 h-5 text-gray-600" />
            <h3 className="font-semibold text-lg">Smart Filters</h3>
            {activeFilterCount > 0 && (
              <Badge variant="secondary" className="ml-2">
                {activeFilterCount} active
              </Badge>
            )}
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            {isExpanded ? "Collapse" : "Expand"}
            <ChevronDown className={cn(
              "w-4 h-4 ml-1 transition-transform",
              isExpanded && "rotate-180"
            )} />
          </Button>
        </div>

        {/* Search Bar */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            type="search"
            placeholder="Search products, materials, applications..."
            className="pl-10 pr-4"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        {/* Quick Filters */}
        <div className="flex items-center gap-2 mt-3">
          <Button
            variant={showNewOnly ? "default" : "outline"}
            size="sm"
            onClick={() => setShowNewOnly(!showNewOnly)}
          >
            <Sparkles className="w-4 h-4 mr-1" />
            New
          </Button>
          <Button
            variant={showTrendingOnly ? "default" : "outline"}
            size="sm"
            onClick={() => setShowTrendingOnly(!showTrendingOnly)}
          >
            <TrendingUp className="w-4 h-4 mr-1" />
            Trending
          </Button>
          <div className="flex-1" />
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-[140px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="relevance">Relevance</SelectItem>
              <SelectItem value="newest">Newest First</SelectItem>
              <SelectItem value="name">Name (A-Z)</SelectItem>
              <SelectItem value="popular">Most Popular</SelectItem>
              <SelectItem value="rating">Highest Rated</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Result Count */}
        <div className="text-sm text-gray-600 mt-3">
          Showing <span className="font-semibold">{filteredCount.toLocaleString()}</span> of{" "}
          <span className="font-semibold">{totalProducts.toLocaleString()}</span> products
        </div>
      </div>

      {/* Expandable Filters */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-hidden"
          >
            <div className="p-4 space-y-4">
              {/* Industries */}
              <Collapsible defaultOpen>
                <CollapsibleTrigger className="flex items-center justify-between w-full py-2">
                  <div className="flex items-center gap-2">
                    <Building2 className="w-4 h-4" />
                    <span className="font-medium">Industries</span>
                    {selectedIndustries.length > 0 && (
                      <Badge variant="secondary" className="text-xs">
                        {selectedIndustries.length}
                      </Badge>
                    )}
                  </div>
                  <ChevronDown className="w-4 h-4" />
                </CollapsibleTrigger>
                <CollapsibleContent className="pt-2">
                  <div className="grid grid-cols-2 gap-2">
                    {industries.map((industry) => (
                      <label
                        key={industry.value}
                        className={cn(
                          "flex items-center justify-between p-2 rounded-md border cursor-pointer transition-colors",
                          selectedIndustries.includes(industry.value)
                            ? "border-blue-500 bg-blue-50"
                            : "border-gray-200 hover:bg-gray-50"
                        )}
                      >
                        <div className="flex items-center gap-2">
                          <input
                            type="checkbox"
                            checked={selectedIndustries.includes(industry.value)}
                            onChange={() => toggleIndustry(industry.value)}
                            className="rounded border-gray-300"
                          />
                          <span className="text-sm">{industry.label}</span>
                        </div>
                        {industry.count && (
                          <span className="text-xs text-gray-500">
                            {industry.count.toLocaleString()}
                          </span>
                        )}
                      </label>
                    ))}
                  </div>
                </CollapsibleContent>
              </Collapsible>

              <Separator />

              {/* Plant Parts */}
              <Collapsible defaultOpen>
                <CollapsibleTrigger className="flex items-center justify-between w-full py-2">
                  <div className="flex items-center gap-2">
                    <Leaf className="w-4 h-4" />
                    <span className="font-medium">Plant Parts</span>
                    {selectedPlantParts.length > 0 && (
                      <Badge variant="secondary" className="text-xs">
                        {selectedPlantParts.length}
                      </Badge>
                    )}
                  </div>
                  <ChevronDown className="w-4 h-4" />
                </CollapsibleTrigger>
                <CollapsibleContent className="pt-2">
                  <div className="grid grid-cols-2 gap-2">
                    {plantParts.map((part) => (
                      <label
                        key={part.value}
                        className={cn(
                          "flex items-center justify-between p-2 rounded-md border cursor-pointer transition-colors",
                          selectedPlantParts.includes(part.value)
                            ? "border-green-500 bg-green-50"
                            : "border-gray-200 hover:bg-gray-50"
                        )}
                      >
                        <div className="flex items-center gap-2">
                          <input
                            type="checkbox"
                            checked={selectedPlantParts.includes(part.value)}
                            onChange={() => togglePlantPart(part.value)}
                            className="rounded border-gray-300"
                          />
                          <span className="text-sm">{part.label}</span>
                        </div>
                        {part.count && (
                          <span className="text-xs text-gray-500">
                            {part.count.toLocaleString()}
                          </span>
                        )}
                      </label>
                    ))}
                  </div>
                </CollapsibleContent>
              </Collapsible>

              <Separator />

              {/* Clear Filters */}
              {activeFilterCount > 0 && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearAllFilters}
                  className="w-full"
                >
                  <X className="w-4 h-4 mr-2" />
                  Clear All Filters
                </Button>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Active Filter Pills */}
      {activeFilterCount > 0 && !isExpanded && (
        <div className="p-4 pt-0 flex flex-wrap gap-2">
          {selectedIndustries.map((industry) => (
            <Badge
              key={industry}
              variant="secondary"
              className="cursor-pointer"
              onClick={() => toggleIndustry(industry)}
            >
              {industries.find(i => i.value === industry)?.label}
              <X className="w-3 h-3 ml-1" />
            </Badge>
          ))}
          {selectedPlantParts.map((part) => (
            <Badge
              key={part}
              variant="secondary"
              className="cursor-pointer"
              onClick={() => togglePlantPart(part)}
            >
              {plantParts.find(p => p.value === part)?.label}
              <X className="w-3 h-3 ml-1" />
            </Badge>
          ))}
          {showNewOnly && (
            <Badge
              variant="secondary"
              className="cursor-pointer"
              onClick={() => setShowNewOnly(false)}
            >
              New Products
              <X className="w-3 h-3 ml-1" />
            </Badge>
          )}
          {showTrendingOnly && (
            <Badge
              variant="secondary"
              className="cursor-pointer"
              onClick={() => setShowTrendingOnly(false)}
            >
              Trending
              <X className="w-3 h-3 ml-1" />
            </Badge>
          )}
        </div>
      )}
    </div>
  );
}