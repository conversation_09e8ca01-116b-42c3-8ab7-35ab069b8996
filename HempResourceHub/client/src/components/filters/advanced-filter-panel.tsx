import React, { useState, useEffect } from 'react';
import { 
  Filter, 
  X, 
  Save, 
  Share2, 
  RotateCcw,
  ChevronDown,
  ChevronUp,
  Mic,
  Search
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import { cn } from '@/lib/utils';

interface FilterSection {
  id: string;
  label: string;
  icon?: React.ReactNode;
  expanded?: boolean;
}

interface FilterValue {
  search?: string;
  plantParts?: number[];
  industries?: number[];
  stages?: string[];
  sustainabilityRange?: [number, number];
  companies?: number[];
  dateRange?: { start: Date | null; end: Date | null };
  hasImage?: boolean;
  hasCompany?: boolean;
  hasTechnicalSpecs?: boolean;
}

interface SavedFilter {
  id: string;
  name: string;
  filters: FilterValue;
  createdAt: Date;
}

interface AdvancedFilterPanelProps {
  filters: FilterValue;
  onFiltersChange: (filters: FilterValue) => void;
  plantParts?: any[];
  industries?: any[];
  companies?: any[];
  stages?: string[];
  onClose?: () => void;
  className?: string;
}

export function AdvancedFilterPanel({
  filters,
  onFiltersChange,
  plantParts = [],
  industries = [],
  companies = [],
  stages = [],
  onClose,
  className
}: AdvancedFilterPanelProps) {
  const [localFilters, setLocalFilters] = useState<FilterValue>(filters);
  const [savedFilters, setSavedFilters] = useState<SavedFilter[]>([]);
  const [filterName, setFilterName] = useState('');
  const [expandedSections, setExpandedSections] = useState<Set<string>>(
    new Set(['search', 'categories'])
  );
  const [isListening, setIsListening] = useState(false);

  // Load saved filters from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('hemp-saved-filters');
    if (saved) {
      setSavedFilters(JSON.parse(saved));
    }
  }, []);

  // Voice search support
  const startVoiceSearch = () => {
    if (!('webkitSpeechRecognition' in window)) {
      alert('Voice search is not supported in your browser');
      return;
    }

    const recognition = new (window as any).webkitSpeechRecognition();
    recognition.lang = 'en-US';
    recognition.interimResults = false;
    recognition.maxAlternatives = 1;

    recognition.onstart = () => setIsListening(true);
    recognition.onend = () => setIsListening(false);
    
    recognition.onresult = (event: any) => {
      const transcript = event.results[0][0].transcript;
      handleSearchChange(transcript);
    };

    recognition.start();
  };

  // Handle filter changes
  const handleSearchChange = (value: string) => {
    setLocalFilters(prev => ({ ...prev, search: value }));
  };

  const handlePlantPartToggle = (partId: number) => {
    setLocalFilters(prev => ({
      ...prev,
      plantParts: prev.plantParts?.includes(partId)
        ? prev.plantParts.filter(id => id !== partId)
        : [...(prev.plantParts || []), partId]
    }));
  };

  const handleIndustryToggle = (industryId: number) => {
    setLocalFilters(prev => ({
      ...prev,
      industries: prev.industries?.includes(industryId)
        ? prev.industries.filter(id => id !== industryId)
        : [...(prev.industries || []), industryId]
    }));
  };

  const handleStageToggle = (stage: string) => {
    setLocalFilters(prev => ({
      ...prev,
      stages: prev.stages?.includes(stage)
        ? prev.stages.filter(s => s !== stage)
        : [...(prev.stages || []), stage]
    }));
  };

  const handleSustainabilityChange = (value: number[]) => {
    setLocalFilters(prev => ({
      ...prev,
      sustainabilityRange: [value[0], value[1]] as [number, number]
    }));
  };

  // Toggle section expansion
  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev => {
      const next = new Set(prev);
      if (next.has(sectionId)) {
        next.delete(sectionId);
      } else {
        next.add(sectionId);
      }
      return next;
    });
  };

  // Apply filters
  const applyFilters = () => {
    onFiltersChange(localFilters);
    updateURL(localFilters);
  };

  // Reset filters
  const resetFilters = () => {
    const emptyFilters: FilterValue = {
      search: '',
      plantParts: [],
      industries: [],
      stages: [],
      sustainabilityRange: [0, 10],
      companies: [],
      hasImage: false,
      hasCompany: false,
      hasTechnicalSpecs: false
    };
    setLocalFilters(emptyFilters);
    onFiltersChange(emptyFilters);
    updateURL(emptyFilters);
  };

  // Save current filter set
  const saveFilterSet = () => {
    if (!filterName.trim()) return;

    const newFilter: SavedFilter = {
      id: Date.now().toString(),
      name: filterName,
      filters: localFilters,
      createdAt: new Date()
    };

    const updated = [...savedFilters, newFilter];
    setSavedFilters(updated);
    localStorage.setItem('hemp-saved-filters', JSON.stringify(updated));
    setFilterName('');
  };

  // Load saved filter
  const loadSavedFilter = (filter: SavedFilter) => {
    setLocalFilters(filter.filters);
    onFiltersChange(filter.filters);
  };

  // Delete saved filter
  const deleteSavedFilter = (filterId: string) => {
    const updated = savedFilters.filter(f => f.id !== filterId);
    setSavedFilters(updated);
    localStorage.setItem('hemp-saved-filters', JSON.stringify(updated));
  };

  // Update URL with filter state
  const updateURL = (filters: FilterValue) => {
    const params = new URLSearchParams();
    
    if (filters.search) params.set('q', filters.search);
    if (filters.plantParts?.length) params.set('parts', filters.plantParts.join(','));
    if (filters.industries?.length) params.set('industries', filters.industries.join(','));
    if (filters.stages?.length) params.set('stages', filters.stages.join(','));
    if (filters.sustainabilityRange) {
      params.set('sustainability', filters.sustainabilityRange.join('-'));
    }
    
    const newURL = `${window.location.pathname}?${params.toString()}`;
    window.history.replaceState({}, '', newURL);
  };

  // Generate shareable link
  const shareFilters = () => {
    const url = window.location.href;
    if (navigator.share) {
      navigator.share({
        title: 'Hemp Database Filter',
        text: 'Check out these filtered hemp products',
        url
      });
    } else {
      navigator.clipboard.writeText(url);
      // Add toast notification here
    }
  };

  // Count active filters
  const activeFilterCount = [
    localFilters.search,
    localFilters.plantParts?.length,
    localFilters.industries?.length,
    localFilters.stages?.length,
    localFilters.sustainabilityRange && 
      (localFilters.sustainabilityRange[0] > 0 || localFilters.sustainabilityRange[1] < 10),
    localFilters.hasImage,
    localFilters.hasCompany,
    localFilters.hasTechnicalSpecs
  ].filter(Boolean).length;

  return (
    <div className={cn("bg-background border rounded-lg p-4 space-y-4", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Filter className="h-5 w-5" />
          <h3 className="font-semibold">Advanced Filters</h3>
          {activeFilterCount > 0 && (
            <Badge variant="secondary">{activeFilterCount} active</Badge>
          )}
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={shareFilters}
            className="gap-1"
          >
            <Share2 className="h-4 w-4" />
            Share
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={resetFilters}
            className="gap-1"
          >
            <RotateCcw className="h-4 w-4" />
            Reset
          </Button>
          {onClose && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      {/* Search Section */}
      <Collapsible
        open={expandedSections.has('search')}
        onOpenChange={() => toggleSection('search')}
      >
        <CollapsibleTrigger asChild>
          <Button
            variant="ghost"
            className="w-full justify-between p-2"
          >
            <span className="font-medium">Search</span>
            {expandedSections.has('search') ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent className="pt-2 space-y-3">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search products, benefits, keywords..."
              value={localFilters.search || ''}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="pl-9 pr-10"
            />
            <Button
              variant="ghost"
              size="sm"
              onClick={startVoiceSearch}
              className={cn(
                "absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0",
                isListening && "text-red-500 animate-pulse"
              )}
            >
              <Mic className="h-4 w-4" />
            </Button>
          </div>
        </CollapsibleContent>
      </Collapsible>

      {/* Categories Section */}
      <Collapsible
        open={expandedSections.has('categories')}
        onOpenChange={() => toggleSection('categories')}
      >
        <CollapsibleTrigger asChild>
          <Button
            variant="ghost"
            className="w-full justify-between p-2"
          >
            <span className="font-medium">Categories</span>
            {expandedSections.has('categories') ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent className="pt-2 space-y-4">
          {/* Plant Parts */}
          <div>
            <Label className="text-sm font-medium mb-2">Plant Parts</Label>
            <div className="grid grid-cols-2 gap-2">
              {plantParts.map(part => (
                <div key={part.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={`part-${part.id}`}
                    checked={localFilters.plantParts?.includes(part.id) || false}
                    onCheckedChange={() => handlePlantPartToggle(part.id)}
                  />
                  <Label
                    htmlFor={`part-${part.id}`}
                    className="text-sm font-normal cursor-pointer"
                  >
                    {part.name}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          {/* Industries */}
          <div>
            <Label className="text-sm font-medium mb-2">Industries</Label>
            <div className="grid grid-cols-2 gap-2 max-h-48 overflow-y-auto">
              {industries.map(industry => (
                <div key={industry.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={`industry-${industry.id}`}
                    checked={localFilters.industries?.includes(industry.id) || false}
                    onCheckedChange={() => handleIndustryToggle(industry.id)}
                  />
                  <Label
                    htmlFor={`industry-${industry.id}`}
                    className="text-sm font-normal cursor-pointer"
                  >
                    {industry.name}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          {/* Stages */}
          <div>
            <Label className="text-sm font-medium mb-2">Commercialization Stage</Label>
            <div className="flex flex-wrap gap-2">
              {stages.map(stage => (
                <Badge
                  key={stage}
                  variant={localFilters.stages?.includes(stage) ? "default" : "outline"}
                  className="cursor-pointer"
                  onClick={() => handleStageToggle(stage)}
                >
                  {stage}
                </Badge>
              ))}
            </div>
          </div>
        </CollapsibleContent>
      </Collapsible>

      {/* Sustainability Section */}
      <Collapsible
        open={expandedSections.has('sustainability')}
        onOpenChange={() => toggleSection('sustainability')}
      >
        <CollapsibleTrigger asChild>
          <Button
            variant="ghost"
            className="w-full justify-between p-2"
          >
            <span className="font-medium">Sustainability Score</span>
            {expandedSections.has('sustainability') ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent className="pt-2 space-y-3">
          <div className="px-2">
            <div className="flex justify-between text-sm text-muted-foreground mb-2">
              <span>{localFilters.sustainabilityRange?.[0] || 0}</span>
              <span>{localFilters.sustainabilityRange?.[1] || 10}</span>
            </div>
            <Slider
              value={localFilters.sustainabilityRange || [0, 10]}
              onValueChange={handleSustainabilityChange}
              min={0}
              max={10}
              step={0.5}
              className="w-full"
            />
          </div>
        </CollapsibleContent>
      </Collapsible>

      {/* Data Availability Section */}
      <Collapsible
        open={expandedSections.has('availability')}
        onOpenChange={() => toggleSection('availability')}
      >
        <CollapsibleTrigger asChild>
          <Button
            variant="ghost"
            className="w-full justify-between p-2"
          >
            <span className="font-medium">Data Availability</span>
            {expandedSections.has('availability') ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent className="pt-2 space-y-3">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label htmlFor="has-image" className="text-sm font-normal">
                Has Product Image
              </Label>
              <Switch
                id="has-image"
                checked={localFilters.hasImage || false}
                onCheckedChange={(checked) => 
                  setLocalFilters(prev => ({ ...prev, hasImage: checked }))
                }
              />
            </div>
            <div className="flex items-center justify-between">
              <Label htmlFor="has-company" className="text-sm font-normal">
                Has Company Info
              </Label>
              <Switch
                id="has-company"
                checked={localFilters.hasCompany || false}
                onCheckedChange={(checked) => 
                  setLocalFilters(prev => ({ ...prev, hasCompany: checked }))
                }
              />
            </div>
            <div className="flex items-center justify-between">
              <Label htmlFor="has-specs" className="text-sm font-normal">
                Has Technical Specs
              </Label>
              <Switch
                id="has-specs"
                checked={localFilters.hasTechnicalSpecs || false}
                onCheckedChange={(checked) => 
                  setLocalFilters(prev => ({ ...prev, hasTechnicalSpecs: checked }))
                }
              />
            </div>
          </div>
        </CollapsibleContent>
      </Collapsible>

      {/* Saved Filters */}
      <Collapsible
        open={expandedSections.has('saved')}
        onOpenChange={() => toggleSection('saved')}
      >
        <CollapsibleTrigger asChild>
          <Button
            variant="ghost"
            className="w-full justify-between p-2"
          >
            <span className="font-medium">Saved Filters</span>
            {expandedSections.has('saved') ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent className="pt-2 space-y-3">
          <div className="flex gap-2">
            <Input
              placeholder="Filter name..."
              value={filterName}
              onChange={(e) => setFilterName(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && saveFilterSet()}
            />
            <Button
              size="sm"
              onClick={saveFilterSet}
              disabled={!filterName.trim()}
            >
              <Save className="h-4 w-4" />
            </Button>
          </div>
          
          {savedFilters.length > 0 && (
            <div className="space-y-2">
              {savedFilters.map(filter => (
                <div
                  key={filter.id}
                  className="flex items-center justify-between p-2 rounded border hover:bg-muted/50"
                >
                  <button
                    className="flex-1 text-left text-sm"
                    onClick={() => loadSavedFilter(filter)}
                  >
                    {filter.name}
                  </button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => deleteSavedFilter(filter.id)}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              ))}
            </div>
          )}
        </CollapsibleContent>
      </Collapsible>

      {/* Apply Button */}
      <div className="pt-2">
        <Button
          className="w-full"
          onClick={applyFilters}
        >
          Apply Filters
        </Button>
      </div>
    </div>
  );
}

export default React.memo(AdvancedFilterPanel);