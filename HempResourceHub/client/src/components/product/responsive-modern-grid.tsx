import React, { useState, useMemo, useCallback } from "react";
import { HempProduct } from "@shared/schema";
import { EnhancedModernProductCard } from "./enhanced-modern-product-card";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { 
  Grid3X3, 
  List, 
  SortAsc, 
  SortDesc, 
  Filter,
  ChevronDown,
  Loader2,
  Package
} from "lucide-react";
import { cn } from "@/lib/utils";
import { componentStyles } from "@/lib/design-system";

interface ProductWithMetadata extends HempProduct {
  image_url?: string;
  ai_generated_image_url?: string;
  rating?: number;
  company_name?: string;
  sustainability_score?: number;
  trl_level?: number;
  industryName?: string;
  subIndustryName?: string;
  plantPartName?: string;
}

interface ResponsiveModernGridProps {
  products: ProductWithMetadata[];
  isLoading?: boolean;
  error?: string | null;
  onProductBookmark?: (productId: number) => void;
  onProductShare?: (product: HempProduct) => void;
  bookmarkedProducts?: number[];
  className?: string;
  showFilters?: boolean;
  showSorting?: boolean;
  itemsPerPage?: number;
  enableInfiniteScroll?: boolean;
}

type SortOption = 'name' | 'stage' | 'rating' | 'sustainability' | 'trl' | 'recent';
type ViewMode = 'grid' | 'list';

export function ResponsiveModernGrid({
  products,
  isLoading = false,
  error = null,
  onProductBookmark,
  onProductShare,
  bookmarkedProducts = [],
  className,
  showFilters = true,
  showSorting = true,
  itemsPerPage = 12,
  enableInfiniteScroll = false
}: ResponsiveModernGridProps) {
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [sortBy, setSortBy] = useState<SortOption>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [currentPage, setCurrentPage] = useState(1);
  const [displayedItems, setDisplayedItems] = useState(itemsPerPage);

  // Sort products
  const sortedProducts = useMemo(() => {
    if (!products) return [];

    const sorted = [...products].sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortBy) {
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'stage':
          const stageOrder = { 'research': 1, 'development': 2, 'pilot': 3, 'commercial': 4 };
          aValue = stageOrder[a.commercializationStage?.toLowerCase() as keyof typeof stageOrder] || 0;
          bValue = stageOrder[b.commercializationStage?.toLowerCase() as keyof typeof stageOrder] || 0;
          break;
        case 'rating':
          aValue = a.rating || 0;
          bValue = b.rating || 0;
          break;
        case 'sustainability':
          aValue = a.sustainability_score || 0;
          bValue = b.sustainability_score || 0;
          break;
        case 'trl':
          aValue = a.trl_level || 0;
          bValue = b.trl_level || 0;
          break;
        case 'recent':
          aValue = new Date(a.createdAt || 0).getTime();
          bValue = new Date(b.createdAt || 0).getTime();
          break;
        default:
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
      }

      if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
      return 0;
    });

    return sorted;
  }, [products, sortBy, sortOrder]);

  // Paginated products
  const paginatedProducts = useMemo(() => {
    if (enableInfiniteScroll) {
      return sortedProducts.slice(0, displayedItems);
    }
    const startIndex = (currentPage - 1) * itemsPerPage;
    return sortedProducts.slice(startIndex, startIndex + itemsPerPage);
  }, [sortedProducts, currentPage, itemsPerPage, displayedItems, enableInfiniteScroll]);

  const totalPages = Math.ceil(sortedProducts.length / itemsPerPage);

  // Load more for infinite scroll
  const loadMore = useCallback(() => {
    setDisplayedItems(prev => prev + itemsPerPage);
  }, [itemsPerPage]);

  // Handle sort change
  const handleSortChange = (newSortBy: SortOption) => {
    if (newSortBy === sortBy) {
      setSortOrder(prev => prev === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(newSortBy);
      setSortOrder('asc');
    }
  };

  // Loading skeleton
  if (isLoading) {
    return (
      <div className={cn("w-full", className)}>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {Array.from({ length: 8 }).map((_, i) => (
            <div key={i} className="space-y-4">
              <Skeleton className="aspect-[4/3] w-full rounded-xl bg-gray-800" />
              <div className="space-y-2">
                <Skeleton className="h-4 w-3/4 bg-gray-800" />
                <Skeleton className="h-4 w-1/2 bg-gray-800" />
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={cn("w-full flex items-center justify-center py-12", className)}>
        <div className="text-center">
          <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-white mb-2">Error Loading Products</h3>
          <p className="text-gray-400">{error}</p>
        </div>
      </div>
    );
  }

  // Empty state
  if (!products || products.length === 0) {
    return (
      <div className={cn("w-full flex items-center justify-center py-12", className)}>
        <div className="text-center">
          <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-white mb-2">No Products Found</h3>
          <p className="text-gray-400">Try adjusting your search or filters</p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("w-full space-y-6", className)}>
      {/* Controls Bar */}
      {(showFilters || showSorting) && (
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          {/* Results Count */}
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="bg-gray-800/50 text-gray-300 border-gray-700">
              {sortedProducts.length} products
            </Badge>
          </div>

          {/* Controls */}
          <div className="flex items-center gap-4">
            {/* Sort Controls */}
            {showSorting && (
              <div className="flex items-center gap-2">
                <Select value={sortBy} onValueChange={(value) => handleSortChange(value as SortOption)}>
                  <SelectTrigger className="w-40 bg-black/50 border-gray-700 text-white">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-black border-gray-700">
                    <SelectItem value="name">Name</SelectItem>
                    <SelectItem value="stage">Stage</SelectItem>
                    <SelectItem value="rating">Rating</SelectItem>
                    <SelectItem value="sustainability">Sustainability</SelectItem>
                    <SelectItem value="trl">TRL Level</SelectItem>
                    <SelectItem value="recent">Recently Added</SelectItem>
                  </SelectContent>
                </Select>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSortOrder(prev => prev === 'asc' ? 'desc' : 'asc')}
                  className="h-10 w-10 p-0 border border-gray-700 hover:bg-gray-800"
                >
                  {sortOrder === 'asc' ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />}
                </Button>
              </div>
            )}

            {/* View Mode Toggle */}
            <div className="flex items-center border border-gray-700 rounded-lg overflow-hidden">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('grid')}
                className="h-10 px-3 rounded-none border-none"
              >
                <Grid3X3 className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('list')}
                className="h-10 px-3 rounded-none border-none"
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Products Grid */}
      <div className={cn(
        viewMode === 'grid' 
          ? "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
          : "space-y-4"
      )}>
        {paginatedProducts.map((product) => (
          <EnhancedModernProductCard
            key={product.id}
            product={product}
            industryName={product.industryName}
            subIndustryName={product.subIndustryName}
            plantPartName={product.plantPartName}
            onBookmark={onProductBookmark}
            onShare={onProductShare}
            isBookmarked={bookmarkedProducts.includes(product.id)}
            variant={viewMode === 'list' ? 'compact' : 'default'}
            className={viewMode === 'list' ? "max-w-none" : ""}
          />
        ))}
      </div>

      {/* Pagination or Load More */}
      {enableInfiniteScroll ? (
        displayedItems < sortedProducts.length && (
          <div className="flex justify-center pt-8">
            <Button
              onClick={loadMore}
              className="px-8 py-3 bg-purple-500 hover:bg-purple-600"
            >
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Load More Products
            </Button>
          </div>
        )
      ) : (
        totalPages > 1 && (
          <div className="flex items-center justify-center gap-2 pt-8">
            <Button
              variant="ghost"
              onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
              disabled={currentPage === 1}
              className="border border-gray-700"
            >
              Previous
            </Button>
            
            <div className="flex items-center gap-1">
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                const pageNum = i + 1;
                return (
                  <Button
                    key={pageNum}
                    variant={currentPage === pageNum ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setCurrentPage(pageNum)}
                    className="h-10 w-10 p-0"
                  >
                    {pageNum}
                  </Button>
                );
              })}
            </div>

            <Button
              variant="ghost"
              onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
              disabled={currentPage === totalPages}
              className="border border-gray-700"
            >
              Next
            </Button>
          </div>
        )
      )}
    </div>
  );
}
