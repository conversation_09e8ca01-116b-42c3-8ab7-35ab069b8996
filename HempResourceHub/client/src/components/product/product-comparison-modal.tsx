import { useState, useEffect } from "react";
import { HempProduct } from "@shared/schema";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  X, 
  Check, 
  Minus, 
  Star, 
  Leaf, 
  Building2, 
  TrendingUp,
  BarChart3,
  Package,
  DollarSign,
  Clock,
  AlertCircle
} from "lucide-react";
import { cn } from "@/lib/utils";
import { Progress } from "@/components/ui/progress";

interface ProductComparisonModalProps {
  products: (HempProduct & {
    image_url?: string;
    rating?: number;
    company_name?: string;
    sustainability_score?: number;
    views?: number;
  })[];
  isOpen: boolean;
  onClose: () => void;
  industryNames?: Record<number, string>;
  plantPartNames?: Record<number, string>;
}

export const ProductComparisonModal = ({
  products,
  isOpen,
  onClose,
  industryNames = {},
  plantPartNames = {}
}: ProductComparisonModalProps) => {
  const [selectedTab, setSelectedTab] = useState("overview");

  if (products.length < 2) {
    return null;
  }

  // Comparison categories
  const categories = {
    overview: {
      label: "Overview",
      icon: Package,
      fields: [
        { key: "name", label: "Product Name", type: "text" },
        { key: "description", label: "Description", type: "text" },
        { key: "company_name", label: "Company", type: "text" },
        { key: "commercialization_stage", label: "Stage", type: "badge" },
        { key: "created_at", label: "Added", type: "date" }
      ]
    },
    performance: {
      label: "Performance",
      icon: BarChart3,
      fields: [
        { key: "rating", label: "Rating", type: "rating" },
        { key: "views", label: "Views", type: "number" },
        { key: "sustainability_score", label: "Sustainability", type: "progress" }
      ]
    },
    technical: {
      label: "Technical",
      icon: Leaf,
      fields: [
        { key: "plant_part_id", label: "Plant Part", type: "lookup", lookup: plantPartNames },
        { key: "industry_sub_category_id", label: "Industry", type: "lookup", lookup: industryNames },
        { key: "benefits_advantages", label: "Benefits", type: "list" }
      ]
    }
  };

  const renderFieldValue = (product: any, field: any) => {
    const value = product[field.key];

    if (!value && value !== 0) return <Minus className="w-4 h-4 text-gray-400" />;

    switch (field.type) {
      case "text":
        return <span className="text-sm">{value}</span>;
      
      case "badge":
        return <Badge variant="outline" className="text-xs">{value}</Badge>;
      
      case "date":
        return <span className="text-sm">{new Date(value).toLocaleDateString()}</span>;
      
      case "rating":
        return (
          <div className="flex items-center gap-1">
            <Star className="w-4 h-4 fill-amber-400 text-amber-400" />
            <span className="text-sm font-medium">{value.toFixed(1)}</span>
          </div>
        );
      
      case "number":
        return <span className="text-sm font-medium">{value.toLocaleString()}</span>;
      
      case "progress":
        return (
          <div className="w-full max-w-[100px]">
            <Progress value={value} className="h-2" />
            <span className="text-xs text-gray-500 mt-1">{value}%</span>
          </div>
        );
      
      case "lookup":
        return <span className="text-sm">{field.lookup[value] || value}</span>;
      
      case "list":
        return Array.isArray(value) ? (
          <ul className="text-xs space-y-1">
            {value.slice(0, 3).map((item, idx) => (
              <li key={idx} className="flex items-start gap-1">
                <Check className="w-3 h-3 text-green-500 mt-0.5 flex-shrink-0" />
                <span>{item}</span>
              </li>
            ))}
            {value.length > 3 && (
              <li className="text-gray-500">+{value.length - 3} more</li>
            )}
          </ul>
        ) : null;
      
      default:
        return <span className="text-sm">{String(value)}</span>;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold">
            Compare Products
          </DialogTitle>
        </DialogHeader>

        <Tabs value={selectedTab} onValueChange={setSelectedTab} className="mt-4">
          <TabsList className="grid grid-cols-3 w-full max-w-md mx-auto">
            {Object.entries(categories).map(([key, category]) => (
              <TabsTrigger key={key} value={key} className="flex items-center gap-2">
                <category.icon className="w-4 h-4" />
                {category.label}
              </TabsTrigger>
            ))}
          </TabsList>

          <ScrollArea className="h-[60vh] mt-6">
            {Object.entries(categories).map(([key, category]) => (
              <TabsContent key={key} value={key} className="mt-0">
                <div className="space-y-6">
                  {/* Product Images */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pb-4 border-b">
                    {products.map((product, idx) => (
                      <div key={product.id} className="text-center">
                        <div className="aspect-square rounded-lg overflow-hidden mb-2 bg-gray-100 dark:bg-gray-800">
                          <img
                            src={product.image_url || "/images/unknown-hemp-image.png"}
                            alt={product.name}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <h4 className="font-medium text-sm line-clamp-2">{product.name}</h4>
                        {idx === 0 && (
                          <Badge className="mt-1" variant="secondary">Base Product</Badge>
                        )}
                      </div>
                    ))}
                  </div>

                  {/* Comparison Fields */}
                  <div className="space-y-4">
                    {category.fields.map((field) => (
                      <div key={field.key} className="grid grid-cols-5 gap-4 items-start">
                        <div className="font-medium text-sm text-gray-600 dark:text-gray-400">
                          {field.label}
                        </div>
                        {products.map((product) => (
                          <div key={product.id} className="col-span-1">
                            {renderFieldValue(product, field)}
                          </div>
                        ))}
                      </div>
                    ))}
                  </div>
                </div>
              </TabsContent>
            ))}
          </ScrollArea>
        </Tabs>

        <div className="flex justify-between items-center mt-6 pt-4 border-t">
          <div className="flex items-center gap-2 text-sm text-gray-500">
            <AlertCircle className="w-4 h-4" />
            <span>Comparing {products.length} products</span>
          </div>
          <Button onClick={onClose}>Close Comparison</Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};