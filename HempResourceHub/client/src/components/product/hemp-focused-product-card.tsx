import { Link } from "wouter";
import { HempProduct, PlantPart, Industry } from "@shared/schema";
import { Badge } from "@/components/ui/badge";
import { ArrowRight, Leaf, TreePine, Factory, Building2, Sparkles, Star, TrendingUp } from "lucide-react";
import { useState } from "react";
import { cn } from "@/lib/utils";

interface HempFocusedProductCardProps {
  product: HempProduct & { image_url?: string };
  plantParts?: PlantPart[];
  industries?: Industry[];
  viewMode?: 'grid' | 'list' | 'mobile';
  className?: string;
}

const HempFocusedProductCard = ({ 
  product, 
  plantParts, 
  industries, 
  viewMode = 'grid',
  className 
}: HempFocusedProductCardProps) => {
  const [imageError, setImageError] = useState(false);
  
  // Extract data
  const stage = product.commercializationStage || 'Research';
  const plantPart = plantParts?.find(p => p.id === product.plantPartId);
  const industry = industries?.find(i => i.id === product.industrySubCategoryId);
  
  // Updated stage colors with black/purple/green theme
  const stageStyles = {
    'Growing': {
      bg: 'bg-hemp-500/20',
      text: 'text-hemp-400',
      border: 'border-hemp-500/50',
      icon: <TrendingUp className="w-3 h-3" />
    },
    'Established': {
      bg: 'bg-purple-600/20',
      text: 'text-purple-400',
      border: 'border-purple-600/50',
      icon: <Star className="w-3 h-3" />
    },
    'Research': {
      bg: 'bg-purple-600/20',
      text: 'text-purple-400',
      border: 'border-purple-600/50',
      icon: <Sparkles className="w-3 h-3" />
    },
    'Speculative': {
      bg: 'bg-gray-600/20',
      text: 'text-gray-400',
      border: 'border-gray-600/50',
      icon: <TrendingUp className="w-3 h-3" />
    }
  };
  
  const currentStageStyle = stageStyles[stage as keyof typeof stageStyles] || stageStyles.Research;
  
  // Get category-specific fallback image
  const getFallbackImage = () => {
    const fallbacks: Record<string, string> = {
      'Hemp Seed': '/images/fallbacks/hemp-seeds.jpg',
      'Hemp Bast (Fiber)': '/images/fallbacks/hemp-fiber.jpg',
      'Hemp Flowers': '/images/fallbacks/hemp-flower.jpg',
      'Hemp Roots': '/images/fallbacks/hemp-root.jpg',
      'Hemp Hurd (Shivs)': '/images/fallbacks/hemp-hurd.jpg',
      'Hemp Leaves': '/images/fallbacks/hemp-leaves.jpg'
    };
    return fallbacks[plantPart?.name || ''] || '/images/unknown-hemp-image.png';
  };
  
  const productImageUrl = product.imageUrl || product.image_url;
  const imageUrl = imageError ? getFallbackImage() : (productImageUrl || getFallbackImage());
  const benefits = product.benefits_advantages || [];
  const isAIGenerated = productImageUrl?.includes('replicate') || productImageUrl?.includes('dall-e');
  
  // Mobile view - compact with hemp focus
  if (viewMode === 'mobile') {
    return (
      <Link href={`/product/${product.id}`}>
        <div className={cn(
          "group bg-dark-card backdrop-blur-sm rounded-xl p-3 border border-dark-border",
          "hover:border-hemp-500/50 transition-all duration-300",
          "hover:shadow-lg hover:shadow-hemp-500/10",
          className
        )}>
          <div className="flex gap-3">
            {/* Thumbnail with plant part overlay */}
            <div className="relative w-20 h-20 rounded-lg overflow-hidden bg-dark-sidebar flex-shrink-0">
              <img
                src={imageUrl}
                alt={product.name}
                className="w-full h-full object-cover"
                onError={() => setImageError(true)}
              />
              {plantPart && (
                <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/90 to-transparent px-1.5 py-0.5">
                  <span className="text-[10px] text-hemp-400 font-medium">{plantPart.name}</span>
                </div>
              )}
            </div>
            
            {/* Content */}
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-sm line-clamp-1 mb-1 text-white">
                {product.name}
              </h3>
              
              {/* Industry and stage in one line */}
              <div className="flex items-center gap-2 mb-1">
                {industry && (
                  <span className="text-xs text-purple-400">
                    {industry.name}
                  </span>
                )}
                <Badge className={cn(
                  "text-[10px] px-1.5 py-0",
                  currentStageStyle.bg,
                  currentStageStyle.text,
                  currentStageStyle.border
                )}>
                  {stage}
                </Badge>
              </div>
              
              {/* First benefit */}
              {benefits.length > 0 && (
                <p className="text-xs text-gray-400 line-clamp-1">
                  {benefits[0]}
                </p>
              )}
            </div>
            
            <ArrowRight className="w-4 h-4 text-gray-600 group-hover:text-hemp-400 transition-all flex-shrink-0 self-center" />
          </div>
        </div>
      </Link>
    );
  }
  
  // List view - horizontal layout with prominent hemp info
  if (viewMode === 'list') {
    return (
      <Link href={`/product/${product.id}`}>
        <div className={cn(
          "group bg-dark-card backdrop-blur-sm rounded-xl p-4",
          "transition-all duration-300 hover:bg-dark-hover",
          "border border-dark-border hover:border-hemp-500/50",
          "hover:shadow-lg hover:shadow-hemp-500/10",
          className
        )}>
          <div className="flex items-center gap-4">
            {/* Image with plant part badge */}
            <div className="relative w-32 h-32 flex-shrink-0 rounded-lg overflow-hidden bg-dark-sidebar">
              <img
                src={imageUrl}
                alt={product.name}
                className="w-full h-full object-cover"
                onError={() => setImageError(true)}
              />
              {plantPart && (
                <Badge className="absolute top-2 left-2 bg-hemp-500/90 text-white border-none text-xs px-2 py-0.5">
                  <Leaf className="w-3 h-3 mr-1" />
                  {plantPart.name}
                </Badge>
              )}
              {isAIGenerated && (
                <Badge className="absolute top-2 right-2 bg-purple-600/90 text-white border-none text-xs px-2 py-0.5">
                  <Sparkles className="w-3 h-3" />
                </Badge>
              )}
            </div>
            
            {/* Content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between gap-4 mb-2">
                <div>
                  <h3 className="font-semibold text-lg text-white group-hover:text-hemp-400 transition-colors">
                    {product.name}
                  </h3>
                  {industry && (
                    <p className="text-sm text-purple-400 mt-0.5">
                      {industry.name}
                    </p>
                  )}
                </div>
                <Badge className={cn(
                  "flex items-center gap-1 px-3 py-1",
                  currentStageStyle.bg,
                  currentStageStyle.text,
                  currentStageStyle.border
                )}>
                  {currentStageStyle.icon}
                  {stage}
                </Badge>
              </div>
              
              {/* Benefits - more prominent display */}
              {benefits.length > 0 && (
                <div className="space-y-1">
                  <p className="text-xs font-medium text-gray-400 uppercase tracking-wider">Key Benefits:</p>
                  <div className="flex flex-wrap gap-2">
                    {benefits.slice(0, 3).map((benefit, idx) => (
                      <span key={idx} className="text-sm text-gray-300 flex items-center gap-1">
                        <span className="w-1 h-1 bg-hemp-400 rounded-full" />
                        {benefit}
                      </span>
                    ))}
                    {benefits.length > 3 && (
                      <span className="text-sm text-hemp-400">
                        +{benefits.length - 3} more
                      </span>
                    )}
                  </div>
                </div>
              )}
              
              {/* Companies if available */}
              {(product as any).hemp_company_products && (product as any).hemp_company_products.length > 0 && (
                <div className="flex items-center gap-1 mt-2 text-sm text-gray-500">
                  <Building2 className="w-3 h-3" />
                  <span>
                    {(product as any).hemp_company_products[0].hemp_companies.name}
                    {(product as any).hemp_company_products.length > 1 && ` +${(product as any).hemp_company_products.length - 1}`}
                  </span>
                </div>
              )}
            </div>
            
            <ArrowRight className="w-5 h-5 text-gray-600 group-hover:text-hemp-400 transition-all group-hover:translate-x-1 flex-shrink-0" />
          </div>
        </div>
      </Link>
    );
  }
  
  // Grid view (default) - redesigned for hemp focus
  return (
    <Link href={`/product/${product.id}`}>
      <div className={cn(
        "group bg-dark-card backdrop-blur-sm rounded-xl overflow-hidden",
        "transition-all duration-300 hover:shadow-2xl hover:shadow-hemp-500/20",
        "hover:-translate-y-1 border border-dark-border hover:border-hemp-500/50",
        "hover:bg-dark-hover",
        className
      )}>
        {/* Image section with overlays */}
        <div className="aspect-[4/3] relative overflow-hidden bg-dark-sidebar">
          <img
            src={imageUrl}
            alt={product.name}
            className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
            onError={() => setImageError(true)}
          />
          
          {/* Gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black via-black/50 to-transparent" />
          
          {/* Top badges */}
          <div className="absolute top-3 left-3 right-3 flex justify-between items-start">
            {plantPart && (
              <Badge className="bg-hemp-500/90 text-white border-none text-xs px-2 py-1">
                <Leaf className="w-3 h-3 mr-1" />
                {plantPart.name}
              </Badge>
            )}
            {isAIGenerated && (
              <Badge className="bg-purple-600/90 text-white border-none text-xs px-2 py-1">
                <Sparkles className="w-3 h-3" />
              </Badge>
            )}
          </div>
        </div>
        
        {/* Content section - focused on benefits and stage */}
        <div className="p-4 space-y-3">
          {/* Product title and industry */}
          <div className="mb-2">
            <h3 className="font-bold text-lg text-white mb-1 line-clamp-2">
              {product.name}
            </h3>
            {industry && (
              <p className="text-sm text-purple-400 font-medium">
                {industry.name}
              </p>
            )}
          </div>
          {/* Stage with icon */}
          <div className="flex items-center justify-between">
            <Badge className={cn(
              "flex items-center gap-1.5 px-3 py-1",
              currentStageStyle.bg,
              currentStageStyle.text,
              currentStageStyle.border
            )}>
              {currentStageStyle.icon}
              {stage}
            </Badge>
            
            {/* Company count if available */}
            {(product as any).hemp_company_products && (product as any).hemp_company_products.length > 0 && (
              <span className="text-xs text-gray-500 flex items-center gap-1">
                <Building2 className="w-3 h-3" />
                {(product as any).hemp_company_products.length} companies
              </span>
            )}
          </div>
          
          {/* Benefits preview - more prominent */}
          {benefits.length > 0 && (
            <div className="space-y-1">
              <p className="text-xs font-medium text-gray-500 uppercase tracking-wider">Benefits:</p>
              <div className="space-y-1">
                {benefits.slice(0, 2).map((benefit, idx) => (
                  <p key={idx} className="text-sm text-gray-300 flex items-start gap-1.5">
                    <span className="w-1 h-1 bg-hemp-400 rounded-full mt-1.5 flex-shrink-0" />
                    <span className="line-clamp-1">{benefit}</span>
                  </p>
                ))}
                {benefits.length > 2 && (
                  <p className="text-sm text-hemp-400 font-medium">
                    +{benefits.length - 2} more benefits
                  </p>
                )}
              </div>
            </div>
          )}
          
          {/* View details link */}
          <div className="flex items-center justify-end pt-2 border-t border-dark-border">
            <span className="text-sm text-gray-500 group-hover:text-hemp-400 transition-colors flex items-center gap-1">
              View Details
              <ArrowRight className="w-4 h-4 transition-transform group-hover:translate-x-1" />
            </span>
          </div>
        </div>
      </div>
    </Link>
  );
};

export default HempFocusedProductCard;