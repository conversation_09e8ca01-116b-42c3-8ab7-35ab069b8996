import React, { useState } from "react";
import { <PERSON> } from "wouter";
import { HempProduct } from "@shared/schema";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { 
  ArrowRight, 
  Star, 
  Sparkles, 
  Building2, 
  Leaf, 
  Eye,
  Bookmark,
  Share2,
  ExternalLink,
  TrendingUp,
  Award,
  Zap
} from "lucide-react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { componentStyles } from "@/lib/design-system";

interface EnhancedModernProductCardProps {
  product: HempProduct & { 
    image_url?: string;
    ai_generated_image_url?: string;
    rating?: number;
    company_name?: string;
    sustainability_score?: number;
    trl_level?: number;
  };
  industryName?: string;
  subIndustryName?: string;
  plantPartName?: string;
  onBookmark?: (productId: number) => void;
  onShare?: (product: HempProduct) => void;
  isBookmarked?: boolean;
  showActions?: boolean;
  variant?: 'default' | 'compact' | 'featured';
  className?: string;
}

export function EnhancedModernProductCard({
  product,
  industryName,
  subIndustryName,
  plantPartName,
  onBookmark,
  onShare,
  isBookmarked = false,
  showActions = true,
  variant = 'default',
  className
}: EnhancedModernProductCardProps) {
  const [imageError, setImageError] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  // Determine the best image to use
  const imageUrl = !imageError 
    ? (product.ai_generated_image_url || product.image_url || product.imageUrl || '/images/unknown-hemp-image.png')
    : '/images/unknown-hemp-image.png';

  // Get commercialization stage color
  const getStageColor = (stage?: string) => {
    switch (stage?.toLowerCase()) {
      case 'research': return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'development': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      case 'pilot': return 'bg-orange-500/20 text-orange-400 border-orange-500/30';
      case 'commercial': return 'bg-green-500/20 text-green-400 border-green-500/30';
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  // Get TRL level badge
  const getTRLBadge = (trl?: number) => {
    if (!trl) return null;
    const color = trl >= 7 ? 'text-green-400' : trl >= 4 ? 'text-yellow-400' : 'text-blue-400';
    return (
      <Badge variant="outline" className={cn("text-xs", color)}>
        TRL {trl}
      </Badge>
    );
  };

  // Truncate description
  const truncatedDescription = product.description?.length > 120 
    ? product.description.slice(0, 120) + '...'
    : product.description;

  // Card variants
  const cardVariants = {
    default: "aspect-[4/3]",
    compact: "aspect-[16/9]",
    featured: "aspect-[3/2]"
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={cn("group relative", className)}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <Card className={cn(
        "h-full overflow-hidden transition-all duration-300",
        componentStyles.productCard.interactive,
        "hover:shadow-2xl hover:-translate-y-1"
      )}>
        {/* Image Container */}
        <div className={cn("relative overflow-hidden bg-gray-900", cardVariants[variant])}>
          <img
            src={imageUrl}
            alt={product.name}
            className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
            onError={() => setImageError(true)}
          />
          
          {/* Gradient Overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent" />
          
          {/* Top Right Badges */}
          <div className="absolute top-3 right-3 flex flex-col gap-2">
            {product.trl_level && getTRLBadge(product.trl_level)}
            {product.sustainability_score && product.sustainability_score > 7 && (
              <Badge className="bg-green-500/20 text-green-400 border-green-500/30">
                <Leaf className="h-3 w-3 mr-1" />
                Eco+
              </Badge>
            )}
          </div>

          {/* Bottom Left Plant Part Badge */}
          {plantPartName && (
            <div className="absolute bottom-3 left-3">
              <Badge className={componentStyles.hemp.badge}>
                <Sparkles className="h-3 w-3 mr-1" />
                {plantPartName}
              </Badge>
            </div>
          )}

          {/* Action Buttons Overlay */}
          {showActions && (
            <div className={cn(
              "absolute top-3 left-3 flex gap-2 transition-opacity duration-200",
              isHovered ? "opacity-100" : "opacity-0"
            )}>
              <Button
                size="sm"
                variant="ghost"
                className="h-8 w-8 p-0 rounded-full bg-black/50 backdrop-blur-sm hover:bg-black/70"
                onClick={(e) => {
                  e.preventDefault();
                  onBookmark?.(product.id);
                }}
              >
                <Bookmark className={cn("h-4 w-4", isBookmarked && "fill-current text-yellow-400")} />
              </Button>
              
              <Button
                size="sm"
                variant="ghost"
                className="h-8 w-8 p-0 rounded-full bg-black/50 backdrop-blur-sm hover:bg-black/70"
                onClick={(e) => {
                  e.preventDefault();
                  onShare?.(product);
                }}
              >
                <Share2 className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>

        {/* Content */}
        <div className="p-6 flex flex-col flex-1">
          {/* Header */}
          <div className="mb-3">
            <h3 className="font-semibold text-lg text-white mb-2 line-clamp-2 group-hover:text-purple-300 transition-colors">
              {product.name}
            </h3>
            
            {/* Category and Industry Badges */}
            <div className="flex flex-wrap gap-2 mb-3">
              {industryName && (
                <Badge variant="outline" className={componentStyles.interactive.badge}>
                  <Building2 className="h-3 w-3 mr-1" />
                  {industryName}
                </Badge>
              )}
              {subIndustryName && (
                <Badge variant="outline" className="bg-gray-700/50 text-gray-300 border-gray-600">
                  {subIndustryName}
                </Badge>
              )}
            </div>
          </div>

          {/* Description */}
          {truncatedDescription && (
            <p className="text-gray-300 text-sm mb-4 flex-1 leading-relaxed">
              {truncatedDescription}
            </p>
          )}

          {/* Commercialization Stage */}
          {product.commercializationStage && (
            <div className="mb-4">
              <Badge className={getStageColor(product.commercializationStage)}>
                <TrendingUp className="h-3 w-3 mr-1" />
                {product.commercializationStage}
              </Badge>
            </div>
          )}

          {/* Benefits Preview */}
          {product.benefitsAdvantages && product.benefitsAdvantages.length > 0 && (
            <div className="mb-4">
              <div className="flex flex-wrap gap-1">
                {product.benefitsAdvantages.slice(0, 2).map((benefit, index) => (
                  <Badge 
                    key={index}
                    variant="secondary"
                    className="text-xs bg-green-500/10 text-green-400 border-green-500/20"
                  >
                    {benefit}
                  </Badge>
                ))}
                {product.benefitsAdvantages.length > 2 && (
                  <Badge variant="secondary" className="text-xs bg-gray-700/50 text-gray-400">
                    +{product.benefitsAdvantages.length - 2} more
                  </Badge>
                )}
              </div>
            </div>
          )}

          {/* Footer */}
          <div className="flex items-center justify-between mt-auto pt-4 border-t border-gray-800">
            {/* Company Info */}
            {product.company_name && (
              <div className="flex items-center gap-2 text-sm text-gray-400">
                <Building2 className="h-4 w-4" />
                <span className="truncate">{product.company_name}</span>
              </div>
            )}

            {/* View Details Button */}
            <Link href={`/product/${product.id}`}>
              <Button
                size="sm"
                className={cn(
                  "ml-auto transition-all duration-200",
                  componentStyles.interactive.button,
                  "group-hover:bg-purple-600"
                )}
              >
                <span className="mr-2">View Details</span>
                <ArrowRight className="h-4 w-4 transition-transform group-hover:translate-x-1" />
              </Button>
            </Link>
          </div>

          {/* Rating/Quality Indicators */}
          {(product.rating || product.sustainability_score) && (
            <div className="flex items-center gap-4 mt-3 pt-3 border-t border-gray-800">
              {product.rating && (
                <div className="flex items-center gap-1">
                  <Star className="h-4 w-4 text-yellow-400 fill-current" />
                  <span className="text-sm text-gray-300">{product.rating.toFixed(1)}</span>
                </div>
              )}
              
              {product.sustainability_score && (
                <div className="flex items-center gap-1">
                  <Leaf className="h-4 w-4 text-green-400" />
                  <span className="text-sm text-gray-300">
                    {product.sustainability_score}/10
                  </span>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Hover Glow Effect */}
        <div className={cn(
          "absolute inset-0 rounded-xl transition-opacity duration-300 pointer-events-none",
          "bg-gradient-to-r from-purple-500/5 to-green-500/5",
          isHovered ? "opacity-100" : "opacity-0"
        )} />
      </Card>
    </motion.div>
  );
}
