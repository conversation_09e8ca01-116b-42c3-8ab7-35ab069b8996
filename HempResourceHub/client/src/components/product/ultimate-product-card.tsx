import { useState, memo } from "react";
import { <PERSON> } from "wouter";
import { HempProduct } from "@shared/schema";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  ArrowRight, 
  Star, 
  Sparkles, 
  Building2, 
  Leaf,
  Heart,
  Share2,
  Eye,
  Clock,
  TrendingUp,
  Award,
  BarChart3,
  ExternalLink,
  GitCompare,
  Maximize2,
  ShoppingCart,
  Bookmark,
  MessageSquare,
  ThumbsUp
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface UltimateProductCardProps {
  product: HempProduct & { 
    image_url?: string;
    ai_generated_image_url?: string;
    rating?: number;
    company_name?: string;
    views?: number;
    is_featured?: boolean;
    sustainability_score?: number;
    commercialization_stage?: string;
  };
  industryName?: string;
  subIndustryName?: string;
  plantPartName?: string;
  variant?: "default" | "compact" | "detailed";
  showQuickActions?: boolean;
  onFavorite?: (productId: number) => void;
  onShare?: (product: HempProduct) => void;
  onCompare?: (product: HempProduct) => void;
  onQuickView?: (product: HempProduct) => void;
  isCompareSelected?: boolean;
  showRating?: boolean;
  className?: string;
}

const UltimateProductCard = memo(({
  product,
  industryName,
  subIndustryName,
  plantPartName,
  variant = "default",
  showQuickActions = true,
  onFavorite,
  onShare,
  onCompare,
  onQuickView,
  isCompareSelected = false,
  showRating = true,
  className
}: UltimateProductCardProps) => {
  const [imageError, setImageError] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [isFavorited, setIsFavorited] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [userRating, setUserRating] = useState<number | null>(null);
  const [hoveredRating, setHoveredRating] = useState<number | null>(null);
  const [showComments, setShowComments] = useState(false);
  const [likesCount, setLikesCount] = useState(Math.floor(Math.random() * 100));
  const [isLiked, setIsLiked] = useState(false);
  
  const imageUrl = !imageError 
    ? (product.ai_generated_image_url || product.image_url || product.imageUrl)
    : '/images/unknown-hemp-image.png';

  const isNew = product.created_at && new Date(product.created_at) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
  const hasHighRating = product.rating && product.rating >= 4.5;
  
  const handleFavorite = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsFavorited(!isFavorited);
    onFavorite?.(product.id);
  };

  const handleShare = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onShare?.(product);
  };

  const handleQuickView = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (onQuickView) {
      onQuickView(product);
    } else {
      setShowPreview(!showPreview);
    }
  };

  const handleCompare = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onCompare?.(product);
  };

  const handleBookmark = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsBookmarked(!isBookmarked);
  };

  const handleLike = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsLiked(!isLiked);
    setLikesCount(prev => isLiked ? prev - 1 : prev + 1);
  };

  const handleRatingClick = (rating: number) => {
    setUserRating(rating);
  };

  // Variant-specific heights
  const cardHeight = {
    compact: "h-[320px]",
    default: "h-[420px]",
    detailed: "h-[480px]"
  }[variant];

  return (
    <TooltipProvider>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        whileHover={{ y: -4 }}
        onHoverStart={() => setIsHovered(true)}
        onHoverEnd={() => setIsHovered(false)}
        className={cn("group relative", className)}
      >
        <div className={cn(
          "relative overflow-hidden bg-white dark:bg-gray-800/50 backdrop-blur-sm rounded-2xl",
          "border border-gray-200 dark:border-gray-700 transition-all duration-300",
          "shadow-[var(--shadow-elevation-low)] hover:shadow-[var(--shadow-elevation-high)]",
          "hover:border-primary/30 dark:hover:border-primary/40",
          cardHeight
        )}>
          {/* Image Section */}
          <div className="relative h-[200px] overflow-hidden bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-900">
            <motion.img
              src={imageUrl}
              alt={product.name}
              className="w-full h-full object-cover"
              onError={() => setImageError(true)}
              animate={{ scale: isHovered ? 1.1 : 1 }}
              transition={{ duration: 0.5 }}
            />
            
            {/* Gradient Overlay */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent" />
            
            {/* Top Badges */}
            <div className="absolute top-3 left-3 right-3 flex items-start justify-between">
              <div className="flex flex-wrap gap-2">
                {industryName && (
                  <Badge className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm shadow-md">
                    {industryName}
                  </Badge>
                )}
                {isNew && (
                  <Badge className="bg-green-500 text-white border-0 shadow-md">
                    <Sparkles className="w-3 h-3 mr-1" />
                    New
                  </Badge>
                )}
                {product.is_featured && (
                  <Badge className="bg-amber-500 text-white border-0 shadow-md">
                    <Award className="w-3 h-3 mr-1" />
                    Featured
                  </Badge>
                )}
              </div>
              
              {/* Quick Actions */}
              {showQuickActions && (
                <AnimatePresence>
                  {isHovered && (
                    <motion.div
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: 20 }}
                      className="flex gap-1"
                    >
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            size="icon"
                            variant="ghost"
                            className="h-8 w-8 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm"
                            onClick={handleQuickView}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>Quick View</TooltipContent>
                      </Tooltip>
                      
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            size="icon"
                            variant="ghost"
                            className="h-8 w-8 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm"
                            onClick={handleFavorite}
                          >
                            <Heart className={cn("h-4 w-4", isFavorited && "fill-red-500 text-red-500")} />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          {isFavorited ? "Remove from favorites" : "Add to favorites"}
                        </TooltipContent>
                      </Tooltip>
                      
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            size="icon"
                            variant="ghost"
                            className="h-8 w-8 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm"
                            onClick={handleShare}
                          >
                            <Share2 className="h-4 w-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>Share</TooltipContent>
                      </Tooltip>
                      
                      {onCompare && (
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              size="icon"
                              variant="ghost"
                              className={cn(
                                "h-8 w-8 backdrop-blur-sm",
                                isCompareSelected 
                                  ? "bg-primary text-white" 
                                  : "bg-white/90 dark:bg-gray-800/90"
                              )}
                              onClick={handleCompare}
                            >
                              <GitCompare className="h-4 w-4" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>Compare</TooltipContent>
                        </Tooltip>
                      )}
                      
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            size="icon"
                            variant="ghost"
                            className="h-8 w-8 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm"
                            onClick={handleBookmark}
                          >
                            <Bookmark className={cn("h-4 w-4", isBookmarked && "fill-primary text-primary")} />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          {isBookmarked ? "Remove bookmark" : "Bookmark"}
                        </TooltipContent>
                      </Tooltip>
                    </motion.div>
                  )}
                </AnimatePresence>
              )}
            </div>
            
            {/* Bottom Stats Bar */}
            {variant !== "compact" && (
              <div className="absolute bottom-0 left-0 right-0 p-3 bg-gradient-to-t from-black/80 to-transparent">
                <div className="flex items-center gap-4 text-white/90 text-sm">
                  {product.rating && (
                    <div className="flex items-center gap-1">
                      <Star className="w-3.5 h-3.5 fill-amber-400 text-amber-400" />
                      <span className="font-medium">{product.rating.toFixed(1)}</span>
                    </div>
                  )}
                  {product.views && (
                    <div className="flex items-center gap-1">
                      <Eye className="w-3.5 h-3.5" />
                      <span>{product.views.toLocaleString()}</span>
                    </div>
                  )}
                  {product.sustainability_score && (
                    <div className="flex items-center gap-1">
                      <Leaf className="w-3.5 h-3.5 text-green-400" />
                      <span>{product.sustainability_score}%</span>
                    </div>
                  )}
                  <div className="flex items-center gap-1">
                    <ThumbsUp className={cn("w-3.5 h-3.5", isLiked && "fill-blue-400 text-blue-400")} />
                    <span>{likesCount}</span>
                  </div>
                </div>
              </div>
            )}
          </div>
          
          {/* Content Section */}
          <div className="p-5 flex flex-col h-[calc(100%-200px)]">
            {/* Title */}
            <Link href={`/product/${product.id}`}>
              <h3 className="font-semibold text-lg text-gray-900 dark:text-white mb-2 line-clamp-2 group-hover:text-primary transition-colors cursor-pointer">
                {product.name}
              </h3>
            </Link>
            
            {/* Description - only show in default and detailed variants */}
            {variant !== "compact" && product.description && (
              <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 mb-3">
                {product.description}
              </p>
            )}
            
            {/* Meta Information */}
            <div className="flex flex-wrap items-center gap-3 text-sm text-gray-500 dark:text-gray-400 mb-auto">
              {product.company_name && (
                <div className="flex items-center gap-1">
                  <Building2 className="w-3.5 h-3.5" />
                  <span className="truncate max-w-[140px]">{product.company_name}</span>
                </div>
              )}
              {plantPartName && (
                <div className="flex items-center gap-1">
                  <Leaf className="w-3.5 h-3.5 text-green-600 dark:text-green-400" />
                  <span>{plantPartName}</span>
                </div>
              )}
              {variant === "detailed" && product.commercialization_stage && (
                <div className="flex items-center gap-1">
                  <TrendingUp className="w-3.5 h-3.5 text-blue-600 dark:text-blue-400" />
                  <span>{product.commercialization_stage}</span>
                </div>
              )}
            </div>
            
            {/* Tags */}
            <div className="flex flex-wrap gap-2 mt-3">
              {subIndustryName && (
                <Badge variant="secondary" className="text-xs">
                  {subIndustryName}
                </Badge>
              )}
              {hasHighRating && (
                <Badge variant="outline" className="text-xs border-amber-200 dark:border-amber-800">
                  <Star className="w-3 h-3 mr-1 fill-amber-400 text-amber-400" />
                  Top Rated
                </Badge>
              )}
              {variant === "detailed" && product.created_at && (
                <Badge variant="outline" className="text-xs">
                  <Clock className="w-3 h-3 mr-1" />
                  {new Date(product.created_at).toLocaleDateString()}
                </Badge>
              )}
            </div>
            
            {/* Interactive Rating (when detailed or when user has rated) */}
            {(showRating && (variant === "detailed" || userRating !== null)) && (
              <div className="flex items-center gap-2 mt-3">
                <span className="text-xs text-gray-500 dark:text-gray-400">Rate this:</span>
                <div className="flex gap-0.5">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <button
                      key={star}
                      className="p-0.5 transition-transform hover:scale-110"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        handleRatingClick(star);
                      }}
                      onMouseEnter={() => setHoveredRating(star)}
                      onMouseLeave={() => setHoveredRating(null)}
                    >
                      <Star
                        className={cn(
                          "w-4 h-4 transition-colors",
                          (hoveredRating || userRating || 0) >= star
                            ? "fill-amber-400 text-amber-400"
                            : "text-gray-300 dark:text-gray-600"
                        )}
                      />
                    </button>
                  ))}
                </div>
                {userRating && (
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    You rated: {userRating}
                  </span>
                )}
              </div>
            )}
            
            {/* Footer Actions */}
            <div className="flex items-center justify-between mt-4 pt-3 border-t border-gray-100 dark:border-gray-700">
              <Link href={`/product/${product.id}`}>
                <Button variant="ghost" size="sm" className="text-xs group/btn">
                  View Details
                  <ArrowRight className="w-3 h-3 ml-1 transition-transform group-hover/btn:translate-x-1" />
                </Button>
              </Link>
              
              {variant === "detailed" && (
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="text-xs"
                  onClick={(e) => {
                    e.preventDefault();
                    window.open(`/product/${product.id}`, '_blank');
                  }}
                >
                  <ExternalLink className="w-3 h-3 mr-1" />
                  Open
                </Button>
              )}
            </div>
          </div>
          
          {/* Progress Indicator for Loading */}
          {!product.image_url && !product.ai_generated_image_url && (
            <div className="absolute bottom-0 left-0 right-0 h-1 bg-gray-200 dark:bg-gray-700">
              <motion.div
                className="h-full bg-primary"
                initial={{ width: 0 }}
                animate={{ width: "100%" }}
                transition={{ duration: 2, repeat: Infinity }}
              />
            </div>
          )}
        </div>
        
        {/* Quick Preview Modal - could be implemented as a separate component */}
        <AnimatePresence>
          {showPreview && (
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              className="absolute inset-0 z-50 bg-white dark:bg-gray-800 rounded-2xl shadow-2xl p-6"
              onClick={(e) => e.stopPropagation()}
            >
              {/* Quick preview content */}
              <Button
                size="icon"
                variant="ghost"
                className="absolute top-2 right-2"
                onClick={handleQuickView}
              >
                ×
              </Button>
              <h3 className="font-semibold text-lg mb-2">{product.name}</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {product.description || "No description available"}
              </p>
              {/* Add more preview content as needed */}
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    </TooltipProvider>
  );
});

UltimateProductCard.displayName = "UltimateProductCard";

export default UltimateProductCard;
export { UltimateProductCard };