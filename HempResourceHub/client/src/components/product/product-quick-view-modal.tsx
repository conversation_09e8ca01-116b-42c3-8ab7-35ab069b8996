import { useState } from "react";
import { HempProduct } from "@shared/schema";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, SheetTitle } from "@/components/ui/sheet";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { 
  Star, 
  Leaf, 
  Building2, 
  TrendingUp,
  Eye,
  Heart,
  Share2,
  ShoppingCart,
  ExternalLink,
  Clock,
  Award,
  CheckCircle,
  Package,
  Info,
  BarChart3,
  MessageSquare,
  ThumbsUp
} from "lucide-react";
import { cn } from "@/lib/utils";
import { Progress } from "@/components/ui/progress";
import { Link } from "wouter";

interface ProductQuickViewModalProps {
  product: HempProduct & {
    image_url?: string;
    ai_generated_image_url?: string;
    rating?: number;
    company_name?: string;
    sustainability_score?: number;
    views?: number;
    is_featured?: boolean;
  };
  isOpen: boolean;
  onClose: () => void;
  industryName?: string;
  subIndustryName?: string;
  plantPartName?: string;
  onFavorite?: (productId: number) => void;
  onShare?: (product: HempProduct) => void;
}

export const ProductQuickViewModal = ({
  product,
  isOpen,
  onClose,
  industryName,
  subIndustryName,
  plantPartName,
  onFavorite,
  onShare
}: ProductQuickViewModalProps) => {
  const [isFavorited, setIsFavorited] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  
  const images = [
    product.ai_generated_image_url || product.image_url || "/images/unknown-hemp-image.png",
    // Add more images if available
  ];

  const handleFavorite = () => {
    setIsFavorited(!isFavorited);
    onFavorite?.(product.id);
  };

  const handleShare = () => {
    onShare?.(product);
  };

  const benefits = product.benefits_advantages || [];
  const isNew = product.created_at && new Date(product.created_at) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent className="w-full sm:max-w-2xl overflow-hidden">
        <SheetHeader>
          <SheetTitle className="text-2xl font-bold pr-8">{product.name}</SheetTitle>
        </SheetHeader>

        <ScrollArea className="h-[calc(100vh-120px)] mt-6">
          <div className="space-y-6 pr-4">
            {/* Image Gallery */}
            <div className="space-y-4">
              <div className="aspect-video rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-800">
                <img
                  src={images[selectedImageIndex]}
                  alt={product.name}
                  className="w-full h-full object-cover"
                />
              </div>
              
              {/* Image Thumbnails */}
              {images.length > 1 && (
                <div className="flex gap-2">
                  {images.map((img, idx) => (
                    <button
                      key={idx}
                      onClick={() => setSelectedImageIndex(idx)}
                      className={cn(
                        "w-16 h-16 rounded-md overflow-hidden border-2 transition-all",
                        selectedImageIndex === idx
                          ? "border-primary"
                          : "border-transparent opacity-70 hover:opacity-100"
                      )}
                    >
                      <img src={img} alt="" className="w-full h-full object-cover" />
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Quick Stats */}
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
              {product.rating && (
                <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3 text-center">
                  <div className="flex items-center justify-center gap-1 mb-1">
                    <Star className="w-4 h-4 fill-amber-400 text-amber-400" />
                    <span className="font-bold">{product.rating.toFixed(1)}</span>
                  </div>
                  <span className="text-xs text-gray-500">Rating</span>
                </div>
              )}
              
              {product.views && (
                <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3 text-center">
                  <div className="flex items-center justify-center gap-1 mb-1">
                    <Eye className="w-4 h-4 text-blue-500" />
                    <span className="font-bold">{product.views.toLocaleString()}</span>
                  </div>
                  <span className="text-xs text-gray-500">Views</span>
                </div>
              )}
              
              {product.sustainability_score && (
                <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3 text-center">
                  <div className="flex items-center justify-center gap-1 mb-1">
                    <Leaf className="w-4 h-4 text-green-500" />
                    <span className="font-bold">{product.sustainability_score}%</span>
                  </div>
                  <span className="text-xs text-gray-500">Eco Score</span>
                </div>
              )}
              
              <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3 text-center">
                <div className="flex items-center justify-center gap-1 mb-1">
                  <TrendingUp className="w-4 h-4 text-purple-500" />
                  <span className="font-bold text-sm">{product.commercialization_stage || "Research"}</span>
                </div>
                <span className="text-xs text-gray-500">Stage</span>
              </div>
            </div>

            {/* Tags and Metadata */}
            <div className="flex flex-wrap gap-2">
              {industryName && (
                <Badge variant="secondary">
                  <Package className="w-3 h-3 mr-1" />
                  {industryName}
                </Badge>
              )}
              {subIndustryName && (
                <Badge variant="outline">{subIndustryName}</Badge>
              )}
              {plantPartName && (
                <Badge variant="outline" className="border-green-200 dark:border-green-800">
                  <Leaf className="w-3 h-3 mr-1" />
                  {plantPartName}
                </Badge>
              )}
              {isNew && (
                <Badge className="bg-green-500 text-white">
                  <Award className="w-3 h-3 mr-1" />
                  New
                </Badge>
              )}
              {product.is_featured && (
                <Badge className="bg-amber-500 text-white">
                  <Star className="w-3 h-3 mr-1" />
                  Featured
                </Badge>
              )}
            </div>

            <Separator />

            {/* Tabbed Content */}
            <Tabs defaultValue="overview" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="benefits">Benefits</TabsTrigger>
                <TabsTrigger value="details">Details</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-4 mt-4">
                <div>
                  <h3 className="font-semibold mb-2 flex items-center gap-2">
                    <Info className="w-4 h-4" />
                    Description
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">
                    {product.description || "No description available for this product."}
                  </p>
                </div>

                {product.company_name && (
                  <div>
                    <h3 className="font-semibold mb-2 flex items-center gap-2">
                      <Building2 className="w-4 h-4" />
                      Company
                    </h3>
                    <p className="text-sm">{product.company_name}</p>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="benefits" className="space-y-4 mt-4">
                <h3 className="font-semibold flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  Key Benefits & Advantages
                </h3>
                {benefits.length > 0 ? (
                  <ul className="space-y-2">
                    {benefits.map((benefit, idx) => (
                      <li key={idx} className="flex items-start gap-2 text-sm">
                        <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                        <span>{benefit}</span>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p className="text-sm text-gray-500">No specific benefits listed for this product.</p>
                )}
              </TabsContent>

              <TabsContent value="details" className="space-y-4 mt-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">Product ID:</span>
                    <p className="font-medium">#{product.id}</p>
                  </div>
                  {product.created_at && (
                    <div>
                      <span className="text-gray-500">Added:</span>
                      <p className="font-medium">
                        {new Date(product.created_at).toLocaleDateString()}
                      </p>
                    </div>
                  )}
                  <div>
                    <span className="text-gray-500">Category:</span>
                    <p className="font-medium">{industryName || "Not specified"}</p>
                  </div>
                  <div>
                    <span className="text-gray-500">Plant Part:</span>
                    <p className="font-medium">{plantPartName || "Not specified"}</p>
                  </div>
                </div>
              </TabsContent>
            </Tabs>

            <Separator />

            {/* Action Buttons */}
            <div className="flex gap-3">
              <Link href={`/product/${product.id}`} className="flex-1">
                <Button className="w-full" size="lg">
                  <ExternalLink className="w-4 h-4 mr-2" />
                  View Full Details
                </Button>
              </Link>
              
              <Button
                size="lg"
                variant="outline"
                onClick={handleFavorite}
                className="flex-1"
              >
                <Heart className={cn("w-4 h-4 mr-2", isFavorited && "fill-current")} />
                {isFavorited ? "Favorited" : "Favorite"}
              </Button>
              
              <Button
                size="icon"
                variant="outline"
                onClick={handleShare}
              >
                <Share2 className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </ScrollArea>
      </SheetContent>
    </Sheet>
  );
};