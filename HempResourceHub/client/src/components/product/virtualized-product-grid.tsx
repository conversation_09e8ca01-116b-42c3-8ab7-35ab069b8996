import React, { useRef, useCallback } from 'react';
import { useVirtualizer } from '@tanstack/react-virtual';
import { InteractiveProductCard } from './interactive-product-card';
import { Skeleton } from '@/components/ui/skeleton';

interface VirtualizedProductGridProps {
  products: any[];
  columns?: number;
  isLoading?: boolean;
  onFavorite?: (productId: number) => void;
  onBookmark?: (productId: number) => void;
  onShare?: (product: any) => void;
  favorites?: number[];
  bookmarks?: number[];
  industryNames?: Record<number, string>;
  subIndustryNames?: Record<number, string>;
  plantPartNames?: Record<number, string>;
}

export function VirtualizedProductGrid({
  products,
  columns = 3,
  isLoading = false,
  onFavorite,
  onBookmark,
  onShare,
  favorites = [],
  bookmarks = [],
  industryNames = {},
  subIndustryNames = {},
  plantPartNames = {}
}: VirtualizedProductGridProps) {
  const parentRef = useRef<HTMLDivElement>(null);
  const scrollingRef = useRef<number | null>(null);

  // Calculate responsive columns
  const getColumns = useCallback(() => {
    if (typeof window === 'undefined') return columns;
    const width = window.innerWidth;
    if (width < 640) return 1; // Mobile
    if (width < 1024) return 2; // Tablet
    if (width < 1536) return columns; // Desktop
    return columns + 1; // Wide screen
  }, [columns]);

  const [responsiveColumns, setResponsiveColumns] = React.useState(getColumns());

  React.useEffect(() => {
    const handleResize = () => {
      setResponsiveColumns(getColumns());
    };
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [getColumns]);

  // Calculate rows needed
  const rowCount = Math.ceil(products.length / responsiveColumns);
  
  // Row virtualizer
  const rowVirtualizer = useVirtualizer({
    count: rowCount,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 400, // Estimated row height
    overscan: 2, // Render 2 extra rows above and below viewport
    scrollingDelay: 150,
  });

  // Handle smooth scrolling
  const handleScroll = useCallback(() => {
    if (scrollingRef.current !== null) {
      clearTimeout(scrollingRef.current);
    }
    scrollingRef.current = window.setTimeout(() => {
      scrollingRef.current = null;
    }, 150);
  }, []);

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4 gap-6">
        {Array.from({ length: 12 }).map((_, i) => (
          <div key={i} className="space-y-3">
            <Skeleton className="h-48 w-full rounded-lg" />
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </div>
        ))}
      </div>
    );
  }

  if (products.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-muted-foreground">No products found matching your criteria.</p>
      </div>
    );
  }

  return (
    <div
      ref={parentRef}
      className="h-[calc(100vh-200px)] overflow-auto scrollbar-thin scrollbar-thumb-gray-700 scrollbar-track-gray-900"
      onScroll={handleScroll}
    >
      <div
        style={{
          height: `${rowVirtualizer.getTotalSize()}px`,
          width: '100%',
          position: 'relative',
        }}
      >
        {rowVirtualizer.getVirtualItems().map((virtualRow) => {
          const startIndex = virtualRow.index * responsiveColumns;
          const endIndex = Math.min(startIndex + responsiveColumns, products.length);
          const rowProducts = products.slice(startIndex, endIndex);

          return (
            <div
              key={virtualRow.key}
              data-index={virtualRow.index}
              ref={rowVirtualizer.measureElement}
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                transform: `translateY(${virtualRow.start}px)`,
              }}
            >
              <div className={`grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-${columns} 2xl:grid-cols-${columns + 1} gap-6 px-2`}>
                {rowProducts.map((product) => (
                  <div key={product.id} className="w-full">
                    <InteractiveProductCard
                      product={product}
                      industryNames={industryNames}
                      subIndustryNames={subIndustryNames}
                      plantPartNames={plantPartNames}
                      onFavorite={() => onFavorite?.(product.id)}
                      onBookmark={() => onBookmark?.(product.id)}
                      onShare={() => onShare?.(product)}
                      isFavorited={favorites.includes(product.id)}
                      isBookmarked={bookmarks.includes(product.id)}
                    />
                  </div>
                ))}
                {/* Fill empty cells in the last row */}
                {rowProducts.length < responsiveColumns && virtualRow.index === rowCount - 1 && (
                  Array.from({ length: responsiveColumns - rowProducts.length }).map((_, i) => (
                    <div key={`empty-${i}`} className="w-full" />
                  ))
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}

// Export a memoized version to prevent unnecessary re-renders
export default React.memo(VirtualizedProductGrid);