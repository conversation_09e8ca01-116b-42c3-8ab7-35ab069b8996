import { useProductComparison } from "@/hooks/use-product-comparison";
import { ProductComparisonModal } from "./product-comparison-modal";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { X, GitCompare, Minimize2, Maximize2 } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";
import { useState } from "react";

interface ProductComparisonBarProps {
  industryNames?: Record<number, string>;
  plantPartNames?: Record<number, string>;
}

export const ProductComparisonBar = ({
  industryNames,
  plantPartNames
}: ProductComparisonBarProps) => {
  const { 
    products, 
    isComparisonOpen, 
    removeProduct, 
    clearComparison, 
    openComparison, 
    closeComparison 
  } = useProductComparison();
  
  const [isMinimized, setIsMinimized] = useState(false);

  if (products.length === 0) {
    return null;
  }

  return (
    <>
      <AnimatePresence>
        {!isComparisonOpen && (
          <motion.div
            initial={{ y: 100, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: 100, opacity: 0 }}
            className={cn(
              "fixed bottom-4 left-4 right-4 z-40",
              "max-w-4xl mx-auto"
            )}
          >
            <div className={cn(
              "bg-white dark:bg-gray-800 rounded-lg shadow-2xl border border-gray-200 dark:border-gray-700",
              "backdrop-blur-sm"
            )}>
              {/* Header */}
              <div className="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center gap-3">
                  <GitCompare className="w-5 h-5 text-primary" />
                  <span className="font-medium">Compare Products</span>
                  <Badge variant="secondary">{products.length}</Badge>
                </div>
                
                <div className="flex items-center gap-2">
                  <Button
                    size="icon"
                    variant="ghost"
                    onClick={() => setIsMinimized(!isMinimized)}
                  >
                    {isMinimized ? <Maximize2 className="w-4 h-4" /> : <Minimize2 className="w-4 h-4" />}
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={clearComparison}
                  >
                    Clear All
                  </Button>
                  <Button
                    size="sm"
                    onClick={openComparison}
                    disabled={products.length < 2}
                  >
                    Compare Now
                  </Button>
                </div>
              </div>
              
              {/* Product List */}
              {!isMinimized && (
                <div className="p-3">
                  <div className="flex gap-3 overflow-x-auto">
                    {products.map((product) => (
                      <motion.div
                        key={product.id}
                        initial={{ scale: 0.8, opacity: 0 }}
                        animate={{ scale: 1, opacity: 1 }}
                        exit={{ scale: 0.8, opacity: 0 }}
                        className="flex-shrink-0 relative group"
                      >
                        <div className="w-24 h-24 rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-700">
                          <img
                            src={product.image_url || product.ai_generated_image_url || "/images/unknown-hemp-image.png"}
                            alt={product.name}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <button
                          onClick={() => removeProduct(product.id)}
                          className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          <X className="w-3 h-3" />
                        </button>
                        <p className="text-xs mt-1 line-clamp-2 text-center">
                          {product.name}
                        </p>
                      </motion.div>
                    ))}
                    
                    {/* Add more products hint */}
                    {products.length < 4 && (
                      <div className="flex-shrink-0 w-24 h-24 rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600 flex items-center justify-center">
                        <div className="text-center">
                          <GitCompare className="w-6 h-6 text-gray-400 mx-auto mb-1" />
                          <p className="text-xs text-gray-500">Add more</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Comparison Modal */}
      <ProductComparisonModal
        products={products}
        isOpen={isComparisonOpen}
        onClose={closeComparison}
        industryNames={industryNames}
        plantPartNames={plantPartNames}
      />
    </>
  );
};