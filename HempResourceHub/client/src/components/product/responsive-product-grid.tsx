import { useState, useEffect, useCallback } from "react";
import { HempProduct } from "@shared/schema";
import { UltimateProductCard } from "./ultimate-product-card";
import { Button } from "@/components/ui/button";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { Grid3x3, List, LayoutGrid } from "lucide-react";
import { cn } from "@/lib/utils";
import { motion, AnimatePresence } from "framer-motion";
import { useMediaQuery } from "@/hooks/use-media-query";

interface ResponsiveProductGridProps {
  products: (HempProduct & { 
    image_url?: string;
    ai_generated_image_url?: string;
    rating?: number;
    company_name?: string;
  })[];
  industryNames?: Record<number, string>;
  subIndustryNames?: Record<number, string>;
  plantPartNames?: Record<number, string>;
  loading?: boolean;
  showViewControls?: boolean;
  defaultView?: "grid" | "list" | "compact";
  onProductClick?: (product: HempProduct) => void;
  onFavorite?: (productId: number) => void;
  onShare?: (product: HempProduct) => void;
  className?: string;
}

const ResponsiveProductGrid = ({
  products,
  industryNames,
  subIndustryNames,
  plantPartNames,
  loading = false,
  showViewControls = true,
  defaultView = "grid",
  onProductClick,
  onFavorite,
  onShare,
  className
}: ResponsiveProductGridProps) => {
  const [viewMode, setViewMode] = useState<"grid" | "list" | "compact">(defaultView);
  const [cardVariant, setCardVariant] = useState<"default" | "compact" | "detailed">("default");
  const isMobile = useMediaQuery("(max-width: 640px)");
  const isTablet = useMediaQuery("(max-width: 1024px)");

  // Adjust view mode based on screen size
  useEffect(() => {
    if (isMobile && viewMode === "compact") {
      setViewMode("list");
    }
  }, [isMobile, viewMode]);

  // Map view mode to card variant
  useEffect(() => {
    const variantMap: Record<string, "default" | "compact" | "detailed"> = {
      grid: "default",
      list: "detailed",
      compact: "compact"
    };
    setCardVariant(variantMap[viewMode]);
  }, [viewMode]);

  const getGridClassName = useCallback(() => {
    if (viewMode === "list") {
      return "grid grid-cols-1 gap-4";
    }
    
    if (viewMode === "compact") {
      return cn(
        "grid gap-4",
        "grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6"
      );
    }

    // Default grid view
    return cn(
      "grid gap-6",
      "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
    );
  }, [viewMode]);

  if (loading) {
    return (
      <div className={cn("space-y-8", className)}>
        {showViewControls && (
          <div className="h-10 bg-gray-200 dark:bg-gray-800 rounded-lg animate-pulse" />
        )}
        <div className={getGridClassName()}>
          {[...Array(8)].map((_, i) => (
            <div
              key={i}
              className="h-[420px] bg-gray-200 dark:bg-gray-800 rounded-2xl animate-pulse"
            />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* View Controls */}
      {showViewControls && (
        <div className="flex items-center justify-between flex-wrap gap-4">
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600 dark:text-gray-400">
              {products.length} products found
            </span>
          </div>
          
          <div className="flex items-center gap-2">
            {/* Mobile View Selector */}
            <div className="sm:hidden">
              <Select value={viewMode} onValueChange={(value: any) => setViewMode(value)}>
                <SelectTrigger className="w-[140px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="grid">
                    <div className="flex items-center gap-2">
                      <Grid3x3 className="w-4 h-4" />
                      Grid View
                    </div>
                  </SelectItem>
                  <SelectItem value="list">
                    <div className="flex items-center gap-2">
                      <List className="w-4 h-4" />
                      List View
                    </div>
                  </SelectItem>
                  {!isMobile && (
                    <SelectItem value="compact">
                      <div className="flex items-center gap-2">
                        <LayoutGrid className="w-4 h-4" />
                        Compact
                      </div>
                    </SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>
            
            {/* Desktop View Controls */}
            <div className="hidden sm:flex items-center gap-1 bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
              <Button
                variant={viewMode === "grid" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("grid")}
                className="h-8 w-8 p-0"
              >
                <Grid3x3 className="w-4 h-4" />
              </Button>
              <Button
                variant={viewMode === "list" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("list")}
                className="h-8 w-8 p-0"
              >
                <List className="w-4 h-4" />
              </Button>
              {!isTablet && (
                <Button
                  variant={viewMode === "compact" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("compact")}
                  className="h-8 w-8 p-0"
                >
                  <LayoutGrid className="w-4 h-4" />
                </Button>
              )}
            </div>
          </div>
        </div>
      )}
      
      {/* Product Grid */}
      <AnimatePresence mode="wait">
        <motion.div
          key={viewMode}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.2 }}
          className={getGridClassName()}
        >
          {products.map((product, index) => (
            <motion.div
              key={product.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.05 }}
            >
              <UltimateProductCard
                product={product}
                industryName={industryNames?.[product.industrySubCategoryId]}
                subIndustryName={subIndustryNames?.[product.industrySubCategoryId || 0]}
                plantPartName={plantPartNames?.[product.plantPartId]}
                variant={cardVariant}
                showQuickActions={!isMobile}
                onFavorite={onFavorite}
                onShare={onShare}
                className={cn(
                  viewMode === "list" && "max-w-4xl mx-auto",
                  "h-full"
                )}
              />
            </motion.div>
          ))}
        </motion.div>
      </AnimatePresence>
      
      {/* Empty State */}
      {products.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500 dark:text-gray-400">No products found</p>
        </div>
      )}
    </div>
  );
};

export default ResponsiveProductGrid;
export { ResponsiveProductGrid };