import { useState, memo } from "react";
import { <PERSON> } from "wouter";
import { HempProduct } from "@shared/schema";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  ArrowRight, 
  Star, 
  Sparkles, 
  Building2, 
  Leaf,
  Heart,
  Share2,
  Eye,
  TrendingUp,
  Award,
  ExternalLink,
  BarChart3
} from "lucide-react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import SmartProductImage from "@/components/ui/smart-product-image";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface EnhancedProductCardV2Props {
  product: HempProduct & { 
    image_url?: string;
    ai_generated_image_url?: string;
    rating?: number;
    company_name?: string;
    views?: number;
    is_featured?: boolean;
    sustainability_score?: number;
    commercialization_stage?: string;
  };
  industryName?: string;
  subIndustryName?: string;
  plantPartName?: string;
  variant?: "default" | "compact" | "detailed";
  showQuickActions?: boolean;
  onFavorite?: (productId: number) => void;
  onShare?: (product: HempProduct) => void;
  className?: string;
}

const EnhancedProductCardV2 = memo(({
  product,
  industryName,
  subIndustryName,
  plantPartName,
  variant = "default",
  showQuickActions = true,
  onFavorite,
  onShare,
  className
}: EnhancedProductCardV2Props) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isFavorited, setIsFavorited] = useState(false);
  
  const isNew = product.created_at && new Date(product.created_at) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
  const hasHighRating = product.rating && product.rating >= 4.5;
  
  const handleFavorite = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsFavorited(!isFavorited);
    onFavorite?.(product.id);
  };

  const handleShare = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onShare?.(product);
  };

  return (
    <TooltipProvider>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        whileHover={{ y: -4 }}
        onHoverStart={() => setIsHovered(true)}
        onHoverEnd={() => setIsHovered(false)}
        className={cn("group relative", className)}
      >
        <div className={cn(
          "relative bg-white dark:bg-gray-900 rounded-xl overflow-hidden",
          "border border-gray-200 dark:border-gray-800",
          "shadow-sm hover:shadow-xl transition-all duration-300",
          "flex flex-col h-full",
          "cursor-pointer" // Make entire card clickable
        )}>
          {/* Image Section - Fixed height */}
          <div className="relative h-52 overflow-hidden bg-gray-100 dark:bg-gray-800">
            <SmartProductImage
              product={{
                name: product.name,
                image_url: product.ai_generated_image_url || product.image_url,
                plantPartId: product.plantPartId
              }}
              plantPartName={plantPartName}
              className="w-full h-full object-cover"
              showAIBadge={true}
            />
            
            {/* Top Badge - Single most important */}
            <div className="absolute top-3 left-3 right-3 flex items-start justify-between">
              <div>
                {isNew ? (
                  <Badge className="bg-green-500 text-white border-0 shadow-sm text-xs font-medium">
                    <Sparkles className="w-3 h-3 mr-1" />
                    New
                  </Badge>
                ) : product.is_featured ? (
                  <Badge className="bg-amber-500 text-white border-0 shadow-sm text-xs font-medium">
                    <Award className="w-3 h-3 mr-1" />
                    Featured
                  </Badge>
                ) : industryName ? (
                  <Badge className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm shadow-sm text-xs font-medium">
                    {industryName}
                  </Badge>
                ) : null}
              </div>
              
              {/* Quick Actions */}
              {showQuickActions && (
                <div className={cn(
                  "flex gap-1 transition-opacity duration-200",
                  isHovered ? "opacity-100" : "opacity-0"
                )}>
                  <Button
                    size="icon"
                    variant="ghost"
                    className="h-8 w-8 bg-white dark:bg-gray-900 shadow-sm"
                    onClick={handleFavorite}
                  >
                    <Heart className={cn("h-4 w-4", isFavorited && "fill-red-500 text-red-500")} />
                  </Button>
                  
                  <Button
                    size="icon"
                    variant="ghost"
                    className="h-8 w-8 bg-white dark:bg-gray-900 shadow-sm"
                    onClick={handleShare}
                  >
                    <Share2 className="h-4 w-4" />
                  </Button>
                </div>
              )}
            </div>
            
            {/* Bottom Stats Bar */}
            <div className="absolute bottom-0 left-0 right-0 p-2 bg-black/50">
              <div className="flex items-center gap-3 text-white text-xs">
                {product.rating && (
                  <div className="flex items-center gap-1">
                    <Star className="w-3 h-3 fill-amber-400 text-amber-400" />
                    <span className="font-medium">{product.rating.toFixed(1)}</span>
                  </div>
                )}
                {product.views && (
                  <div className="flex items-center gap-1">
                    <Eye className="w-3 h-3" />
                    <span>{product.views.toLocaleString()}</span>
                  </div>
                )}
                {product.sustainability_score && (
                  <div className="flex items-center gap-1">
                    <Leaf className="w-3 h-3 text-green-400" />
                    <span>{product.sustainability_score}%</span>
                  </div>
                )}
              </div>
            </div>
          </div>
          
          {/* Content Section - Clear hierarchy */}
          <div className="flex-1 p-5 flex flex-col">
            {/* Title with proper contrast */}
            <Link href={`/product/${product.id}`}>
              <h3 className="font-semibold text-xl text-gray-900 dark:text-white mb-2 line-clamp-2 group-hover:text-primary transition-colors cursor-pointer leading-tight">
                {product.name}
              </h3>
            </Link>
            
            {/* Optional Subheading */}
            {product.company_name && (
              <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                by {product.company_name}
              </p>
            )}
            
            {/* Description - bite-sized content */}
            {product.description && (
              <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-3 mb-4 leading-relaxed">
                {product.description}
              </p>
            )}
            
            {/* Meta Information - Scannable units */}
            <div className="flex-1">
              {plantPartName && (
                <div className="flex items-center gap-2 mb-3">
                  <Leaf className="w-4 h-4 text-green-600 dark:text-green-400" />
                  <span className="text-xs font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide">
                    {plantPartName}
                  </span>
                </div>
              )}
            </div>
            
            {/* Tags - Minimal approach */}
            {subIndustryName && (
              <div className="mb-4">
                <Badge variant="secondary" className="text-xs font-medium">
                  {subIndustryName}
                </Badge>
              </div>
            )}
            
            {/* Footer Actions - Clear CTA */}
            <div className="pt-4 mt-auto">
              <Link href={`/product/${product.id}`} className="block">
                <Button 
                  variant="outline" 
                  className="w-full group/btn hover:bg-primary hover:text-white hover:border-primary transition-all"
                >
                  <span className="font-medium">View Product Details</span>
                  <ArrowRight className="w-4 h-4 ml-2 transition-transform group-hover/btn:translate-x-1" />
                </Button>
              </Link>
              
              {/* Additional metadata */}
              <div className="flex items-center justify-between mt-3 pt-3 border-t border-gray-100 dark:border-gray-800">
                <div className="flex items-center gap-3 text-xs text-gray-500 dark:text-gray-400">
                  {product.rating && (
                    <div className="flex items-center gap-1">
                      <Star className="w-3.5 h-3.5 fill-amber-400 text-amber-400" />
                      <span className="font-medium">{product.rating.toFixed(1)}</span>
                    </div>
                  )}
                  {product.views && (
                    <span>{product.views.toLocaleString()} views</span>
                  )}
                </div>
                {product.commercialization_stage && (
                  <Badge variant="secondary" className="text-xs">
                    {product.commercialization_stage}
                  </Badge>
                )}
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </TooltipProvider>
  );
});

EnhancedProductCardV2.displayName = "EnhancedProductCardV2";

export default EnhancedProductCardV2;