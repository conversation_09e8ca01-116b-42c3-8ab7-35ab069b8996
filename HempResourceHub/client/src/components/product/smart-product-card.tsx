import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { 
  ChevronDown, 
  ChevronUp, 
  Building2, 
  Leaf, 
  Star, 
  Package,
  Sparkles,
  TrendingUp,
  Clock,
  Users
} from "lucide-react";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";

interface ProductVariant {
  id: number;
  name: string;
  size?: string;
  price?: string;
  inStock?: boolean;
}

interface SmartProductCardProps {
  product: {
    id: number;
    name: string;
    description: string;
    image_url?: string;
    ai_generated_image_url?: string;
    company?: { name: string; logo?: string };
    plant_part?: { name: string; icon?: string };
    industry?: { name: string; color?: string };
    commercialization_stage?: string;
    rating?: number;
    reviewCount?: number;
    isNew?: boolean;
    isTrending?: boolean;
    variants?: ProductVariant[];
    similarProducts?: number;
  };
  viewMode?: "grid" | "list";
  onProductClick?: (id: number) => void;
}

// Industry color mapping
const INDUSTRY_COLORS: Record<string, string> = {
  "Food": "bg-green-100 text-green-800 border-green-300",
  "Cosmetics": "bg-pink-100 text-pink-800 border-pink-300",
  "Construction": "bg-gray-100 text-gray-800 border-gray-300",
  "Medical": "bg-blue-100 text-blue-800 border-blue-300",
  "Textiles": "bg-purple-100 text-purple-800 border-purple-300",
  "Energy": "bg-yellow-100 text-yellow-800 border-yellow-300",
};

export function SmartProductCard({ product, viewMode = "grid", onProductClick }: SmartProductCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [selectedVariant, setSelectedVariant] = useState<number | null>(null);

  const imageUrl = product.ai_generated_image_url || product.image_url || "/images/placeholder-product.jpg";
  const hasVariants = product.variants && product.variants.length > 1;
  const industryColor = product.industry ? INDUSTRY_COLORS[product.industry.name] || "bg-gray-100" : "bg-gray-100";

  if (viewMode === "list") {
    return (
      <Card className="hover:shadow-lg transition-all duration-300 cursor-pointer">
        <div className="flex items-center p-4 gap-4" onClick={() => onProductClick?.(product.id)}>
          {/* Image */}
          <div className="relative w-24 h-24 flex-shrink-0">
            <img
              src={imageUrl}
              alt={product.name}
              className="w-full h-full object-cover rounded-lg"
            />
            {product.isNew && (
              <Badge className="absolute -top-2 -right-2 bg-green-500">New</Badge>
            )}
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between gap-2">
              <div className="flex-1">
                <h3 className="font-semibold text-lg truncate">{product.name}</h3>
                <p className="text-sm text-gray-600 line-clamp-2">{product.description}</p>
              </div>
              {hasVariants && (
                <Badge variant="secondary" className="flex-shrink-0">
                  {product.variants!.length} variants
                </Badge>
              )}
            </div>

            {/* Meta info */}
            <div className="flex items-center gap-4 mt-2 text-sm">
              {product.company && (
                <div className="flex items-center gap-1">
                  <Building2 className="w-4 h-4 text-gray-500" />
                  <span className="text-gray-600">{product.company.name}</span>
                </div>
              )}
              {product.plant_part && (
                <div className="flex items-center gap-1">
                  <Leaf className="w-4 h-4 text-green-500" />
                  <span className="text-gray-600">{product.plant_part.name}</span>
                </div>
              )}
              {product.rating && (
                <div className="flex items-center gap-1">
                  <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                  <span className="text-gray-600">
                    {product.rating} ({product.reviewCount || 0})
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Actions */}
          <div className="flex flex-col items-end gap-2">
            <Badge className={cn("border", industryColor)}>
              {product.industry?.name}
            </Badge>
            {product.isTrending && (
              <div className="flex items-center gap-1 text-orange-500">
                <TrendingUp className="w-4 h-4" />
                <span className="text-xs">Trending</span>
              </div>
            )}
          </div>
        </div>
      </Card>
    );
  }

  // Grid view (default)
  return (
    <motion.div
      layout
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
    >
      <Card className="hover:shadow-xl transition-all duration-300 cursor-pointer overflow-hidden">
        {/* Image Section */}
        <div className="relative aspect-square" onClick={() => onProductClick?.(product.id)}>
          <img
            src={imageUrl}
            alt={product.name}
            className="w-full h-full object-cover"
          />
          
          {/* Badges */}
          <div className="absolute top-2 left-2 flex flex-col gap-1">
            {product.isNew && (
              <Badge className="bg-green-500 text-white">
                <Sparkles className="w-3 h-3 mr-1" />
                New
              </Badge>
            )}
            {product.isTrending && (
              <Badge className="bg-orange-500 text-white">
                <TrendingUp className="w-3 h-3 mr-1" />
                Trending
              </Badge>
            )}
          </div>

          {/* Similar Products Indicator */}
          {product.similarProducts && product.similarProducts > 0 && (
            <div className="absolute bottom-2 right-2 bg-black/70 text-white px-2 py-1 rounded-md text-xs">
              +{product.similarProducts} similar
            </div>
          )}
        </div>

        <CardContent className="p-4">
          {/* Product Name and Rating */}
          <div className="flex items-start justify-between gap-2 mb-2">
            <h3 className="font-semibold text-lg line-clamp-1 flex-1">{product.name}</h3>
            {product.rating && (
              <div className="flex items-center gap-1 flex-shrink-0">
                <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                <span className="text-sm font-medium">{product.rating}</span>
              </div>
            )}
          </div>

          {/* Company and Plant Part */}
          <div className="flex items-center gap-3 text-sm text-gray-600 mb-3">
            {product.company && (
              <div className="flex items-center gap-1">
                <Building2 className="w-4 h-4" />
                <span className="truncate">{product.company.name}</span>
              </div>
            )}
            {product.plant_part && (
              <div className="flex items-center gap-1">
                <Leaf className="w-4 h-4 text-green-500" />
                <span>{product.plant_part.name}</span>
              </div>
            )}
          </div>

          {/* Industry Badge */}
          <div className="flex items-center justify-between mb-3">
            <Badge className={cn("border", industryColor)}>
              {product.industry?.name || "General"}
            </Badge>
            {product.commercialization_stage && (
              <Badge variant="outline" className="text-xs">
                <Clock className="w-3 h-3 mr-1" />
                {product.commercialization_stage}
              </Badge>
            )}
          </div>

          {/* Variants Section */}
          {hasVariants && (
            <>
              <Separator className="my-3" />
              <div className="space-y-2">
                <button
                  className="flex items-center justify-between w-full text-sm font-medium"
                  onClick={(e) => {
                    e.stopPropagation();
                    setIsExpanded(!isExpanded);
                  }}
                >
                  <span className="flex items-center gap-1">
                    <Package className="w-4 h-4" />
                    {product.variants!.length} Variants Available
                  </span>
                  {isExpanded ? (
                    <ChevronUp className="w-4 h-4" />
                  ) : (
                    <ChevronDown className="w-4 h-4" />
                  )}
                </button>

                <AnimatePresence>
                  {isExpanded && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: "auto", opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.2 }}
                      className="overflow-hidden"
                    >
                      <div className="pt-2 space-y-1">
                        {product.variants!.map((variant) => (
                          <div
                            key={variant.id}
                            className={cn(
                              "p-2 rounded-md border cursor-pointer transition-colors",
                              selectedVariant === variant.id
                                ? "border-blue-500 bg-blue-50"
                                : "border-gray-200 hover:bg-gray-50"
                            )}
                            onClick={(e) => {
                              e.stopPropagation();
                              setSelectedVariant(variant.id);
                            }}
                          >
                            <div className="flex items-center justify-between text-sm">
                              <span className="font-medium">{variant.name}</span>
                              {variant.size && (
                                <span className="text-gray-500">{variant.size}</span>
                              )}
                            </div>
                            {variant.price && (
                              <span className="text-xs text-gray-600">{variant.price}</span>
                            )}
                          </div>
                        ))}
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2 mt-4">
            <Button 
              className="flex-1" 
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                onProductClick?.(product.id);
              }}
            >
              View Details
            </Button>
            <Button variant="outline" size="sm" className="px-3">
              <Users className="w-4 h-4" />
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}