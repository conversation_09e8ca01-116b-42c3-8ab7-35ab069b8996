import { useState } from "react";
import { <PERSON> } from "wouter";
import { HempProduct } from "@shared/schema";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  ArrowRight, 
  Star, 
  Building2, 
  Leaf,
  Heart,
  Share2,
  Eye,
  TrendingUp,
  ChevronRight,
  Sparkles,
  Award
} from "lucide-react";
import { cn } from "@/lib/utils";
import SmartProductImage from "@/components/ui/smart-product-image";

interface ProductListItemProps {
  product: HempProduct & { 
    image_url?: string;
    ai_generated_image_url?: string;
    rating?: number;
    company_name?: string;
    views?: number;
    is_featured?: boolean;
    sustainability_score?: number;
    commercialization_stage?: string;
  };
  industryName?: string;
  subIndustryName?: string;
  plantPartName?: string;
  onFavorite?: (productId: number) => void;
  onShare?: (product: HempProduct) => void;
  className?: string;
}

export default function ProductListItem({
  product,
  industryName,
  subIndustryName,
  plantPartName,
  onFavorite,
  onShare,
  className
}: ProductListItemProps) {
  const [isFavorited, setIsFavorited] = useState(false);
  
  const isNew = product.created_at && new Date(product.created_at) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
  const hasHighRating = product.rating && product.rating >= 4.5;
  
  const handleFavorite = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsFavorited(!isFavorited);
    onFavorite?.(product.id);
  };

  const handleShare = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onShare?.(product);
  };

  return (
    <div className={cn(
      "group relative bg-white dark:bg-gray-800/50 backdrop-blur-sm rounded-xl",
      "border border-gray-200 dark:border-gray-700 transition-all duration-300",
      "hover:shadow-lg hover:border-primary/30 dark:hover:border-primary/40",
      "overflow-hidden",
      className
    )}>
      <div className="flex gap-6 p-6">
        {/* Image Section */}
        <div className="flex-shrink-0 w-40 h-40 rounded-lg overflow-hidden bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-900">
          <SmartProductImage
            product={{
              name: product.name,
              image_url: product.ai_generated_image_url || product.image_url,
              plantPartId: product.plantPartId
            }}
            plantPartName={plantPartName}
            className="w-full h-full object-cover"
            showAIBadge={true}
          />
        </div>
        
        {/* Content Section */}
        <div className="flex-1 min-w-0">
          {/* Header with badges */}
          <div className="flex items-start justify-between gap-4 mb-2">
            <div className="flex flex-wrap items-center gap-2">
              {industryName && (
                <Badge variant="secondary" className="text-xs">
                  {industryName}
                </Badge>
              )}
              {isNew && (
                <Badge className="bg-green-500 text-white border-0 text-xs">
                  <Sparkles className="w-3 h-3 mr-1" />
                  New
                </Badge>
              )}
              {product.is_featured && (
                <Badge className="bg-amber-500 text-white border-0 text-xs">
                  <Award className="w-3 h-3 mr-1" />
                  Featured
                </Badge>
              )}
              {hasHighRating && (
                <Badge variant="outline" className="text-xs border-amber-200 dark:border-amber-800">
                  <Star className="w-3 h-3 mr-1 fill-amber-400 text-amber-400" />
                  Top Rated
                </Badge>
              )}
            </div>
          </div>
          
          {/* Title */}
          <Link href={`/product/${product.id}`}>
            <h3 className="font-semibold text-lg text-gray-900 dark:text-white mb-2 group-hover:text-primary transition-colors cursor-pointer">
              {product.name}
            </h3>
          </Link>
          
          {/* Description */}
          {product.description && (
            <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 mb-3">
              {product.description}
            </p>
          )}
          
          {/* Meta Information */}
          <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500 dark:text-gray-400 mb-3">
            {product.company_name && (
              <div className="flex items-center gap-1.5">
                <Building2 className="w-3.5 h-3.5" />
                <span>{product.company_name}</span>
              </div>
            )}
            {plantPartName && (
              <div className="flex items-center gap-1.5">
                <Leaf className="w-3.5 h-3.5 text-green-600 dark:text-green-400" />
                <span>{plantPartName}</span>
              </div>
            )}
            {product.commercialization_stage && (
              <div className="flex items-center gap-1.5">
                <TrendingUp className="w-3.5 h-3.5 text-blue-600 dark:text-blue-400" />
                <span>{product.commercialization_stage}</span>
              </div>
            )}
            {subIndustryName && (
              <Badge variant="outline" className="text-xs">
                {subIndustryName}
              </Badge>
            )}
          </div>
          
          {/* Stats Row */}
          <div className="flex items-center gap-4 text-sm">
            {product.rating && (
              <div className="flex items-center gap-1">
                <Star className="w-3.5 h-3.5 fill-amber-400 text-amber-400" />
                <span className="font-medium">{product.rating.toFixed(1)}</span>
              </div>
            )}
            {product.views && (
              <div className="flex items-center gap-1 text-gray-500 dark:text-gray-400">
                <Eye className="w-3.5 h-3.5" />
                <span>{product.views.toLocaleString()} views</span>
              </div>
            )}
            {product.sustainability_score && (
              <div className="flex items-center gap-1 text-gray-500 dark:text-gray-400">
                <Leaf className="w-3.5 h-3.5 text-green-600 dark:text-green-400" />
                <span>{product.sustainability_score}% sustainable</span>
              </div>
            )}
          </div>
        </div>
        
        {/* Actions Section */}
        <div className="flex flex-col items-end justify-between gap-3">
          <div className="flex gap-2">
            <Button
              size="icon"
              variant="ghost"
              className="h-8 w-8"
              onClick={handleFavorite}
            >
              <Heart className={cn("h-4 w-4", isFavorited && "fill-red-500 text-red-500")} />
            </Button>
            <Button
              size="icon"
              variant="ghost"
              className="h-8 w-8"
              onClick={handleShare}
            >
              <Share2 className="h-4 w-4" />
            </Button>
          </div>
          
          <Link href={`/product/${product.id}`}>
            <Button variant="default" size="sm" className="group/btn">
              View Details
              <ChevronRight className="w-4 h-4 ml-1 transition-transform group-hover/btn:translate-x-1" />
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
}