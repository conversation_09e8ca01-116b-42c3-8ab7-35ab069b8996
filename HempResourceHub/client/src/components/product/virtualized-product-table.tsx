import React, { useRef, useState, useMemo } from 'react';
import { useVirtualizer } from '@tanstack/react-virtual';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  ChevronDown,
  ChevronUp,
  ChevronsUpDown,
  Eye,
  EyeOff,
  GripVertical,
  Settings2,
  Download,
  ArrowLeftRight,
  Star,
  Bookmark,
  Share2,
  ExternalLink
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
} from '@/components/ui/dropdown-menu';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { cn } from '@/lib/utils';

export interface TableColumn {
  id: string;
  label: string;
  accessor: string | ((item: any) => any);
  width?: number;
  minWidth?: number;
  sortable?: boolean;
  visible?: boolean;
  sticky?: boolean;
  render?: (value: any, item: any) => React.ReactNode;
}

interface VirtualizedProductTableProps {
  products: any[];
  columns: TableColumn[];
  isLoading?: boolean;
  onSort?: (column: string, direction: 'asc' | 'desc') => void;
  sortColumn?: string;
  sortDirection?: 'asc' | 'desc';
  onRowClick?: (product: any) => void;
  selectedRows?: number[];
  onSelectionChange?: (selectedIds: number[]) => void;
  enableSelection?: boolean;
  enableExpansion?: boolean;
  onFavorite?: (productId: number) => void;
  onBookmark?: (productId: number) => void;
  onShare?: (product: any) => void;
  favorites?: number[];
  bookmarks?: number[];
}

export function VirtualizedProductTable({
  products,
  columns: initialColumns,
  isLoading = false,
  onSort,
  sortColumn,
  sortDirection,
  onRowClick,
  selectedRows = [],
  onSelectionChange,
  enableSelection = true,
  enableExpansion = true,
  onFavorite,
  onBookmark,
  onShare,
  favorites = [],
  bookmarks = []
}: VirtualizedProductTableProps) {
  const parentRef = useRef<HTMLDivElement>(null);
  const [columns, setColumns] = useState(initialColumns);
  const [expandedRows, setExpandedRows] = useState<Set<number>>(new Set());
  const [columnWidths, setColumnWidths] = useState<Record<string, number>>({});

  // Column visibility management
  const visibleColumns = useMemo(
    () => columns.filter(col => col.visible !== false),
    [columns]
  );

  // Row virtualizer
  const rowVirtualizer = useVirtualizer({
    count: products.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 60, // Base row height
    overscan: 10,
  });

  // Handle column visibility toggle
  const toggleColumnVisibility = (columnId: string) => {
    setColumns(prev => prev.map(col => 
      col.id === columnId ? { ...col, visible: !col.visible } : col
    ));
  };

  // Handle row selection
  const handleSelectAll = () => {
    if (selectedRows.length === products.length) {
      onSelectionChange?.([]);
    } else {
      onSelectionChange?.(products.map(p => p.id));
    }
  };

  const handleSelectRow = (productId: number) => {
    if (selectedRows.includes(productId)) {
      onSelectionChange?.(selectedRows.filter(id => id !== productId));
    } else {
      onSelectionChange?.([...selectedRows, productId]);
    }
  };

  // Handle row expansion
  const toggleRowExpansion = (productId: number) => {
    setExpandedRows(prev => {
      const next = new Set(prev);
      if (next.has(productId)) {
        next.delete(productId);
      } else {
        next.add(productId);
      }
      return next;
    });
  };

  // Export selected rows
  const exportSelected = () => {
    const selected = products.filter(p => selectedRows.includes(p.id));
    const csv = [
      // Headers
      visibleColumns.map(col => col.label).join(','),
      // Data
      ...selected.map(product => 
        visibleColumns.map(col => {
          const value = typeof col.accessor === 'function' 
            ? col.accessor(product) 
            : product[col.accessor];
          return JSON.stringify(value || '');
        }).join(',')
      )
    ].join('\n');

    const blob = new Blob([csv], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `hemp-products-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
  };

  // Render column value
  const renderCellValue = (column: TableColumn, product: any) => {
    const value = typeof column.accessor === 'function' 
      ? column.accessor(product) 
      : product[column.accessor];

    if (column.render) {
      return column.render(value, product);
    }

    // Default renders for common fields
    switch (column.id) {
      case 'name':
        return (
          <div className="font-medium truncate max-w-[300px]" title={value}>
            {value}
          </div>
        );
      case 'plantPart':
        return <Badge variant="secondary">{value || 'Unknown'}</Badge>;
      case 'industry':
        return <Badge variant="outline">{value || 'Unknown'}</Badge>;
      case 'stage':
        return (
          <Badge 
            variant={value === 'Commercial' ? 'default' : 'secondary'}
            className={cn(
              value === 'Research' && 'bg-blue-500/20 text-blue-300',
              value === 'Development' && 'bg-yellow-500/20 text-yellow-300',
              value === 'Pilot' && 'bg-purple-500/20 text-purple-300'
            )}
          >
            {value || 'Unknown'}
          </Badge>
        );
      case 'sustainability':
        return value ? (
          <div className="flex items-center gap-1">
            <div className="w-20 h-2 bg-gray-700 rounded-full overflow-hidden">
              <div 
                className="h-full bg-gradient-to-r from-green-500 to-green-400"
                style={{ width: `${value * 10}%` }}
              />
            </div>
            <span className="text-xs text-muted-foreground">{value}/10</span>
          </div>
        ) : '-';
      case 'benefits':
        return Array.isArray(value) ? (
          <div className="flex flex-wrap gap-1">
            {value.slice(0, 2).map((benefit, i) => (
              <Badge key={i} variant="secondary" className="text-xs">
                {benefit}
              </Badge>
            ))}
            {value.length > 2 && (
              <Badge variant="secondary" className="text-xs">
                +{value.length - 2}
              </Badge>
            )}
          </div>
        ) : '-';
      default:
        return value || '-';
    }
  };

  if (isLoading) {
    return <div className="text-center py-8">Loading products...</div>;
  }

  return (
    <div className="space-y-4">
      {/* Toolbar */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {enableSelection && selectedRows.length > 0 && (
            <>
              <span className="text-sm text-muted-foreground">
                {selectedRows.length} selected
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={exportSelected}
                className="gap-2"
              >
                <Download className="h-4 w-4" />
                Export
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {/* Implement comparison */}}
                className="gap-2"
                disabled={selectedRows.length < 2 || selectedRows.length > 5}
              >
                <ArrowLeftRight className="h-4 w-4" />
                Compare
              </Button>
            </>
          )}
        </div>
        
        {/* Column settings */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="gap-2">
              <Settings2 className="h-4 w-4" />
              Columns
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-[200px]">
            <DropdownMenuLabel>Toggle Columns</DropdownMenuLabel>
            <DropdownMenuSeparator />
            {columns.map(column => (
              <DropdownMenuCheckboxItem
                key={column.id}
                checked={column.visible !== false}
                onCheckedChange={() => toggleColumnVisibility(column.id)}
                disabled={column.sticky}
              >
                {column.label}
              </DropdownMenuCheckboxItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Virtual table */}
      <div
        ref={parentRef}
        className="relative h-[calc(100vh-300px)] overflow-auto border rounded-lg"
      >
        <Table>
          <TableHeader className="sticky top-0 z-10 bg-background border-b">
            <TableRow>
              {enableSelection && (
                <TableHead className="w-[50px]">
                  <Checkbox
                    checked={selectedRows.length === products.length}
                    onCheckedChange={handleSelectAll}
                  />
                </TableHead>
              )}
              {enableExpansion && (
                <TableHead className="w-[50px]" />
              )}
              {visibleColumns.map(column => (
                <TableHead
                  key={column.id}
                  className={cn(
                    "relative",
                    column.sticky && "sticky left-0 z-20 bg-background",
                    column.sortable && "cursor-pointer hover:bg-muted/50"
                  )}
                  style={{ 
                    minWidth: column.minWidth || 100,
                    width: columnWidths[column.id] || column.width || 'auto'
                  }}
                  onClick={() => column.sortable && onSort?.(
                    column.id,
                    sortColumn === column.id && sortDirection === 'asc' ? 'desc' : 'asc'
                  )}
                >
                  <div className="flex items-center justify-between">
                    {column.label}
                    {column.sortable && (
                      <div className="ml-2">
                        {sortColumn === column.id ? (
                          sortDirection === 'asc' ? (
                            <ChevronUp className="h-4 w-4" />
                          ) : (
                            <ChevronDown className="h-4 w-4" />
                          )
                        ) : (
                          <ChevronsUpDown className="h-4 w-4 opacity-50" />
                        )}
                      </div>
                    )}
                  </div>
                </TableHead>
              ))}
              <TableHead className="w-[100px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          
          <TableBody>
            <div
              style={{
                height: `${rowVirtualizer.getTotalSize()}px`,
                width: '100%',
                position: 'relative',
              }}
            >
              {rowVirtualizer.getVirtualItems().map((virtualRow) => {
                const product = products[virtualRow.index];
                const isExpanded = expandedRows.has(product.id);
                const isSelected = selectedRows.includes(product.id);

                return (
                  <React.Fragment key={virtualRow.key}>
                    <TableRow
                      data-index={virtualRow.index}
                      ref={rowVirtualizer.measureElement}
                      style={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        width: '100%',
                        transform: `translateY(${virtualRow.start}px)`,
                      }}
                      className={cn(
                        "hover:bg-muted/50 cursor-pointer",
                        isSelected && "bg-muted/30"
                      )}
                      onClick={() => onRowClick?.(product)}
                    >
                      {enableSelection && (
                        <TableCell 
                          className="w-[50px]"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <Checkbox
                            checked={isSelected}
                            onCheckedChange={() => handleSelectRow(product.id)}
                          />
                        </TableCell>
                      )}
                      {enableExpansion && (
                        <TableCell 
                          className="w-[50px]"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => toggleRowExpansion(product.id)}
                          >
                            {isExpanded ? (
                              <ChevronUp className="h-4 w-4" />
                            ) : (
                              <ChevronDown className="h-4 w-4" />
                            )}
                          </Button>
                        </TableCell>
                      )}
                      {visibleColumns.map(column => (
                        <TableCell
                          key={column.id}
                          className={cn(
                            column.sticky && "sticky left-0 z-10 bg-background"
                          )}
                        >
                          {renderCellValue(column, product)}
                        </TableCell>
                      ))}
                      <TableCell className="w-[100px]">
                        <div className="flex items-center gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              onFavorite?.(product.id);
                            }}
                            className={cn(
                              favorites.includes(product.id) && "text-yellow-500"
                            )}
                          >
                            <Star className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              onBookmark?.(product.id);
                            }}
                            className={cn(
                              bookmarks.includes(product.id) && "text-blue-500"
                            )}
                          >
                            <Bookmark className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              onShare?.(product);
                            }}
                          >
                            <Share2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                    
                    {/* Expanded content */}
                    {isExpanded && (
                      <TableRow
                        style={{
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          width: '100%',
                          transform: `translateY(${virtualRow.start + 60}px)`,
                        }}
                      >
                        <TableCell 
                          colSpan={visibleColumns.length + (enableSelection ? 1 : 0) + (enableExpansion ? 1 : 0) + 1}
                          className="bg-muted/20 p-4"
                        >
                          <div className="space-y-4">
                            <div>
                              <h4 className="font-semibold mb-2">Description</h4>
                              <p className="text-sm text-muted-foreground">
                                {product.description || 'No description available'}
                              </p>
                            </div>
                            
                            {product.benefits_advantages?.length > 0 && (
                              <div>
                                <h4 className="font-semibold mb-2">Benefits & Advantages</h4>
                                <div className="flex flex-wrap gap-2">
                                  {product.benefits_advantages.map((benefit: string, i: number) => (
                                    <Badge key={i} variant="secondary">
                                      {benefit}
                                    </Badge>
                                  ))}
                                </div>
                              </div>
                            )}
                            
                            {product.technical_specifications && (
                              <div>
                                <h4 className="font-semibold mb-2">Technical Specifications</h4>
                                <pre className="text-xs bg-black/50 p-2 rounded">
                                  {JSON.stringify(product.technical_specifications, null, 2)}
                                </pre>
                              </div>
                            )}
                            
                            <div className="flex gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => window.open(`/product/${product.id}`, '_blank')}
                                className="gap-2"
                              >
                                <ExternalLink className="h-4 w-4" />
                                View Details
                              </Button>
                            </div>
                          </div>
                        </TableCell>
                      </TableRow>
                    )}
                  </React.Fragment>
                );
              })}
            </div>
          </TableBody>
        </Table>
      </div>
    </div>
  );
}

// Default columns configuration
export const defaultProductColumns: TableColumn[] = [
  { 
    id: 'name', 
    label: 'Product Name', 
    accessor: 'name', 
    sticky: true, 
    sortable: true,
    minWidth: 250
  },
  { 
    id: 'plantPart', 
    label: 'Plant Part', 
    accessor: (product: any) => product.plantPart?.name,
    sortable: true,
    minWidth: 120
  },
  { 
    id: 'industry', 
    label: 'Industry', 
    accessor: (product: any) => product.industrySubCategory?.name,
    sortable: true,
    minWidth: 150
  },
  { 
    id: 'stage', 
    label: 'Stage', 
    accessor: 'commercialization_stage',
    sortable: true,
    minWidth: 120
  },
  { 
    id: 'sustainability', 
    label: 'Sustainability', 
    accessor: 'sustainability_score',
    sortable: true,
    minWidth: 140
  },
  { 
    id: 'company', 
    label: 'Company', 
    accessor: (product: any) => product.primaryCompany?.name,
    minWidth: 150
  },
  { 
    id: 'benefits', 
    label: 'Benefits', 
    accessor: 'benefits_advantages',
    minWidth: 200
  }
];

export default React.memo(VirtualizedProductTable);