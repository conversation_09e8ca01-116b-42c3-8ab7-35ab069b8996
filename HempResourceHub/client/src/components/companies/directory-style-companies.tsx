import React, { useState, useMemo } from "react";
import { <PERSON> } from "wouter";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  Search,
  Building2,
  MapPin,
  Globe,
  Users,
  Star,
  ExternalLink,
  Phone,
  Mail,
  Filter,
  SortAsc,
  SortDesc,
  Verified,
  TrendingUp,
  Award,
  Leaf,
  Factory,
  Package
} from "lucide-react";
import { cn } from "@/lib/utils";
import { componentStyles } from "@/lib/design-system";

interface Company {
  id: string;
  name: string;
  description: string;
  logo?: string;
  website?: string;
  location: string;
  country: string;
  industry: string;
  specialization: string[];
  employeeCount: string;
  foundedYear: number;
  rating: number;
  reviewCount: number;
  isVerified: boolean;
  isPremium: boolean;
  contactEmail?: string;
  contactPhone?: string;
  socialMedia?: {
    linkedin?: string;
    twitter?: string;
  };
  certifications: string[];
  recentNews?: string;
}

const sampleCompanies: Company[] = [
  {
    id: '1',
    name: 'HempTech Industries',
    description: 'Leading manufacturer of hemp-based building materials and sustainable construction solutions.',
    logo: '/images/company-logos/hemptech.png',
    website: 'https://hemptech.com',
    location: 'Denver, CO',
    country: 'United States',
    industry: 'Construction Materials',
    specialization: ['Hemp Concrete', 'Insulation', 'Building Blocks'],
    employeeCount: '50-200',
    foundedYear: 2018,
    rating: 4.8,
    reviewCount: 127,
    isVerified: true,
    isPremium: true,
    contactEmail: '<EMAIL>',
    contactPhone: '+****************',
    certifications: ['ISO 9001', 'LEED Certified', 'Organic Certified'],
    recentNews: 'Secured $5M Series A funding for expansion'
  },
  {
    id: '2',
    name: 'Green Fiber Solutions',
    description: 'Sustainable textile manufacturer specializing in hemp fiber processing and eco-friendly fabrics.',
    logo: '/images/company-logos/greenfiber.png',
    website: 'https://greenfiber.com',
    location: 'Portland, OR',
    country: 'United States',
    industry: 'Textiles',
    specialization: ['Hemp Fiber', 'Sustainable Fabrics', 'Yarn Production'],
    employeeCount: '20-50',
    foundedYear: 2020,
    rating: 4.6,
    reviewCount: 89,
    isVerified: true,
    isPremium: false,
    contactEmail: '<EMAIL>',
    certifications: ['GOTS Certified', 'Fair Trade'],
    recentNews: 'Launched new hemp-cotton blend fabric line'
  },
  {
    id: '3',
    name: 'Hemp Wellness Co.',
    description: 'Premium CBD and hemp wellness products with focus on quality and transparency.',
    logo: '/images/company-logos/hempwellness.png',
    website: 'https://hempwellness.com',
    location: 'Austin, TX',
    country: 'United States',
    industry: 'Health & Wellness',
    specialization: ['CBD Products', 'Hemp Oil', 'Wellness Supplements'],
    employeeCount: '10-20',
    foundedYear: 2019,
    rating: 4.9,
    reviewCount: 234,
    isVerified: true,
    isPremium: true,
    contactEmail: '<EMAIL>',
    certifications: ['FDA Registered', 'Third-Party Tested', 'Organic Certified'],
    recentNews: 'Expanded to 500+ retail locations nationwide'
  }
];

const industries = [
  'All Industries',
  'Construction Materials',
  'Textiles',
  'Health & Wellness',
  'Food & Beverage',
  'Automotive',
  'Paper & Packaging',
  'Bioplastics',
  'Energy'
];

const locations = [
  'All Locations',
  'United States',
  'Canada',
  'Europe',
  'Asia-Pacific',
  'South America'
];

interface DirectoryStyleCompaniesProps {
  className?: string;
}

export function DirectoryStyleCompanies({ className }: DirectoryStyleCompaniesProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedIndustry, setSelectedIndustry] = useState('All Industries');
  const [selectedLocation, setSelectedLocation] = useState('All Locations');
  const [sortBy, setSortBy] = useState<'name' | 'rating' | 'founded' | 'employees'>('rating');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [showVerifiedOnly, setShowVerifiedOnly] = useState(false);

  // Filter and sort companies
  const filteredCompanies = useMemo(() => {
    let companies = sampleCompanies;

    // Filter by search query
    if (searchQuery) {
      companies = companies.filter(company =>
        company.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        company.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        company.specialization.some(spec => 
          spec.toLowerCase().includes(searchQuery.toLowerCase())
        )
      );
    }

    // Filter by industry
    if (selectedIndustry !== 'All Industries') {
      companies = companies.filter(company => company.industry === selectedIndustry);
    }

    // Filter by location
    if (selectedLocation !== 'All Locations') {
      companies = companies.filter(company => company.country === selectedLocation);
    }

    // Filter verified only
    if (showVerifiedOnly) {
      companies = companies.filter(company => company.isVerified);
    }

    // Sort companies
    companies.sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortBy) {
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'rating':
          aValue = a.rating;
          bValue = b.rating;
          break;
        case 'founded':
          aValue = a.foundedYear;
          bValue = b.foundedYear;
          break;
        case 'employees':
          // Convert employee count to number for sorting
          const getEmployeeNumber = (count: string) => {
            if (count.includes('10-20')) return 15;
            if (count.includes('20-50')) return 35;
            if (count.includes('50-200')) return 125;
            return 0;
          };
          aValue = getEmployeeNumber(a.employeeCount);
          bValue = getEmployeeNumber(b.employeeCount);
          break;
        default:
          aValue = a.rating;
          bValue = b.rating;
      }

      if (sortOrder === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    return companies;
  }, [searchQuery, selectedIndustry, selectedLocation, sortBy, sortOrder, showVerifiedOnly]);

  const getIndustryIcon = (industry: string) => {
    switch (industry) {
      case 'Construction Materials': return <Building2 className="h-5 w-5" />;
      case 'Textiles': return <Package className="h-5 w-5" />;
      case 'Health & Wellness': return <Leaf className="h-5 w-5" />;
      default: return <Factory className="h-5 w-5" />;
    }
  };

  return (
    <div className={cn("w-full space-y-8", className)}>
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold text-white">
          <span className="bg-gradient-to-r from-green-400 to-purple-400 bg-clip-text text-transparent">
            Hemp Industry Directory
          </span>
        </h1>
        <p className="text-xl text-gray-300 max-w-3xl mx-auto">
          Discover leading companies in the hemp industry. Connect with manufacturers, 
          suppliers, and innovators driving the hemp economy forward.
        </p>
      </div>

      {/* Search and Filters */}
      <Card className={cn(componentStyles.interface.card, "p-6")}>
        <div className="space-y-4">
          {/* Search Bar */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
            <Input
              placeholder="Search companies, specializations, or locations..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 bg-black/50 border-gray-700 text-white placeholder:text-gray-400"
            />
          </div>

          {/* Filters Row */}
          <div className="flex flex-col lg:flex-row gap-4 items-center">
            <div className="flex flex-wrap gap-4 flex-1">
              <Select value={selectedIndustry} onValueChange={setSelectedIndustry}>
                <SelectTrigger className="w-48 bg-black/50 border-gray-700 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-black border-gray-700">
                  {industries.map(industry => (
                    <SelectItem key={industry} value={industry}>
                      {industry}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={selectedLocation} onValueChange={setSelectedLocation}>
                <SelectTrigger className="w-48 bg-black/50 border-gray-700 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-black border-gray-700">
                  {locations.map(location => (
                    <SelectItem key={location} value={location}>
                      {location}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Button
                variant={showVerifiedOnly ? "default" : "outline"}
                onClick={() => setShowVerifiedOnly(!showVerifiedOnly)}
                className="border-gray-700"
              >
                <Verified className="h-4 w-4 mr-2" />
                Verified Only
              </Button>
            </div>

            {/* Sort Controls */}
            <div className="flex items-center gap-2">
              <Select value={sortBy} onValueChange={(value) => setSortBy(value as any)}>
                <SelectTrigger className="w-40 bg-black/50 border-gray-700 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-black border-gray-700">
                  <SelectItem value="rating">Rating</SelectItem>
                  <SelectItem value="name">Name</SelectItem>
                  <SelectItem value="founded">Founded</SelectItem>
                  <SelectItem value="employees">Size</SelectItem>
                </SelectContent>
              </Select>

              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                className="border border-gray-700"
              >
                {sortOrder === 'asc' ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />}
              </Button>
            </div>
          </div>
        </div>
      </Card>

      {/* Results Summary */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-white">
          Companies Directory
        </h2>
        <span className="text-gray-400">
          {filteredCompanies.length} companies found
        </span>
      </div>

      {/* Companies Grid */}
      <div className="grid gap-6">
        {filteredCompanies.map((company) => (
          <Card key={company.id} className={cn(
            componentStyles.interface.card,
            "p-6 hover:border-gray-600 transition-all duration-200 group",
            company.isPremium && "ring-2 ring-purple-500/20"
          )}>
            <div className="flex flex-col lg:flex-row gap-6">
              {/* Company Logo */}
              <div className="flex-shrink-0">
                <div className="w-20 h-20 bg-gray-800 rounded-xl flex items-center justify-center">
                  {company.logo ? (
                    <img 
                      src={company.logo} 
                      alt={company.name}
                      className="w-16 h-16 object-contain rounded-lg"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                      }}
                    />
                  ) : (
                    <Building2 className="h-10 w-10 text-gray-400" />
                  )}
                </div>
              </div>

              {/* Company Info */}
              <div className="flex-1 space-y-4">
                <div className="flex items-start justify-between gap-4">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <h3 className="text-xl font-semibold text-white group-hover:text-purple-300 transition-colors">
                        {company.name}
                      </h3>
                      {company.isVerified && (
                        <Verified className="h-5 w-5 text-blue-400" />
                      )}
                      {company.isPremium && (
                        <Badge className="bg-purple-500/20 text-purple-400 border-purple-500/30">
                          Premium
                        </Badge>
                      )}
                    </div>
                    
                    <div className="flex items-center gap-4 text-sm text-gray-400">
                      <div className="flex items-center gap-1">
                        {getIndustryIcon(company.industry)}
                        {company.industry}
                      </div>
                      <div className="flex items-center gap-1">
                        <MapPin className="h-4 w-4" />
                        {company.location}
                      </div>
                      <div className="flex items-center gap-1">
                        <Users className="h-4 w-4" />
                        {company.employeeCount} employees
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-1 text-yellow-400">
                    <Star className="h-4 w-4 fill-current" />
                    <span className="font-medium">{company.rating}</span>
                    <span className="text-gray-400 text-sm">({company.reviewCount})</span>
                  </div>
                </div>

                <p className="text-gray-300 leading-relaxed">
                  {company.description}
                </p>

                <div className="flex flex-wrap gap-2">
                  {company.specialization.map((spec) => (
                    <Badge key={spec} variant="secondary" className="bg-gray-800 text-gray-300">
                      {spec}
                    </Badge>
                  ))}
                </div>

                {company.certifications.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {company.certifications.map((cert) => (
                      <Badge key={cert} className="bg-green-500/20 text-green-400 border-green-500/30">
                        <Award className="h-3 w-3 mr-1" />
                        {cert}
                      </Badge>
                    ))}
                  </div>
                )}

                {company.recentNews && (
                  <div className="flex items-center gap-2 text-sm">
                    <TrendingUp className="h-4 w-4 text-blue-400" />
                    <span className="text-blue-400 font-medium">Latest:</span>
                    <span className="text-gray-300">{company.recentNews}</span>
                  </div>
                )}
              </div>

              {/* Actions */}
              <div className="flex lg:flex-col items-center lg:items-end gap-3">
                <Link href={`/companies/${company.id}`}>
                  <Button className="bg-purple-500 hover:bg-purple-600">
                    View Profile
                  </Button>
                </Link>
                
                <div className="flex gap-2">
                  {company.website && (
                    <Button variant="ghost" size="sm" className="text-gray-400 hover:text-white">
                      <Globe className="h-4 w-4" />
                    </Button>
                  )}
                  
                  {company.contactEmail && (
                    <Button variant="ghost" size="sm" className="text-gray-400 hover:text-white">
                      <Mail className="h-4 w-4" />
                    </Button>
                  )}
                  
                  {company.contactPhone && (
                    <Button variant="ghost" size="sm" className="text-gray-400 hover:text-white">
                      <Phone className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {filteredCompanies.length === 0 && (
        <div className="text-center py-12">
          <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-white mb-2">No companies found</h3>
          <p className="text-gray-400">Try adjusting your search or filters</p>
        </div>
      )}
    </div>
  );
}
