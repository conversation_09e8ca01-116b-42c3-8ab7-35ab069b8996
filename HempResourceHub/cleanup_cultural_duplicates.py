#!/usr/bin/env python3
"""
Clean up massive duplication in cultural heritage products
"""

import os
from dotenv import load_dotenv
from supabase import create_client, Client

load_dotenv()

# Initialize Supabase client
url = os.getenv("VITE_SUPABASE_URL")
key = os.getenv("SUPABASE_SERVICE_ROLE_KEY") or os.getenv("VITE_SUPABASE_ANON_KEY")
supabase: Client = create_client(url, key)

def cleanup_cultural_duplicates():
    """Remove duplicate cultural products, keeping only one variant per type"""
    
    cultures = ['Cherokee', 'Slavic', 'Andean', 'Zulu', 'Persian', 'Polynesian', 'Chinese', 
                'Italian', 'Lithuanian', 'Celtic', 'Nordic', 'Mayan', 'Greek', 'Japanese',
                'Indian', 'Turkish', 'Egyptian', 'Roman', 'Viking', 'Aztec']
    
    product_types = ['Artwork', 'Craft', 'Creation', 'Equipment', 'Tool', 'Fabric', 
                     'Component', 'Delicacy', 'Artifact', 'Material', 'Product', 
                     'Remedy', 'Item', 'Cloth', 'Medicine', 'Rope']
    
    total_deleted = 0
    
    for culture in cultures:
        print(f"\nProcessing {culture} products...")
        
        # Get all products for this culture
        response = supabase.table('uses_products').select('id, name').like('name', f'%{culture}%').execute()
        products = response.data
        
        if not products:
            continue
            
        print(f"Found {len(products)} {culture} products")
        
        # Group by product type
        products_by_type = {}
        for product in products:
            product_type = None
            for ptype in product_types:
                if ptype.lower() in product['name'].lower():
                    product_type = ptype
                    break
            
            if product_type:
                if product_type not in products_by_type:
                    products_by_type[product_type] = []
                products_by_type[product_type].append(product)
        
        # Keep only one product per type (prefer "Heritage" variants)
        ids_to_keep = []
        ids_to_delete = []
        
        for ptype, products_list in products_by_type.items():
            if len(products_list) > 1:
                # Sort to prefer Heritage, then Cultural, then Traditional
                products_list.sort(key=lambda x: (
                    0 if 'Heritage' in x['name'] else
                    1 if 'Cultural' in x['name'] else
                    2 if 'Traditional' in x['name'] else 3,
                    x['id']
                ))
                
                # Keep the first one
                ids_to_keep.append(products_list[0]['id'])
                
                # Delete the rest
                for product in products_list[1:]:
                    ids_to_delete.append(product['id'])
                    print(f"  Will delete duplicate: {product['name']}")
            else:
                ids_to_keep.append(products_list[0]['id'])
        
        if ids_to_delete:
            # Delete dependencies first
            supabase.table('image_generation_history').delete().in_('queue_id', 
                supabase.table('image_generation_queue').select('id').in_('product_id', ids_to_delete).execute().data
            ).execute()
            
            supabase.table('image_generation_queue').delete().in_('product_id', ids_to_delete).execute()
            supabase.table('content_queue').delete().in_('product_id', ids_to_delete).execute()
            supabase.table('hemp_company_products').delete().in_('product_id', ids_to_delete).execute()
            
            # Delete the products
            supabase.table('uses_products').delete().in_('id', ids_to_delete).execute()
            
            print(f"✅ Deleted {len(ids_to_delete)} duplicate {culture} products")
            total_deleted += len(ids_to_delete)
    
    print(f"\n✅ Total products deleted: {total_deleted}")
    
    # Get final count
    response = supabase.table('uses_products').select('id', count='exact').execute()
    print(f"Final product count: {response.count}")

if __name__ == "__main__":
    cleanup_cultural_duplicates()