-- Add quality_score to hemp_companies table
ALTER TABLE hemp_companies 
ADD COLUMN IF NOT EXISTS quality_score DECIMAL(3,2) DEFAULT 0.50 CHECK (quality_score >= 0 AND quality_score <= 1);

-- Add index for faster sorting
CREATE INDEX IF NOT EXISTS idx_companies_quality_score ON hemp_companies(quality_score DESC);

-- Update quality scores based on existing data
UPDATE hemp_companies 
SET quality_score = (
  CASE WHEN verified THEN 0.3 ELSE 0 END +
  CASE WHEN website IS NOT NULL THEN 0.2 ELSE 0 END +
  CASE WHEN description IS NOT NULL AND description NOT LIKE '%is a hemp product brand%' THEN 0.2 ELSE 0 END +
  CASE WHEN company_type IS NOT NULL THEN 0.1 ELSE 0 END +
  CASE WHEN founded_year IS NOT NULL THEN 0.1 ELSE 0 END +
  CASE WHEN country IS NOT NULL THEN 0.1 ELSE 0 END
)
WHERE quality_score = 0.50;