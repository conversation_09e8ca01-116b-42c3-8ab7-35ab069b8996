#!/usr/bin/env python3
"""
Batch AI Image Generation Script
Generates images for products missing them using DALL-E or other AI services
"""

import os
import sys
import time
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional
import psycopg2
from psycopg2.extras import RealDictCursor
from dotenv import load_dotenv
import openai
import requests
from concurrent.futures import ThreadPoolExecutor, as_completed

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'logs/image_generation_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)

class ImageGenerator:
    def __init__(self):
        self.conn = self._get_connection()
        self.openai_key = os.getenv('OPENAI_API_KEY')
        self.use_placeholder = not bool(self.openai_key)
        
        if self.openai_key:
            openai.api_key = self.openai_key
            logging.info("Using OpenAI DALL-E for image generation")
        else:
            logging.warning("No OpenAI key found, will generate placeholder URLs")
        
        # Track rate limits
        self.images_generated = 0
        self.max_images_per_hour = 50  # DALL-E rate limit
        self.start_time = time.time()
        
    def _get_connection(self):
        """Get database connection"""
        db_url = os.getenv('DATABASE_URL')
        if not db_url:
            raise ValueError("DATABASE_URL not found in environment variables")
        return psycopg2.connect(db_url, cursor_factory=RealDictCursor)
    
    def get_products_without_images(self, limit: int = 100) -> List[Dict]:
        """Get products that need images"""
        with self.conn.cursor() as cur:
            cur.execute("""
                SELECT 
                    p.id,
                    p.name,
                    p.description,
                    pp.name as plant_part,
                    isc.name as industry,
                    c.name as company_name
                FROM uses_products p
                LEFT JOIN plant_parts pp ON p.plant_part_id = pp.id
                LEFT JOIN industry_sub_categories isc ON p.industry_sub_category_id = isc.id
                LEFT JOIN hemp_companies c ON p.primary_company_id = c.id
                WHERE (p.image_url IS NULL OR p.image_url = '') 
                  AND (p.ai_generated_image_url IS NULL OR p.ai_generated_image_url = '')
                ORDER BY p.created_at DESC
                LIMIT %s
            """, (limit,))
            return cur.fetchall()
    
    def generate_image_prompt(self, product: Dict) -> str:
        """Create a detailed prompt for image generation"""
        name = product['name']
        description = product.get('description', '')[:200]
        plant_part = product.get('plant_part', 'Hemp')
        industry = product.get('industry', 'Industrial')
        
        # Build context-aware prompt
        prompt_parts = [
            f"Professional product photo of {name}",
            "made from sustainable hemp materials",
            f"featuring {plant_part.lower()} components" if plant_part else "",
            f"for {industry.lower()} applications" if industry else "",
            "clean white background, studio lighting",
            "high-quality commercial photography style"
        ]
        
        # Filter empty parts and join
        prompt = ", ".join(part for part in prompt_parts if part)
        
        # Ensure prompt doesn't exceed DALL-E limits
        if len(prompt) > 400:
            prompt = prompt[:397] + "..."
            
        return prompt
    
    def generate_placeholder_url(self, product: Dict) -> str:
        """Generate a deterministic placeholder image URL"""
        # Use a placeholder service with product-specific parameters
        name_hash = hash(product['name']) % 1000
        colors = ['1abc9c', '2ecc71', '3498db', '9b59b6', 'f39c12', 'e74c3c']
        color = colors[name_hash % len(colors)]
        
        # Use placeholder service
        placeholder_url = f"https://via.placeholder.com/800x600/{color}/ffffff?text=Hemp+Product"
        
        return placeholder_url
    
    def generate_dalle_image(self, prompt: str) -> Optional[str]:
        """Generate image using DALL-E API"""
        try:
            response = openai.Image.create(
                prompt=prompt,
                n=1,
                size="1024x1024",
                quality="standard"
            )
            
            if response and response.data:
                return response.data[0].url
            
        except Exception as e:
            logging.error(f"DALL-E generation error: {str(e)}")
            
        return None
    
    def generate_image_for_product(self, product: Dict) -> Optional[str]:
        """Generate or assign an image URL for a product"""
        
        # Check rate limits
        if self.images_generated >= self.max_images_per_hour:
            elapsed = time.time() - self.start_time
            if elapsed < 3600:  # Less than an hour
                sleep_time = 3600 - elapsed
                logging.info(f"Rate limit reached, sleeping for {sleep_time:.0f} seconds")
                time.sleep(sleep_time)
                self.images_generated = 0
                self.start_time = time.time()
        
        # Generate prompt
        prompt = self.generate_image_prompt(product)
        logging.info(f"Generating image for: {product['name']}")
        logging.debug(f"Prompt: {prompt}")
        
        # Generate image
        if self.use_placeholder:
            image_url = self.generate_placeholder_url(product)
        else:
            image_url = self.generate_dalle_image(prompt)
            if image_url:
                self.images_generated += 1
            else:
                # Fallback to placeholder if DALL-E fails
                image_url = self.generate_placeholder_url(product)
        
        return image_url
    
    def update_product_image(self, product_id: int, image_url: str):
        """Update product with generated image URL"""
        with self.conn.cursor() as cur:
            cur.execute("""
                UPDATE uses_products
                SET ai_generated_image_url = %s,
                    image_source = 'ai_generated',
                    image_type = 'placeholder',
                    updated_at = NOW()
                WHERE id = %s
            """, (image_url, product_id))
            self.conn.commit()
    
    def generate_images_batch(self, batch_size: int = 50):
        """Generate images for a batch of products"""
        products = self.get_products_without_images(batch_size)
        total = len(products)
        
        if total == 0:
            logging.info("No products need images!")
            return
        
        logging.info(f"Found {total} products needing images")
        
        success_count = 0
        error_count = 0
        
        for i, product in enumerate(products, 1):
            try:
                image_url = self.generate_image_for_product(product)
                
                if image_url:
                    self.update_product_image(product['id'], image_url)
                    success_count += 1
                    logging.info(f"[{i}/{total}] Generated image for product ID {product['id']}")
                else:
                    error_count += 1
                    logging.error(f"[{i}/{total}] Failed to generate image for product ID {product['id']}")
                
                # Small delay to avoid overwhelming services
                time.sleep(0.5)
                
            except Exception as e:
                error_count += 1
                logging.error(f"Error processing product {product['id']}: {str(e)}")
        
        # Summary
        logging.info(f"\nBatch complete: {success_count} successful, {error_count} errors")
        self.generate_report()
    
    def generate_report(self):
        """Generate summary report of image generation"""
        with self.conn.cursor() as cur:
            # Get statistics
            cur.execute("""
                SELECT 
                    COUNT(*) as total_products,
                    COUNT(CASE WHEN image_url IS NOT NULL OR ai_generated_image_url IS NOT NULL THEN 1 END) as with_images,
                    COUNT(CASE WHEN image_url IS NULL AND ai_generated_image_url IS NULL THEN 1 END) as without_images,
                    COUNT(CASE WHEN ai_generated_image_url IS NOT NULL THEN 1 END) as ai_generated,
                    COUNT(CASE WHEN image_url IS NOT NULL THEN 1 END) as original_images
                FROM uses_products
            """)
            stats = cur.fetchone()
            
            # Get recent generations
            cur.execute("""
                SELECT COUNT(*) as recent_count
                FROM uses_products
                WHERE ai_generated_image_url IS NOT NULL
                  AND updated_at > NOW() - INTERVAL '1 hour'
            """)
            recent = cur.fetchone()
            
            print("\n=== Image Generation Report ===")
            print(f"Total products: {stats['total_products']}")
            print(f"Products with images: {stats['with_images']} ({stats['with_images'] * 100 / stats['total_products']:.1f}%)")
            print(f"Products without images: {stats['without_images']}")
            print(f"AI-generated images: {stats['ai_generated']}")
            print(f"Original images: {stats['original_images']}")
            print(f"Generated in last hour: {recent['recent_count']}")
    
    def close(self):
        """Close database connection"""
        if self.conn:
            self.conn.close()

def main():
    """Main execution function"""
    try:
        generator = ImageGenerator()
        
        # Generate images in batches to avoid timeouts
        while True:
            products_remaining = generator.get_products_without_images(1)
            if not products_remaining:
                logging.info("All products have images!")
                break
                
            generator.generate_images_batch(batch_size=50)
            
            # Check if we should continue
            response = input("\nContinue with next batch? (y/n): ")
            if response.lower() != 'y':
                break
                
    except Exception as e:
        logging.error(f"Error in image generation: {str(e)}")
        raise
    finally:
        generator.close()

if __name__ == "__main__":
    main()