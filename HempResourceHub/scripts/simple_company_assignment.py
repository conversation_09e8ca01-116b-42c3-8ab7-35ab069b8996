#!/usr/bin/env python3
"""
Simple Company Assignment Script
Assigns companies based on keyword matching without external dependencies
"""

import os
import sys
import logging
from datetime import datetime
import psycopg2
from psycopg2.extras import RealDictCursor
from dotenv import load_dotenv

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# Company mappings by keyword
COMPANY_MAPPINGS = {
    # Aerospace (id: 226)
    226: ['aerospace', 'aviation', 'aircraft', 'plane', 'flight', 'cockpit', 'fuselage', 'wing'],
    
    # Marine (id: 227)
    227: ['marine', 'boat', 'ship', 'yacht', 'sail', 'hull', 'deck', 'nautical', 'maritime', 'vessel'],
    
    # Packaging (id: 229)
    229: ['packaging', 'package', 'container', 'wrapper', 'box', 'bottle', 'jar', 'pouch', 'bag'],
    
    # Research & Development (id: 230)
    230: ['research', 'experimental', 'prototype', 'novel', 'innovative', 'advanced', 'next-gen', 'future'],
    
    # Agricultural (id: 231)
    231: ['agricultural', 'farming', 'cultivation', 'harvest', 'seed', 'soil', 'grow', 'crop'],
    
    # Energy (id: 232)
    232: ['energy', 'power', 'solar', 'wind', 'renewable', 'biofuel', 'turbine', 'generator'],
    
    # Cosmetics (id: 233)
    233: ['cosmetic', 'beauty', 'skincare', 'lotion', 'cream', 'serum', 'makeup', 'shampoo', 'conditioner'],
    
    # Food & Nutrition (id: 234)
    234: ['food', 'nutrition', 'supplement', 'protein', 'oil', 'edible', 'beverage', 'snack', 'ingredient'],
    
    # Sports Equipment (id: 235)
    235: ['sport', 'athletic', 'fitness', 'exercise', 'gym', 'yoga', 'tennis', 'golf', 'ski', 'bike'],
    
    # Nano Technologies (id: 236)
    236: ['nano', 'quantum', 'molecular', 'atomic', 'microscale', 'nanoparticle', 'nanotube'],
    
    # Quantum Materials (id: 237)
    237: ['quantum', 'photonic', 'superconducting', 'entangled', 'qubit', 'coherent'],
    
    # Space Technologies (id: 238)
    238: ['space', 'satellite', 'rocket', 'orbit', 'lunar', 'mars', 'asteroid', 'cosmic', 'zero-g'],
    
    # Industrial Manufacturing (id: 239)
    239: ['industrial', 'manufacturing', 'production', 'factory', 'machinery', 'equipment']
}

def get_connection():
    """Get database connection"""
    db_url = os.getenv('DATABASE_URL')
    if not db_url:
        raise ValueError("DATABASE_URL not found in environment variables")
    return psycopg2.connect(db_url, cursor_factory=RealDictCursor)

def find_company_for_product(product_name, product_desc):
    """Find the best matching company ID for a product"""
    name_lower = product_name.lower()
    desc_lower = (product_desc or '').lower()
    combined_text = f"{name_lower} {desc_lower}"
    
    # Check each company's keywords
    best_match_company = None
    best_match_count = 0
    
    for company_id, keywords in COMPANY_MAPPINGS.items():
        match_count = sum(1 for keyword in keywords if keyword in combined_text)
        if match_count > best_match_count:
            best_match_count = match_count
            best_match_company = company_id
    
    # Default to industrial manufacturing if no matches
    if best_match_company is None:
        best_match_company = 239  # Hemp Industrial Manufacturing
    
    return best_match_company

def main():
    """Main execution function"""
    conn = get_connection()
    
    try:
        with conn.cursor() as cur:
            # Get unassigned products
            cur.execute("""
                SELECT id, name, description
                FROM uses_products
                WHERE primary_company_id IS NULL
                ORDER BY id
            """)
            products = cur.fetchall()
            
            logging.info(f"Found {len(products)} products without companies")
            
            # Process in batches
            batch_size = 100
            total_assigned = 0
            
            for i in range(0, len(products), batch_size):
                batch = products[i:i + batch_size]
                updates = []
                
                for product in batch:
                    company_id = find_company_for_product(product['name'], product['description'])
                    updates.append((company_id, product['id']))
                
                # Execute batch update
                if updates:
                    cur.executemany("""
                        UPDATE uses_products 
                        SET primary_company_id = %s,
                            updated_at = NOW()
                        WHERE id = %s
                    """, updates)
                    conn.commit()
                    total_assigned += len(updates)
                    
                    logging.info(f"Batch {i//batch_size + 1}: Assigned {len(updates)} companies (Total: {total_assigned})")
            
            # Final statistics
            cur.execute("""
                SELECT 
                    COUNT(*) as total,
                    COUNT(primary_company_id) as assigned,
                    COUNT(*) FILTER (WHERE primary_company_id IS NULL) as unassigned
                FROM uses_products
            """)
            stats = cur.fetchone()
            
            logging.info(f"\nFinal Statistics:")
            logging.info(f"Total products: {stats['total']}")
            logging.info(f"Assigned: {stats['assigned']} ({stats['assigned'] * 100 / stats['total']:.1f}%)")
            logging.info(f"Unassigned: {stats['unassigned']}")
            
            # Top assigned companies
            cur.execute("""
                SELECT c.name, COUNT(p.id) as count
                FROM uses_products p
                JOIN hemp_companies c ON p.primary_company_id = c.id
                WHERE p.updated_at > NOW() - INTERVAL '1 hour'
                GROUP BY c.name
                ORDER BY count DESC
                LIMIT 10
            """)
            
            print("\nTop 10 company assignments (last hour):")
            for row in cur.fetchall():
                print(f"  {row['name']}: {row['count']} products")
                
    except Exception as e:
        logging.error(f"Error: {str(e)}")
        conn.rollback()
        raise
    finally:
        conn.close()

if __name__ == "__main__":
    main()