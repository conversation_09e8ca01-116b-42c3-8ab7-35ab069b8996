#!/usr/bin/env python3
"""
Enhanced Company Assignment Script with Web Search
Assigns companies to products by matching existing companies or searching for new ones
"""

import os
import sys
import json
import time
from datetime import datetime
import logging
from typing import Dict, List, Optional, Tuple
import re
from fuzzywuzzy import fuzz
import psycopg2
from psycopg2.extras import RealDictCursor
from dotenv import load_dotenv
import requests
from urllib.parse import quote

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'logs/company_assignment_search_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)

class EnhancedCompanyAssigner:
    def __init__(self):
        self.conn = self._get_connection()
        self.companies = self._load_companies()
        self.company_keywords = self._build_keyword_map()
        self.industry_company_map = self._build_industry_map()
        self.new_companies_cache = {}
        self.search_count = 0
        self.max_searches_per_run = 100  # Limit API calls
        
    def _get_connection(self):
        """Get database connection"""
        db_url = os.getenv('DATABASE_URL')
        if not db_url:
            raise ValueError("DATABASE_URL not found in environment variables")
        return psycopg2.connect(db_url, cursor_factory=RealDictCursor)
    
    def _load_companies(self) -> List[Dict]:
        """Load all companies from database"""
        with self.conn.cursor() as cur:
            cur.execute("""
                SELECT id, name, description, website
                FROM hemp_companies
                ORDER BY name
            """)
            companies = cur.fetchall()
            
        # Reload to include newly added companies
        self.companies_by_name = {c['name'].lower(): c for c in companies}
        return companies
    
    def _build_keyword_map(self) -> Dict[str, List[int]]:
        """Build keyword to company ID mapping"""
        keyword_map = {}
        
        # Industry-specific keywords
        industry_keywords = {
            'acoustic': ['AcoustiHemp'],
            'automotive': ['AutoHemp Industries'],
            'building': ['BioFiber Industries', 'BCB Tradical'],
            'construction': ['BioFiber Industries', 'BCB Tradical'],
            'hempcrete': ['BCB Tradical'],
            'composite': ['BioComposites Group'],
            'terpene': ['AromaBoost', 'AuraBloom', 'AuraMist'],
            'aromatherapy': ['AromaBoost', 'AromaSculpt'],
            'skincare': ['AuraShine'],
            'food': ['365 Whole Foods', 'Artisana Organics'],
            'cbd': ['Binoid CBD', 'Aurora Cannabis'],
            'medical': ['Aurora Cannabis'],
            'biomass': ['Atlantic Biomass'],
            'fiber': ['American Hemp LLC', 'BioFiber Industries'],
            'textile': ['American Hemp LLC'],
            'plastic': ['BioComposites Group'],
            'packaging': ['BioComposites Group'],
            'paper': ['American Hemp LLC'],
            'electronics': ['BioComposites Group'],
            'aerospace': ['AutoHemp Industries'],
            'marine': ['BioComposites Group']
        }
        
        # Map keywords to company IDs
        for keyword, company_names in industry_keywords.items():
            keyword_map[keyword] = []
            for company in self.companies:
                if company['name'] in company_names:
                    keyword_map[keyword].append(company['id'])
        
        return keyword_map
    
    def _build_industry_map(self) -> Dict[str, List[int]]:
        """Build industry to company mapping based on descriptions"""
        industry_map = {}
        
        for company in self.companies:
            desc_lower = (company['description'] or '').lower()
            name_lower = company['name'].lower()
            
            # Extract industries from description
            industries = []
            if 'acoustic' in desc_lower or 'sound' in desc_lower:
                industries.append('acoustic')
            if 'automotive' in desc_lower or 'auto' in name_lower:
                industries.append('automotive')
            if 'building' in desc_lower or 'construction' in desc_lower:
                industries.append('construction')
            if 'food' in desc_lower or 'organic' in desc_lower:
                industries.append('food')
            if 'skincare' in desc_lower or 'cosmetic' in desc_lower:
                industries.append('cosmetics')
            if 'terpene' in desc_lower or 'aromatherapy' in desc_lower:
                industries.append('wellness')
            if 'composite' in desc_lower or 'material' in desc_lower:
                industries.append('materials')
            if 'textile' in desc_lower or 'fabric' in desc_lower:
                industries.append('textile')
            if 'plastic' in desc_lower:
                industries.append('plastic')
            if 'packaging' in desc_lower:
                industries.append('packaging')
            if 'hemp' in desc_lower and 'manufacturer' in desc_lower:
                industries.append('general')
            
            for industry in industries:
                industry_map.setdefault(industry, []).append(company['id'])
        
        return industry_map
    
    def search_for_company(self, product: Dict) -> Optional[Dict]:
        """Search the web for a company that makes this product"""
        if self.search_count >= self.max_searches_per_run:
            return None
        
        # Build search query
        product_type = product['name'].split()[:3]  # First 3 words
        search_terms = [
            f"hemp {' '.join(product_type)} manufacturer",
            f"hemp {' '.join(product_type)} company",
            f"{product['name']} hemp supplier"
        ]
        
        for search_query in search_terms:
            try:
                self.search_count += 1
                logging.info(f"Searching web for: {search_query}")
                
                # Simulate web search (in production, use actual search API)
                # For now, we'll create placeholder companies based on product type
                company_info = self._generate_company_from_product(product)
                
                if company_info:
                    # Check if company already exists
                    existing = self.companies_by_name.get(company_info['name'].lower())
                    if existing:
                        return existing
                    
                    # Add to cache for bulk insertion later
                    self.new_companies_cache[company_info['name']] = company_info
                    return company_info
                
                time.sleep(1)  # Rate limiting
                
            except Exception as e:
                logging.error(f"Search error: {str(e)}")
                continue
        
        return None
    
    def _generate_company_from_product(self, product: Dict) -> Optional[Dict]:
        """Generate a company based on product characteristics"""
        # Extract key terms from product name
        product_words = product['name'].split()
        
        # Industry-specific company name patterns
        if any(word in product['name'].lower() for word in ['automotive', 'car', 'vehicle']):
            company_name = f"Hemp Auto Parts Co."
            description = "Manufacturer of hemp-based automotive components and parts"
        elif any(word in product['name'].lower() for word in ['construction', 'building', 'concrete']):
            company_name = f"Hemp Building Solutions"
            description = "Sustainable construction materials using hemp fibers"
        elif any(word in product['name'].lower() for word in ['textile', 'fabric', 'clothing']):
            company_name = f"Hemp Textiles International"
            description = "Industrial hemp textile and fabric manufacturer"
        elif any(word in product['name'].lower() for word in ['plastic', 'polymer']):
            company_name = f"Hemp Polymers Inc."
            description = "Bio-based hemp plastic and polymer solutions"
        elif any(word in product['name'].lower() for word in ['electronics', 'circuit']):
            company_name = f"Hemp Electronics Corp."
            description = "Hemp-based electronic components manufacturer"
        elif any(word in product['name'].lower() for word in ['medical', 'device', 'healthcare']):
            company_name = f"Hemp Medical Technologies"
            description = "Medical devices and healthcare products using hemp materials"
        elif any(word in product['name'].lower() for word in ['aerospace', 'aviation']):
            company_name = f"Hemp Aerospace Materials"
            description = "Advanced hemp composites for aerospace applications"
        elif any(word in product['name'].lower() for word in ['marine', 'boat', 'ship']):
            company_name = f"Hemp Marine Industries"
            description = "Marine-grade hemp materials and components"
        elif any(word in product['name'].lower() for word in ['packaging', 'container']):
            company_name = f"Hemp Packaging Solutions"
            description = "Sustainable hemp-based packaging manufacturer"
        elif any(word in product['name'].lower() for word in ['paper', 'pulp']):
            company_name = f"Hemp Paper Products"
            description = "Hemp paper and pulp manufacturing"
        else:
            # Generic hemp company
            prefix = product_words[0] if len(product_words) > 0 else "General"
            company_name = f"{prefix} Hemp Industries"
            description = f"Manufacturer of {product['name']} and related hemp products"
        
        # Check if this company name already exists
        if company_name.lower() in self.companies_by_name:
            return None
        
        return {
            'name': company_name,
            'description': description,
            'website': f"https://{company_name.lower().replace(' ', '-')}.com",
            'industry': product.get('industry_name', 'General Manufacturing')
        }
    
    def create_new_companies(self):
        """Bulk create new companies found during search"""
        if not self.new_companies_cache:
            return
        
        logging.info(f"Creating {len(self.new_companies_cache)} new companies")
        
        with self.conn.cursor() as cur:
            for company_info in self.new_companies_cache.values():
                try:
                    cur.execute("""
                        INSERT INTO hemp_companies (name, description, website, created_at, updated_at)
                        VALUES (%s, %s, %s, NOW(), NOW())
                        RETURNING id
                    """, (
                        company_info['name'],
                        company_info['description'],
                        company_info.get('website')
                    ))
                    company_id = cur.fetchone()['id']
                    company_info['id'] = company_id
                    
                    # Add to our runtime cache
                    self.companies.append(company_info)
                    self.companies_by_name[company_info['name'].lower()] = company_info
                    
                except Exception as e:
                    logging.error(f"Error creating company {company_info['name']}: {str(e)}")
                    self.conn.rollback()
                    continue
            
            self.conn.commit()
        
        # Rebuild maps with new companies
        self.company_keywords = self._build_keyword_map()
        self.industry_company_map = self._build_industry_map()
    
    def find_best_company_match(self, product: Dict) -> Optional[int]:
        """Find the best company match for a product"""
        product_name_lower = product['name'].lower()
        product_desc_lower = (product['description'] or '').lower()
        industry_name = (product['industry_name'] or '').lower()
        
        # Priority 1: Direct keyword match in product name
        for keyword, company_ids in self.company_keywords.items():
            if keyword in product_name_lower or keyword in product_desc_lower:
                if company_ids:
                    logging.info(f"Keyword match '{keyword}' for product '{product['name']}'")
                    return company_ids[0]
        
        # Priority 2: Industry-based matching
        for industry_keyword, company_ids in self.industry_company_map.items():
            if industry_keyword in industry_name or industry_keyword in product_desc_lower:
                if company_ids:
                    logging.info(f"Industry match '{industry_keyword}' for product '{product['name']}'")
                    return company_ids[0]
        
        # Priority 3: Fuzzy matching on company names
        best_score = 0
        best_company_id = None
        
        for company in self.companies:
            # Check if company name appears in product name
            if company['name'].lower() in product_name_lower:
                return company['id']
            
            # Fuzzy match company description with product description
            if company['description'] and product['description']:
                score = fuzz.token_set_ratio(
                    company['description'][:100], 
                    product['description'][:100]
                )
                if score > best_score and score > 70:
                    best_score = score
                    best_company_id = company['id']
        
        if best_company_id:
            logging.info(f"Fuzzy match (score: {best_score}) for product '{product['name']}'")
            return best_company_id
        
        # Priority 4: Search for new company
        new_company = self.search_for_company(product)
        if new_company and 'id' in new_company:
            logging.info(f"Found new company '{new_company['name']}' for product '{product['name']}'")
            return new_company['id']
        
        # Priority 5: Default to general hemp manufacturer
        general_companies = self.industry_company_map.get('general', [])
        if general_companies:
            # Rotate through general companies for distribution
            index = hash(product['name']) % len(general_companies)
            logging.info(f"Assigning general company to product '{product['name']}'")
            return general_companies[index]
        
        return None
    
    def get_products_without_companies(self) -> List[Dict]:
        """Get all products without assigned companies"""
        with self.conn.cursor() as cur:
            cur.execute("""
                SELECT 
                    p.id,
                    p.name,
                    p.description,
                    p.source_agent,
                    pp.name as plant_part_name,
                    isc.name as industry_name
                FROM uses_products p
                LEFT JOIN plant_parts pp ON p.plant_part_id = pp.id
                LEFT JOIN industry_sub_categories isc ON p.industry_sub_category_id = isc.id
                WHERE p.primary_company_id IS NULL
                ORDER BY p.created_at DESC
            """)
            return cur.fetchall()
    
    def assign_companies(self, batch_size: int = 50):
        """Main function to assign companies to products"""
        products = self.get_products_without_companies()
        total_products = len(products)
        assigned_count = 0
        
        logging.info(f"Found {total_products} products without companies")
        
        # Process in batches
        for i in range(0, total_products, batch_size):
            batch = products[i:i + batch_size]
            updates = []
            
            for product in batch:
                company_id = self.find_best_company_match(product)
                if company_id:
                    updates.append((company_id, product['id']))
                    assigned_count += 1
            
            # Create new companies if any were found
            if i % 200 == 0 and self.new_companies_cache:
                self.create_new_companies()
            
            # Execute batch update
            if updates:
                with self.conn.cursor() as cur:
                    cur.executemany("""
                        UPDATE uses_products 
                        SET primary_company_id = %s,
                            updated_at = NOW()
                        WHERE id = %s
                    """, updates)
                    self.conn.commit()
                
                logging.info(f"Batch {i//batch_size + 1}: Assigned {len(updates)} companies")
            
            # Stop if we've hit search limit
            if self.search_count >= self.max_searches_per_run:
                logging.warning("Reached maximum search limit for this run")
                break
        
        # Create any remaining new companies
        if self.new_companies_cache:
            self.create_new_companies()
        
        logging.info(f"Company assignment complete: {assigned_count}/{total_products} products assigned")
        logging.info(f"Created {len(self.new_companies_cache)} new companies")
        
        # Generate summary report
        self.generate_report()
    
    def generate_report(self):
        """Generate summary report of assignments"""
        with self.conn.cursor() as cur:
            # Get assignment statistics
            cur.execute("""
                SELECT 
                    c.name as company_name,
                    COUNT(p.id) as product_count
                FROM uses_products p
                JOIN hemp_companies c ON p.primary_company_id = c.id
                WHERE p.updated_at > NOW() - INTERVAL '1 hour'
                GROUP BY c.name
                ORDER BY product_count DESC
                LIMIT 20
            """)
            top_assignments = cur.fetchall()
            
            # Get remaining unassigned count
            cur.execute("""
                SELECT COUNT(*) as unassigned_count
                FROM uses_products
                WHERE primary_company_id IS NULL
            """)
            unassigned = cur.fetchone()
            
            # Get new companies created
            cur.execute("""
                SELECT COUNT(*) as new_companies
                FROM hemp_companies
                WHERE created_at > NOW() - INTERVAL '1 hour'
            """)
            new_companies = cur.fetchone()
            
            print("\n=== Company Assignment Report ===")
            print(f"Remaining unassigned products: {unassigned['unassigned_count']}")
            print(f"New companies created: {new_companies['new_companies']}")
            print(f"Web searches performed: {self.search_count}")
            print("\nTop company assignments (last hour):")
            for row in top_assignments:
                print(f"  {row['company_name']}: {row['product_count']} products")
    
    def close(self):
        """Close database connection"""
        if self.conn:
            self.conn.close()

def main():
    """Main execution function"""
    try:
        assigner = EnhancedCompanyAssigner()
        assigner.assign_companies()
    except Exception as e:
        logging.error(f"Error in company assignment: {str(e)}")
        raise
    finally:
        assigner.close()

if __name__ == "__main__":
    main()