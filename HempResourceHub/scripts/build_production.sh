#!/bin/bash

# Production Build Script for Hemp Resource Hub
# This script optimizes the build for production deployment

echo "=== Hemp Resource Hub Production Build ==="
echo "Starting optimized production build process..."

# Set production environment
export NODE_ENV=production

# Clean previous builds
echo "Cleaning previous builds..."
rm -rf dist
rm -rf node_modules/.vite

# Install dependencies with exact versions
echo "Installing dependencies..."
npm ci --production=false

# Run type checking
echo "Running TypeScript type checking..."
npx tsc --noEmit || echo "TypeScript errors found, continuing build..."

# Build client
echo "Building client application..."
npm run build

# Create production-ready server file
echo "Creating production server configuration..."
cat > dist/server.js << 'EOF'
const express = require('express');
const path = require('path');
const compression = require('compression');

const app = express();
const PORT = process.env.PORT || 3001;

// Enable gzip compression
app.use(compression());

// Serve static files with caching
app.use(express.static(path.join(__dirname, 'public'), {
  maxAge: '1y',
  etag: true,
  lastModified: true,
  setHeaders: (res, path) => {
    if (path.endsWith('.html')) {
      res.setHeader('Cache-Control', 'no-cache');
    }
  }
}));

// API routes
app.use('/api', require('../server/routes'));

// Serve index.html for all other routes (SPA)
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

app.listen(PORT, () => {
  console.log(`Production server running on port ${PORT}`);
});
EOF

# Create package.json for production
echo "Creating production package.json..."
cat > dist/package.json << EOF
{
  "name": "hemp-resource-hub-production",
  "version": "1.0.0",
  "main": "server.js",
  "scripts": {
    "start": "node server.js"
  },
  "dependencies": {
    "express": "^4.18.2",
    "compression": "^1.7.4"
  }
}
EOF

# Analyze bundle size
echo "Analyzing bundle size..."
if [ -f "dist/public/assets/*.js" ]; then
  echo "Bundle sizes:"
  du -sh dist/public/assets/*.js | sort -h
fi

# Create deployment info
echo "Creating deployment info..."
cat > dist/DEPLOYMENT.md << EOF
# Hemp Resource Hub - Production Deployment

## Build Information
- Build Date: $(date)
- Node Version: $(node --version)
- NPM Version: $(npm --version)

## Deployment Steps
1. Upload the \`dist\` folder to your server
2. Run \`npm install\` in the dist folder
3. Set environment variables (VITE_SUPABASE_URL, VITE_SUPABASE_ANON_KEY, etc.)
4. Run \`npm start\` to start the production server

## Environment Variables Required
- VITE_SUPABASE_URL
- VITE_SUPABASE_ANON_KEY
- DATABASE_URL
- PORT (optional, defaults to 3001)

## Performance Optimizations Applied
- Code splitting with manual chunks
- Gzip compression enabled
- Static asset caching (1 year)
- Tree shaking and minification
- Dead code elimination
EOF

echo ""
echo "=== Build Summary ==="
echo "Total build size: $(du -sh dist | cut -f1)"
echo "Client assets: $(du -sh dist/public/assets 2>/dev/null | cut -f1 || echo 'N/A')"
echo ""
echo "Production build complete!"
echo "Deploy the 'dist' folder to your production server."