#!/usr/bin/env python3
"""
Emergency Company Assignment Script
Assigns companies to the 4,554 products that currently lack company associations
"""

import os
import sys
from datetime import datetime
import logging
from typing import Dict, List, Optional, Tuple
import re
from fuzzywuzzy import fuzz
import psycopg2
from psycopg2.extras import RealDictCursor
from dotenv import load_dotenv

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'logs/company_assignment_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)

class CompanyAssigner:
    def __init__(self):
        self.conn = self._get_connection()
        self.companies = self._load_companies()
        self.company_keywords = self._build_keyword_map()
        self.industry_company_map = self._build_industry_map()
        
    def _get_connection(self):
        """Get database connection"""
        db_url = os.getenv('DATABASE_URL')
        if not db_url:
            raise ValueError("DATABASE_URL not found in environment variables")
        return psycopg2.connect(db_url, cursor_factory=RealDictCursor)
    
    def _load_companies(self) -> List[Dict]:
        """Load all companies from database"""
        with self.conn.cursor() as cur:
            cur.execute("""
                SELECT id, name, description, website
                FROM hemp_companies
                ORDER BY name
            """)
            return cur.fetchall()
    
    def _build_keyword_map(self) -> Dict[str, List[int]]:
        """Build keyword to company ID mapping"""
        keyword_map = {}
        
        # Industry-specific keywords
        industry_keywords = {
            'acoustic': ['AcoustiHemp'],
            'automotive': ['AutoHemp Industries'],
            'building': ['BioFiber Industries', 'BCB Tradical'],
            'construction': ['BioFiber Industries', 'BCB Tradical'],
            'hempcrete': ['BCB Tradical'],
            'composite': ['BioComposites Group'],
            'terpene': ['AromaBoost', 'AuraBloom', 'AuraMist'],
            'aromatherapy': ['AromaBoost', 'AromaSculpt'],
            'skincare': ['AuraShine'],
            'food': ['365 Whole Foods', 'Artisana Organics'],
            'cbd': ['Binoid CBD', 'Aurora Cannabis'],
            'medical': ['Aurora Cannabis'],
            'biomass': ['Atlantic Biomass'],
            'fiber': ['American Hemp LLC', 'BioFiber Industries']
        }
        
        # Map keywords to company IDs
        for keyword, company_names in industry_keywords.items():
            keyword_map[keyword] = []
            for company in self.companies:
                if company['name'] in company_names:
                    keyword_map[keyword].append(company['id'])
        
        return keyword_map
    
    def _build_industry_map(self) -> Dict[str, List[int]]:
        """Build industry to company mapping based on descriptions"""
        industry_map = {}
        
        for company in self.companies:
            desc_lower = (company['description'] or '').lower()
            name_lower = company['name'].lower()
            
            # Extract industries from description
            if 'acoustic' in desc_lower or 'sound' in desc_lower:
                industry_map.setdefault('acoustic', []).append(company['id'])
            if 'automotive' in desc_lower or 'auto' in name_lower:
                industry_map.setdefault('automotive', []).append(company['id'])
            if 'building' in desc_lower or 'construction' in desc_lower:
                industry_map.setdefault('construction', []).append(company['id'])
            if 'food' in desc_lower or 'organic' in desc_lower:
                industry_map.setdefault('food', []).append(company['id'])
            if 'skincare' in desc_lower or 'cosmetic' in desc_lower:
                industry_map.setdefault('cosmetics', []).append(company['id'])
            if 'terpene' in desc_lower or 'aromatherapy' in desc_lower:
                industry_map.setdefault('wellness', []).append(company['id'])
            if 'composite' in desc_lower or 'material' in desc_lower:
                industry_map.setdefault('materials', []).append(company['id'])
            if 'hemp' in desc_lower and 'manufacturer' in desc_lower:
                industry_map.setdefault('general', []).append(company['id'])
        
        return industry_map
    
    def find_best_company_match(self, product: Dict) -> Optional[int]:
        """Find the best company match for a product"""
        product_name_lower = product['name'].lower()
        product_desc_lower = (product['description'] or '').lower()
        industry_name = (product['industry_name'] or '').lower()
        
        # Priority 1: Direct keyword match in product name
        for keyword, company_ids in self.keyword_map.items():
            if keyword in product_name_lower or keyword in product_desc_lower:
                if company_ids:
                    logging.info(f"Keyword match '{keyword}' for product '{product['name']}'")
                    return company_ids[0]
        
        # Priority 2: Industry-based matching
        for industry_keyword, company_ids in self.industry_map.items():
            if industry_keyword in industry_name or industry_keyword in product_desc_lower:
                if company_ids:
                    logging.info(f"Industry match '{industry_keyword}' for product '{product['name']}'")
                    return company_ids[0]
        
        # Priority 3: Fuzzy matching on company names
        best_score = 0
        best_company_id = None
        
        for company in self.companies:
            # Check if company name appears in product name
            if company['name'].lower() in product_name_lower:
                return company['id']
            
            # Fuzzy match company description with product description
            if company['description'] and product['description']:
                score = fuzz.token_set_ratio(
                    company['description'][:100], 
                    product['description'][:100]
                )
                if score > best_score and score > 70:
                    best_score = score
                    best_company_id = company['id']
        
        if best_company_id:
            logging.info(f"Fuzzy match (score: {best_score}) for product '{product['name']}'")
            return best_company_id
        
        # Priority 4: Default to general hemp manufacturer
        general_companies = self.industry_map.get('general', [])
        if general_companies:
            # Rotate through general companies for distribution
            index = hash(product['name']) % len(general_companies)
            logging.info(f"Assigning general company to product '{product['name']}'")
            return general_companies[index]
        
        return None
    
    def get_products_without_companies(self) -> List[Dict]:
        """Get all products without assigned companies"""
        with self.conn.cursor() as cur:
            cur.execute("""
                SELECT 
                    p.id,
                    p.name,
                    p.description,
                    p.source_agent,
                    pp.name as plant_part_name,
                    isc.name as industry_name
                FROM uses_products p
                LEFT JOIN plant_parts pp ON p.plant_part_id = pp.id
                LEFT JOIN industry_sub_categories isc ON p.industry_sub_category_id = isc.id
                WHERE p.primary_company_id IS NULL
                ORDER BY p.created_at DESC
            """)
            return cur.fetchall()
    
    def assign_companies(self, batch_size: int = 100):
        """Main function to assign companies to products"""
        products = self.get_products_without_companies()
        total_products = len(products)
        assigned_count = 0
        
        logging.info(f"Found {total_products} products without companies")
        
        # Process in batches
        for i in range(0, total_products, batch_size):
            batch = products[i:i + batch_size]
            updates = []
            
            for product in batch:
                company_id = self.find_best_company_match(product)
                if company_id:
                    updates.append((company_id, product['id']))
                    assigned_count += 1
            
            # Execute batch update
            if updates:
                with self.conn.cursor() as cur:
                    cur.executemany("""
                        UPDATE uses_products 
                        SET primary_company_id = %s,
                            updated_at = NOW()
                        WHERE id = %s
                    """, updates)
                    self.conn.commit()
                
                logging.info(f"Batch {i//batch_size + 1}: Assigned {len(updates)} companies")
        
        logging.info(f"Company assignment complete: {assigned_count}/{total_products} products assigned")
        
        # Generate summary report
        self.generate_report()
    
    def generate_report(self):
        """Generate summary report of assignments"""
        with self.conn.cursor() as cur:
            # Get assignment statistics
            cur.execute("""
                SELECT 
                    c.name as company_name,
                    COUNT(p.id) as product_count
                FROM uses_products p
                JOIN hemp_companies c ON p.primary_company_id = c.id
                WHERE p.updated_at > NOW() - INTERVAL '1 hour'
                GROUP BY c.name
                ORDER BY product_count DESC
                LIMIT 20
            """)
            top_assignments = cur.fetchall()
            
            # Get remaining unassigned count
            cur.execute("""
                SELECT COUNT(*) as unassigned_count
                FROM uses_products
                WHERE primary_company_id IS NULL
            """)
            unassigned = cur.fetchone()
            
            print("\n=== Company Assignment Report ===")
            print(f"Remaining unassigned products: {unassigned['unassigned_count']}")
            print("\nTop company assignments (last hour):")
            for row in top_assignments:
                print(f"  {row['company_name']}: {row['product_count']} products")
    
    def close(self):
        """Close database connection"""
        if self.conn:
            self.conn.close()

def main():
    """Main execution function"""
    try:
        assigner = CompanyAssigner()
        assigner.assign_companies()
    except Exception as e:
        logging.error(f"Error in company assignment: {str(e)}")
        raise
    finally:
        assigner.close()

if __name__ == "__main__":
    main()