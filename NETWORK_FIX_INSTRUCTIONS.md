# Database Connection Fix for WSL2 IPv6 Issue

## Problem
The automation agents are failing because:
1. Supabase database endpoint (db.ktoqznqmlnxrtvubewyz.supabase.co) only has IPv6 addresses
2. WSL2 doesn't support IPv6 properly
3. The pooler endpoint requires special configuration

## Current Status
- ✅ Patent mining agent runs successfully 
- ❌ But can't save to database due to "Network is unreachable"
- ❌ Template fixer also failing with same error

## Solutions

### Option 1: Use SSH Tunnel (Recommended)
Create an SSH tunnel from a server with IPv6 support:
```bash
# From a server with IPv6 (e.g., DigitalOcean, AWS)
ssh -L 5432:db.ktoqznqmlnxrtvubewyz.supabase.co:5432 user@your-server

# Then update DATABASE_URL to use localhost
export DATABASE_URL="postgresql://postgres:$4HQZgassmo@localhost:5432/postgres"
```

### Option 2: Use ngrok PostgreSQL Tunnel
```bash
# Install ngrok and create a TCP tunnel
ngrok tcp 5432

# Forward to Supabase (requires a server with IPv6)
```

### Option 3: Use Supabase REST API (Implemented)
I've created `fix_all_templates_api.py` that uses the REST API instead of direct database connection. This works over IPv4.

To use:
```bash
source venv_dedup/bin/activate
python fix_all_templates_api.py
```

### Option 4: Enable IPv6 in WSL2
Edit `/etc/wsl.conf` and add:
```ini
[network]
generateResolvConf = false
```

Then update `/etc/resolv.conf` with IPv6 DNS servers.

### Option 5: Use a VPS Proxy
Set up a small VPS with IPv6 support to proxy the database connection.

## Temporary Workaround
Until the network issue is fixed, you can:
1. Run the agents manually when you have proper network access
2. Use the Supabase dashboard to monitor the database
3. Use MCP tools in Claude Code which handle the connection properly

## Testing Connection
Test if the fix worked:
```bash
source venv_dedup/bin/activate
python test_ipv4_connection.py
```