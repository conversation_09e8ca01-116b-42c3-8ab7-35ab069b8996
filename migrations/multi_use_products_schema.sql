-- Multi-Use Products Schema Enhancement
-- Enables products to have multiple plant parts and industries
-- Author: Database Enhancement Team
-- Date: January 2025

-- =====================================================
-- PART 1: CREATE JUNCTION TABLES
-- =====================================================

-- Junction table for products with multiple plant parts
CREATE TABLE IF NOT EXISTS product_plant_parts (
    id SERIAL PRIMARY KEY,
    product_id INTEGER NOT NULL REFERENCES uses_products(id) ON DELETE CASCADE,
    plant_part_id INTEGER NOT NULL REFERENCES plant_parts(id) ON DELETE RESTRICT,
    is_primary BOOLEAN DEFAULT FALSE,
    usage_percentage DECIMAL(5,2) CHECK (usage_percentage >= 0 AND usage_percentage <= 100),
    usage_description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(product_id, plant_part_id)
);

-- Junction table for products with multiple industry subcategories
CREATE TABLE IF NOT EXISTS product_industry_subcategories (
    id SERIAL PRIMARY KEY,
    product_id INTEGER NOT NULL REFERENCES uses_products(id) ON DELETE CASCADE,
    subcategory_id INTEGER NOT NULL REFERENCES industry_sub_categories(id) ON DELETE RESTRICT,
    is_primary BOOLEAN DEFAULT FALSE,
    relevance_score DECIMAL(3,2) DEFAULT 1.0 CHECK (relevance_score >= 0 AND relevance_score <= 1),
    application_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(product_id, subcategory_id)
);

-- Indexes for performance
CREATE INDEX idx_product_plant_parts_product ON product_plant_parts(product_id);
CREATE INDEX idx_product_plant_parts_plant ON product_plant_parts(plant_part_id);
CREATE INDEX idx_product_plant_parts_primary ON product_plant_parts(is_primary) WHERE is_primary = TRUE;

CREATE INDEX idx_product_industry_subcategories_product ON product_industry_subcategories(product_id);
CREATE INDEX idx_product_industry_subcategories_subcategory ON product_industry_subcategories(subcategory_id);
CREATE INDEX idx_product_industry_subcategories_primary ON product_industry_subcategories(is_primary) WHERE is_primary = TRUE;

-- =====================================================
-- PART 2: MIGRATE EXISTING DATA
-- =====================================================

-- Migrate existing plant_part_id relationships
INSERT INTO product_plant_parts (product_id, plant_part_id, is_primary, usage_percentage)
SELECT 
    id as product_id,
    plant_part_id,
    TRUE as is_primary,
    100.0 as usage_percentage
FROM uses_products
WHERE plant_part_id IS NOT NULL
ON CONFLICT (product_id, plant_part_id) DO NOTHING;

-- Migrate existing industry_sub_category_id relationships
INSERT INTO product_industry_subcategories (product_id, subcategory_id, is_primary, relevance_score)
SELECT 
    id as product_id,
    industry_sub_category_id as subcategory_id,
    TRUE as is_primary,
    1.0 as relevance_score
FROM uses_products
WHERE industry_sub_category_id IS NOT NULL
ON CONFLICT (product_id, subcategory_id) DO NOTHING;

-- =====================================================
-- PART 3: ADD TRIGGER FUNCTIONS FOR CONSISTENCY
-- =====================================================

-- Function to ensure at least one primary plant part
CREATE OR REPLACE FUNCTION ensure_primary_plant_part()
RETURNS TRIGGER AS $$
BEGIN
    -- If this is being set to non-primary, check if there's another primary
    IF NEW.is_primary = FALSE THEN
        IF NOT EXISTS (
            SELECT 1 FROM product_plant_parts 
            WHERE product_id = NEW.product_id 
            AND plant_part_id != NEW.plant_part_id 
            AND is_primary = TRUE
        ) THEN
            RAISE EXCEPTION 'Product must have at least one primary plant part';
        END IF;
    END IF;
    
    -- If setting to primary, unset other primaries for this product
    IF NEW.is_primary = TRUE THEN
        UPDATE product_plant_parts 
        SET is_primary = FALSE 
        WHERE product_id = NEW.product_id 
        AND plant_part_id != NEW.plant_part_id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to ensure at least one primary industry
CREATE OR REPLACE FUNCTION ensure_primary_industry()
RETURNS TRIGGER AS $$
BEGIN
    -- If this is being set to non-primary, check if there's another primary
    IF NEW.is_primary = FALSE THEN
        IF NOT EXISTS (
            SELECT 1 FROM product_industry_subcategories 
            WHERE product_id = NEW.product_id 
            AND subcategory_id != NEW.subcategory_id 
            AND is_primary = TRUE
        ) THEN
            RAISE EXCEPTION 'Product must have at least one primary industry';
        END IF;
    END IF;
    
    -- If setting to primary, unset other primaries for this product
    IF NEW.is_primary = TRUE THEN
        UPDATE product_industry_subcategories 
        SET is_primary = FALSE 
        WHERE product_id = NEW.product_id 
        AND subcategory_id != NEW.subcategory_id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers
CREATE TRIGGER ensure_primary_plant_part_trigger
    BEFORE INSERT OR UPDATE ON product_plant_parts
    FOR EACH ROW
    EXECUTE FUNCTION ensure_primary_plant_part();

CREATE TRIGGER ensure_primary_industry_trigger
    BEFORE INSERT OR UPDATE ON product_industry_subcategories
    FOR EACH ROW
    EXECUTE FUNCTION ensure_primary_industry();

-- =====================================================
-- PART 4: CREATE HELPER VIEWS
-- =====================================================

-- View to get all plant parts for a product
CREATE OR REPLACE VIEW product_all_plant_parts AS
SELECT 
    p.id as product_id,
    p.name as product_name,
    ARRAY_AGG(
        json_build_object(
            'id', pp.id,
            'name', pp.name,
            'is_primary', ppp.is_primary,
            'usage_percentage', ppp.usage_percentage,
            'usage_description', ppp.usage_description
        ) ORDER BY ppp.is_primary DESC, pp.name
    ) as plant_parts
FROM uses_products p
JOIN product_plant_parts ppp ON p.id = ppp.product_id
JOIN plant_parts pp ON ppp.plant_part_id = pp.id
GROUP BY p.id, p.name;

-- View to get all industries for a product
CREATE OR REPLACE VIEW product_all_industries AS
SELECT 
    p.id as product_id,
    p.name as product_name,
    ARRAY_AGG(
        json_build_object(
            'id', isc.id,
            'name', isc.name,
            'industry_name', i.name,
            'is_primary', pis.is_primary,
            'relevance_score', pis.relevance_score,
            'application_notes', pis.application_notes
        ) ORDER BY pis.is_primary DESC, i.name, isc.name
    ) as industries
FROM uses_products p
JOIN product_industry_subcategories pis ON p.id = pis.product_id
JOIN industry_sub_categories isc ON pis.subcategory_id = isc.id
JOIN industries i ON isc.industry_id = i.id
GROUP BY p.id, p.name;

-- =====================================================
-- PART 5: UPDATE TRIGGERS
-- =====================================================

-- Update timestamp trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_product_plant_parts_updated_at
    BEFORE UPDATE ON product_plant_parts
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_product_industry_subcategories_updated_at
    BEFORE UPDATE ON product_industry_subcategories
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- PART 6: EXAMPLE USAGE
-- =====================================================

/*
-- Example: Hempcrete uses multiple plant parts and industries

-- Add multiple plant parts
INSERT INTO product_plant_parts (product_id, plant_part_id, is_primary, usage_percentage, usage_description)
VALUES 
    (1234, 2, TRUE, 70.0, 'Hemp hurd provides the bulk material'),
    (1234, 5, FALSE, 30.0, 'Bast fiber adds structural reinforcement');

-- Add multiple industries
INSERT INTO product_industry_subcategories (product_id, subcategory_id, is_primary, relevance_score, application_notes)
VALUES 
    (1234, 15, TRUE, 1.0, 'Primary use in construction as building material'),
    (1234, 23, FALSE, 0.8, 'Also used in sustainable architecture'),
    (1234, 31, FALSE, 0.6, 'Research applications in materials science');
*/

-- Grant permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON product_plant_parts TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON product_industry_subcategories TO authenticated;
GRANT SELECT ON product_all_plant_parts TO authenticated;
GRANT SELECT ON product_all_industries TO authenticated;
GRANT USAGE ON SEQUENCE product_plant_parts_id_seq TO authenticated;
GRANT USAGE ON SEQUENCE product_industry_subcategories_id_seq TO authenticated;