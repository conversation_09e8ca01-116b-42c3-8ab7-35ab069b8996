-- Source Provenance & Verification Schema Enhancement
-- Adds tracking for data sources, confidence scores, and verification status
-- Author: Database Enhancement Team
-- Date: January 2025

-- =====================================================
-- PART 1: ADD PROVENANCE COLUMNS TO PRODUCTS TABLE
-- =====================================================

-- Add source tracking columns
ALTER TABLE uses_products 
ADD COLUMN IF NOT EXISTS source_url TEXT,
ADD COLUMN IF NOT EXISTS source_type VARCHAR(50) CHECK (source_type IN (
    'manufacturer_website',
    'research_paper',
    'patent',
    'news_article',
    'industry_database',
    'government_report',
    'trade_association',
    'user_submission',
    'ai_generated',
    'manual_entry',
    'unknown'
)),
ADD COLUMN IF NOT EXISTS confidence_score DECIMAL(3,2) DEFAULT 0.50 CHECK (confidence_score >= 0 AND confidence_score <= 1),
ADD COLUMN IF NOT EXISTS verification_status VARCHAR(20) DEFAULT 'unverified' CHECK (verification_status IN (
    'unverified',
    'pending_review',
    'verified',
    'disputed',
    'deprecated'
)),
ADD COLUMN IF NOT EXISTS last_verified_at TIMESTAMP,
ADD COLUMN IF NOT EXISTS verified_by VARCHAR(100),
ADD COLUMN IF NOT EXISTS verification_notes TEXT,
ADD COLUMN IF NOT EXISTS canonical_product_id INTEGER REFERENCES uses_products(id),
ADD COLUMN IF NOT EXISTS similarity_score DECIMAL(3,2) CHECK (similarity_score >= 0 AND similarity_score <= 1),
ADD COLUMN IF NOT EXISTS data_version INTEGER DEFAULT 1,
ADD COLUMN IF NOT EXISTS extraction_metadata JSONB;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_uses_products_verification_status ON uses_products(verification_status);
CREATE INDEX IF NOT EXISTS idx_uses_products_confidence_score ON uses_products(confidence_score);
CREATE INDEX IF NOT EXISTS idx_uses_products_source_type ON uses_products(source_type);
CREATE INDEX IF NOT EXISTS idx_uses_products_canonical_product ON uses_products(canonical_product_id);
CREATE INDEX IF NOT EXISTS idx_uses_products_last_verified ON uses_products(last_verified_at);

-- =====================================================
-- PART 2: CREATE AUDIT TABLES
-- =====================================================

-- Product version history table
CREATE TABLE IF NOT EXISTS uses_products_versions (
    id SERIAL PRIMARY KEY,
    product_id INTEGER NOT NULL REFERENCES uses_products(id) ON DELETE CASCADE,
    version_number INTEGER NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    plant_part_id INTEGER,
    industry_sub_category_id INTEGER,
    benefits_advantages TEXT,
    commercialization_stage VARCHAR(50),
    manufacturing_processes TEXT,
    sustainability_aspects TEXT,
    technical_specifications TEXT,
    keywords TEXT[],
    source_url TEXT,
    source_type VARCHAR(50),
    confidence_score DECIMAL(3,2),
    verification_status VARCHAR(20),
    change_type VARCHAR(50) CHECK (change_type IN (
        'create',
        'update',
        'merge',
        'verify',
        'dispute',
        'deprecate'
    )),
    change_reason TEXT,
    changed_by VARCHAR(100),
    changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    change_metadata JSONB,
    UNIQUE(product_id, version_number)
);

-- Create index for querying history
CREATE INDEX idx_product_versions_product ON uses_products_versions(product_id);
CREATE INDEX idx_product_versions_changed_at ON uses_products_versions(changed_at);

-- =====================================================
-- PART 3: CREATE SOURCE MANAGEMENT TABLES
-- =====================================================

-- Trusted sources table
CREATE TABLE IF NOT EXISTS trusted_sources (
    id SERIAL PRIMARY KEY,
    domain VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    source_type VARCHAR(50) NOT NULL,
    trust_score DECIMAL(3,2) DEFAULT 0.80 CHECK (trust_score >= 0 AND trust_score <= 1),
    is_active BOOLEAN DEFAULT TRUE,
    verification_frequency_days INTEGER DEFAULT 90,
    last_checked_at TIMESTAMP,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Product sources relationship (multiple sources per product)
CREATE TABLE IF NOT EXISTS product_sources (
    id SERIAL PRIMARY KEY,
    product_id INTEGER NOT NULL REFERENCES uses_products(id) ON DELETE CASCADE,
    source_url TEXT NOT NULL,
    source_type VARCHAR(50) NOT NULL,
    extracted_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    extraction_method VARCHAR(50),
    raw_data JSONB,
    is_primary_source BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(product_id, source_url)
);

CREATE INDEX idx_product_sources_product ON product_sources(product_id);
CREATE INDEX idx_product_sources_primary ON product_sources(is_primary_source) WHERE is_primary_source = TRUE;

-- =====================================================
-- PART 4: CREATE VERIFICATION WORKFLOW TABLES
-- =====================================================

-- Verification queue for human review
CREATE TABLE IF NOT EXISTS verification_queue (
    id SERIAL PRIMARY KEY,
    product_id INTEGER NOT NULL REFERENCES uses_products(id) ON DELETE CASCADE,
    priority VARCHAR(20) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    reason VARCHAR(100) NOT NULL,
    details TEXT,
    queued_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    assigned_to VARCHAR(100),
    assigned_at TIMESTAMP,
    completed_at TIMESTAMP,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN (
        'pending',
        'in_progress',
        'completed',
        'skipped',
        'escalated'
    )),
    verification_action TEXT,
    notes TEXT
);

CREATE INDEX idx_verification_queue_status ON verification_queue(status);
CREATE INDEX idx_verification_queue_priority ON verification_queue(priority);
CREATE INDEX idx_verification_queue_product ON verification_queue(product_id);

-- =====================================================
-- PART 5: CREATE CONFIDENCE SCORING FUNCTION
-- =====================================================

CREATE OR REPLACE FUNCTION calculate_confidence_score(p_product_id INTEGER)
RETURNS DECIMAL(3,2) AS $$
DECLARE
    v_score DECIMAL(3,2) := 0.0;
    v_source_score DECIMAL(3,2) := 0.0;
    v_completeness_score DECIMAL(3,2) := 0.0;
    v_verification_score DECIMAL(3,2) := 0.0;
    v_age_score DECIMAL(3,2) := 0.0;
    v_product RECORD;
BEGIN
    -- Get product details
    SELECT * INTO v_product FROM uses_products WHERE id = p_product_id;
    
    -- Source scoring (40% weight)
    IF v_product.source_type IN ('manufacturer_website', 'patent', 'government_report') THEN
        v_source_score := 0.9;
    ELSIF v_product.source_type IN ('research_paper', 'industry_database', 'trade_association') THEN
        v_source_score := 0.8;
    ELSIF v_product.source_type IN ('news_article') THEN
        v_source_score := 0.6;
    ELSIF v_product.source_type IN ('user_submission') THEN
        v_source_score := 0.4;
    ELSIF v_product.source_type IN ('ai_generated', 'manual_entry') THEN
        v_source_score := 0.3;
    ELSE
        v_source_score := 0.1;
    END IF;
    
    -- Completeness scoring (30% weight)
    v_completeness_score := 0.0;
    IF v_product.description IS NOT NULL AND LENGTH(v_product.description) > 200 THEN
        v_completeness_score := v_completeness_score + 0.2;
    END IF;
    IF v_product.benefits_advantages IS NOT NULL THEN
        v_completeness_score := v_completeness_score + 0.2;
    END IF;
    IF v_product.technical_specifications IS NOT NULL THEN
        v_completeness_score := v_completeness_score + 0.2;
    END IF;
    IF v_product.commercialization_stage IS NOT NULL THEN
        v_completeness_score := v_completeness_score + 0.2;
    END IF;
    IF EXISTS (SELECT 1 FROM product_images WHERE product_id = p_product_id) THEN
        v_completeness_score := v_completeness_score + 0.2;
    END IF;
    
    -- Verification scoring (20% weight)
    IF v_product.verification_status = 'verified' THEN
        v_verification_score := 1.0;
    ELSIF v_product.verification_status = 'pending_review' THEN
        v_verification_score := 0.5;
    ELSE
        v_verification_score := 0.0;
    END IF;
    
    -- Age scoring (10% weight) - newer is better for unverified
    IF v_product.verification_status != 'verified' THEN
        IF v_product.created_at > CURRENT_TIMESTAMP - INTERVAL '30 days' THEN
            v_age_score := 1.0;
        ELSIF v_product.created_at > CURRENT_TIMESTAMP - INTERVAL '90 days' THEN
            v_age_score := 0.7;
        ELSIF v_product.created_at > CURRENT_TIMESTAMP - INTERVAL '180 days' THEN
            v_age_score := 0.5;
        ELSE
            v_age_score := 0.3;
        END IF;
    ELSE
        v_age_score := 1.0; -- Verified products don't decay
    END IF;
    
    -- Calculate weighted score
    v_score := (v_source_score * 0.4) + 
               (v_completeness_score * 0.3) + 
               (v_verification_score * 0.2) + 
               (v_age_score * 0.1);
    
    RETURN ROUND(v_score, 2);
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- PART 6: CREATE TRIGGERS FOR VERSIONING
-- =====================================================

-- Function to create version history
CREATE OR REPLACE FUNCTION create_product_version()
RETURNS TRIGGER AS $$
DECLARE
    v_version_number INTEGER;
    v_change_type VARCHAR(50);
BEGIN
    -- Determine version number
    SELECT COALESCE(MAX(version_number), 0) + 1 
    INTO v_version_number
    FROM uses_products_versions 
    WHERE product_id = NEW.id;
    
    -- Determine change type
    IF TG_OP = 'INSERT' THEN
        v_change_type := 'create';
    ELSIF OLD.verification_status != NEW.verification_status AND NEW.verification_status = 'verified' THEN
        v_change_type := 'verify';
    ELSIF OLD.canonical_product_id IS NULL AND NEW.canonical_product_id IS NOT NULL THEN
        v_change_type := 'merge';
    ELSE
        v_change_type := 'update';
    END IF;
    
    -- Insert version record
    INSERT INTO uses_products_versions (
        product_id, version_number, name, description,
        plant_part_id, industry_sub_category_id,
        benefits_advantages, commercialization_stage,
        manufacturing_processes, sustainability_aspects,
        technical_specifications, keywords,
        source_url, source_type, confidence_score,
        verification_status, change_type
    ) VALUES (
        NEW.id, v_version_number, NEW.name, NEW.description,
        NEW.plant_part_id, NEW.industry_sub_category_id,
        NEW.benefits_advantages, NEW.commercialization_stage,
        NEW.manufacturing_processes, NEW.sustainability_aspects,
        NEW.technical_specifications, NEW.keywords,
        NEW.source_url, NEW.source_type, NEW.confidence_score,
        NEW.verification_status, v_change_type
    );
    
    -- Update version number
    NEW.data_version := v_version_number;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create versioning trigger
CREATE TRIGGER product_versioning_trigger
    BEFORE INSERT OR UPDATE ON uses_products
    FOR EACH ROW
    EXECUTE FUNCTION create_product_version();

-- =====================================================
-- PART 7: POPULATE INITIAL DATA
-- =====================================================

-- Insert some trusted sources
INSERT INTO trusted_sources (domain, name, source_type, trust_score) VALUES
    ('hempinc.com', 'Hemp Inc Official', 'manufacturer_website', 0.90),
    ('eiha.org', 'European Industrial Hemp Association', 'trade_association', 0.85),
    ('votehemp.com', 'Vote Hemp', 'trade_association', 0.85),
    ('ncbi.nlm.nih.gov', 'PubMed Central', 'research_paper', 0.95),
    ('patents.google.com', 'Google Patents', 'patent', 0.90),
    ('usda.gov', 'USDA', 'government_report', 0.95),
    ('hempbizjournal.com', 'Hemp Business Journal', 'news_article', 0.70),
    ('leafly.com', 'Leafly', 'industry_database', 0.75)
ON CONFLICT (domain) DO NOTHING;

-- Update confidence scores for existing products
UPDATE uses_products 
SET confidence_score = calculate_confidence_score(id)
WHERE confidence_score IS NULL;

-- =====================================================
-- PART 8: CREATE HELPER FUNCTIONS
-- =====================================================

-- Function to queue product for verification
CREATE OR REPLACE FUNCTION queue_for_verification(
    p_product_id INTEGER,
    p_reason VARCHAR(100),
    p_priority VARCHAR(20) DEFAULT 'medium',
    p_details TEXT DEFAULT NULL
) RETURNS BOOLEAN AS $$
BEGIN
    INSERT INTO verification_queue (product_id, reason, priority, details)
    VALUES (p_product_id, p_reason, p_priority, p_details)
    ON CONFLICT DO NOTHING;
    
    RETURN TRUE;
EXCEPTION
    WHEN OTHERS THEN
        RETURN FALSE;
END;
$$ LANGUAGE plpgsql;

-- Function to verify a product
CREATE OR REPLACE FUNCTION verify_product(
    p_product_id INTEGER,
    p_verified_by VARCHAR(100),
    p_notes TEXT DEFAULT NULL
) RETURNS BOOLEAN AS $$
BEGIN
    UPDATE uses_products
    SET 
        verification_status = 'verified',
        last_verified_at = CURRENT_TIMESTAMP,
        verified_by = p_verified_by,
        verification_notes = p_notes,
        confidence_score = GREATEST(confidence_score, 0.80)
    WHERE id = p_product_id;
    
    -- Update verification queue
    UPDATE verification_queue
    SET 
        status = 'completed',
        completed_at = CURRENT_TIMESTAMP,
        verification_action = 'verified',
        notes = p_notes
    WHERE product_id = p_product_id AND status IN ('pending', 'in_progress');
    
    RETURN TRUE;
EXCEPTION
    WHEN OTHERS THEN
        RETURN FALSE;
END;
$$ LANGUAGE plpgsql;

-- Grant permissions
GRANT SELECT, INSERT, UPDATE ON uses_products_versions TO authenticated;
GRANT SELECT, INSERT, UPDATE ON trusted_sources TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON product_sources TO authenticated;
GRANT SELECT, INSERT, UPDATE ON verification_queue TO authenticated;
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;