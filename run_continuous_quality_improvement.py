#!/usr/bin/env python3
"""
Continuous Quality Improvement Script
Processes all low-quality products in batches until none remain
"""

import os
import sys
import time
import json
from datetime import datetime

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.agents.quality_improvement_agent import QualityImprovementAgent

def main():
    """Run continuous improvement on all low-quality products"""
    print("Starting Continuous Quality Improvement Process")
    print("=" * 60)
    
    agent = None
    total_processed = 0
    total_improved = 0
    batch_number = 0
    batch_size = 50  # Process 50 products at a time
    
    try:
        agent = QualityImprovementAgent()
        
        while True:
            # Check how many low quality products remain
            low_quality_products = agent.get_low_quality_products(1000)
            remaining = len(low_quality_products)
            
            if remaining == 0:
                print("\n✅ All products have been improved!")
                break
            
            batch_number += 1
            print(f"\nBatch #{batch_number}")
            print("-" * 60)
            print(f"Remaining low quality products: {remaining}")
            
            # Run a batch
            results = agent.run_improvement_batch(batch_size=batch_size)
            
            # Update totals
            total_processed += results['products_processed']
            total_improved += results['products_improved']
            
            # Save batch results
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            batch_file = f'logs/quality_improvement_batch_{batch_number}_{timestamp}.json'
            
            os.makedirs('logs', exist_ok=True)
            with open(batch_file, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            
            print(f"Batch results saved to: {batch_file}")
            
            # Rate limiting between batches
            if remaining > batch_size:
                print("\nWaiting 5 seconds before next batch...")
                time.sleep(5)
        
        # Final summary
        print("\n" + "=" * 60)
        print("FINAL SUMMARY")
        print("=" * 60)
        print(f"Total batches processed: {batch_number}")
        print(f"Total products processed: {total_processed}")
        print(f"Total products improved: {total_improved}")
        print(f"Success rate: {total_improved/total_processed*100:.1f}%")
        
        # Save final summary
        summary = {
            'completed_at': datetime.now().isoformat(),
            'total_batches': batch_number,
            'total_processed': total_processed,
            'total_improved': total_improved,
            'success_rate': total_improved/total_processed if total_processed > 0 else 0
        }
        
        with open('logs/quality_improvement_summary.json', 'w') as f:
            json.dump(summary, f, indent=2)
        
    except KeyboardInterrupt:
        print("\n\n⚠️ Process interrupted by user")
        print(f"Processed {total_processed} products so far")
        
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        if agent:
            agent.close()
        print("\nQuality improvement process ended")

if __name__ == "__main__":
    main()