#!/usr/bin/env python3
"""
Demonstration script for the Advanced Deduplication Pipeline
Shows how to find and merge duplicates in the Hemp Database
"""

import os
import sys
from datetime import datetime
import logging

# Add the src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from deduplication.advanced_deduplication_pipeline import AdvancedDeduplicator, DuplicateCandidate

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def demonstrate_single_product_deduplication(dedup: AdvancedDeduplicator):
    """Demonstrate finding duplicates for a single product"""
    print("\n" + "="*60)
    print("DEMO 1: Single Product Duplicate Detection")
    print("="*60)
    
    # Find a product with potential duplicates
    dedup.cursor.execute("""
        SELECT p1.id, p1.name, p1.plant_part_id, p1.industry_sub_category_id
        FROM uses_products p1
        WHERE EXISTS (
            SELECT 1 FROM uses_products p2
            WHERE p2.id != p1.id
            AND p2.plant_part_id = p1.plant_part_id
            AND SIMILARITY(p2.name, p1.name) > 0.3
        )
        AND p1.verification_status != 'deprecated'
        ORDER BY p1.created_at DESC
        LIMIT 1
    """)
    
    result = dedup.cursor.fetchone()
    if not result:
        print("No suitable product found for demonstration")
        return
    
    product_id = result['id']
    product_name = result['name']
    
    print(f"\nAnalyzing product: {product_name} (ID: {product_id})")
    print("-" * 40)
    
    # Find duplicates
    duplicates = dedup.find_duplicates(product_id, limit=20)
    
    if not duplicates:
        print("No duplicates found")
        return
    
    print(f"\nFound {len(duplicates)} potential duplicates:")
    print("\n{:<10} {:<30} {:<15} {:<10}".format("ID", "Name", "Match Type", "Score"))
    print("-" * 70)
    
    for dup in duplicates[:10]:  # Show top 10
        print("{:<10} {:<30} {:<15} {:<10.3f}".format(
            dup.product_id,
            dup.name[:30],
            dup.match_type,
            dup.similarity_score
        ))
    
    # Show detailed analysis for top match
    if duplicates:
        top_match = duplicates[0]
        print(f"\nDetailed Analysis of Top Match (ID: {top_match.product_id}):")
        print(f"- Match Type: {top_match.match_type}")
        print(f"- Similarity Score: {top_match.similarity_score:.3f}")
        print(f"- Match Details:")
        
        for key, value in top_match.match_details.items():
            if key == 'shared_components' and isinstance(value, dict):
                print(f"  - {key}:")
                for comp_type, components in value.items():
                    if components:
                        print(f"    - {comp_type}: {', '.join(list(components)[:3])}")
            elif not isinstance(value, (dict, list)):
                print(f"  - {key}: {value}")

def demonstrate_batch_deduplication(dedup: AdvancedDeduplicator):
    """Demonstrate batch duplicate detection"""
    print("\n" + "="*60)
    print("DEMO 2: Batch Duplicate Detection")
    print("="*60)
    
    print("\nAnalyzing 50 recent products...")
    
    # Process batch
    all_duplicates = dedup.batch_find_duplicates(sample_size=50)
    
    if not all_duplicates:
        print("No duplicates found in batch")
        return
    
    # Statistics
    total_products_with_dups = len(all_duplicates)
    total_duplicate_pairs = sum(len(dups) for dups in all_duplicates.values())
    
    # Count by match type
    match_type_counts = {'exact': 0, 'fuzzy': 0, 'semantic': 0, 'component': 0}
    high_confidence_count = 0
    
    for product_dups in all_duplicates.values():
        for dup in product_dups:
            match_type_counts[dup.match_type] += 1
            if dup.similarity_score > 0.9:
                high_confidence_count += 1
    
    print(f"\nBatch Analysis Results:")
    print(f"- Products with duplicates: {total_products_with_dups}")
    print(f"- Total duplicate relationships: {total_duplicate_pairs}")
    print(f"- High confidence matches (>0.9): {high_confidence_count}")
    
    print(f"\nMatches by Type:")
    for match_type, count in match_type_counts.items():
        if count > 0:
            print(f"- {match_type.capitalize()}: {count}")
    
    # Show examples of each match type
    print("\nExample Matches by Type:")
    
    for match_type in ['exact', 'fuzzy', 'semantic', 'component']:
        print(f"\n{match_type.upper()} MATCH EXAMPLE:")
        
        # Find an example
        example_found = False
        for product_id, dups in all_duplicates.items():
            for dup in dups:
                if dup.match_type == match_type and dup.similarity_score > 0.8:
                    # Get product names
                    dedup.cursor.execute(
                        "SELECT name FROM uses_products WHERE id = %s",
                        (product_id,)
                    )
                    product_name = dedup.cursor.fetchone()['name']
                    
                    print(f"  Product 1: {product_name} (ID: {product_id})")
                    print(f"  Product 2: {dup.name} (ID: {dup.product_id})")
                    print(f"  Score: {dup.similarity_score:.3f}")
                    example_found = True
                    break
            if example_found:
                break
        
        if not example_found:
            print(f"  No high-confidence {match_type} matches found")

def demonstrate_merge_workflow(dedup: AdvancedDeduplicator):
    """Demonstrate the merge workflow for duplicates"""
    print("\n" + "="*60)
    print("DEMO 3: Duplicate Merge Workflow")
    print("="*60)
    
    # Find exact duplicates to merge
    dedup.cursor.execute("""
        SELECT p1.id as primary_id, p1.name as primary_name,
               p2.id as duplicate_id, p2.name as duplicate_name
        FROM uses_products p1
        JOIN uses_products p2 ON 
            p1.plant_part_id = p2.plant_part_id
            AND p1.industry_sub_category_id = p2.industry_sub_category_id
            AND lower(trim(p1.name)) = lower(trim(p2.name))
            AND p1.id < p2.id
        WHERE p1.verification_status != 'deprecated'
            AND p2.verification_status != 'deprecated'
        LIMIT 1
    """)
    
    result = dedup.cursor.fetchone()
    if not result:
        print("\nNo exact duplicates found for merge demonstration")
        print("(This is actually a good sign - the database is clean!)")
        return
    
    primary_id = result['primary_id']
    duplicate_id = result['duplicate_id']
    
    print(f"\nFound exact duplicate:")
    print(f"- Primary: {result['primary_name']} (ID: {primary_id})")
    print(f"- Duplicate: {result['duplicate_name']} (ID: {duplicate_id})")
    
    # Show what would be merged
    print("\nMerge Preview:")
    print("- Company associations will be consolidated")
    print("- Images will be combined (duplicates removed)")
    print("- Longer description will be kept")
    print("- Keywords will be merged")
    print("- Duplicate will be marked as 'deprecated'")
    
    # In a real scenario, you would call:
    # success = dedup.merge_duplicates(primary_id, [duplicate_id])
    print("\n(Merge not executed in demo mode)")

def generate_actionable_report(dedup: AdvancedDeduplicator):
    """Generate an actionable deduplication report"""
    print("\n" + "="*60)
    print("ACTIONABLE DEDUPLICATION REPORT")
    print("="*60)
    
    # Get statistics
    dedup.cursor.execute("""
        WITH duplicate_pairs AS (
            SELECT p1.id as id1, p2.id as id2
            FROM uses_products p1
            JOIN uses_products p2 ON 
                p1.plant_part_id = p2.plant_part_id
                AND p1.id < p2.id
                AND p1.verification_status != 'deprecated'
                AND p2.verification_status != 'deprecated'
            WHERE SIMILARITY(p1.name, p2.name) > 0.8
        )
        SELECT 
            (SELECT COUNT(DISTINCT id) FROM uses_products WHERE verification_status != 'deprecated') as total_products,
            (SELECT COUNT(*) FROM duplicate_pairs) as potential_duplicates,
            (SELECT COUNT(*) FROM uses_products WHERE confidence_score < 0.5) as low_confidence,
            (SELECT COUNT(*) FROM uses_products WHERE verification_status = 'unverified') as unverified
    """)
    
    stats = dedup.cursor.fetchone()
    
    print(f"\nDatabase Statistics:")
    print(f"- Total active products: {stats['total_products']:,}")
    print(f"- Potential duplicate pairs: {stats['potential_duplicates']:,}")
    print(f"- Low confidence products: {stats['low_confidence']:,}")
    print(f"- Unverified products: {stats['unverified']:,}")
    
    # Recommendations
    print(f"\nRecommendations:")
    print(f"1. Run full deduplication on all {stats['total_products']:,} products")
    print(f"2. Prioritize verification of {stats['low_confidence']:,} low-confidence products")
    print(f"3. Implement automated deduplication in data ingestion pipeline")
    print(f"4. Set up regular deduplication jobs (weekly recommended)")
    
    # Action items
    print(f"\nImmediate Action Items:")
    print(f"1. Review and merge {min(stats['potential_duplicates'], 50)} high-confidence duplicates")
    print(f"2. Queue top 100 unverified products for human review")
    print(f"3. Update confidence scores using calculate_confidence_score()")
    print(f"4. Generate canonical product mappings for known duplicates")

def main():
    """Main demonstration function"""
    # Get database URL
    db_url = os.getenv('DATABASE_URL')
    if not db_url:
        print("ERROR: DATABASE_URL environment variable not set")
        print("Please set it to your Supabase connection string")
        return
    
    # Initialize deduplicator
    print("Initializing Advanced Deduplication System...")
    dedup = AdvancedDeduplicator(db_url)
    
    try:
        dedup.connect()
        print("Connected to database successfully\n")
        
        # Run demonstrations
        demonstrate_single_product_deduplication(dedup)
        demonstrate_batch_deduplication(dedup)
        demonstrate_merge_workflow(dedup)
        generate_actionable_report(dedup)
        
        # Save full report
        print("\n" + "="*60)
        print("Generating full deduplication report...")
        
        all_duplicates = dedup.batch_find_duplicates(sample_size=200)
        report = dedup.generate_deduplication_report(all_duplicates)
        
        filename = f"deduplication_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        with open(filename, 'w') as f:
            f.write(report)
        
        print(f"Full report saved to: {filename}")
        
    except Exception as e:
        logger.error(f"Error during demonstration: {e}")
        raise
    
    finally:
        dedup.disconnect()
        print("\nDeduplication demonstration completed!")

if __name__ == "__main__":
    main()