#!/usr/bin/env python3
"""
Clean up low-quality products added by agents
Focus on July 8, 2025 products with 0.00 quality scores
"""

import psycopg2
import os
from datetime import datetime

def cleanup_low_quality_products():
    """Remove or fix low-quality products"""
    database_url = os.environ.get('DATABASE_URL')
    
    if not database_url:
        print("❌ Error: DATABASE_URL not set")
        return
    
    try:
        conn = psycopg2.connect(database_url)
        cur = conn.cursor()
        
        print("🧹 Analyzing low-quality products...")
        
        # First, identify low-quality products from July 8 by agent
        cur.execute("""
            SELECT id, name, description, source_agent
            FROM uses_products
            WHERE source_agent IN ('Simplified Patent Mining Agent', 'Academic Research Agent')
            AND created_at >= '2025-07-08'::date
            AND created_at < '2025-07-09'::date
            ORDER BY id
        """)
        
        low_quality_products = cur.fetchall()
        print(f"\nFound {len(low_quality_products)} products from problematic agents on July 8")
        
        # Show sample of problematic products
        print("\nSample of low-quality products:")
        for i, (id, name, desc, agent) in enumerate(low_quality_products[:5]):
            print(f"{i+1}. ID: {id}")
            print(f"   Name: {name}")
            print(f"   Agent: {agent}")
            print(f"   Issues: ", end="")
            
            # Identify specific issues
            issues = []
            if "  " in name:
                issues.append("double spaces in name")
            if "The " in name and "Application" in name:
                issues.append("malformed name pattern")
            if "based on academic research findings" in desc:
                issues.append("template description")
            if "developed using patented technology" in desc:
                issues.append("generic patent description")
            
            print(", ".join(issues) if issues else "general quality issues")
            print()
        
        # Ask for confirmation
        print(f"\n⚠️  Ready to delete {len(low_quality_products)} low-quality products")
        response = input("Proceed with deletion? (yes/no): ")
        
        if response.lower() == 'yes':
            # Delete the products
            product_ids = [p[0] for p in low_quality_products]
            
            cur.execute("""
                DELETE FROM uses_products
                WHERE id = ANY(%s)
            """, (product_ids,))
            
            deleted_count = cur.rowcount
            conn.commit()
            
            print(f"✅ Successfully deleted {deleted_count} low-quality products")
            
            # Show updated stats
            cur.execute("SELECT COUNT(*) FROM uses_products")
            total_count = cur.fetchone()[0]
            
            print(f"\n📊 Updated Database Stats:")
            print(f"   Total Products: {total_count}")
            print(f"   Removed {deleted_count} low-quality products")
            
        else:
            print("❌ Cleanup cancelled")
            conn.rollback()
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error during cleanup: {e}")

if __name__ == "__main__":
    cleanup_low_quality_products()