#!/usr/bin/env python3
"""
Image Quality Validator and Prompt Generator for Hemp Products
Implements recommendations 3-4: Image validation and better prompt generation
"""

import os
import re
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

class ImageIssueType(Enum):
    """Types of image quality issues"""
    FACE_PORTRAIT = "face_portrait"
    INTERIOR_ROOM = "interior_room"
    KEYBOARD_COMPUTER = "keyboard_computer"
    GENERIC_PLACEHOLDER = "generic_placeholder"
    MARIJUANA_CANNABIS = "marijuana_cannabis"
    DUPLICATE_OVERUSED = "duplicate_overused"
    MISSING = "missing"
    LOW_RELEVANCE = "low_relevance"
    VALID = "valid"

@dataclass
class ImageValidationResult:
    """Result of image validation"""
    issue_type: ImageIssueType
    confidence: float
    reason: str
    suggested_prompt: Optional[str] = None

class HempImageValidator:
    """Validates hemp product images for quality and relevance"""
    
    # Problematic patterns in image URLs or descriptions
    PROBLEMATIC_PATTERNS = {
        ImageIssueType.FACE_PORTRAIT: [
            r'face', r'portrait', r'person', r'headshot', r'selfie',
            r'man', r'woman', r'smile', r'human'
        ],
        ImageIssueType.INTERIOR_ROOM: [
            r'living', r'room', r'interior', r'furniture', r'couch',
            r'sofa', r'house', r'home', r'decor', r'indoor'
        ],
        ImageIssueType.KEYBOARD_COMPUTER: [
            r'keyboard', r'computer', r'laptop', r'desktop', r'mouse',
            r'monitor', r'screen', r'tech', r'office'
        ],
        ImageIssueType.GENERIC_PLACEHOLDER: [
            r'placeholder', r'generic', r'default', r'unknown',
            r'fallback', r'question', r'missing'
        ],
        ImageIssueType.MARIJUANA_CANNABIS: [
            r'marijuana', r'weed', r'joint', r'smoking', r'bong',
            r'pipe', r'recreational'
        ]
    }
    
    # Valid hemp-related patterns
    VALID_PATTERNS = [
        r'hemp', r'fiber', r'textile', r'seed', r'oil', r'cbd',
        r'extract', r'tincture', r'capsule', r'cream', r'lotion',
        r'concrete', r'plastic', r'biochar', r'sustainable',
        r'industrial', r'product', r'bottle', r'package'
    ]
    
    def validate_image(self, image_url: str, product_name: str, 
                      product_description: str) -> ImageValidationResult:
        """
        Validate if an image is appropriate for a hemp product
        """
        if not image_url:
            return ImageValidationResult(
                ImageIssueType.MISSING,
                1.0,
                "No image URL provided"
            )
        
        # Convert to lowercase for pattern matching
        url_lower = image_url.lower()
        name_lower = product_name.lower()
        desc_lower = product_description.lower() if product_description else ""
        
        # Check for problematic patterns
        for issue_type, patterns in self.PROBLEMATIC_PATTERNS.items():
            for pattern in patterns:
                if re.search(pattern, url_lower):
                    return ImageValidationResult(
                        issue_type,
                        0.9,
                        f"Image URL contains problematic pattern: {pattern}"
                    )
        
        # Check if image seems relevant to the product
        relevance_score = self._calculate_relevance(url_lower, name_lower, desc_lower)
        
        if relevance_score < 0.3:
            return ImageValidationResult(
                ImageIssueType.LOW_RELEVANCE,
                0.8,
                "Image appears unrelated to hemp product"
            )
        
        return ImageValidationResult(
            ImageIssueType.VALID,
            relevance_score,
            "Image appears valid"
        )
    
    def _calculate_relevance(self, url: str, name: str, description: str) -> float:
        """Calculate relevance score between image and product"""
        score = 0.0
        matches = 0
        
        for pattern in self.VALID_PATTERNS:
            if re.search(pattern, url):
                score += 0.2
                matches += 1
            if re.search(pattern, name):
                score += 0.1
                matches += 1
            if re.search(pattern, description):
                score += 0.05
                matches += 1
        
        # Normalize score
        return min(score, 1.0)

class HempImagePromptGenerator:
    """Generates high-quality image prompts for hemp products"""
    
    # Industry-specific prompt templates
    INDUSTRY_TEMPLATES = {
        "food": "Professional product photography of {product_name}, {specific_details}, food-grade packaging, kitchen or dining setting, natural lighting, appetizing presentation",
        "cosmetics": "Elegant beauty product photo of {product_name}, {specific_details}, premium skincare packaging, spa-like setting, soft natural lighting, luxury aesthetic",
        "medical": "Clinical product photo of {product_name}, {specific_details}, pharmaceutical-grade packaging, medical or laboratory setting, clean white background, professional lighting",
        "textiles": "Fashion textile photography of {product_name}, {specific_details}, fabric texture visible, sustainable fashion context, studio lighting, eco-friendly aesthetic",
        "construction": "Industrial product photo of {product_name}, {specific_details}, construction or architectural setting, showing strength and durability, professional photography",
        "agriculture": "Agricultural product photo of {product_name}, {specific_details}, farm or garden setting, natural outdoor lighting, sustainable farming context"
    }
    
    # Plant part specific details
    PLANT_PART_DETAILS = {
        "flower": "hemp flowers, CBD-rich buds, trichomes visible",
        "seed": "hemp seeds, nutritious appearance, omega-rich",
        "fiber": "hemp fibers, strong textile material, natural texture",
        "oil": "hemp oil, golden or green tint, premium bottle",
        "root": "hemp root extract or material, earthy natural appearance",
        "leaf": "hemp leaves, distinctive serrated edges, vibrant green",
        "stem": "hemp stalks or hurd, industrial material, fibrous texture"
    }
    
    def generate_prompt(self, product_name: str, description: str,
                       industry: str, plant_part: str,
                       additional_details: Optional[Dict] = None) -> str:
        """
        Generate a high-quality image prompt for a hemp product
        """
        # Clean product name
        clean_name = self._clean_product_name(product_name)
        
        # Get industry template
        template = self.INDUSTRY_TEMPLATES.get(
            industry.lower(), 
            "Professional product photo of {product_name}, {specific_details}, "
            "hemp-based product, sustainable packaging, studio lighting"
        )
        
        # Get plant part details
        part_details = self.PLANT_PART_DETAILS.get(
            plant_part.lower(),
            "hemp-based material"
        )
        
        # Extract specific details from description
        specific_details = self._extract_product_details(description, product_name)
        
        # Add plant part to specific details
        if part_details:
            specific_details = f"{specific_details}, made with {part_details}"
        
        # Generate final prompt
        prompt = template.format(
            product_name=clean_name,
            specific_details=specific_details
        )
        
        # Add any additional details
        if additional_details:
            extra = ", ".join([f"{k}: {v}" for k, v in additional_details.items()])
            prompt += f", {extra}"
        
        # Ensure prompt isn't too long
        if len(prompt) > 300:
            prompt = prompt[:297] + "..."
        
        return prompt
    
    def _clean_product_name(self, name: str) -> str:
        """Clean product name for use in prompt"""
        # Remove company names and excessive technical terms
        cleaned = re.sub(r'\b(LLC|Inc|Corp|Company|Industries)\b', '', name, flags=re.I)
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()
        return cleaned
    
    def _extract_product_details(self, description: str, name: str) -> str:
        """Extract key product details from description"""
        details = []
        
        # Look for key product attributes
        if re.search(r'tincture|oil|extract', description, re.I):
            details.append("liquid extract in glass bottle")
        elif re.search(r'capsule|pill|tablet', description, re.I):
            details.append("dietary supplement capsules")
        elif re.search(r'cream|lotion|balm', description, re.I):
            details.append("topical skincare product")
        elif re.search(r'fabric|textile|cloth', description, re.I):
            details.append("sustainable textile material")
        elif re.search(r'concrete|block|building', description, re.I):
            details.append("construction material")
        elif re.search(r'plastic|polymer|resin', description, re.I):
            details.append("biodegradable plastic alternative")
        
        # Look for concentration or strength
        concentration = re.search(r'(\d+)\s*(mg|%|percent)', description, re.I)
        if concentration:
            details.append(f"{concentration.group(0)} concentration")
        
        # Look for certifications
        if re.search(r'organic|certified|gmp|fda', description, re.I):
            details.append("certified quality")
        
        return ", ".join(details) if details else "premium hemp product"

# Example usage
if __name__ == "__main__":
    validator = HempImageValidator()
    generator = HempImagePromptGenerator()
    
    # Test validation
    test_cases = [
        ("https://example.com/face-portrait.jpg", "Hemp Oil", "Premium hemp oil"),
        ("https://example.com/hemp-tincture.jpg", "CBD Tincture", "Full spectrum CBD"),
        ("https://example.com/living-room.jpg", "Hemp Fiber", "Strong hemp fiber"),
    ]
    
    print("Image Validation Tests:")
    for url, name, desc in test_cases:
        result = validator.validate_image(url, name, desc)
        print(f"\nURL: {url}")
        print(f"Result: {result.issue_type.value} (confidence: {result.confidence})")
        print(f"Reason: {result.reason}")
    
    # Test prompt generation
    print("\n\nPrompt Generation Tests:")
    products = [
        ("CBG Focus Tincture", "Premium CBG extract for mental clarity", "medical", "flower"),
        ("Hemp Concrete Blocks", "Sustainable building blocks with hemp fiber", "construction", "fiber"),
        ("Hemp Seed Oil Moisturizer", "Nourishing facial cream with hemp seed oil", "cosmetics", "seed"),
    ]
    
    for name, desc, industry, part in products:
        prompt = generator.generate_prompt(name, desc, industry, part)
        print(f"\nProduct: {name}")
        print(f"Prompt: {prompt}")