#!/usr/bin/env python3
"""
Quick Company Enrichment - Using Supabase MCP for immediate results
No Python dependencies needed - runs through <PERSON>'s MCP
"""

print("🏢 Quick Company Enrichment Script")
print("This script will create a SQL file you can run via Supabase MCP")
print("=" * 50)

# Generate SQL for common website patterns
website_updates = """
-- Add websites for companies based on common patterns
UPDATE hemp_companies SET
    website = CASE 
        WHEN name = 'Elixinol' THEN 'https://elixinol.com'
        WHEN name = 'Manitoba Harvest' THEN 'https://manitobaharvest.com'
        WHEN name = 'Charlotte''s Web' THEN 'https://charlottesweb.com'
        WHEN name = 'CV Sciences' THEN 'https://cvsciences.com'
        WHEN name = 'Nutiva' THEN 'https://nutiva.com'
        WHEN name = 'Dr. Bronner''s' THEN 'https://drbronner.com'
        WHEN name = 'Patagonia Provisions' THEN 'https://patagoniaprovisions.com'
        WHEN name = 'Evo Hemp' THEN 'https://evohemp.com'
        WHEN name = 'Hemp Oil Canada' THEN 'https://hempoilcan.com'
        WHEN name = 'Hempz' THEN 'https://hempz.com'
        WHEN name = 'The Body Shop' THEN 'https://thebodyshop.com'
        WHEN name = 'Pacific Hemp' THEN 'https://pacifichemp.com'
        ELSE website
    END,
    updated_at = NOW()
WHERE website IS NULL 
  AND name IN ('Elixinol', 'Manitoba Harvest', 'Charlotte''s Web', 'CV Sciences', 
               'Nutiva', 'Dr. Bronner''s', 'Patagonia Provisions', 'Evo Hemp',
               'Hemp Oil Canada', 'Hempz', 'The Body Shop', 'Pacific Hemp');
"""

# Add common countries
country_updates = """
-- Add countries for well-known companies
UPDATE hemp_companies SET
    country = CASE 
        WHEN name IN ('Charlotte''s Web', 'CV Sciences', 'Nutiva', 'Dr. Bronner''s', 
                      'Patagonia Provisions', 'Evo Hemp') THEN 'United States'
        WHEN name IN ('Manitoba Harvest', 'Hemp Oil Canada') THEN 'Canada'
        WHEN name = 'Elixinol' THEN 'Australia'
        WHEN name = 'The Body Shop' THEN 'United Kingdom'
        WHEN name LIKE '%USA%' OR name LIKE '%America%' THEN 'United States'
        WHEN name LIKE '%Canada%' OR name LIKE '%Canadian%' THEN 'Canada'
        WHEN name LIKE '%UK%' OR name LIKE '%British%' THEN 'United Kingdom'
        WHEN name LIKE '%Euro%' OR name LIKE '%EU%' THEN 'European Union'
        ELSE country
    END,
    updated_at = NOW()
WHERE country IS NULL;
"""

# Add founded years based on industry knowledge
founded_year_updates = """
-- Add founded years for known companies
UPDATE hemp_companies SET
    founded_year = CASE 
        WHEN name = 'Dr. Bronner''s' THEN 1948
        WHEN name = 'The Body Shop' THEN 1976
        WHEN name = 'Manitoba Harvest' THEN 1998
        WHEN name = 'Nutiva' THEN 1999
        WHEN name = 'Hemp Oil Canada' THEN 1998
        WHEN name = 'Charlotte''s Web' THEN 2013
        WHEN name = 'CV Sciences' THEN 2010
        WHEN name = 'Elixinol' THEN 2014
        WHEN name = 'Hempz' THEN 1998
        ELSE founded_year
    END,
    updated_at = NOW()
WHERE founded_year IS NULL
  AND name IN ('Dr. Bronner''s', 'The Body Shop', 'Manitoba Harvest', 'Nutiva',
               'Hemp Oil Canada', 'Charlotte''s Web', 'CV Sciences', 'Elixinol', 'Hempz');
"""

# Fix company types
company_type_updates = """
-- Standardize company types to lowercase
UPDATE hemp_companies 
SET company_type = LOWER(company_type)
WHERE company_type != LOWER(company_type);

-- Infer company types from names and descriptions
UPDATE hemp_companies SET
    company_type = CASE
        WHEN description LIKE '%manufacturer%' OR description LIKE '%manufacturing%' 
             OR description LIKE '%produces%' THEN 'manufacturer'
        WHEN description LIKE '%distributor%' OR description LIKE '%wholesale%' THEN 'distributor'
        WHEN description LIKE '%retailer%' OR description LIKE '%retail%' THEN 'retailer'
        WHEN description LIKE '%brand%' AND company_type = 'brand' THEN 'brand'
        WHEN description LIKE '%grower%' OR description LIKE '%cultivation%' THEN 'grower'
        WHEN description LIKE '%research%' OR description LIKE '%laboratory%' THEN 'research'
        ELSE company_type
    END,
    updated_at = NOW()
WHERE company_type IN ('Brand', 'brand');
"""

# Write SQL file
with open('company_enrichment_queries.sql', 'w') as f:
    f.write("-- Company Enrichment SQL Queries\n")
    f.write("-- Run these via Supabase MCP\n\n")
    f.write(website_updates + "\n\n")
    f.write(country_updates + "\n\n")
    f.write(founded_year_updates + "\n\n")
    f.write(company_type_updates + "\n\n")
    f.write("-- Check results\n")
    f.write("SELECT COUNT(*) as total,\n")
    f.write("       COUNT(website) as with_website,\n")
    f.write("       COUNT(country) as with_country,\n")
    f.write("       COUNT(founded_year) as with_founded_year\n")
    f.write("FROM hemp_companies;\n")

print("✅ Created company_enrichment_queries.sql")
print("\nTo run the enrichment:")
print("1. Copy the contents of company_enrichment_queries.sql")
print("2. Use Claude to execute via Supabase MCP")
print("\nOr run directly:")
print("python3 run_company_enrichment.py")