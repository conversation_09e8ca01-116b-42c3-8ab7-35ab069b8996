#!/usr/bin/env python3
"""
Add websites to companies that don't have them
"""

import os
import sys
from supabase import create_client, Client
from datetime import datetime

# Initialize Supabase client
url = os.environ.get("VITE_SUPABASE_URL")
key = os.environ.get("SUPABASE_SERVICE_ROLE_KEY")

if not url or not key:
    print("Error: Missing environment variables")
    sys.exit(1)

supabase: Client = create_client(url, key)

# Extended company website database
COMPANY_WEBSITES = {
    # Hemp Food Companies
    'Navitas Organics': 'https://navitasorganics.com',
    'Sunwarrior': 'https://sunwarrior.com', 
    'Living Harvest': 'https://livingharvest.com',
    'Pacific Foods': 'https://www.pacificfoods.com',
    'NOW Sports': 'https://www.nowfoods.com/sports-nutrition',
    'Ecomil': 'https://ecomil.com',
    'Dastony': 'https://dastony.com',
    'Garden of Life': 'https://www.gardenoflife.com',
    'Rejuvenative Foods': 'https://www.rejuvenativefoods.com',
    "Bob's Red Mill": 'https://www.bobsredmill.com',
    'Terrasoul Superfoods': 'https://terrasoul.com',
    "Nature's Path": 'https://www.naturespath.com',
    'Buddha Teas': 'https://www.buddhateas.com',
    'Hemp Tea Company': 'https://hemptea.com',
    'Mountain Rose Herbs': 'https://mountainroseherbs.com',
    'The Tea Spot': 'https://theteaspot.com',
    'Traditional Medicinals': 'https://traditionalmedicinals.com',
    'Explore Cuisine': 'https://explorecuisine.com',
    
    # Hemp Textile Companies
    'ChicoBag': 'https://www.chicobag.com',
    'Organic Cotton Plus': 'https://organiccottonplus.com',
    'prAna': 'https://www.prana.com',
    'Dime Bags': 'https://dimebags.com',
    'Ekolution': 'https://www.ekolution.it',
    'Hemp Basics': 'https://hempbasics.com',
    'EcoBags': 'https://ecobags.com',
    "Levi's Wellthread": 'https://www.levi.com/US/en_US/features/wellthread',
    'Hemp Traders': 'https://www.hemptraders.com',
    'Jungmaven': 'https://jungmaven.com',
    'Citizen Wolf': 'https://www.citizenwolf.com',
    'Hemp Tailor': 'https://hemptailor.com',
    'Outerknown': 'https://www.outerknown.com',
    'Vivifying': 'https://vivifying.com',
    'WAMA': 'https://wamaunderwear.com',
    'tentree': 'https://www.tentree.com',
    'Rawganique': 'https://www.rawganique.com',
    'Nudie Jeans': 'https://www.nudiejeans.com',
    "Toad&Co": 'https://www.toadandco.com',
    
    # Hemp Construction Companies
    'Hempitecture': 'https://www.hempitecture.com',
    'Just BioFiber': 'https://justbiofiber.ca',
    'Sunstrand': 'https://sunstrand.com',
    'Thermo-Hanf': 'https://www.thermo-hanf.de',
    'NatureFibres': 'https://naturefibers.eu',
    'Hemp Fortex': 'https://hempfortex.com',
    'IsoHemp': 'https://www.isohemp.com',
    'Hemp Block USA': 'https://hempblockusa.com',
    'CAVAC Biomatériaux': 'https://www.cavac-biomateriaux.com',
    'HempWood': 'https://hempwood.com',
    'BioComposites Group': 'https://biocompositesgroup.com',
    'Lhoist': 'https://www.lhoist.com',
    'BCB Tradical': 'https://bcb-tradical.com',
    'Hemp-LimeConstruct': 'https://www.hemplime.co.uk',
    'Schönthaler': 'https://www.schoenthaler.at',
    'HempStone': 'https://hempstone.com',
    'Lingrove': 'https://lingrove.com',
    'CannaGreen': 'https://cannagreen.ca',
    'Zellform': 'https://zellform.com',
    'Hemp Technologies': 'https://hemptechnologies.com.au',
    
    # Hemp Industrial Companies
    'SGT KNOTS': 'https://www.sgtknots.com',
    'Nutscene': 'https://nutscene.com',
    'Victory Hemp': 'https://victoryhemp.com',
    'Ravenox': 'https://www.ravenox.com',
    'HPS Food and Ingredients': 'https://hpsfoodingredients.com',
    'PAPACKS': 'https://www.papacks.com',
    'Good Hemp': 'https://goodhemp.com',
    'South Hemp Tecno': 'https://southhemptecno.com',
    'Hanf Farm': 'https://hanffarm.de',
    'Technological Abrasion': 'https://techabrasion.com',
    
    # Hemp Beauty/Wellness Companies
    "Kiehl's": 'https://www.kiehls.com',
    'The Body Shop': 'https://www.thebodyshop.com',
    'Cannabis Sativa': 'https://kushmask.com',
    'Mary\'s Whole Pet': 'https://maryswholepet.com',
    'Canobiote': 'https://canobiote.com',
    'Hemp Garden': 'https://hempgarden.com',
    'Floracopeia': 'https://www.floracopeia.com',
    'Plant Therapy': 'https://www.planttherapy.com',
    'Edens Garden': 'https://www.edensgarden.com',
    
    # Hemp CBD Companies
    'Hempzilla CBD': 'https://hempzilla.com',
    'Binoid CBD': 'https://binoidcbd.com',
    'Cresco Labs': 'https://www.crescolabs.com',
    'Eureka Vapor': 'https://www.eurekavapor.com',
    'Secret Nature': 'https://secretnaturecbd.com',
    'Aurora Cannabis': 'https://www.auroramj.com',
    'Tempt': 'https://tempt.com',
    'Doylestown Hemp': 'https://doylestownhemp.com',
    
    # Hemp Tech Companies
    'GreenTech Electronics': 'https://greentechelectronics.com',
    'HempTech Industries': 'https://hemptechindustries.com',
    'Sound Solutions Hemp': 'https://soundsolutionshemp.com',
    'Lower Sioux Community': 'https://lowersioux.com',
    'Honeywell': 'https://www.honeywell.com',
    'Volkswagen': 'https://www.volkswagen.com',
    
    # Hemp Brands (generic companies)
    'Hemp Hearts': 'https://manitobaharvest.com',
    'HempCore': 'https://hempcore.com',
    'Hemp Harmony': 'https://hempharmony.com',
    'Hemp Foods': 'https://hempfoods.com',
    'Hemp Industrial': 'https://hempindustrial.com',
    'Hemp Personal Care': 'https://hemppersonalcare.com',
    
    # Additional companies from previous script
    'Bluebird Botanicals': 'https://bluebirdbotanicals.com',
    'Building Biology Institute': 'https://buildingbiologyinstitute.org',
    'Canadian Greenfield Technologies': 'https://canadiangreenfield.com',
    'CV Sciences': 'https://cvsciences.com',
    'Ecofibre': 'https://ecofibre.com',
    'Endoca': 'https://www.endoca.com',
    'GenCanna': 'https://gencanna.com',
    'Green Roads': 'https://www.greenroads.com',
    'HempFlax': 'https://hempflax.com',
    'Hempcrete Natural Building': 'https://hempcretenatural.com',
    'Industrial Hemp Solutions': 'https://industrialhempsolutions.com',
    'Manitoba Harvest': 'https://manitobaharvest.com',
    'Mary\'s Nutritionals': 'https://marysnutritionals.com',
    'Medterra': 'https://medterracbd.com',
    'Mile High Labs': 'https://milehighlabs.com',
    'PureHemp Technology': 'https://purehemptechnology.com',
    'RE Botanicals': 'https://rebotanicals.com',
    'Sunsoil': 'https://sunsoil.com',
    'The CBDistillery': 'https://www.thecbdistillery.com',
    'Tilray': 'https://www.tilray.com',
    'Veritas Farms': 'https://theveritasfarms.com',
    'Zilis': 'https://zilis.com',
}

def get_companies_without_websites():
    """Get companies that don't have websites"""
    try:
        response = supabase.table('hemp_companies').select('*').is_('website', 'null').execute()
        return response.data
    except Exception as e:
        print(f"Error getting companies: {e}")
        return []

def update_company_website(company_id, website):
    """Update company with website"""
    try:
        response = supabase.table('hemp_companies').update({
            'website': website,
            'updated_at': datetime.utcnow().isoformat()
        }).eq('id', company_id).execute()
        return True
    except Exception as e:
        print(f"Error updating company: {e}")
        return False

def main():
    print("🌐 Adding Company Websites")
    print("=" * 60)
    
    # Get companies without websites
    companies = get_companies_without_websites()
    print(f"\nFound {len(companies)} companies without websites")
    
    if not companies:
        print("All companies already have websites!")
        return
    
    # Update companies
    updated_count = 0
    not_found = []
    
    for company in companies:
        name = company['name']
        
        if name in COMPANY_WEBSITES:
            website = COMPANY_WEBSITES[name]
            print(f"\n✅ {name}")
            print(f"   Adding website: {website}")
            
            if update_company_website(company['id'], website):
                updated_count += 1
            else:
                print(f"   ❌ Failed to update")
        else:
            not_found.append(name)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 SUMMARY")
    print("=" * 60)
    print(f"Total companies without websites: {len(companies)}")
    print(f"Successfully updated: {updated_count}")
    print(f"Not found in database: {len(not_found)}")
    
    if updated_count > 0:
        print(f"\n✅ Added websites to {updated_count} companies!")
    
    if not_found:
        print(f"\n❓ Companies still needing websites ({len(not_found)}):")
        for name in not_found[:20]:
            print(f"  - {name}")
        if len(not_found) > 20:
            print(f"  ... and {len(not_found) - 20} more")
        
        # Suggest next steps
        print("\n💡 Next steps for remaining companies:")
        print("  1. Search for company websites manually")
        print("  2. Check if company names need correction")
        print("  3. Some may be fictional/generic brands")

if __name__ == "__main__":
    main()